<!-- 工单审核列表 -->
<template>
	<view class="main">
		<!-- <scroll-view :lower-threshold="50" :refresher-threshold="50" scroll-y refresher-enabled
			refresher-default-style="white" :refresher-triggered="triggered" @refresherrefresh="onRefresh"
			@scrolltolower="showMoreData"> -->
			<view class="card-box" v-for="(data,index) in tableData" :key="index" @click="toDetail(data)">
				<view class="flex-between hand">
					<view class="hand-title flex-center">
						<view class="icon-box">
							<u-icon name="custom-icon-gongdan" customPrefix="custom-icon" color="#FFFFFF" size="20">
							</u-icon>
						</view>
						<text>{{data.title}}</text>
					</view>
					<view class="status">
						{{data.statusName}}
					</view>
				</view>
				<view class="table">
					<view class="info">
						<text>工单编号：</text> <text>{{data.serialNo}}</text>
					</view>
					<view class="info">
						<text>工单类型：</text> <text>{{data.type}}</text>
					</view>
					<view class="info">
						<text>地址：</text> <text>{{data.address}}</text>
					</view>
					<view class="info">
						<text>完成时间：</text> <text class="text-color-o">{{data.completeTime}}</text>
					</view>
				</view>
			</view>
			<u-loadmore bg-color="F9F9F9" :status="state.status" loading-text="努力加载中" loadmore-text="加载更多" nomore-text="没有了更多" />
		<!-- </scroll-view> -->
		<view class="">
			<u-calendar :max-date="maxDate" v-model="state.dateShow" ref="calendar" @close="state.dateShow=false"
				@change="chooseDate"></u-calendar>
		</view>
	</view>
</template>

<script lang="ts" setup>
	import {
		reactive,
		ref
	} from "vue";
	import {
		onShow
	} from '@dcloudio/uni-app';
	import {
		onPullDownRefresh,
		onReachBottom
	} from '@dcloudio/uni-app'
	import {
		workOrderList
	} from '@/common/api/workOrder'
	import {
		maxDate
	} from '@/common/data/publicdata'
	import {
		removeSlash
	} from '@/common/utils/removeIdSlash'
	// 选择区域
	const state = reactive < {
		activceTab: any,
		dateShow: boolean,
		status: string,
		query: any,
	} > ({
		activceTab: {},
		dateShow: false,
		status: 'loadmore',
		query: {
			page: 1,
			size: 10
		}
	})
	const triggered = ref < boolean > ()
	const tableData = ref < any > ([])
	//
	const toDetail = (params ? : any) => {
		uni.$u.route({
			url: 'pages/smartOperation/workOrderHandover/detail/index',
			params: {
				id: params.id,
				fromType: 'approval',
				status: params.status
			}
		})
	}
	//选择日期
	const chooseDate = () => {}

	// 加载更多
	const showMoreData = async () => {
		console.log('dddddd')
		state.status = 'loading'
		await getWorkOrderList()
	} ///

	// 下拉刷新
	const onRefresh = async () => {
		triggered.value = true
		state.query.page = 1
		await getWorkOrderList()
	}
	// 巡检列表
	const getWorkOrderList = async () => {
		state.status = 'loadmore'
		state.query = {
			...state.query,
			status: 'HANDOVER_REVIEW',
			stepProcessUserId: removeSlash(uni.getStorageSync('userInfo').id?.id)
			// type: '二供泵房'
		}
		const res = await workOrderList(state.query)
		const data = res.data?.data?.data
		const total = res.data?.data?.total
		if (state.query.page === 1) {
			tableData.value = data
		} else {
			tableData.value = tableData.value.concat(data)
		}
		if (data?.length > 0 && total > tableData.value.length) {

			state.query.page += 1
			state.status = 'loadmore'
		} else {
			state.status = 'nomore'
		}
		triggered.value = false
		uni.stopPullDownRefresh()
	}

	onReachBottom(async()=>{
		showMoreData()
	})

	onPullDownRefresh(async()=>{
		onRefresh()
	})
	onShow(async () => {
		onRefresh()
	})
</script>

<style lang="scss" scoped>
	.main{
		padding-top: 20rpx;
	}
	.card-box {
		width: 686rpx;
		margin: 20rpx auto;
		border-radius: 16rpx;
		padding: 22rpx 28rpx;
		background-color: #FFFFFF;

		.hand {
			.hand-title {
				height: 42rpx;
				line-height: 42rpx;

				.icon-box {
					text-align: center;
					line-height: 32rpx;
					height: 32rpx;
					width: 32rpx;
					background-color: #3862F8;
					border-radius: 4rpx;
				}

				text {
					font-size: 28rpx;
					margin-left: 16rpx;
					color: #060F27;
					font-weight: 600;
				}
			}

			.status {
				color: #91949F;
			}
		}

		.table {
			margin-top: 24rpx;

			.info {
				font-size: 24rpx;
				padding-bottom: 18rpx;
				display: flex;

				text {
					&:nth-child(1) {
						color: #91949F;
					}

					&:nth-child(2) {
						flex: 1;
					}
				}


				.bg {
					padding: 2rpx 12rpx;
					background: rgba(56, 98, 248, 0.2);
					border-radius: 8rpx;

					text {
						color: #3862F8;
					}
				}
			}
		}
	}
</style>
