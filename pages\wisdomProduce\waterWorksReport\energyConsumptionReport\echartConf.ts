export const lineOption = (dataX?: string[], seriesData?: { name: string, data: number[] }[], color?: string) => {
	const dataArray = seriesData?.map(s => ({
		name: s.name,
		data: s.data || [],
		type: 'line',
		color: color,
		areaStyle: {
			opacity: 0.1
		}
	})) || []

	return {
		color: ['#1890FF', '#91CB74', '#FAC858', '#EE6666', '#73C0DE', '#3CA272', '#FC8452', '#9A60B4', '#ea7ccc'],
		animation:false,
		grid: {
			left: 50,
			right: 20,
			top: 20,
		},
		xAxis: {
			type: 'category',
			boundaryGap: false,
			data: dataX || []
		},
		yAxis: {
			type: 'value',
			show: true
		},
		tooltip: {
			trigger: 'axis',
			confine: true
		},
		dataZoom: {
			type: 'inside',
			filterMode: 'filter'
		},
		series: dataArray
	}
}