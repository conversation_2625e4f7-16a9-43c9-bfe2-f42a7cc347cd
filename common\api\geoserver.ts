/// <reference types="@dcloudio/types" />
import { createRequest } from '../vmeitime-http/interface';
import { gisConfig } from '../data/gisData';
import { queryCache, cached } from '../utils/queryCache';
import { getQueryableLayers, standardizeQueryResult } from '../config/geoLayers';

declare const uni: {
  request(options: {
    url: string;
    method?: string;
    timeout?: number;
    data?: any;
  }): Promise<{
    statusCode: number;
    data: any;
  }>;
  getStorageSync(key: string): any;
  setStorageSync(key: string, value: any): void;
};

/**
 * GeoServer 查询接口封装
 */

// 创建 GeoServer 专用的 HTTP 请求实例
const createGeoServerRequest = () => {
  const config = gisConfig();
  return createRequest({
    baseUrl: config.gisPipeDynamicService,
    header: {
      'Content-Type': 'application/json',
    },
  });
};

// 获取认证 token
const getAuthToken = async () => {
  let token = uni.getStorageSync('gToken');
  if (!token) {
    // 如果没有 token，尝试获取
    const { getGtoken } = await import('./map');
    token = await getGtoken();
  }
  return token;
};

/**
 * 构建 WFS 查询 URL
 */
const buildWFSUrl = (params: {
  service?: string;
  version?: string;
  request?: string;
  typeName?: string;
  outputFormat?: string;
  cql_filter?: string;
  bbox?: string;
  maxFeatures?: number;
}) => {
  const config = gisConfig();
  const baseUrl = config.gisUtilitiesService || config.gisService;

  const defaultParams = {
    service: 'WFS',
    version: '1.1.0',
    request: 'GetFeature',
    outputFormat: 'application/json',
    maxFeatures: 1000,
    ...params,
  };

  const queryString = Object.entries(defaultParams)
    .filter(([_, value]) => value !== undefined && value !== null)
    .map(([key, value]) => `${key}=${encodeURIComponent(String(value))}`)
    .join('&');

  return `${baseUrl}/wfs?${queryString}`;
};

/**
 * 构建 WMS 查询 URL (GetFeatureInfo)
 */
const buildWMSQueryUrl = (params: {
  layers: string;
  query_layers?: string;
  x: number;
  y: number;
  width: number;
  height: number;
  bbox: string;
  srs?: string;
  info_format?: string;
  feature_count?: number;
}) => {
  const config = gisConfig();
  const baseUrl = config.gisUtilitiesService || config.gisService;

  const defaultParams = {
    service: 'WMS',
    version: '1.1.1',
    request: 'GetFeatureInfo',
    srs: 'EPSG:4326',
    info_format: 'application/json',
    feature_count: 50,
    query_layers: params.layers,
    ...params,
  };

  const queryString = Object.entries(defaultParams)
    .map(([key, value]) => `${key}=${encodeURIComponent(String(value))}`)
    .join('&');

  return `${baseUrl}/wms?${queryString}`;
};

/**
 * 点击查询接口 (基于WMS GetFeatureInfo)
 * 根据地图坐标点查询管网要素
 */
export const queryPipelineByPoint = async (params: {
  layers:Array<string>;
  longitude: number;
  latitude: number;
  srs: string;
  mapWidth: number;
  mapHeight: number;
  mapExtent: any;
  tolerance?: number;
}) => {
  try {
    const {
      layers,
      longitude,
      latitude,
      srs,
      mapWidth,
      mapHeight,
      mapExtent,
      tolerance = 5,
    } = params;

    // 生成查询BBOX
    let queryBbox: string;
    if (mapExtent) {
      queryBbox = `${mapExtent.xmin},${mapExtent.ymin},${mapExtent.xmax},${mapExtent.ymax}`;
    } else {
      // 根据坐标系和容差生成BBOX
      let offset: number;
      if (srs === 'EPSG:3857') {
        offset = tolerance * 10; // Web Mercator，米
      } else {
        offset = (tolerance * 10) / 111000; // WGS84，度
      }
      queryBbox = `${longitude - offset},${latitude - offset},${longitude + offset},${latitude + offset}`;
    }

    // 计算像素坐标
    const bboxParts = queryBbox.split(',').map(Number);
    const [minX, minY, maxX, maxY] = bboxParts;
    const pixelX = Math.round(((longitude - minX) / (maxX - minX)) * mapWidth);
    const pixelY = Math.round(((maxY - latitude) / (maxY - minY)) * mapHeight);

    // 将多个图层合并为一个查询
    const layersString = layers.join(',');

    // 构建WMS GetFeatureInfo请求
    const url = buildWMSQueryUrl({
      layers: layersString,
      query_layers: layersString,
      x: pixelX,
      y: pixelY,
      width: mapWidth,
      height: mapHeight,
      bbox: queryBbox,
      srs: srs,
      info_format: 'application/json',
      feature_count: 50,
    });

    const response = await uni.request({ url, method: 'GET', timeout: 15000 });
    if (response.statusCode !== 200) {
      throw new Error(`HTTP ${response.statusCode}`);
    }
    // 直接返回 GeoServer 原始响应（通常为 FeatureCollection）
    return response.data;
  } catch (error) {
    console.error('WMS点击查询失败:', error);
    throw error;
  }
};

/**
 * 获取图层字段信息（DescribeFeatureType）
 * 注意：GeoServer 通常返回 XML（XSD），此处做轻量解析。
 */
export const getLayerFields = async (layerName: string) => {
  try {
    const config = gisConfig();
    const baseUrl = config.gisUtilitiesService || config.gisService;

    const url = `${baseUrl}/wfs?service=WFS&version=1.1.0&request=DescribeFeatureType&typeName=${encodeURIComponent(
      layerName,
    )}`;

    const response = await uni.request({ url, method: 'GET', timeout: 10000 });

    if (response.statusCode !== 200 || !response.data) {
      throw new Error(`HTTP ${response.statusCode}`);
    }

    const xml = typeof response.data === 'string' ? response.data : JSON.stringify(response.data);

    // 解析 XSD 中的字段定义：<xsd:element name="FIELD" type="xsd:string" .../>
    const fieldRegex = /<xsd:element[^>]*name="([^"]+)"[^>]*type="([^"]+)"[^>]*\/>/gim;
    const fields: Array<{ name: string; alias: string; type: string; length?: number; nullable?: boolean }> = [];

    let match: RegExpExecArray | null;
    while ((match = fieldRegex.exec(xml)) !== null) {
      const name = match[1];
      const rawType = match[2] || '';

      // 跳过几何字段（通常为 gml:Geometry / gml:*）
      if (/^gml:/i.test(rawType) || /geom/i.test(name) || /the_geom/i.test(name)) continue;

      // 类型映射
      let type = 'string';
      if (/xsd:(int|integer|long|short|decimal|double|float)/i.test(rawType)) {
        type = 'number';
      } else if (/xsd:(date|dateTime|time)/i.test(rawType)) {
        type = 'date';
      }

      fields.push({ name, alias: name, type });
    }

    return fields;
  } catch (error) {
    console.error('获取图层字段失败:', error);
    return [];
  }
};

/**
 * 范围查询接口
 * 根据几何范围查询管网要素
 */
export const queryPipelineByRange = async (params: {
  geometry: {
    type: 'Polygon' | 'Circle' | 'Rectangle';
    coordinates: number[][] | number[]; // 多边形坐标或圆心坐标
    radius?: number; // 圆形查询时的半径
  } | number[][][] | number[][]; // 兼容直接传入 rings
  layers?: string[];
  returnFields?: string[];
  maxFeatures?: number;
  srs?: string; // 坐标系（如 'EPSG:4326' / 'EPSG:3857'），直接使用
}) => {
  try {
    const {
      geometry,
      layers = ['pipe_layer', 'valve_layer', 'hydrant_layer'],
      returnFields = ['*'],
      maxFeatures = 500,
      srs = 'EPSG:4326',
    } = params;

    let cqlFilter = '';

    // 工具：闭合多边形环（不改变坐标，仅保证有效 WKT）
    const ensureClosedRing = (ring: number[][]): number[][] => {
      if (!ring || ring.length < 3) return ring || [];
      const first = ring[0];
      const last = ring[ring.length - 1];
      if (first[0] !== last[0] || first[1] !== last[1]) {
        return [...ring, [first[0], first[1]]];
      }
      return ring;
    };

    // 根据几何类型构建 CQL 过滤条件
    if (Array.isArray(geometry)) {
      // 兼容 rings 直接传入：geometry 为 number[][][] 或 number[][]
      const rings: number[][][] = Array.isArray(geometry[0][0]) ? (geometry as number[][][]) : [geometry as unknown as number[][]];
      const firstRing = ensureClosedRing(rings[0]);
      const wkt = `SRID=${srs.replace('EPSG:', '')};POLYGON((${firstRing.map(coord => `${coord[0]} ${coord[1]}`).join(', ')}))`;
      cqlFilter = `INTERSECTS(geom, ${wkt})`;
    } else if ((geometry as any)?.type) {
      const g = geometry as any;
      switch (g.type) {
        case 'Polygon': {
          let coords = g.coordinates[0] as number[][];
          coords = ensureClosedRing(coords);
          const wkt = `SRID=${srs.replace('EPSG:', '')};POLYGON((${coords.map(coord => `${coord[0]} ${coord[1]}`).join(', ')}))`;
          cqlFilter = `INTERSECTS(geom, ${wkt})`;
          break;
        }
        case 'Circle': {
          const [centerX, centerY] = g.coordinates as number[];
          const radius = g.radius || 100;
          const wktPoint = `SRID=${srs.replace('EPSG:', '')};POINT(${centerX} ${centerY})`;
          cqlFilter = `DWITHIN(geom, ${wktPoint}, ${radius}, meters)`;
          break;
        }
        case 'Rectangle': {
          const [minX, minY, maxX, maxY] = g.coordinates as number[];
          // CQL BBOX 支持指定 SRS
          cqlFilter = `BBOX(geom, ${minX}, ${minY}, ${maxX}, ${maxY}, '${srs}')`;
          break;
        }
        default:
          throw new Error(`不支持的几何类型: ${g.type}`);
      }
    } else {
      throw new Error('geometry 参数格式不支持');
    }

    // 将多个图层合并为一个查询
    const layersString = layers.join(',');

    const url = buildWFSUrl({
      typeName: layersString,
      cql_filter: cqlFilter,
      maxFeatures: maxFeatures,
    });

    console.log(`WFS批量范围查询图层 [${layersString}]:`, { cqlFilter, url });

    const response = await uni.request({
      url,
      method: 'GET',
      timeout: 15000,
    });

    return response;

  } catch (error) {
    console.error('范围查询失败:', error);
  }
};

/**
 * 条件查询接口
 * 根据属性条件查询管网要素
 */
export const queryPipelineByCondition = async (params: {
  cqlFilter?: string;
  layers: string[];
  maxFeatures?: number;
}) => {
  try {
    const {
      cqlFilter,
      layers,
      maxFeatures = 99999
    } = params;

    // 将多个图层合并为一个查询
    const layersString = layers.join(',');

    const queryParams: Record<string, any> = {
      typeName: layersString,
      maxFeatures: maxFeatures,
    };

    if (cqlFilter) {
      queryParams.cql_filter = cqlFilter;
    }

    const url = buildWFSUrl(queryParams);

    const response = await uni.request({
      url,
      method: 'GET',
      timeout: 15000,
    });
    return response;

  } catch (error) {
    console.error('条件查询失败:', error);
    return {
      success: false,
      error: error.message || '查询失败',
      data: [],
    };
  }
};

/**
 * 获取图层信息
 */
export const getLayerCapabilities = async (layerName?: string) => {
  try {
    const config = gisConfig();
    const baseUrl = config.gisUtilitiesService || config.gisService;

    const url = `${baseUrl}/wfs?service=WFS&version=1.1.0&request=GetCapabilities`;

    const response = await uni.request({
      url,
      method: 'GET',
    });

    if (response.statusCode === 200) {
      return {
        success: true,
        data: response.data,
      };
    } else {
      throw new Error(`HTTP ${response.statusCode}`);
    }
  } catch (error) {
    console.error('获取图层信息失败:', error);
    return {
      success: false,
      error: error.message || '获取图层信息失败',
    };
  }
};

/**
 * 获取要素详细信息
 */
export const getFeatureDetails = async (params: {
  layerName: string;
  featureId: string | number;
  returnFields?: string[];
}) => {
  try {
    const { layerName, featureId, returnFields = ['*'] } = params;

    const cqlFilter = `id='${featureId}' OR objectid=${featureId} OR fid=${featureId}`;

    const url = buildWFSUrl({
      typeName: layerName,
      cql_filter: cqlFilter,
      maxFeatures: 1,
    });

    const response = await uni.request({
      url,
      method: 'GET',
    });

    if (response.statusCode === 200 && response.data?.features?.length > 0) {
      const feature = response.data.features[0];
      return {
        success: true,
        data: {
          ...feature.properties,
          geometry: feature.geometry,
        },
      };
    } else {
      return {
        success: false,
        error: '未找到要素',
        data: null,
      };
    }
  } catch (error) {
    console.error('获取要素详情失败:', error);
    return {
      success: false,
      error: error.message || '获取要素详情失败',
      data: null,
    };
  }
};

// 辅助函数：根据属性推断图层类型
const inferLayerFromProperties = (properties: any): string => {
  // 根据属性字段推断图层类型
  const props = Object.keys(properties).map(key => key.toLowerCase());

  if (props.includes('pipe_type') || props.includes('material') || props.includes('diameter')) {
    return 'pipe_layer';
  } else if (props.includes('valve_type') || props.includes('valve_status')) {
    return 'valve_layer';
  } else if (props.includes('hydrant_type') || props.includes('pressure')) {
    return 'hydrant_layer';
  } else if (props.includes('meter_type') || props.includes('meter_id')) {
    return 'meter_layer';
  }

  return 'unknown_layer';
};


/**
 * 批量查询接口
 * 支持同时进行多种查询
 */
export const batchQuery = async (
  queries: Array<{
    type: 'point' | 'range' | 'condition';
    params: any;
    id?: string;
  }>,
) => {
  try {
    const results = await Promise.allSettled(
      queries.map(async query => {
        let result;
        switch (query.type) {
          case 'point':
            result = await queryPipelineByPoint(query.params);
            break;
          case 'range':
            result = await queryPipelineByRange(query.params);
            break;
          case 'condition':
            result = await queryPipelineByCondition(query.params);
            break;
          default:
            throw new Error(`不支持的查询类型: ${query.type}`);
        }
        return {
          id: query.id,
          type: query.type,
          ...result,
        };
      }),
    );

    return {
      success: true,
      data: results.map((result, index) => {
        if (result.status === 'fulfilled') {
          return result.value;
        } else {
          return {
            id: queries[index].id,
            type: queries[index].type,
            success: false,
            error: result.reason?.message || '查询失败',
          };
        }
      }),
    };
  } catch (error) {
    console.error('批量查询失败:', error);
    return {
      success: false,
      error: error.message || '批量查询失败',
      data: [],
    };
  }
};
