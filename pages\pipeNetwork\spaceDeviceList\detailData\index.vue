<template>
	<view class="main">
		<u-navbar fixed placeholder safeAreaInsetTop bgColor="#FFFFFF" title="查看详情" :autoBack="true" leftIconSize="20">
			<template #right>
				<view class="nv-right" @click="toAttribute">
					<text>属性上报</text>
				</view>
			</template>
		</u-navbar>
		<view class="card-box">
			<view class="card-title flex-center">
				<text class="custom-icon custom-icon-caiyouduo_hemaixiangqing-dingdanxiangqing"></text>
				<view class="label">基础信息</view>
			</view>
			<u-gap height="2" bg-color="#EBEDF6"></u-gap>

			<view class="list">
				<view class="flex-center l-c">
					<text>GISNO：</text> <text>{{attrs.SID}}</text>
				</view>
				<view class="flex-center l-c">
					<text>OBJECTID：</text> <text>{{attrs.OBJECTID}}</text>
				</view>
				<view class="flex-center l-c">
					<text>地面高程：</text> <text>{{(attrs.Z||'--')+' mm'}}</text>
				</view>
				<view class="flex-center l-c">
					<text>口径：</text> <text>{{(attrs.DIAMETER||'--')+' mm'}}</text>
				</view>
				<view class="flex-center l-c">
					<text>埋深：</text> <text>{{(attrs.DEPTH||'--')+' mm'}}</text>
				</view>
				<view class="flex-center l-c">
					<text>埋设方式：</text> <text>{{(attrs.BURYTYPE||'--')}}</text>
				</view>
				<view class="flex-center l-c">
					<text>设备材质：</text> <text>{{(attrs.MATERIAL||'--')}}</text>
				</view>
			</view>
		</view>
		<FileUpload ref="refFileUpload" :imgs="additionalInfo.imgs" :videos="additionalInfo.videos"
			:files="additionalInfo.files" :audios="additionalInfo.audios" :readonly="true" class="detail"></FileUpload>
		<view class="botton-placeholder">
			<!-- 底部占位 -->
		</view>
		<view class="button">
			<u-button type="primary" color="#3862F8" @click="upload">上传文件</u-button>
		</view>
	</view>
</template>

<script lang="ts" setup>
	import {
		onLoad,
		onShow
	} from '@dcloudio/uni-app'
	import FileUpload from '../../../../components/fileUpload/fileUpload.vue'
	import {
		reactive,
		ref
	} from "vue";
	import {
		getPipeAdditionalInfo
	} from '../../../../common/api/map';
	const additionalInfo = reactive<{
		imgs : string[]
		audios : string[]
		videos : string[]
		files : string[]
	}>({
		imgs: [],
		audios: [],
		videos: [],
		files: []
	})

	const refreshAdditionalInfo = async () => {
		console.log('refresh');
		if (layerid.value === undefined || attrs.value === undefined) return
		const res = await getPipeAdditionalInfo({
			layerid: layerid.value,
			objectid: attrs.value?.OBJECTID
		})
		console.log(res);
		const data = res.data?.data
		additionalInfo.imgs = data?.img?.split(',').filter(item => !!item) || []
		additionalInfo.videos = data?.vidio?.split(',').filter(item => !!item) || []
		additionalInfo.files = data?.files?.split(',').filter(item => !!item) || []
		additionalInfo.audios = data?.audio?.split(',').filter(item => !!item) || []
	}
	const imageError = (e : any, i : number) => {
		additionalInfo.imgs[i] = '../../../../static/img/404.png'
	}
	const previewImage = (url ?: string) => {
		if (!url) return
		uni.previewImage({
			urls: [url]
		})
	}
	onShow(() => {
		console.log('show');
		refreshAdditionalInfo()
	})
	// onMounted(() => {
	// 	refreshAdditionalInfo()
	// })
	const attrs = ref<Record<string,
		any>>({})
	const layerid = ref<string>('')
	const layername = ref<string>('')
	onLoad((options : any) => {
		if (!options.attrs) return
		const attr = JSON.parse(decodeURIComponent(options.attrs))
		layerid.value = decodeURIComponent(options.layerid)
		layername.value = decodeURIComponent(options.layername)
		attrs.value = attr || {}
	})
	const upload = () => {
		uni.navigateTo({
			url: '../fileUpload/index?layerid=' + layerid.value + '&id=' + attrs
				.value.OBJECTID
		})
	}

	const toAttribute = () => {
		const attributes = encodeURIComponent(JSON.stringify(attrs.value))
		uni.navigateTo({
			url: '../attributeReport/index?layername=' + layername.value + '&layerid=' + layerid.value +
				'&attrs=' +
				attributes
		})
	}
</script>

<style lang="scss" scoped>
	.empty {
		display: flex;
		align-items: center;
		height: 80rpx;
	}

	.detail {

		:deep(.content-card) {
			width: 686rpx;
		}
	}

	.card-box {
		width: 686rpx;
		border-radius: 8px;
		padding: 10rpx 0;
		margin: 20rpx auto;
		color: #FFFFFF;
		background-color: #FFFFFF;

		.card-title {
			padding: 14rpx 28rpx;

			text {
				font-size: 32rpx;
				color: #3862F8;
			}

			.label {
				padding-left: 10rpx;
				font-style: normal;
				font-weight: 600;
				font-size: 28rpx;
				color: #060F27;
			}
		}

		.list {
			padding: 10rpx 28rpx 10rpx 28rpx;
			color: #060F27;

			.l-c {
				padding: 10rpx 0;

				text {
					&:nth-child(1) {
						color: #91949F;
					}

					&:nth-child(3) {
						color: #060F27;
					}
				}
			}

			.l-file {
				padding: 10rpx 0;
			}

			&.images {
				display: flex;
				justify-content: flex-start;
				align-items: center;
				flex-wrap: wrap;

				.image {
					height: 112rpx;
					width: 112rpx;
					margin-right: 12rpx;
				}
			}
		}
	}

	.botton-placeholder {
		height: 140rpx;
	}

	.button {
		width: 100%;
		background-color: #FFFFFF;
		position: fixed;
		bottom: 0rpx;
		height: 120rpx;
		box-shadow: 0px 4rpx 30rpx rgba(178, 183, 199, 0.5);

		button {
			border-radius: 8rpx;
			width: 686rpx;
			margin: 20rpx auto;
		}
	}
</style>