/**
 * GeoServer 图层配置
 * 定义各种管网图层的配置信息
 */

export interface LayerConfig {
  id: string
  name: string
  typeName: string // GeoServer 中的图层名称
  displayName: string
  fields: LayerField[]
  queryable: boolean
  visible: boolean
  minScale?: number
  maxScale?: number
  style?: string
  priority: number // 查询优先级
}

export interface LayerField {
  name: string
  alias: string
  type: 'string' | 'number' | 'date' | 'boolean'
  queryable: boolean
  displayable: boolean
  unit?: string
}

/**
 * 管网图层配置
 */
export const pipeLayerConfigs: LayerConfig[] = [
  {
    id: 'pipe_layer',
    name: '管道图层',
    typeName: 'pipe_network:pipes',
    displayName: '管道',
    queryable: true,
    visible: true,
    priority: 1,
    fields: [
      { name: 'id', alias: 'ID', type: 'string', queryable: true, displayable: false },
      { name: 'pipe_name', alias: '管道名称', type: 'string', queryable: true, displayable: true },
      { name: 'pipe_type', alias: '管道类型', type: 'string', queryable: true, displayable: true },
      { name: 'material', alias: '材质', type: 'string', queryable: true, displayable: true },
      { name: 'diameter', alias: '管径', type: 'number', queryable: true, displayable: true, unit: 'mm' },
      { name: 'length', alias: '长度', type: 'number', queryable: true, displayable: true, unit: 'm' },
      { name: 'build_year', alias: '建设年份', type: 'number', queryable: true, displayable: true },
      { name: 'install_date', alias: '安装日期', type: 'date', queryable: true, displayable: true },
      { name: 'status', alias: '状态', type: 'string', queryable: true, displayable: true },
      { name: 'pressure', alias: '压力', type: 'number', queryable: true, displayable: true, unit: 'MPa' },
      { name: 'flow_rate', alias: '流量', type: 'number', queryable: true, displayable: true, unit: 'm³/h' }
    ]
  },
  {
    id: 'valve_layer',
    name: '阀门图层',
    typeName: 'pipe_network:valves',
    displayName: '阀门',
    queryable: true,
    visible: true,
    priority: 2,
    fields: [
      { name: 'id', alias: 'ID', type: 'string', queryable: true, displayable: false },
      { name: 'valve_name', alias: '阀门名称', type: 'string', queryable: true, displayable: true },
      { name: 'valve_type', alias: '阀门类型', type: 'string', queryable: true, displayable: true },
      { name: 'diameter', alias: '口径', type: 'number', queryable: true, displayable: true, unit: 'mm' },
      { name: 'material', alias: '材质', type: 'string', queryable: true, displayable: true },
      { name: 'manufacturer', alias: '制造商', type: 'string', queryable: true, displayable: true },
      { name: 'install_date', alias: '安装日期', type: 'date', queryable: true, displayable: true },
      { name: 'status', alias: '状态', type: 'string', queryable: true, displayable: true },
      { name: 'operation_status', alias: '开关状态', type: 'string', queryable: true, displayable: true }
    ]
  },
  {
    id: 'hydrant_layer',
    name: '消防栓图层',
    typeName: 'pipe_network:hydrants',
    displayName: '消防栓',
    queryable: true,
    visible: true,
    priority: 3,
    fields: [
      { name: 'id', alias: 'ID', type: 'string', queryable: true, displayable: false },
      { name: 'hydrant_name', alias: '消防栓名称', type: 'string', queryable: true, displayable: true },
      { name: 'hydrant_type', alias: '消防栓类型', type: 'string', queryable: true, displayable: true },
      { name: 'diameter', alias: '口径', type: 'number', queryable: true, displayable: true, unit: 'mm' },
      { name: 'pressure', alias: '压力', type: 'number', queryable: true, displayable: true, unit: 'MPa' },
      { name: 'install_date', alias: '安装日期', type: 'date', queryable: true, displayable: true },
      { name: 'last_check_date', alias: '最后检查日期', type: 'date', queryable: true, displayable: true },
      { name: 'status', alias: '状态', type: 'string', queryable: true, displayable: true },
      { name: 'location_desc', alias: '位置描述', type: 'string', queryable: true, displayable: true }
    ]
  },
  {
    id: 'meter_layer',
    name: '水表图层',
    typeName: 'pipe_network:meters',
    displayName: '水表',
    queryable: true,
    visible: true,
    priority: 4,
    fields: [
      { name: 'id', alias: 'ID', type: 'string', queryable: true, displayable: false },
      { name: 'meter_name', alias: '水表名称', type: 'string', queryable: true, displayable: true },
      { name: 'meter_type', alias: '水表类型', type: 'string', queryable: true, displayable: true },
      { name: 'diameter', alias: '口径', type: 'number', queryable: true, displayable: true, unit: 'mm' },
      { name: 'manufacturer', alias: '制造商', type: 'string', queryable: true, displayable: true },
      { name: 'model', alias: '型号', type: 'string', queryable: true, displayable: true },
      { name: 'install_date', alias: '安装日期', type: 'date', queryable: true, displayable: true },
      { name: 'last_reading', alias: '最后读数', type: 'number', queryable: true, displayable: true, unit: 'm³' },
      { name: 'reading_date', alias: '读数日期', type: 'date', queryable: true, displayable: true },
      { name: 'status', alias: '状态', type: 'string', queryable: true, displayable: true }
    ]
  },
  {
    id: 'manhole_layer',
    name: '检查井图层',
    typeName: 'pipe_network:manholes',
    displayName: '检查井',
    queryable: true,
    visible: true,
    priority: 5,
    fields: [
      { name: 'id', alias: 'ID', type: 'string', queryable: true, displayable: false },
      { name: 'manhole_name', alias: '检查井名称', type: 'string', queryable: true, displayable: true },
      { name: 'manhole_type', alias: '检查井类型', type: 'string', queryable: true, displayable: true },
      { name: 'depth', alias: '深度', type: 'number', queryable: true, displayable: true, unit: 'm' },
      { name: 'diameter', alias: '井口直径', type: 'number', queryable: true, displayable: true, unit: 'mm' },
      { name: 'material', alias: '材质', type: 'string', queryable: true, displayable: true },
      { name: 'install_date', alias: '建设日期', type: 'date', queryable: true, displayable: true },
      { name: 'last_clean_date', alias: '最后清理日期', type: 'date', queryable: true, displayable: true },
      { name: 'status', alias: '状态', type: 'string', queryable: true, displayable: true }
    ]
  }
]

/**
 * 管网类型配置
 */
export const pipeTypeConfigs = [
  { value: 'water_supply', label: '给水管', color: '#0066CC' },
  { value: 'sewage', label: '污水管', color: '#8B4513' },
  { value: 'storm_water', label: '雨水管', color: '#4169E1' },
  { value: 'gas', label: '燃气管', color: '#FFD700' },
  { value: 'heating', label: '供热管', color: '#FF6347' },
  { value: 'telecom', label: '通信管', color: '#32CD32' }
]

/**
 * 材质配置
 */
export const materialConfigs = [
  { value: 'PE', label: 'PE管', description: '聚乙烯管' },
  { value: 'PVC', label: 'PVC管', description: '聚氯乙烯管' },
  { value: 'PPR', label: 'PPR管', description: '聚丙烯管' },
  { value: 'HDPE', label: 'HDPE管', description: '高密度聚乙烯管' },
  { value: 'CI', label: '铸铁管', description: '铸铁管' },
  { value: 'DI', label: '球墨铸铁管', description: '球墨铸铁管' },
  { value: 'STEEL', label: '钢管', description: '钢管' },
  { value: 'CONCRETE', label: '混凝土管', description: '钢筋混凝土管' },
  { value: 'CERAMIC', label: '陶瓷管', description: '陶瓷管' },
  { value: 'COMPOSITE', label: '复合管', description: '复合材料管' }
]

/**
 * 查询字段映射
 * 将前端字段名映射到 GeoServer 字段名
 */
export const fieldMappings = {
  // 通用字段
  id: ['id', 'objectid', 'fid', 'gid'],
  name: ['name', 'pipe_name', 'valve_name', 'hydrant_name', 'meter_name', 'manhole_name'],
  type: ['type', 'pipe_type', 'valve_type', 'hydrant_type', 'meter_type', 'manhole_type'],
  material: ['material', 'pipe_material', 'valve_material'],
  diameter: ['diameter', 'pipe_diameter', 'valve_diameter', 'meter_diameter'],
  buildYear: ['build_year', 'construction_year', 'install_year'],
  installDate: ['install_date', 'construction_date', 'build_date'],
  status: ['status', 'condition', 'state'],
  
  // 管道特有字段
  length: ['length', 'pipe_length'],
  pressure: ['pressure', 'working_pressure'],
  flowRate: ['flow_rate', 'flow', 'discharge'],
  
  // 阀门特有字段
  operationStatus: ['operation_status', 'valve_status', 'open_close'],
  manufacturer: ['manufacturer', 'maker', 'brand'],
  
  // 消防栓特有字段
  lastCheckDate: ['last_check_date', 'inspection_date', 'check_date'],
  locationDesc: ['location_desc', 'location_description', 'address'],
  
  // 水表特有字段
  model: ['model', 'model_number', 'type_model'],
  lastReading: ['last_reading', 'current_reading', 'meter_reading'],
  readingDate: ['reading_date', 'meter_date', 'read_date'],
  
  // 检查井特有字段
  depth: ['depth', 'well_depth', 'manhole_depth'],
  lastCleanDate: ['last_clean_date', 'cleaning_date', 'maintenance_date']
}

/**
 * 获取图层配置
 */
export const getLayerConfig = (layerId: string): LayerConfig | undefined => {
  return pipeLayerConfigs.find(config => config.id === layerId)
}

/**
 * 获取所有可查询图层
 */
export const getQueryableLayers = (): LayerConfig[] => {
  return pipeLayerConfigs.filter(config => config.queryable)
}

/**
 * 获取图层字段配置
 */
export const getLayerFields = (layerId: string): LayerField[] => {
  const config = getLayerConfig(layerId)
  return config ? config.fields : []
}

/**
 * 获取可显示字段
 */
export const getDisplayableFields = (layerId: string): LayerField[] => {
  const fields = getLayerFields(layerId)
  return fields.filter(field => field.displayable)
}

/**
 * 获取可查询字段
 */
export const getQueryableFields = (layerId: string): LayerField[] => {
  const fields = getLayerFields(layerId)
  return fields.filter(field => field.queryable)
}

/**
 * 根据字段名获取映射的 GeoServer 字段名
 */
export const getMappedFieldName = (fieldName: string, layerId?: string): string[] => {
  const mappings = fieldMappings[fieldName]
  if (mappings) {
    return mappings
  }
  
  // 如果没有映射，返回原字段名
  return [fieldName]
}

/**
 * 获取管网类型配置
 */
export const getPipeTypeConfig = (value: string) => {
  return pipeTypeConfigs.find(config => config.value === value)
}

/**
 * 获取材质配置
 */
export const getMaterialConfig = (value: string) => {
  return materialConfigs.find(config => config.value === value)
}

/**
 * 默认查询配置
 */
export const defaultQueryConfig = {
  maxFeatures: 500,
  tolerance: 10, // 米
  bufferDistance: 100, // 米
  timeout: 30000, // 30秒
  retryCount: 3,
  cacheTimeout: 300000 // 5分钟缓存
}

/**
 * 查询结果字段标准化
 */
export const standardizeQueryResult = (result: any, layerId: string) => {
  const config = getLayerConfig(layerId)
  if (!config) return result
  
  const standardized: any = { ...result }
  
  // 标准化字段名
  config.fields.forEach(field => {
    const mappedNames = getMappedFieldName(field.name)
    for (const mappedName of mappedNames) {
      if (result[mappedName] !== undefined) {
        standardized[field.name] = result[mappedName]
        break
      }
    }
  })
  
  // 添加显示名称
  standardized._displayName = config.displayName
  standardized._layerId = layerId
  
  return standardized
}