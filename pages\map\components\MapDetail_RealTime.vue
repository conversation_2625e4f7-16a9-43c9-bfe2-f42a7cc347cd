<template>
	<view class="pipe-detail">
		<MapDetailRealTimePlant v-if="props.menu.id==='sssb_sc'" :title="menuName"></MapDetailRealTimePlant>
		<MapDetailRealTimeFlow v-if="props.menu.id==='sssb_llj'" :title="menuName"></MapDetailRealTimeFlow>
		<MapDetailRealTimePressure v-if="props.menu.id==='sssb_ylj'" :title="menuName"></MapDetailRealTimePressure>
		<MapDetailRealTimeQuality v-if="props.menu.id==='sssb_sz'" :title="menuName"></MapDetailRealTimeQuality>
		<MapDetailRealTimeBigUser v-if="props.menu.id==='sssb_dyh'" :title="menuName"></MapDetailRealTimeBigUser>
		<MapDetailRealTimePump v-if="props.menu.id==='sssb_ecgs'" :title="menuName"></MapDetailRealTimePump>
	</view>

</template>

<script lang="ts" setup>
	import { computed } from 'vue'
	import MapDetailRealTimePlant from './MapDetail_RealTime_Plant.vue'
	import MapDetailRealTimeFlow from './MapDetail_RealTime_Flow.vue'
	import MapDetailRealTimePressure from './MapDetail_RealTime_Pressure.vue'
	import MapDetailRealTimeQuality from './MapDetail_RealTime_Quality.vue'
	import MapDetailRealTimeBigUser from './MapDetail_RealTime_BigUser.vue'
	import MapDetailRealTimePump from './MapDetail_RealTime_Pump.vue'

	const props = defineProps<{
		menu ?: {
			name : string,
			alias ?: string; icon : string; id : string; type : string; isActive ?: boolean; color ?: string
		},
	}>()
	const menuName = computed(() => {
		return props.menu.alias || props.menu.name
	})
</script>

<style lang="scss" scoped>
	.pipe-detail {
		padding-bottom: 140rpx;

		.pipe-detail-item {
			.title {
				font-size: 32rpx;
				margin: 32rpx 0 0;
			}
		}

		.pipe-detail-total {
			.total-blocks {
				width: 100%;
				display: flex;
				padding: 24rpx;
				justify-content: flex-start;

				.total-block {
					background-color: #0073ff;
					padding: 10rpx;
					color: #fff;
					width: 280rpx;
					height: 120rpx;
					margin-right: 24rpx;
					text-align: center;

					.text,
					.value {
						line-height: 50rpx;
					}
				}
			}
		}

		.pipe-detail-chart {
			.chart-box {
				height: 480rpx;
			}
		}

	}
</style>