<template>
	<div>
		<AttrPop v-for="(item, i) in state.pumps" :key="i" :style="{ left: item.left, top: item.top }"
			:status="item.status" :text="item.name" :attr="item.attr" :unit="item.unit"></AttrPop>
		<AttrPop v-for="(item, i) in state.pools" :key="i" :style="{ left: item.left, top: item.top }"
			:status="item.status" :text="item.name" :attr="item.attr" :unit="item.unit"></AttrPop>
	</div>
</template>
<script lang="ts" setup>
	import {
		reactive,
		onMounted
	} from 'vue'
	import {
		useStationRealTimeData
			} from '@/common/hooks/useStations'
	import AttrPop from './AttrPop.vue'

	const state = reactive < {
		pumps: {
			name: string;status ? : 'online' | 'offline';attr ? : string;unit ? : string;left: string;top: string
		} []
		pools: {
			name: string;status ? : 'online' | 'offline';attr ? : string;unit ? : string;left: string;top: string
		} []
	} > ({
		pumps: [{
				name: '流量计',
				left: '230px',
				top: '350px',
				attr: '--',
				unit: 'm³/h'
			},
			{
				name: '流量计',
				left: '235px',
				top: '315px',
				attr: '--',
				unit: 'm³/h'
			},
			{
				name: '流量计',
				left: '240px',
				top: '280px',
				attr: '--',
				unit: 'm³/h'
			},
			{
				name: '流量计',
				left: '245px',
				top: '245px',
				attr: '--',
				unit: 'm³/h'
			}
		],
		pools: [{
				name: '1#清水池液位',
				left: '500px',
				top: '250px',
				attr: '--',
				unit: 'm'
			},
			{
				name: '2#清水池液位',
				left: '500px',
				top: '500px',
				attr: '--',
				unit: 'm'
			}
		]
	})
	const realtime = useStationRealTimeData()
	const refreshPumpData = async (stationId: string, index: number) => {
		if (!stationId) return
		try {
			await realtime.getRealTimeData(stationId)
		} catch (error) {
			//
		}
		const attrObj = realtime.realtimeList.value.find(item => item.property === 'Instantaneous_flow')
		state.pumps[index].attr = attrObj?.value ?? '--'
		state.pumps[index].unit = attrObj?.unit ?? 'm³/h'
	}
	const refreshPoolData = async (stationId: string, index: number) => {
		if (!stationId) return
		try {
			await realtime.getRealTimeData(stationId)
		} catch (error) {
			//
		}
		const attrObj = realtime.realtimeList.value.find(item => item.property === 'level')
		state.pools[index].attr = attrObj?.value ?? '--'
		state.pools[index].unit = attrObj?.unit ?? 'm'
	}
	onMounted(() => {
		refreshPumpData('402880b18a3aec86018a456b900a01a3', 0)
		refreshPumpData('402880b18a3aec86018a456c5d8501a8', 1)
		refreshPumpData('402880b18a3aec86018a456d8f5e01ab', 2)
		refreshPumpData('402880b18a3aec86018a456dfe7101ae', 3)
		refreshPoolData('402880b18a3aec86018a40a42a2a00db', 0)
		refreshPoolData('402880b18a3aec86018a40a45b4c00dc', 1)
	})
</script>
<style lang="scss" scoped>
	.img {
		position: absolute;
		width: 17px;
		height: 17px;

		&.online {
			transform: rotate(360deg);
			animation: rotation 3s linear infinite;
		}
	}

	@keyframes rotation {
		from {
			-webkit-transform: rotate(0deg);
		}

		to {
			-webkit-transform: rotate(360deg);
		}
	}
</style>
