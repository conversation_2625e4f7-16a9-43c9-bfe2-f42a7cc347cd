import * as utils from '@/uni_modules/hic-upgrade/utils/index.js';

/**
 * @description 版本更新
 * @param {Boolean} tip 是否显示提示信息
 *  app初始化进行版本判断, 不显示提示信息   -> false
 *  关于页面 手动点击版本更新, 显示提示信息   -> true
 */
export async function versionUpdate(tip) {
	console.log('top---', tip)
	if (!tip) { return }
	// 1 请求返回服务端版本信息
	const res : any = await getLatestVersion({
		platform: utils.platform    // 运行平台
	});
	if (!res) {
		if (tip) uni.$z.toast('暂无更新');
		return;
	}
	if (getCache('forceUpdate') == undefined || !getCache('forceUpdate')) {
		console.log('res', res)
		// 2 存储服务端版本信息
		utils.setServerInfo({
			version: res.versionName,
			versionCode: res.versionCode,
			downloadUrl: res.downloadUrl,
			wgtUrl: res.wgtUrl,
			forceUpdate: res.forceUpdate,
			isSilently: res.isSilently,
			desc: res.versionDesc
		} as any)
		// 3 判断是否更新
		if (!utils.isUpdate()) {
			if (tip) uni.$z.toast('已是最新版本');
			return;
		}
		const updateInfo = utils.getUpdateInfo();
		const info = utils.getServerVersion();
		// 4 wgt 静默更新
		console.log('top---', updateInfo)
		if (updateInfo.type == 'wgt' && info.isSilently) {
			// 下载完成 直接安装 下次启动生效
			const path = await utils.downloadFile(updateInfo.url);
			await utils.installForAndroid(path);
			return;
		}
		// 5 跳转页面 提示更新
		await utils.toUpgradePage('/uni_modules/hic-upgrade/pages/upgrade');
	}
}

/**
 * 模拟服务端请求
 */
// function getLatestVersion(data) {
// 	return new Promise((resolve, reject) => {
// 		const url = 'http://xxx/xx.apk';
// 		setTimeout(function () {
// 			resolve({
// 				versionName: '1.2.5',
// 				versionCode: 127,
// 				downloadUrl: url.indexOf('apk')!==-1?url:'',
// 				wgtUrl: url.indexOf('wgt')!==-1?url:'',
// 				forceUpdate: 0,
// 				isSilently: 0,
// 				versionDesc: '更新一些已知bug...'
// 			});
// 		}, 500);
// 	});
// }
function getLatestVersion(data) {
	return new Promise((resolve, reject) => {
		const systemConfig = uni.getStorageSync('systemConfig')
		const account = systemConfig?.account
		uni.request({
			url: 'http://app.siloon.com:8081/api/app/version/checkVersion/__UNI__7F59064',
			method: 'GET',
			data: {
				tenantKey: account
			},
			success: function (res : any) {
				const data = res.data.data
				const currentVersion = utils.getCurrentVersion()
				console.log('当前版本', currentVersion)
				if (data.versionCode > currentVersion.versionCode) {
					resolve({
						versionName: data.versionName,
						versionCode: data.versionCode,
						downloadUrl: data.url.indexOf('apk') !== -1 ? data.url : '',
						wgtUrl: data.url.indexOf('wgt') !== -1 ? data.url : '',
						forceUpdate: false,//data.url.indexOf('apk')!==-1,
						isSilently: 0,
						versionDesc: data.versionContent,
					});
				}
			}
		})
	});
}

function getCache(key : string, value ?: string, seconds = 3600 * 24) {
	let nowTime = Date.parse(new Date()) / 1000;
	if (key && value) {
		let expire = nowTime + Number(seconds);
		uni.setStorageSync(key, JSON.stringify(value) + '|' + expire)
		console.log('已经把' + key + '存入缓存,过期时间为' + expire)
	} else if (key && !value) {
		let val = uni.getStorageSync(key);
		if (val) {
			// 缓存存在，判断是否过期
			let temp = val.split('|')
			if (!temp[1] || temp[1] <= nowTime) {
				uni.removeStorageSync(key)
				console.log(key + '缓存已失效')
				return '';
			} else {
				return JSON.parse(temp[0]);
			}
		}
	}
}