import { http } from "../vmeitime-http"

/**
 * 查询所有的分区
 */
export const getAllPartition = () => {
	return http().get('api/spp/dma/partition/all')
}
/**
 * 获取所有分区的产销差
 */
export const getDMANRW1 = (params ?: any) => {
	return http().get('api/spp/statisticsReport/nrwForApp', params)
}
export const getDMANRW = (params ?: any) => {
	return http().get('api/jinzhou/nrwForApp', params)
}
/**
 * 获取所有分区的产销差
 */
export const getJZDMANRW = () => {
	return http().get('api/jinzhou/nrwForApp')
}

/**
 * DMA漏损分布
 */
export const getDMALoss = () => {
	return http().get('api/spp/dma/partitionEvaluate/normalListForApp')
}