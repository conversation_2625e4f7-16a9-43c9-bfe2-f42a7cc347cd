import { defineStore } from 'pinia'

// useStore 可以是 useUser、useCart 之类的任何东西
// 第一个参数是应用程序中 store 的唯一 id
export const useRtkStore = defineStore('rtk', {
	// 推荐使用 完整类型推断的箭头函数
	state: () => {
		return {
			rtkLocationChars: [] as string[],
			formattedRtkLocation: { longitude: '', latitude: '' },
			poleHeight: 1.4,
			/**
			 * 从哪里来地图的,可以是从采集线的页面过来
			 */
			fromWhereToMap: '',
			/**
			 * 线采集的起始点
			 */
			startPoint: {} as any,
			/**
			 * 线采集的终止点
			 */
			endPoint: {} as any,
			/**
			 * 地图选中的当前点或线
			 */
			curPoint: {} as any,
			/**
			 * 当前是选择的起点还是终点
			 */
			startOrEnd: 'start',
			curTask: undefined
		};
	},
	getters: {
		rtkLocation: (state) => {
			return {
				utc: state.rtkLocationChars[1],
				latitude: state.rtkLocationChars[2],
				latitudeBall: state.rtkLocationChars[3],
				longitude: state.rtkLocationChars[4],
				longitudeBall: state.rtkLocationChars[5],
				status: state.rtkLocationChars[6],
				satellite: state.rtkLocationChars[7],
				accuracy: state.rtkLocationChars[8],
				z: state.rtkLocationChars[9],
				zUnit: state.rtkLocationChars[10],
				delta: state.rtkLocationChars[11],
			}
		}
	},
	actions: {
		setRtkLocationChars(payload : any[]) {
			this.rtkLocationChars = payload
		},
		setFormattedRtkLocation(payload : { longitude : string; latitude : string }) {
			this.formattedRtkLocation = payload
		},
		setPoleHeight(payload : number) {
			this.poleHeight = payload
		},
		setFromWhereToMap(payload : 'line' | '') {
			this.fromWhereToMap = payload
		},
		setStartPoint(payload : any) {
			this.startPoint = payload
		},
		setEndPoint(payload : any) {
			this.endPoint = payload
		},
		setCurPoint(payload : any) {
			this.curPoint = payload
		},
		setStartOrEnd(payload : 'start' | 'end') {
			this.startOrEnd = payload
		},
		setCurTask(payload : any) {
			this.curTask = payload
		}
	}
})