<template>
	<view class="main">
		<view style="position: absolute;">
			<u-navbar :border-bottom="false" :background="{backgroundColor: state.navBgColor}" title="巡检处理" is-back
				:titleColor="state.navColor" :back-iconColor="state.navColor">
				<template #right>
					<view class="nv-right" @click="faultReport">
						<text :style="{color:state.navColor}" style="font-size: 34rpx;">故障上报</text>
					</view>
				</template>
			</u-navbar>
		</view>
		<view class="bg">
		</view>
		<view class="detail">
			<view class="card-box">
				<u-form :model="form" ref="refForm" :label-style="{'color':'#91949F'}" borderBottom labelWidth="180"
					input-align="right" :errorType="['toast']">
					<!-- 	<u-form-item label="关联工单：" required prop="workOrderName" borderBottom>
						<u-input v-model="form.workOrderName" disabled>
						</u-input>
					</u-form-item> -->
					<u-form-item label="处理结果：" required prop="result" borderBottom>
						<template #right>
							<u-icon name="arrow-right" size="28" color="#91949F"></u-icon>
						</template>
						<input @click="state.statusShow=true" v-model="form.result" inputmode="none"
							placeholder-class="placeholderClass" placeholder="请选择处理结果">
						</input>
					</u-form-item>
					<u-form-item label="巡检备注：" required prop="remark" borderBottom>
					</u-form-item>
					<u-form-item label="" borderBottom>
						<u-input height="160" border type="textarea" input-align="left" v-model="form.remark"
							placeholder="请输入备注"></u-input>
					</u-form-item>
					<u-form-item label="巡检图片：" prop="img" :borderBottom="false">

					</u-form-item>
					<view class="content-card">
						<!-- <view class="camera">
							<view class="margin-center">
								<u-image src="/static/img/icons/camera.png" width="48" height="48"></u-image>
							</view>
							<text>拍照上传</text>
						</view> -->
						<u-upload :custom-btn="true" :header="header" width="112rpx" height="112rpx"
							:source-type="['album','camera']" ref="refUpload" :show-upload-list="!!imageList?.length"
							:action="actionUrl" @onRemove="deletePic" @onSuccess="onSuccess">
							<template #addBtn>
								<view class="file-s">
									<view class="margin-center">
										<text class="custom-icon custom-icon-xiangji icon"></text>
									</view>
									<text>拍照上传</text>
								</view>
							</template>
						</u-upload>
					</view>
				</u-form>
			</view>

		</view>

		<view class="button">
			<u-button type="primary" color="#3862F8" @click="submit">提交</u-button>
		</view>
		<u-action-sheet border-radius="16" safe-area-inset-bottom title="处理结果" :list="state.statusList"
			v-model="state.statusShow" @click="chooseStatus"></u-action-sheet>
		<!-- <u-action-sheet @close="statusShow=false"  closeOnClickOverlay closeOnClickAction
			 v-model="statusShow" keyname :actions="state.statusList">
		</u-action-sheet> -->
		<u-toast ref="refToast"></u-toast>
	</view>
</template>

<script lang="ts" setup>
	import {
		onMounted,
		onBeforeMount,
		getCurrentInstance,
		reactive,
		ref
	} from "vue";

	import {
		onReady
	} from '@dcloudio/uni-app';
	import {
		saveCircuitTask
	} from '@/common/api/asset'
	import {
		onPageScroll
	} from '@dcloudio/uni-app'
	// 监听页面滑动距离顶部
	onPageScroll(e => {
		state.scrollTop = e.scrollTop
		if (e.scrollTop < 65) {
			state.navColor = '#FFFFFF'
			state.navBgColor = 'transparent'
		} else {
			state.navColor = '#000000'
			state.navBgColor = '#FFFFFF'
		}
	})
	const header = reactive<any>({
		'X-Authorization': 'Bearer ' + uni.getStorageSync('token')
	})

	const imageList = ref<any>([])
	const actionUrl = ref<string>('')
	const state = reactive<{
		status : any,
		scrollTop : number,
		navColor : string,
		statusShow : boolean,
		navBgColor : string,
		ids : any,
		statusList : any
	}>({
		statusShow: false,
		ids: {},
		statusList: [{
			text: '完成',
			value: 1
		}, {
			text: '未完成',
			value: 0
		}],
		status: {
			aa: {
				label: '待巡检',
				color: '#F8A038',
				bgColor: 'rgba(248, 160, 56, 0.2)'
			},
			bb: {
				label: '合格',
				color: '#2EE740',
				bgColor: 'rgba(46, 231, 64, 0.2)'
			},
			cc: {
				label: '不合格',
				color: '#F83838',
				bgColor: 'rgba(248, 56, 56, 0.2)'
			},
		},
		navColor: '#FFFFFF',
		navBgColor: 'transparent',
		scrollTop: 0
	})
	const rules = reactive<any>({
		result: [{
			required: true,
			message: '请选择处理结果'
		}],
		remark: [{
			required: true,
			message: '请输入巡检备注'
		}],
		// file: [{
		// 	required: true,
		// 	message: '请添加巡检照片'
		// }],
	})
	const refForm = ref<any>()
	const refToast = ref<any>()
	const form = reactive<any>({
		status: 1,
		result: '完成',
		mainId: '',
		remark: '',
		img: '',
		id: ''
	})
	// 选择处理状态
	const chooseStatus = (i : number) => {
		form.status = state.statusList[i].value
		form.result = state.statusList[i].text
	}

	const onSuccess = (data, index, lists, name) => {
		imageList.value = lists.map((img : any) => {
			return img.response
		})
		form.img = imageList.value.join(',')
	}
	const deletePic = event => {
		imageList.value.splice(event.index, 1)
		form.img = imageList.value.join(',')
	}
	//故障上报
	const faultReport = () => {
		uni.$u.route({
			url: 'pages/smartOperation/assetCircuitInspectionTask/faultReport',
			params: {
				deviceLabelCode: state.ids.deviceLabelCode
			}
		})
	}

	//提交询价处理
	const submit = () => {
		refForm.value.validate(async (valid : any) => {
			if (valid) {
				uni.showModal({
					title: '提示',
					content: '确定提交吗',
					success: (res) => {
						if (res.confirm) {
							form.mainId = state.ids.mainId
							form.id = state.ids.id
							saveCircuitTask(form).then(res => {
								uni.showModal({
									title: '提示',
									content: '提交成功',
									showCancel: false,
									success: function (res) {
										if (res.confirm) {
											uni.navigateBack()
										}
									}
								});
							}).catch(err => {
								refToast.value.show({
									title: '提交失败',
									type: 'error',
								})
							})
						}
					}
				})
			}
		})
	}

	onBeforeMount(async () => {
		const globalProperties = getCurrentInstance()?.appContext.config.globalProperties
		console.log('globalProperties-----', globalProperties)
		const path = uni.getStorageSync('url')
		actionUrl.value = path + 'file/api/upload/file'
	})
	onReady(async () => {
		console.log('ready')
		refForm.value.setRules(rules)
	})

	onMounted(async () => {
		var pages = getCurrentPages();
		var page = pages[pages.length - 1];
		state.ids = page.$page?.options
	})
</script>

<style lang="scss" scoped>
	.main {
		height: 100vh;
	}

	.bg {
		width: 100%;
		height: 480rpx;
		background: linear-gradient(117.58deg, #3862F8 0%, #6AA6FF 100%);
	}

	.detail {
		top: -260rpx;
		position: relative;

		.card-box {
			min-height: 376rpx;
			width: 686rpx;
			margin: 0 auto;
			border-radius: 16rpx;
			padding: 22rpx 28rpx;
			background-color: #FFFFFF;

		}

		.content-card {
			width: 686rpx;
			// margin: 20rpx auto;
			background-color: #FFFFFF;
			min-height: 224rpx;
			border-radius: 8px;
			// padding: 24rpx 0 0 28rpx;

			text {
				color: #91949F;
				font-size: 28rpx;
			}

			.camera {
				width: 112rpx;
				height: 112rpx;
				margin-top: 20rpx;
				display: flex;
				justify-content: center;
				flex-direction: column;
				text-align: center;
				background: #F9F9F9;

				text {
					color: #91949F;
					font-size: 20rpx;
				}
			}

			.margin-center {
				margin: 0 auto;
			}
		}

		.file-s {
			width: 116rpx;
			height: 116rpx;
			display: flex;
			justify-content: center;
			flex-direction: column;
			text-align: center;
			background: #F9F9F9;
			position: relative;

			text {
				color: #91949F;
				font-size: 20rpx;
			}

			.close-icon {
				border-radius: 50%;
				width: 32rpx;
				height: 32rpx;
				background-color: red;
				line-height: 32rpx;
				text-align: center;
				position: absolute;
				right: 0;
				top: 0;
			}
		}

		.margin-center {
			margin: 0 auto;
			width: 48rpx;
			height: 48rpx;

			.icon {
				font-size: 48rpx;
			}
		}
	}

	::v-deep .u-form-item__body {
		padding: 16rpx;
	}

	::v-deep .u-form-item {
		padding: 0;
	}
</style>