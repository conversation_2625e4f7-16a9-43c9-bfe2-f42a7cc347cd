import { reactive, toRefs } from 'vue'
export const useSystemInfo = () => {
	const state = reactive<Partial<UniNamespace.GetSystemInfoResult>>({
		statusBarHeight: 0,
		windowHeight: 0
	})
	const fitNav = () => {
		// 获取手机信息
		let info = uni.getSystemInfoSync()
		//顶部高度
		state.statusBarHeight = info.statusBarHeight
		state.windowHeight = info.windowHeight
	}
	return {
		...toRefs(state),
		fitNav
	}
}