<template>
	<view class="collapse">
		<view class="allbox" @click="lock(!isOpen)">
			<view class="title_box">
				<slot name="title">
					<u-icon v-if="icon" size="16" class="u-icon" :name="icon" />
					<view>
						<view :class="{'box':true,'title':isOpen,'is-disabled':disabled}">
							{{title}}
						</view>
						<view class="Subtitle" v-if="Subtitle">
							{{Subtitle}}
						</view>
					</view>
				</slot>
			</view>
			<view class="right right-arrow" v-if="showArrow"
				:class="{ 'right-arrow-active': isOpen,'collapse--animation': showAnimation === true }">
				<uni-icons :color="disabled?'#ddd':'#bbb'" size="14" type="bottom" />
			</view>
		</view>
		<view class="content" v-if="isOpen">
			<slot name="content">
			</slot>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'Word',
		props: {
			thumb: { //图片
				type: String,
				default: ''
			},
			title: { //标题
				type: String,
				default: ''
			},
			Subtitle: { //副标题
				type: String,
				default: ''
			},
			showArrow: { //右侧箭头
				type: Boolean,
				default: false
			},
			showAnimation: { //动画
				type: Boolean,
				default: true
			},
			// 是否禁用
			disabled: {
				type: Boolean,
				default: false
			},
			defaultExpand: {
				type: Boolean,
				default: false
			},
			icon: {
				type: String,
				default: ''
			}
		},
		data() {
			return {
				isOpen: this.defaultExpand,
			};
		},
		methods: {
			// 点击
			lock(isOpen, type) {
				if (this.disabled) return
				this.isOpen = isOpen
			},
		}
	}
</script>

<style lang="scss">
	.collapse {
		.box {
			height: 40px;
			line-height: 40px;
			font-size: 16px;
			overflow: hidden;
			text-overflow: ellipsis; // text-overflow css3的属性，当文本溢出时，显示省略号
			display: -webkit-box;
			-webkit-line-clamp: 1; // 设置两行文字溢出
			-webkit-box-orient: vertical;
			word-break: break-all;
			word-wrap: break-word;
			white-space: pre-wrap;

			&.is-disabled {
				color: #999;
			}
		}
		.u-icon{
			margin-right: 4rpx;
		}
		.title {
			// color: #0086d6;
		}

		.allbox {
			display: flex;
			justify-content: space-between;
			background-color: #fff;
			border-bottom: 1px solid #ebedec;
		}

		// 主标题
		.title_box {
			display: flex;
			padding-left: 10px;

			.img {
				width: 25px;
				height: 25px;
				margin: auto;
			}
		}

		.right {
			// padding: 5px 5px 5px 0;
			line-height: 40px;
			margin: auto 0;
			display: flex;
			flex-direction: row;
			align-items: center;

			&-arrow {
				/* #ifndef APP-NVUE */
				display: flex;
				box-sizing: border-box;
				/* #endif */
				align-items: center;
				justify-content: center;
				width: 20px;
				height: 20px;
				margin-right: 10px;
				transform: rotate(0deg);

				&-active {
					transform: rotate(-180deg);
				}
			}
		}

		// 副标题
		.Subtitle {
			color: #a7b1ae;
			padding: 0px 5px 5px 10px;
			font-size: 14px;
		}

		.content {
			padding: 0;
			padding-left: 20rpx;
			background-color: #fff;
			// border-bottom: 1px solid #ebedec;
		}

		&--animation {
			transition-property: transform;
			transition-duration: 0.3s;
			transition-timing-function: ease;
		}
	}
</style>