<template>
  <div
    class="attr-pop"
    :class="props.status"
  >
    <div
      v-if="props.status !== undefined"
      class="status"
    ></div>
    <div class="text">
      {{ props.text ?? '--' }}
    </div>
    <div
      v-if="props.attr !== undefined"
      class="attr"
    >
      {{ props.attr ?? '--' }}
    </div>
    <div
      v-if="props.unit !== undefined"
      class="unit"
    >
      {{ props.unit }}
    </div>
  </div>
</template>
<script lang="ts" setup>
const props = defineProps<{ status?: 'online' | 'offline'; text?: string; attr?: string; unit?: string }>()
</script>
<style lang="scss" scoped>
.attr-pop {
  position: absolute;
  min-width: 80px;
  height: 25px;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 13px;
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  justify-content: space-between;
  color: #fff;
  padding: 0 12px;
  line-height: 1em;
  .status {
    width: 15px;
    height: 25px;
    position: relative;
    &::before {
      content: ' ';
      position: absolute;
      width: 9px;
      height: 9px;
      left: 0;
      top: 8px;
      border-radius: 50%;
      background-color: #b5c8da;
    }
  }
  .text {
    flex: 1;
  }
  .attr {
    color: #00ff00;
    padding: 0 8px;
    margin: 0 4px;
    background-color: #06a2e4;
  }
  .unit{
    font-size: 12px;
  }
  &.online {
    .status {
      &::before {
        background-color: #34f8c6;
      }
    }
    .attr {
      color: #34f8c6;
    }
  }
}
</style>
