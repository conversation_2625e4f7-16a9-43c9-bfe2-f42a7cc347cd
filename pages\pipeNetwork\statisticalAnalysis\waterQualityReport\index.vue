<template>
  <view class="trend-chart-container">
    
    <!-- 筛选条件 -->
    <view class="filter-section">
      <u-form label-position="left" :model="filterForm" ref="uForm" :label-style="{'color':'#91949F'}" border-bottom input-align="right" label-width="180">
        <u-form-item label="设备：">
          <input 
            placeholder="选择设备" 
            inputmode="none" 
            placeholder-class="placeholderClass" 
            v-model="filterForm.monitorPointName" 
            @click="showMonitorPointPicker = true"
          />
          <template #right>
            <u-icon name="arrow-down" color="#91949F" size="34"></u-icon>
          </template>
        </u-form-item>
        <u-form-item label="时间类型：">
          <input 
            placeholder="选择时间类型" 
            inputmode="none" 
            placeholder-class="placeholderClass" 
            v-model="filterForm.queryTypeName" 
            @click="showQueryTypePicker = true"
          />
          <template #right>
            <u-icon name="arrow-down" color="#91949F" size="34"></u-icon>
          </template>
        </u-form-item>
        <u-form-item label="开始时间：">
          <input 
            placeholder="选择开始时间" 
            inputmode="none" 
            placeholder-class="placeholderClass" 
            v-model="filterForm.startTime" 
            @click="chooseDate('start')"
          />
          <template #right>
            <u-icon name="calendar" color="#91949F" size="34"></u-icon>
          </template>
        </u-form-item>
        <u-form-item label="结束时间：">
          <input 
            placeholder="选择结束时间" 
            inputmode="none" 
            placeholder-class="placeholderClass" 
            v-model="filterForm.endTime" 
            @click="chooseDate('end')"
          />
          <template #right>
            <u-icon name="calendar" color="#91949F" size="34"></u-icon>
          </template>
        </u-form-item>
      </u-form>
      <view class="query-button">
        <u-button type="primary" color="#3862F8" @click="queryData">查询</u-button>
      </view>
    </view>

    <!-- 图表/表格切换 -->
    <view class="view-toggle">
      <view 
        :class="['toggle-btn', { active: viewMode === 'chart' }]" 
        @click="switchViewMode('chart')"
      >
        图表模式
      </view>
      <view 
        :class="['toggle-btn', { active: viewMode === 'table' }]" 
        @click="switchViewMode('table')"
      >
        表格模式
      </view>
    </view>

    <!-- 图表视图 -->
    <view v-if="viewMode === 'chart'" class="chart-view">
      <view class="chart-content">
        <l-echart ref="trendChart"></l-echart>
      </view>
    </view>

    <!-- 表格视图 -->
    <view v-if="viewMode === 'table'" class="table-view">
      <scroll-view scroll-y scroll-x style="height: calc(40vh + 40rpx);">
        <u-table font-size="30" padding="14rpx">
          <u-tr class="u-tr">
            <u-th class="u-th" v-for="(header, index) in tableHeaders" :key="index" :width="header.width + 'rpx'">
              {{ header.label }}
            </u-th>
          </u-tr>
          <u-tr class="u-tr" v-for="(item, index) in tableData" :key="index">
            <u-td class="u-td" v-for="(header, hIndex) in tableHeaders" :key="hIndex" :width="header.width + 'rpx'">
              {{ formatTableValue(item[header.key], header.key) }}
            </u-td>
          </u-tr>
        </u-table>
      </scroll-view>
    </view>

    <!-- 监测点选择器 -->
    <u-picker 
      v-model="showMonitorPointPicker" 
      mode="selector" 
      :range="monitorPointList" 
      range-key="name"
      @confirm="confirmMonitorPoint"
    ></u-picker>

    <!-- 时间类型选择器 -->
    <u-picker 
      v-model="showQueryTypePicker" 
      mode="selector" 
      :range="queryTypeList" 
      range-key="label"
      @confirm="confirmQueryType"
    ></u-picker>

    <!-- 日期选择器 -->
    <u-calendar v-model="showDatePicker" ref="calendar" @change="confirmDate"></u-calendar>
  </view>
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref, watch, nextTick } from 'vue'
import dayjs from 'dayjs'
import * as echarts from 'echarts'
import lEchart from '@/uni_modules/lime-echart/components/l-echart/l-echart.vue'
import { getAllStationGroup } from '@/common/api/waterplant'
import { getDataCompare } from '@/common/api/monitoring'

const props = defineProps<{
  stationId: string
  title?: string
}>()

const trendChart = ref<any>({})
const viewMode = ref<'chart' | 'table'>('chart')
const showMonitorPointPicker = ref(false)
const showQueryTypePicker = ref(false)
const showDatePicker = ref(false)
const dateType = ref<'start' | 'end'>('start')

// 筛选表单
const filterForm = reactive({
  monitorPointName: '',
  monitorPointId: '',
  queryTypeName: '日-间隔15分',
  queryType: '15m',
  startTime: dayjs().format('YYYY-MM-DD'),
  endTime: dayjs().format('YYYY-MM-DD')
})

// 监测点列表
const monitorPointList = ref<any[]>([])
const monitorPointTypeObj = ref<any>({})

// 时间类型列表
const queryTypeList = ref([
  { label: '日-间隔1分', value: '1m' },
  { label: '日-间隔5分', value: '5m' },
  { label: '日-间隔15分', value: '15m' },
  { label: '日-间隔1小时', value: '60m' },
  { label: '月', value: 'day' },
  { label: '年', value: 'month' }
])

// 表格数据
const tableHeaders = ref<any[]>([])
const tableData = ref<any[]>([])

// 切换视图模式
const switchViewMode = (mode: 'chart' | 'table') => {
  viewMode.value = mode
  if (mode === 'chart' && tableData.value.length > 0) {
    nextTick(() => {
      renderChart()
    })
  }
}

// 选择日期
const chooseDate = (type: 'start' | 'end') => {
  dateType.value = type
  showDatePicker.value = true
}

// 确认日期选择
const confirmDate = (date: any) => {
  showDatePicker.value = false
  if (dateType.value === 'start') {
    filterForm.startTime = date.result
  } else {
    filterForm.endTime = date.result
  }
}

// 确认监测点选择
const confirmMonitorPoint = (data: any) => {
  const selected = monitorPointList.value[data[0]]
  filterForm.monitorPointName = selected.name
  filterForm.monitorPointId = selected.id
  showMonitorPointPicker.value = false
}

// 确认时间类型选择
const confirmQueryType = (data: any) => {
  const selected = queryTypeList.value[data[0]]
  filterForm.queryTypeName = selected.label
  filterForm.queryType = selected.value
  showQueryTypePicker.value = false

  // 根据时间类型调整默认时间范围
  const now = dayjs()
  if (selected.value === 'day' || selected.value === '1m' || selected.value === '5m' || selected.value === '15m' || selected.value === '60m') {
    filterForm.startTime = now.format('YYYY-MM-DD')
    filterForm.endTime = now.format('YYYY-MM-DD')
  } else if (selected.value === 'month') {
    filterForm.startTime = now.subtract(1, 'month').format('YYYY-MM-DD')
    filterForm.endTime = now.format('YYYY-MM-DD')
  } else if (selected.value === 'year') {
    filterForm.startTime = now.subtract(1, 'year').format('YYYY-MM-DD')
    filterForm.endTime = now.format('YYYY-MM-DD')
  }
}

// 格式化表格值
const formatTableValue = (value: any, key: string) => {
  if (key === 'ts') {
    if (filterForm.queryType === '1m' || filterForm.queryType === '5m' || filterForm.queryType === '15m' || filterForm.queryType === '60m') {
      return dayjs(value).format('HH:mm:ss')
    } else if (filterForm.queryType === 'day') {
      return dayjs(value).format('YYYY-MM-DD')
    } else if (filterForm.queryType === 'month') {
      return dayjs(value).format('YYYY-MM')
    }
  }
  if (typeof value === 'number') {
    return value.toFixed(2)
  }
  return value || '-'
}

// 获取监测点列表
const getMonitorPoints = async () => {
  if (!props.stationId) return

  try {
    const res: any = await getAllStationGroup({ stationId: props.stationId })

    // 处理分组数据，提取所有监测点
    const allPoints: any[] = []
    const pointsTypeObj: any = {};
    if (res.data && Array.isArray(res.data)) {
      res.data.forEach((group: any) => {
        allPoints.push({
          id: group.type,
          name: group.type
        })
        if (group.attrList && Array.isArray(group.attrList)) {
          pointsTypeObj[group.type] = group.attrList
        }
      })
    }

    monitorPointList.value = allPoints
    monitorPointTypeObj.value = pointsTypeObj

    // 设置默认选中第一个监测点
    if (monitorPointList.value.length > 0) {
      filterForm.monitorPointName = monitorPointList.value[0].name
      filterForm.monitorPointId = monitorPointList.value[0].id
    }
  } catch (error) {
    console.error('获取监测点列表失败:', error)
  }
}

// 查询数据
const queryData = async () => {
  if (!filterForm.monitorPointId) {
    uni.showToast({ title: '请选择监测点', icon: 'none' })
    return
  }

  try {
    uni.showLoading({ title: '查询中...' })
    
    const params = {
      attributes: (monitorPointTypeObj.value[filterForm.monitorPointId].map((attr: any) => attr.id) || []).join(','),
      queryType: filterForm.queryType,
      start: dayjs(filterForm.startTime).startOf('day').valueOf(),
      end: dayjs(filterForm.endTime).endOf('day').valueOf()
    }
    
    const res: any = await getDataCompare(params)
    const result = res.data?.data
    
    if (result) {
      // 处理表格数据
      tableHeaders.value = result.tableInfo?.map((info: any) => ({
        label: info.columnName,
        key: info.columnValue,
        width: 300
      })) || []
      
      tableData.value = result.tableDataList || []
      
      // 如果当前是图表模式，渲染图表
      if (viewMode.value === 'chart') {
        renderChart()
      }
    }
    
    uni.hideLoading()
  } catch (error) {
    uni.hideLoading()
    console.error('查询数据失败:', error)
    uni.showToast({ title: '查询失败', icon: 'none' })
  }
}

// 渲染图表
const renderChart = () => {
  if (!tableData.value.length) return;

  trendChart.value.init(echarts, (chart: any) => {
    const xAxisData = tableData.value.map((item: any) =>
      dayjs(item.ts).format('MM-DD HH:mm') // 更改为 'item.ts'
    );

    const series: any[] = [];
    const yAxis: any[] = [
      // 如果要显示多Y轴，清空此数组
      {
        type: 'value',
        axisLine: {
          show: true,
        },
        axisLabel: {
          show: true,
        }
      }
    ];
    const legendData: string[] = [];
    const colors = ['#3862F8', '#5AD8A6', '#F6BD16', '#73DDFF', '#7CFFB2', '#FF647C', '#DE63FF', '#B2FF7C', '#FFB27C', '#7C7CFF']; // 更多颜色用于多条折线

    // 过滤掉 tableHeaders 中的 'ts' (时间) 列
    const valueHeaders = tableHeaders.value.filter(h => h.key !== 'ts');

    valueHeaders.forEach((header, index) => {
      legendData.push(header.label);
      series.push({
        name: header.label,
        type: 'line',
        smooth: true,
        data: tableData.value.map((item: any) => parseFloat(item[header.key]) || 0),
        // yAxisIndex: index, // 为每个系列分配一个唯一的 yAxisIndex
        areaStyle: {
          opacity: 0.1
        },
        itemStyle: {
          color: colors[index % colors.length] // 从调色板中分配颜色
        }
      });

      // yAxis.push({
      //   type: 'value',
      //   name: `${header.unit ? `(${header.unit})` : ''}`,
      //   position: index === 0 ? 'left' : 'right', // 第一个 Y 轴在左侧，其他在右侧
      //   offset: index > 0 ? (index - 1) * 30 : 0, // 多个右侧 Y 轴的偏移量。第一个右侧 Y 轴 (index 1) 偏移量为 0。
      //   axisLine: {
      //     show: true,
      //     lineStyle: {
      //       color: colors[index % colors.length]
      //     }
      //   },
      //   axisLabel: {
      //     formatter: `{value} ${header.unit || ''}`
      //   }
      // });
    });

    const option = {
      color: colors, // 将颜色应用于图表
      grid: {
        left: 50,
        // right: (valueHeaders.length > 1 ? (valueHeaders.length - 1) * 60 + 20 : 20), // 根据右侧 Y 轴的数量调整右侧网格
        right: 0,
        top: 40, // 为图例留出更多空间
        bottom: 60
      },
      legend: {
        data: legendData,
        top: 0,
        left: 'center',
        type: 'scroll' // 启用滚动以适应多个图例
      },
      xAxis: {
        type: 'category',
        data: xAxisData,
        axisLabel: {
          rotate: 45,
          interval: Math.ceil(xAxisData.length / 7) // 调整间隔以防止标签重叠
        }
      },
      yAxis: yAxis, // 使用动态生成的 yAxis 数组
      tooltip: {
        trigger: 'axis',
        confine: true
      },
      dataZoom: [
        {
          type: 'inside',
          filterMode: 'filter'
        },
        {
          type: 'slider',
          height: 30,
          bottom: 10
        }
      ],
      series: series // 使用动态生成的 series 数组
    };

    chart.setOption(option);
  });
}

// 监听stationId变化
watch(() => props.stationId, (newId) => {
  if (newId) {
    getMonitorPoints()
  }
}, { immediate: true })

onMounted(() => {
  if (props.stationId) {
    getMonitorPoints()
  }
})
</script>

<style lang="scss" scoped>
.trend-chart-container {
  padding: 20rpx;
}

.chart-header {
  margin-bottom: 30rpx;
}

.chart-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  text-align: left;
}

.filter-section {
  background-color: #f4f7fe;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.query-button {
  margin-top: 20rpx;
}

.view-toggle {
  display: flex;
  margin-bottom: 20rpx;
}

.toggle-btn {
  flex: 1;
  height: 64rpx;
  line-height: 64rpx;
  text-align: center;
  background-color: #f9f9f9;
  color: #91949f;
  font-size: 28rpx;
  
  &:first-child {
    border-radius: 16rpx 0 0 16rpx;
  }
  
  &:last-child {
    border-radius: 0 16rpx 16rpx 0;
  }
  
  &.active {
    background-color: #3862f8;
    color: #fff;
  }
}

.chart-view {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 20rpx;
}

.chart-content {
  width: 100%;
  height: 40vh;
}

.table-view {
  background-color: #fff;
  border-radius: 16rpx;
}

::v-deep .u-form-item {
  padding: 0;
}

::v-deep .u-form-item__body {
  padding: 0;
}

.placeholderClass {
  color: #c0c4cc;
}
</style>
