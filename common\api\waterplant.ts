import { http } from '../vmeitime-http/index'

export const getStationList = (params: {
	page: number
	size: number
	type?: String
	projectId?: String
}) => {
	return http().get('api/station/list', params)
}

export const getAllStationGroup = (params: {
	stationId: String
}) => {
	return http().get('api/station/allAttrList', params)
}

export const getAllStationAttrs = (params: {
	stationId: String
}) => {
	return http().get('api/station/stationAttrList', params)
}

export const getStationData = (stationId: String,type?:String) => {
	return http().get(`istar/api/station/data/detail/${stationId}`, {type:type})
}