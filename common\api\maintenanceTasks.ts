import { http } from '../vmeitime-http/index'

export const maintainTaskList = (params: {
	fromTime?: number
	toTime?: number
	page: number
	size: number
	maintainUser: string
	isAssigned?: boolean
	isComplete?: boolean
}) => {
	return http().get('api/sm/maintainTask', params)
}
/**
 * 设备保养提交
 */
export const maintainItemReport = (id: string, params: {
	taskItemId?: string,
	img?: string,
	audio?: string,
	video?: string,
	file?: string,
	remark?: string
}) => {
	return http().post(`api/sm/maintainTaskItem/${id}/report`, params)
}
/**
 * 完成养护任务
 */
export const completeMaintenanceTask = (id: string) => {
	return http().post(`api/sm/maintainTask/${id}/complete`)
}