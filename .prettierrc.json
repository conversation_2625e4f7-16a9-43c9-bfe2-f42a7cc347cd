{"semi": true, "singleQuote": true, "jsxSingleQuote": true, "printWidth": 100, "tabWidth": 2, "useTabs": false, "trailingComma": "all", "arrowParens": "avoid", "bracketSpacing": true, "bracketSameLine": false, "endOfLine": "lf", "htmlWhitespaceSensitivity": "strict", "trimTrailingWhitespace": true, "embeddedLanguageFormatting": "auto", "singleAttributePerLine": false, "quoteProps": "as-needed", "overrides": [{"files": ["*.ts", "*.tsx", "*.jsx"], "options": {"parser": "typescript"}}]}