<template>
  <scroll-view 
    :scroll-y="true" 
    class="cover-view-menu border-box"
    :class="[touch.directionY === 'up' ? 'pull-up' : '']" 
    @touchend="$emit('touchEnd', $event)" 
    @touchstart="$emit('touchStart', $event)"
  >
    <view class="menu-list flex-around">
      <template v-for="(layer, index) in menus" :key="index">
        <view v-if="index < showCount" class="layer-box">
          <text class="layer-title">{{ layer.name }}</text>
          <view class="layer-menu">
            <view 
              class="menu-item" 
              v-for="(menu, i) in layer.list" 
              :key="i"
              @click="$emit('menuClick', menu.id, layer.id)"
            >
              <view 
                class="icon-bg" 
                :class="[index > 0 ? 'round-rect' : '']"
                :style="{ 
                  background: menu.color || (menu.id === currentMenu ? '#3862F8' : '#E2E3E5') 
                }"
              >
                <text 
                  :class="['layer-menu-icon', 'custom-icon', menu.icon]"
                  :style="{ 
                    color: menu.color ? '#ffffff' : 
                           menu.id === currentMenu ? '#ffffff' : '#060F27' 
                  }"
                />
              </view>
              <text 
                class="layer-menu-text"
                :style="{ 
                  color: menu.id === currentMenu ? '#3862F8' : '#060F27' 
                }"
              >
                {{ menu.alias || menu.name }}
              </text>
            </view>
          </view>
        </view>
      </template>
    </view>
  </scroll-view>
</template>

<script>
export default {
  name: 'BottomMenu',
  props: {
    menus: {
      type: Array,
      default: () => []
    },
    showCount: {
      type: Number,
      default: 1
    },
    currentMenu: {
      type: String,
      default: ''
    },
    touch: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['menuClick', 'touchStart', 'touchEnd']
}
</script>

<style scoped lang="scss">
.cover-view-menu {
  position: absolute;
  bottom: 0;
  background-color: #FBFBFB;
  height: 350rpx;
  transition: all 0.5s ease-in-out;

  &.pull-up {
    height: 800rpx;
  }
}

.border-box {
  width: 100%;
  padding-top: 30rpx;
  background-color: #FBFBFB;
  position: absolute;
  bottom: 0;
  align-items: center;
  justify-content: space-around;
  border-radius: 16rpx 16rpx 0rpx 0;

  .menu-list {
    flex-direction: column;
    display: flex;
    justify-content: space-around;
    width: 100%;

    .layer-box {
      width: 100%;
      padding: 0 32rpx 25rpx;

      .layer-title {
        color: #91949F;
      }

      .layer-menu {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        flex-wrap: wrap;
        border-radius: 8px;
        background-color: #ffffff;
        margin-top: 20rpx;
        padding-bottom: 20rpx;

        .menu-item {
          width: 20%;
          align-items: center;
          padding: 25rpx 0 0;
          text-align: center;
          display: flex;
          justify-content: center;
          flex-direction: column;

          .icon-bg {
            border-radius: 50%;
            width: 64rpx;
            height: 64rpx;
            display: flex;
            align-items: center;
            justify-content: center;

            &.round-rect {
              border-radius: 16rpx;
            }
          }

          .layer-menu-text {
            word-break: keep-all;
          }
        }
      }
    }
  }
}
</style>