import { computed, ref } from 'vue'
export const usePipeTabs = () => {
	let queryParams = undefined

	const tabs = ref<{ name: string, value: string }[]>()
	const currentTab = ref<string>('')
	let allCurrentTabData = []
	const limit = 20
	let page = 0
	let timer: any = undefined
	const currentTabData = ref<any[]>([])
	const currentTabName = computed(() => { return tabs.value.find(item => item.value === currentTab.value)?.name })
	const refreshData = (data: any[]) => {
		console.log('正在更新');
		uni.showLoading({ "title": "加载中", "mask": true })
		try {
			page = 0
			if (!currentTab.value) currentTabData.value = []
			console.log(currentTab.value, queryParams);
			// const res = await pipeQuery.excute(currentTab.value, queryParams)
			// console.log(res);
			// console.log(res.features);
			allCurrentTabData = data || []
			currentTabData.value = allCurrentTabData.slice(0, limit)
		} catch (e) {
			//TODO handle the exception
			console.log(e);
			uni.hideLoading()
			throw new Error(e.message)
		}
		uni.hideLoading()

	}
	const filter = ref<string>('')
	const filterData = () => {
		timer && clearTimeout(timer)
		timer = setTimeout(() => {
			page = 0
			if (filter.value === undefined) {
				currentTabData.value = allCurrentTabData.splice(0, limit)
			} else {
				currentTabData.value = allCurrentTabData.filter(item => item.SID?.toLowerCase()?.indexOf(filter.value?.trim()?.toLowerCase()) !== -1).slice(0, limit)
			}
		}, 500)


	}
	const showMore = () => {
		if (currentTabData.value.length >= allCurrentTabData.length) return
		page++
		currentTabData.value.push(...allCurrentTabData.slice(page * limit, (page + 1) * limit))
	}

	const init = (params: any) => {
		queryParams = params
	}
	const setCurrent = (id: string) => {
		currentTab.value = id
	}

	return {
		tabs,
		currentTab,
		setCurrent,
		currentTabData,
		showMore,
		init,
		filter,
		filterData,
		refreshData,
		currentTabName
	}
}