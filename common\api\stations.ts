// 站点相关接口
import {
	http
} from '../vmeitime-http'

/**
 * 查询站点列表
 */
export const getStationList = (params : {
	page ?: number
	size ?: number
	type ?: string
	projectId ?: string
}) => {
	return http().get('api/station/list', {
		page: 1,
		size: 9999,
		...params
	})
}
/**
 * 获取水厂供水量
 */
export const getWaterPlantWaterSupply = () => {
	return http().get('istar/api/production/waterPlant/getWaterSupplyInfo')
}
/**
 * 获取流量监测点状态列表
 */
export const getFlowMonitorStatusList = (status ?: string) => {
	return http().get('istar/api/flowMonitoringStation/getList', { status })
}
/**
 * 获取压力状态列表
 */
export const getPressureMonitorStatusList = (status ?: string) => {
	return http().get('istar/api/pressureMonitoringStation/getList', { status })
}
/**
 * 水质状态列表
 */
export const getQualityMonitorStatusList = (status ?: string) => {
	return http().get('istar/api/waterQualityStation/getList', { status })
}
/**
 * 大用户状态列表
 */
export const getBigUserStatusList = (status ?: string) => {
	return http().get('istar/api/bigUser/getList', { status })
}
/**
 * 泵站状态列表
 */
export const getPumpStationList = (params : { projectId ?: string; name ?: string }) => {
	return http().get('istar/api/boosterPumpStation/getWaterSupplyInfo', params)
}
/**
 * 获取站点的实时监测值
 */
export const getStationRealTimeProperties = (id : string, params ?: { type ?: string }) => {
	return http().get(`istar/api/station/data/detail/${id}`, params)
}


export const getRatio = (params : {
	start : number,
	end : number,
	stationId : string
	type : any
}) => {
	return http().get(`istar/api/flowMonitoringStation/getRatio`, params)
}