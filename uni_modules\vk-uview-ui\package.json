{"id": "vk-uview-ui", "name": "vk-uview-ui", "displayName": "【开箱即用】uView Vue3 横空出世，继承uView1意志，再战江湖，风云再起！", "version": "1.4.5", "description": "同时支持 Vue3.0 和 Vue2.0，你没看错，现在 uView 支持 Vue3.0 了！（不支持nvue，此版本为uView1.0的分支）", "keywords": ["vk-uview-ui", "vk云开发", "vue3.0", "uniapp", "uview"], "repository": "https://gitee.com/vk-uni/vk-uview-ui.git", "engines": {"HBuilderX": "^3.1.0"}, "dcloudext": {"sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "插件不采集任何数据", "permissions": "无"}, "npmurl": "https://vkuviewdoc.fsq.pub", "type": "component-vue"}, "uni_modules": {"dependencies": [], "encrypt": [], "platforms": {"cloud": {"tcb": "y", "aliyun": "y"}, "client": {"Vue": {"vue2": "y", "vue3": "y"}, "App": {"app-vue": "y", "app-nvue": "n"}, "H5-mobile": {"Safari": "y", "Android Browser": "y", "微信浏览器(Android)": "y", "QQ浏览器(Android)": "y"}, "H5-pc": {"Chrome": "y", "IE": "u", "Edge": "y", "Firefox": "y", "Safari": "y"}, "小程序": {"微信": "y", "阿里": "u", "百度": "u", "字节跳动": "u", "QQ": "u"}, "快应用": {"华为": "u", "联盟": "u"}}}}}