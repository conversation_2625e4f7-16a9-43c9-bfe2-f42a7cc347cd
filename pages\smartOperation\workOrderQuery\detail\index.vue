<template>
	<view class="main">
		<work-order-detail :fromType="detail.fromType" :workOrderId="detail.id" v-if="detail"></work-order-detail>
	</view>
</template>


<script lang="ts" setup>
	import {
		onMounted,
		ref
	} from 'vue'
	import workOrderDetail from '../../workOrderDetail/index.vue'
	
	const detail = ref < any > (null)
	const onRefresh = ()=>{
		detail.value = null
		var pages = getCurrentPages();
		var page = pages[pages.length - 1];
		console.log('dasda', page.$page.options)
		detail.value = page.$page.options
	}
	
	onMounted(() => {
		onRefresh()
	})
</script>

<style>

</style>
