<template>
	<view>
		<u-navbar fixed placeholder safeAreaInsetTop bgColor="#FFFFFF" title="水量报表" :autoBack="true" leftIconSize="20">
			<template #right>
				<view class="nv-right" @click="state.screenShow = true">
					<image src="/static/img/icons/shaixuan-black.png" style="height: 48rpx;width: 48rpx;"></image>
				</view>
			</template>
		</u-navbar>
		<view class="list">
			<view class="title">
				水量报表（{{state.currentName}})
			</view>
			<scroll-view scroll-x scroll-y>
				<!-- <wyb-table borderColor="#EBEDF6" first-col-bg-color="#FFFFFF" :showvertborder="true"
					header-bg-color="#F9F9F9" first-line-fixed ref="table" width="100%" :headers="headers"
					:contents="tableData" /> -->
				<scroll-view scroll-x scroll-y>
					<u-table font-size="30" padding="14rpx">
						<u-tr class="u-tr">
							<u-th class="u-th" v-for="(item,index) in headers" :key="index" :width="item.width+'rpx'">
								{{item.label}}{{item.unit}}
							</u-th>
						</u-tr>
						<u-tr class="u-tr" v-for="(item,index) in tableData" :key="index">
							<u-td class="u-td" v-for="(key,index) in headers" :key="index" :width="key.width+'rpx'">
								{{item[key.key] || '0'}}
							</u-td>
						</u-tr>
					</u-table>
				</scroll-view>
			</scroll-view>
		</view>
			<u-popup mode="right" width="528" v-model="state.screenShow" closeOnClickOverlay overlay
				@close="state.screenShow = false">
				<view class="popup">
				<view class="screen-list">
					<u-form :model="screenForm" ref="form1" :label-style="{'color':'#91949F'}" border-bottom input-align="right"
						label-width="180">
						<u-form-item label="区域选择：" prop="screenForm.stationName">
							<input fontSize="14"inputmode="none" placeholder-class="placeholderClass" placeholder="点击选择" v-model="screenForm.stationName" @click="state.stationShow=true">
							</input>
							<template #right>
								<u-icon name="arrow-down" color="#91949F" size="34"></u-icon>
							</template>
						</u-form-item>
						<u-form-item label="查询类型：" prop="screenForm.queryTypeName">
							<input fontSize="14" inputmode="none" placeholder-class="placeholderClass" placeholder="点击选择" v-model="screenForm.queryTypeName" @click="state.typeShow=true">
							</input>
							<template #right>
								<u-icon name="arrow-down" color="#91949F" size="34"></u-icon>
							</template>
						</u-form-item>
						<u-form-item label="开始日期：" prop="screenForm.start">
							<input fontSize="14" inputmode="none" placeholder-class="placeholderClass" placeholder="点击选择" v-model="screenForm.start" @click="openCalendar('start')">
							</input>
							<template #right>
								<u-icon name="calendar" color="#91949F" size="34"></u-icon>
							</template>
						</u-form-item>
						<u-form-item label="截止日期：" prop="screenForm.end">
							<input fontSize="14" inputmode="none" placeholder-class="placeholderClass" placeholder="点击选择" v-model="screenForm.end" @click="openCalendar('end')">
							</input>
							<template #right>
								<u-icon name="calendar" color="#91949F" size="34"></u-icon>
							</template>
						</u-form-item>
					</u-form>
				</view>
				<view class="popup-button">
					<u-button type="primary" color="#3862F8" @click="submitScreen">确定</u-button>
				</view>
				</view>
			</u-popup>
		<!-- 报告类型 -->
		<u-picker v-model="state.typeShow" mode="selector" :default-selector="[0]" :range="typeList" range-key="name"
			@confirm="selectClick"></u-picker>
		<!-- 区域 -->
		<u-picker v-model="state.stationShow" mode="multiSelector" :default-selector="[0, 0, 0]" :range="stations"
			range-key="name" @confirm="selectStationClick" @columnchange="onColumnChange"></u-picker>
		<u-calendar v-model="state.dateShow" ref="calendar" @close="state.dateShow=false" @change="chooseDate">
		</u-calendar>
	</view>
</template>

<script lang="ts" setup>
	import {
		reactive,
		onMounted,
		ref
	} from "vue"
	import dayjs from 'dayjs'
	import {
		getWaterSupplyReport,
		getSimpleTree
	} from '@/common/api/report'
	import {
		queryTypeList
	} from '@/common/data/publicData'

	const state = reactive < {
		screenShow: boolean,
		typeShow: boolean,
		dateShow: boolean,
		stationShow: boolean,
		dateType: string,
		currentName: string,
		stationsTree: any,
	} > ({
		screenShow: false,
		typeShow: false,
		dateShow: false,
		stationShow: false,
		dateType: '',
		currentName: '',
		stationsTree: []
	})

	const screenForm = reactive < any > ({
		queryType: 'day',
		queryTypeName: '日报表',
		start: dayjs().format('YYYY-MM-DD'),
		end: dayjs().format('YYYY-MM-DD'),
		stationId: '',
		stationName: '',
	})
	const stations = ref < any > ([])
	const typeList = reactive < any > (queryTypeList)
	const headers = ref < any > ([])
	const tableData = ref < any > ([])

	//选择报表类型
	const selectClick = (index: number) => {
		const type = typeList[index]
		screenForm.queryTypeName = type.name
		screenForm.queryType = type.value
		screenForm.start = dayjs().format(type.format)
		screenForm.end = dayjs().format(type.format)
		state.typeShow = false
	}

	// 选择日期
	const chooseDate = (date: any) => {
		console.log(date)
		state.dateShow = false
		if (screenForm.queryType === 'day') {
			screenForm[state.dateType] = date.result
		} else if (screenForm.queryType === 'month') {
			screenForm[state.dateType] = date.year + '-' + date.month
		} else {
			screenForm[state.dateType] = date.year
		}
	}

	// 获取所有站点列表
	const stationList = async () => {
		const res = await getSimpleTree({
			type: '泵站',
		})
		state.stationsTree = res.data?.data
		await formatPicker(state.stationsTree, 0, 0)
		console.log(stations.value)
		// stations.value = res.data?.data
		await selectStationClick([0, 0, 0])
	}
	//选择报表类型
	const selectStationClick = async (index: any) => {
		const i = index.pop()
		const last = stations.value[stations.value.length - 1] as any
		const first = last ? last[i] : {}
		screenForm.stationId = first.id
		screenForm.stationName = first.name
		state.stationShow = false
	}

	const openCalendar = (type: string) => {
		state.dateType = type
		state.dateShow = true
	}
	//
	const onColumnChange = ({
		column: column,
		index: index
	}) => {
		if (column === 0) {
			formatPicker(state.stationsTree, index, 0)
		} else if (column === 1) {
			formatPicker(state.stationsTree, 0, index)
		}
	}

	// 报表
	const waterSupplyReport = async () => {
		const params = {
			...screenForm,
			start: dayjs(screenForm.start).startOf(screenForm.queryType).valueOf(),
			end: dayjs(screenForm.end).endOf(screenForm.queryType).valueOf(),
		}
		state.currentName = screenForm.stationName
		const res = await getWaterSupplyReport(params)
		const result = res.data?.data
		headers.value = result.tableInfo.map((info: any) => {
			return {
				label: info.columnName,
				key: info.columnValue,
				unit: info.unit ? '(' + info.unit + ')' : '',
				width: '260',
			}
		})
		tableData.value = result.tableDataList
	}
	const formatPicker = async (data: any, fi ? : number, si ? : number) => {
		stations.value.push(data)
		if (data[fi].children?.length > 0) {
			await formatPicker(data[si].children, fi, si)
		}
	}

	// 提交筛选
	const submitScreen = async () => {
		state.screenShow = false
		await waterSupplyReport()
	}


	onMounted(async () => {
		await stationList()
		await waterSupplyReport()
	})
</script>

<style lang="scss" scoped>
	.list {
		margin-top: 20rpx;
		min-height: 90vh;
		padding: 22rpx 32rpx;
		background-color: #FFFFFF;

		.title {
			font-weight: 700;
			font-size: 30rpx;
			color: #060F27;
			padding-bottom: 20rpx;
		}
	}

	.popup {
		width: 100%;
		height: 100%;
		position: relative;

		.screen-list {
			padding: 222rpx 34rpx;
		}

		.popup-button {
			width: 94%;
			position: absolute;
			bottom: 40rpx;
			left: 0;
			right: 0;
			margin: 0 auto;
		}

		::v-deep .placeholderClass {
			font-size: 28rpx;
		}
	}
	.u-td{
		height: 44rpx;
	}

</style>
