// 指挥调度-防汛预案
import { http } from '../vmeitime-http/index'

// 列表
export const taskList = (params:
  { page: number
    size: number
    name?: string 
    status?: string 
    areaId: string
	}) => {
  return http().get('api/fx/task/list', params)
}

//删除
export const delTask = (ids?: string[]) => {
 return http().delete('api/fx/task',ids)
}

export const taskDetail = (id: string) => {
 return http().get(`api/fx/task/${id}`)
}


//修改
export const updateTask = (params:{ 
    id: string
    name: string
    type: string
    stationId: string
    pondingReason: string
    solution: string
    mainUserId: string
    mainUserPhone: string
    circuitUserId: string
    circuitUserPhone: string
    circuitDuty: string
    carId: string
    items: any
 }) => {
  return http().put( 'api/fx/task',params)
}

//新增
export const addTask = (params:{ 
    name: string
    type: string
    stationId: string
    pondingReason: string
    solution: string
    mainUserId: string
    mainUserPhone: string
    circuitUserId: string
    circuitUserPhone: string
    circuitDuty: string
    carId: string
    items: any
 }) => {
  return http().post( 'api/fx/task',params)
}

//反馈
export const complete = (params:{ 
    id: string
    feedbackImgs: string
    feedbackAudio: string
    feedbackVideo: string
    feedbackFile: string
    feedbackRemark: string
 }) => {
  return http().post( 'api/fx/task/complete',params)
} 

//审核
export const review = (params:{ 
    id: string
    reviewerImgs: string
    reviewerAudio: string
    reviewerVideo: string
    reviewerFile: string
    reviewerRemark: string
    status: string
 }) => {
  return http().post( 'api/fx/task/review',params)
} 

// APP
export const ownTaskList = (params:
  { page: number
    size: number
    name?: string 
    areaId: string,
	status?: string
	}) => {
  return http().get('api/fx/task/own', params)
}

//创建
export const createdTask = (params:{ 
    name: string
    type: string
    stationId: string
    pondingReason: string
    solution: string
    mainUserId: string
    mainUserPhone: string
    circuitUserId: string
    circuitUserPhone: string
    circuitDuty: string
    carId: string
    items: any
 }) => {
	return http().post('api/fx/task', params)
}

// 任务类型列表
export const taskTypeList = (params?:
  { page: number
    size: number
    name?: string }) => {
  return http().get('api/fx/taskType/list',params)
}

// 区域树
export const areaTree = () => {
  return http().get('api/fx/area/tree')
}

// 站点树
export const stationTree = () => {
  return http().get('api/fx/area/stationTree')
}

// 应急预案列表
export const planList = (params?:{
	page:number,
	size:number,
	stationId?:string
}) => {
  return http().get('api/fx/plan/list',params)
}
// 应急预案列表
export const materialStoreList = (params?:{
	page:number,
	size:number,
	areaId?:string
}) => {
  return http().get('api/fx/materialStore/list',params)
}


//防汛车辆
export const circuitCarList = (params: {
	page: number
	size: number
	type?: string
}) => {
	return http().get(`api/maintain/circuitCar/list`)
}

// 我待办的流程
export const listTodoProcess = (params?: any) => {
    return http().get('workflow/process/todoList',params)
}

// 查询流程列表
export const listProcess = (query?: any) => {
    return http().get('workflow/process/list',query)
}
// 所有任务的流程
export const listAllProcess = (query?: any) => {
   return http().get('workflow/process/allList',query)
}
// 流程分类
export const listAllCategory = (params?: any) => {
    return http().get('workflow/category/listAll',params)
}

// 查询流程列表
export const getProcessForm = (query?: any) => {
    return  http().get('workflow/process/getProcessForm', query)
}

// 我已办的流程
export const listFinishedProcess = (params?: any) => {
   return http().get('workflow/process/finishedList',params)
}
// 删除流程实例
export const delProcess = (ids: any) => {
    return http().delete('workflow/process/instance/' + ids)
}
//获取应急任务概览
export const taskCount = () =>{
	return http().get('api/fx/task/count')
}

// 流程实例
export const startProcess = (processDefId: string, data?: any) => {
    return http().post('workflow/process/start/' + processDefId, data)
}

export const detailProcess = (query?: any) => {
    return http().get('workflow/process/detail',query)
}

// 处理任务实例
export const completeTask = ( data?: any) => {
    return http().post('workflow/task/complete', data)
}

// 拒绝任务实例
export const rejectTask = ( data?: any) => {
    return http().post('workflow/task/reject', data)
}


//催办和短信
export const sendTaskRemind = (params:{
    procInsId:string
    type: string
}) => {
	return http().post('api/fx/taskNotify',params)
}

