import {
	loadEsriModules
} from "../../utils/arcMapHelper"
// import {
// 	gisConfig
// } from '../../data/gisData'
export const usePipeQuery = () => {
	let query = undefined
	let Query = undefined
	const init = async () => {
		[query, Query] = await loadEsriModules(['esri/rest/query', 'esri/rest/support/Query'])
		console.log('query is inited');
	}
	const excute = async (url, params) => {
		if (!query || !Query) await init()
		// const url = gisConfig().gisPipeDynamicService + '/' + layerId
		return query.executeQueryJSON(url, {
			where: '1=1',
			...(params || {})
		})
	}
	return {
		init,
		excute
	}
}
