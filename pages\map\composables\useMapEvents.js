import { getAllPartition } from '../../../common/api/dma'

export function useMapEvents(mapData) {
  const {
    verticalBar,
    location,
    onemapMenu,
    touch,
    cleanMark,
    basemap,
    dmaVisible,
    partitions,
    pullTimer
  } = mapData

  // Touch events
  const handleTouchStart = (e) => {
    touch.touchStart(e)
  }

  const handleTouchEnd = (e) => {
    touch.touchEnd(e)
    if (touch.directionY === 'up') {
      pullTimer.value = setTimeout(() => {
        onemapMenu.setRootMenuCount(onemapMenu.menus.length)
      }, 500)
    } else {
      clearTimeout(pullTimer.value)
      onemapMenu.setRootMenuCount(1)
    }
  }

  // Map actions
  const clearMap = () => {
    cleanMark.value++
  }

  const getLocation = () => {
    location.refreshNum++
  }

  const changeBaseMap = (type) => {
    basemap.value = type
  }

  const closeCover = () => {
    verticalBar.setCurrent('')
  }

  const toggleDma = async () => {
    dmaVisible.value = !dmaVisible.value
    if (!partitions.value.length) {
      await initPartition()
    }
  }

  const initPartition = async () => {
    try {
      const res = await getAllPartition()
      partitions.value = res.data || []
    } catch (e) {
      console.error('Failed to load partitions:', e)
    }
  }

  // Map click handler
  const handleMapClick = (attributes) => {
    // This will be handled by the popup composable
  }

  return {
    handleTouchStart,
    handleTouchEnd,
    clearMap,
    getLocation,
    changeBaseMap,
    closeCover,
    toggleDma,
    initPartition,
    handleMapClick
  }
}