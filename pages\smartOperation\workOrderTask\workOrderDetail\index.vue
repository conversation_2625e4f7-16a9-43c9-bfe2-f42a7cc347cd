<!-- 工单详情 -->
<template>
	<view class="main">
		<u-navbar title="工单详情">
			<!-- <template #right>
				<view class="nv-right">
					撤回
				</view>
			</template> -->
		</u-navbar>
		<work-order-detail :fromType="detail.fromType" :statusName="detail.statusName" :workOrderId="detail.id"
			v-if="detail"></work-order-detail>
		<view class="more-tab" v-if="state.showMore" :style="{height: 110*(moreTabbers.length)+'rpx'}">
			<view class="back" v-for="(item,index) in moreTabbers" @click="toOtherDispatch(item)" :key="index">
				<u-icon :name="item.iconPath" customPrefix="custom-icon" size="40">
				</u-icon>
				<view>{{item.text}}</view>
			</view>
		</view>
		<view class="button flex-around" v-if="detail && (detail.status === 'ASSIGN' || detail.status === 'PENDING') ">
			<u-button type="primary" @click="receive(detail)">接收</u-button>
			<u-button type="primary" @click="toInvalidHandle(detail)">退回</u-button>
		</view>
		<block v-else>
			<view class="button flex-around" v-if="detail.status!=='APPROVED'">
				<!-- 与包裹页面所有内容的元素u-page同级，且在它的下方 -->
				<u-tabbar :list="tabbers" @change="clickTabber" v-model="state.current"
					active-color="#3862F8"></u-tabbar>
			</view>
		</block>
		<u-toast ref="refToast" />
	</view>
</template>

<script lang="ts" setup>
	import workOrderDetail from '../../workOrderDetail/index.vue'
	import {
		onMounted,
		reactive,
		ref
	} from "vue";
	import {
		onShow
	} from '@dcloudio/uni-app';
	import {
		receiveWorkOrder
	} from '@/common/api/workOrder'
	const state = reactive<{
		current : number
		showMore : boolean
	}>({
		current: 10,
		showMore: false
	})

	const detail = ref<any>(null)
	const refToast = ref<any>()
	const tabbers = ref<any>([
		{
			iconPath: "custom-icon-chufadaodaxiao",
			selectedIconPath: "custom-icon-chufadaodaxiao",
			text: '到场',
			stage: 'ARRIVING',
			isDot: false,
			customIcon: true,
		},
		{
			iconPath: "custom-icon-chuli",
			selectedIconPath: "custom-icon-chuli",
			text: '处理',
			stage: 'PROCESSING',
			customIcon: true,
		},
		{
			iconPath: "custom-icon-wancheng",
			selectedIconPath: "custom-icon-wancheng",
			text: '完成',
			stage: 'SUBMIT',
			customIcon: true,
		},
		{
			iconPath: "custom-icon-gengduo",
			selectedIconPath: "custom-icon-gengduo",
			text: '更多',
			type: 'more',
			customIcon: true,
		}
	])

	const moreTabbers = ref<any>([
		{
			iconPath: "custom-icon-back",
			selectedIconPath: "custom-icon-back",
			text: '退回',
			type: 'chargebackRequest',
			customIcon: true,
		}
	])

	const receive = (detail : any) => {
		uni.showModal({
			title: '提示',
			content: '确定接收吗',
			success: (res) => {
				if (res.confirm) {
					receiveWorkOrder(detail.id, {}).then(res => {
						if (res.data?.code === 200) {
							// var pages = getCurrentPages();
							// var page = pages[pages.length - 2];
							// console.log(page)
							// page.$vm.onRefresh()
							refToast.value.show({
								title: '提交成功',
								type: 'success',
								back: true
							})
						}
					})
				}
			}
		})
	}

	const toInvalidHandle = (detail : any) => {
		uni.$u.route({
			url: 'pages/smartOperation/workOrderTask/workOrderDetail/returnHandle/index',
			params: {
				id: detail.id
			},
			animationType: true
		})
	}
	const toEventDispatch = (tabber : any) => {
		uni.$u.route({
			url: 'pages/smartOperation/workOrderTask/workOrderDetail/eventHandle/index',
			params: {
				id: detail.value.id,
				stage: tabber.stage,
				title: tabber.text
			},
			animationType: true
		})
	}
	const toOtherDispatch = (data : any) => {
		if(data.type==='handCollaboration'){
			uni.$u.route({
				url: 'pages/smartOperation/workOrderTask/workOrderDetail/collaboration/index',
				params: {
					id: detail.value.id,
					title: data.text,
					type: data.type
				},
				animationType: true
			})
		}else{
			uni.$u.route({
				url: 'pages/smartOperation/workOrderTask/workOrderDetail/otherHandle/index',
				params: {
					id: detail.value.id,
					title: data.text,
					type: data.type
				},
				animationType: true
			})
		}
	}
	const clickTabber = (index : number) => {
		state.current = index
		if (index < 3) {
			toEventDispatch(tabbers.value[index])
		} else {
			state.showMore = !state.showMore
			// toOtherDispatch(tabbers.value[index])
		}
	}

	const onRefresh = () => {
		detail.value = null
		var pages = getCurrentPages();
		var page = pages[pages.length - 1];
		detail.value = page.$page.options
	}


	onShow(async () => {
		onRefresh()

	})
	onMounted(() => { })
</script>

<style lang="scss" scoped>
	.main {
		padding-top: 20rpx;
		padding-bottom: 100rpx;

		.card-box {
			width: 686rpx;
			margin: 0 auto;
			border-radius: 16rpx;
			background-color: #FFFFFF;

			.hand {
				.hand-title {
					height: 42rpx;
					line-height: 42rpx;

					text {
						font-size: 28rpx;
						color: #060F27;
						font-weight: 600;
					}
				}

				.status {
					color: #91949F;
				}
			}

			.table {
				margin-top: 24rpx;

				.info {
					font-size: 24rpx;
					padding-bottom: 18rpx;
					display: flex;

					text {
						&:nth-child(1) {
							color: #91949F;
						}

						&:nth-child(2) {
							flex: 1;
						}
					}


					.bg {
						padding: 2rpx 12rpx;
						background: rgba(56, 98, 248, 0.2);
						border-radius: 8rpx;

						text {
							color: #3862F8;
						}
					}
				}
			}


			.card-title {
				padding: 14rpx 28rpx;

				.label {
					padding-left: 10rpx;
					font-style: normal;
					font-weight: 600;
					font-size: 28rpx;
					color: #060F27;
				}
			}

			.list {
				padding: 0 28rpx 10rpx 28rpx;
				color: #060F27;

				.l-c {
					padding: 10rpx 0;

					text {
						&:nth-child(1) {
							color: #91949F;
						}

						&:nth-child(3) {
							color: #060F27;
						}
					}

					.address {
						color: #FBB934;
						overflow: hidden;
						white-space: nowrap;
						width: 84%;
						text-overflow: ellipsis;
					}

					.navigation {
						text-align: right;
						font-size: 24rpx;
						color: #3862F8;
					}
				}

				.l-file {
					padding: 10rpx 0;
				}
			}

			.time-line {
				padding: 20rpx 28rpx;
			}

			.u-node {
				width: 32rpx;
				height: 32rpx;
				border-radius: 50%;
				display: flex;
				justify-content: center;
				align-items: center;
				background: rgba(56, 98, 248, 0.2);

				.circle-center {
					height: 16rpx;
					width: 16rpx;
					border-radius: 50%;
					background-color: #3862F8;
				}
			}

			.u-order-title {
				color: #91949F;
				font-size: 24rpx;
			}

			.u-order-desc {
				color: rgb(150, 150, 150);
				font-size: 28rpx;
				margin: 16rpx 0;
				font-size: 24rpx;

				text {
					&:nth-child(2) {
						color: #060F27;
						padding-left: 10rpx;
					}
				}
			}

			.u-order-time {
				color: rgb(200, 200, 200);
				font-size: 26rpx;
			}
		}

		.button {
			width: 100%;
			background-color: #FFFFFF;
			position: fixed;
			bottom: 0rpx;
			height: 120rpx;
			z-index: 9999;
			box-shadow: 0px 4rpx 30rpx rgba(178, 183, 199, 0.5);

			button {
				border-radius: 8rpx;
				width: 343rpx;
				margin: 20rpx auto;
			}
		}
	}

	.more-tab {
		position: fixed;
		bottom: 120rpx;
		right: 0;
		width: 20%;
		background-color: #FFFFFF;
		// height: 330rpx;
		padding: 10rpx 0;
		z-index: 99999999;

		.back {
			line-height: 40rpx;
			text-align: center;
			height: 100rpx;
			padding: 10rpx 0;
		}

		.forward {
			line-height: 40rpx;
			text-align: center;
			height: 100rpx;
			border-top: 1rpx solid #EBEDF6;
			padding: 10rpx 0;
		}
	}
</style>