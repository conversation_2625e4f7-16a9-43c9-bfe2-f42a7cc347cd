<!-- 事件分派 -->
<template>
	<view class="main">
		<u-navbar :title="workorder.title"></u-navbar>
		<view class="uform">
			<u-form :model="form" ref="refForm" :label-style="{'color':'#91949F'}" borderBottom labelWidth="180"
				input-align="right" :errorType="['toast']">
				<view class="card-box">
					<u-form-item v-if="workorder.type === 'handoverRequest'" required label="处理人：" prop="expectUserName"
						labelWidth="220">
						<input placeholder="请选择处理人" nputmode="none" v-model="form.expectUserName" inputAlign="right"
							placeholder-class="placeholderClass" @click="toChooseExpectUser">
						</input>
						<template #right>
							<u-icon name="arrow-right" size="28"></u-icon>
						</template>
					</u-form-item>
					<u-form-item required label="审核人员：" prop="nextProcessUserName" labelWidth="220">
						<input placeholder="请选审核人员" nputmode="none" v-model="form.nextProcessUserName"
							inputAlign="right" placeholder-class="placeholderClass" @click="toChooseUser">
						</input>
						<template #right>
							<u-icon name="arrow-right" size="28"></u-icon>
						</template>
					</u-form-item>
					<u-form-item label="备注：" :borderBottom="false" required>
					</u-form-item>
					<u-form-item label="" prop="processRemark">
						<u-input type="textarea" input-align="left"   inputAlign="left" placeholder="请输入备注" v-model="form.processRemark" border height="160">
						</u-input>
					</u-form-item>
				</view>
			</u-form>
		</view>
		<view class="button">
			<u-button type="primary" color="#3862F8" @click="submit">提交</u-button>
		</view>
		<u-toast ref="refToast" />
	</view>
</template>

<script lang="ts" setup>
	import {
		onMounted,
		reactive,
		ref
	} from "vue";
	import {
		onShow,
		onReady
	} from '@dcloudio/uni-app';
	import {
		useStore
	} from '@/store/index'
	import {
		removeSlash
	} from '@/common/utils/removeIdSlash'
	import {
		storeToRefs
	} from 'pinia'
	import {
		handoverRequest,
		chargebackRequestWorkOrder
	} from '@/common/api/workOrder'

	const store = useStore();
	const rules = reactive<any>({
		expectUserName: [{
			required: true,
			message: '请选择处理人'
		}],
		nextProcessUserName: [{
			required: true,
			message: '请选择审核人'
		}],
		processRemark: [{
			required: true,
			message: '请填写原因'
		}]
	})

	const refForm = ref<any>()
	const chooseUserType = ref<any>()
	const refUser = ref<any>()
	const refToast = ref<any>()
	const workorder = ref<any>({})
	const form = reactive<any>({
		nextProcessUserId: '',
		nextProcessUserName: '',
		processRemark: ''
	})

	const toChooseUser = () => {
		uni.$u.route({
			url: 'pages/userTree/organization',
			key: ''
		})
	}

	const toChooseExpectUser = () => {
		uni.$u.route({
			url: 'pages/userTree/organization',
			params: {
				key: 'expectUserName'
			}
		})
	}
	const submit = () => {
		refForm.value.validate(async (valid : any) => {
			if (valid) {
				uni.showModal({
					title: '提示',
					content: '确定提交吗',
					success: (res) => {
						if (res.confirm) {
							console.log(workorder.value)
							if (workorder.value.type === 'handoverRequest') {
								handoverRequestAction()
							} else if (workorder.value.type === 'chargebackRequest') {
								chargebackRequestAction()
							}
							// assignWorkOrder(workorderId.value, params).then(res => {
							// 	if (res.data?.code === 200) {
							// 		refToast.value.show({
							// 			title: '分派成功',
							// 			type: 'success',
							// 			callback: () => {
							// 				uni.navigateBack({
							// 					delta: 2
							// 				})
							// 			}
							// 		})
							// 	} else {
							// 		refToast.value.show({
							// 			title: '分派失败',
							// 			type: 'error'
							// 		})
							// 	}
							// })
						}
					}
				})
			}
		})
	}

	// 转单申请
	const handoverRequestAction = () => {
		handoverRequest(workorder.value.id, {

			...form, processAdditionalInfo: JSON.stringify({
				expectUserName: form.expectUserName,
				expectUserId: form.expectUserId
			})

		}).then(res => {
			if (res.data?.code === 200) {
				let {
					userData
				} = storeToRefs(store);
				userData.value = null
				refToast.value.show({
					title: '提交成功',
					type: 'success',
					callback: () => {
						uni.navigateBack({
							delta: 2
						})
					}
				})
			} else {
				refToast.value.show({
					title: '提交失败',
					type: 'error'
				})
			}
		})
	}

	// 退回申请
	const chargebackRequestAction = () => {
		chargebackRequestWorkOrder(workorder.value.id, {
			...form,
			stage: 'CHARGEBACK'
		}).then(res => {
			if (res.data?.code === 200) {
				let {
					userData
				} = storeToRefs(store);
				userData.value = null
				refToast.value.show({
					title: '退回成功',
					type: 'success',
					callback: () => {
						uni.navigateBack({
							delta: 2
						})
					}
				})
			} else {
				refToast.value.show({
					title: '退回失败',
					type: 'error'
				})
			}
		})
	}

	// 选择处理人
	const chooseUser = (user : any) => {
		if (user) {
			form.nextProcessUserName = user.firstName
			form.nextProcessUserId = removeSlash(user.id?.id)
		}
	}
	// 选择处理人
	const chooseOtherUser = (user : any) => {
		if (user) {
			form.expectUserName = user.firstName
			form.expectUserId = removeSlash(user.id?.id)
		}
	}

	onReady(async () => {
		console.log('ready')
		refForm.value.setRules(rules)
	})

	onShow(async () => {
		let {
			userData,
			otherUserData
		} = store;
		if (userData) {
			chooseUser(userData)
		}
		if (otherUserData) {
			chooseOtherUser(otherUserData)
		}
	})
	onMounted(async () => {
		var pages = getCurrentPages();
		var page = pages[pages.length - 1];
		workorder.value = page.$page.options
		let { userData, otherUserData } = storeToRefs(store);
		userData.value = null
		otherUserData.value = null

	})
</script>

<style lang="scss" scoped>
	.main {
		.uform {
			padding: 20rpx 32rpx;
		}

		.card-box {
			border-radius: 8px;
			min-height: 80rpx;
			padding: 20rpx 28rpx;
			margin: 0 auto 20rpx auto;
			color: #FFFFFF;
			background-color: #FFFFFF;

			.form {
				padding: 0 28rpx 10rpx 28rpx;
				color: #060F27;
			}

			.l-file {
				padding: 10rpx 0;
			}
		}


		::v-deep.u-form-item {
			padding: 0;
		}
	}
</style>