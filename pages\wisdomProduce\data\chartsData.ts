export function lineOption(dateX?: any, left?:number, right?:number) {
  return {
    // color: ['#318DFF', '#A431FF', '#FC2B2B', '#FFB800'],
    // backgroundColor: useAppStore().isDark ? '#131624' : '#F4F7FA',
    name: '',
    grid: {
      left: left || 40,
      right: right || 40,
      top: 80,
      bottom: 50
    },
    legend: {
      type: 'scroll',
      width: 600,
      top: 10,
      textStyle: {
        fontSize: 12
      }
    },
    tooltip: { trigger: 'axis' },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: dateX
    },
    yAxis: [{
      position: 'left',
      type: 'value',
      name: '',
      axisLine: {
        show: true,
        lineStyle: {
          types: 'solid'
        }
      },
      axisLabel: {
        show: true,
        textStyle: {
          // fontSize: 14 //更改坐标轴文字大小
        }
      },
      splitLine: {
        lineStyle: {
          type: [5, 10],
          dashOffset: 5
        }
      }
    }, {
      position: 'right',
      type: 'value',
      name: '',
      axisLine: {
        show: true,
        lineStyle: {
          types: 'solid'
        }
      },
      axisLabel: {
        show: true,
        textStyle: {
          // color: '#656b84' // 更改坐标轴文字颜色
          // fontSize: 14 //更改坐标轴文字大小
        }
      },
      splitLine: {
        lineStyle: {
          type: [5, 10],
          dashOffset: 5
        }
      }
    }],
    series: [{}]
  }
}