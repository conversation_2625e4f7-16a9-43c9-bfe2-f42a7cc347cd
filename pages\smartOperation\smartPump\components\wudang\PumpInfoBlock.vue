<template>
  <div class="pump-info-block">
    <FieldSet
      :type="'default'"
      :size="'small'"
      style="margin: 0; font-size: 16px; --el-border-color: rgb(13, 86, 196)"
    >
      {{ props.title }}
    </FieldSet>
    <div class="content">
      <div
        v-for="(item, i) in attrs"
        :key="i"
        class="attr-block"
      >
        <div class="value-box">
          <span class="value">{{ item?.formatter?.(item?.value) ?? item?.value ?? '--' }}</span>
          <span
            v-if="item?.unit"
            class="unit"
          >
            {{ item?.unit }}
          </span>
        </div>
        <div class="text">
          <span class="text">{{ item?.propertyName }}</span>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
const props = defineProps<{
  title: string
  info?: any[]
  index: number
}>()
const attrs = computed(() => {
  const freq = props.info?.find(item => item.property === 'freq' + props.index)
  const run = props.info?.find(item => item.property === 'run' + props.index)
  const ec = props.info?.find(item => item.property === 'ec' + props.index)
  const status = props.info?.find(item => item.property === 'status' + props.index)
  if (status) {
    status.formatter = val => {
      return val === '0' ? '停止' : val === '1' ? '变频运行' : val === '2' ? '工频运行' : '--'
    }
  }
  return [freq, run, ec, status]
})
</script>
<style lang="scss" scoped>
.pump-info-block {
  color: #fff;
  width: 100%;
  height: 305px;
  padding: 12px;
  .content {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    justify-content: space-around;
    width: 100%;
    height: calc(100% - 32px);
    background-color: #002468;
    margin-top: 10px;
    .attr-block {
      width: 40%;
      height: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      .value-box {
        height: 60px;
        width: 100%;
        display: flex;
        align-items: baseline;
        justify-content: center;
        background-color: rgb(0, 91, 177);
        margin-bottom: 12px;
      }
      .value {
        font-size: 24px;
        line-height: 60px;
        font-family: CKTKingKong;
        letter-spacing: 1px;
        text-shadow: 0 0 20px rgba(0, 200, 255, 0.5);
        font-weight: 700;
        background-image: linear-gradient(to right, #34ffd4, #34efff);
        -webkit-background-clip: text;
        background-clip: text;
        color: transparent;
      }
      .unit {
        font-size: 12px;
        margin-left: 6px;
      }
      .text {
        font-size: 14px;
      }
    }
  }
}
</style>
