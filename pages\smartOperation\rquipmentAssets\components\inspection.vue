<!-- 巡检信息 -->
<template>
	<view class="">
		<view class="card-box">
			<view class="flex-between hand">
				<view class="hand-title flex-center">
					<view class="icon-box">
						<u-icon name="custom-icon-gongdan" customPrefix="custom-icon" color="#FFFFFF" size="20">
						</u-icon>
					</view>
					<text>巡检统计</text>
				</view>
			</view>
			<u-gap height="1" bg-color="#bbb"></u-gap>
			<view class="table" style="padding: 0rpx;">
				<view class="info" style="background-color: #eaeaea;padding: 24rpx">
					<image src="@/static/img/icons/devices/inspection-num.png" class="icon-img"></image>
					<text>巡检次数：</text> <text>{{circuitInfo.count || '-'}}</text>
				</view>
				<view class="info" style="background-color: #eaeaea;padding: 24rpx;margin-top: 10rpx;">
					<image src="@/static/img/icons/devices/inspection-time.png" class="icon-img"></image>
					<text>最近巡检：</text> <text>{{circuitInfo.latestCircuitTime || '-'}}</text>
				</view>
			</view>
		</view>

		<view class="card-box">
			<view class="flex-between hand">
				<view class="hand-title flex-center">
					<image src="@/static/img/icons/devices/title-icon.png" style="width: 20rpx;height: 40rpx;"></image>
					<text>巡检情况</text>
				</view>
			</view>
			<u-gap height="1" bg-color="#d4d4d4"></u-gap>
			<view class="table">
				<view class="line-echarts">
					<l-echart ref="pieChart"></l-echart>
				</view>
			</view>
		</view>

		<view class="card-box">
			<view class="flex-between hand">
				<view class="hand-title flex-center">
					<image src="@/static/img/icons/devices/title-icon.png" style="width: 20rpx;height: 40rpx;"></image>
					<text>巡检计划</text>
				</view>
			</view>
			<u-gap height="1" bg-color="#bbb"></u-gap>
			<view class="table" style="padding: 0rpx;min-height: 200rpx;">
				<view class="list" v-for="(item,index) in circuitList" :key="index">
					<text style="font-weight: 600;">{{item.name || '-'}}</text>
					<view class="info flex-between" style="padding: 10rpx 0rpx;">
						<view style="width: 50%;text-align: left;">限制时间： <text>{{item.limitDays || '-'}}</text></view>
						<view style="width: 50%;text-align: left;">循环周期： <text>{{item.cycleDays || '-'}}</text></view>
					</view>
					<view class="info flex-between" style="padding: 0rpx;">
						<view style="width: 50%;text-align: left;">
							下一次保养时间：<text style="color: #0055ff;">{{item.nextTime || '-'}}</text>
						</view>
						<view style="width: 50%;text-align: left;">
							任务人： <text>{{item.userName || '-'}}</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="card-box">
			<view class="flex-between hand">
				<view class="hand-title flex-center">
					<image src="@/static/img/icons/devices/title-icon.png" style="width: 20rpx;height: 40rpx;"></image>
					<text>巡检记录</text>
				</view>
			</view>
			<u-gap height="1" bg-color="#bbb"></u-gap>
			<view class="table" style="padding: 0rpx;min-height: 200rpx;">
				<view class="list" v-for="(item,index) in circuitRecordList" :key="index">
					<text style="font-weight: 600;">{{item.name || '-'}}</text>
					<view class="info flex-between" style="padding: 10rpx 0rpx;">
						<view style="width: 50%;text-align: left;" class="flex">
							<view>开始时间：</view>
							<text>{{proxy.formatTime(item.realStartTime) || '-'}}</text>
						</view>
						<view style="width: 50%;text-align: left;" class="flex-center">
							<view>结束时间：</view>
							<text>{{proxy.formatTime(item.realEndTime) || '-'}}</text>
						</view>
					</view>
					<view class="info flex-between" style="padding: 0rpx;">
						<view style="width: 50%;text-align: left;" class="flex-center">
							<view>限制时间：</view>
							<text>{{item.nextTime || '-'}}</text>
						</view>
						<view style="width: 50%;text-align: left;" class="flex-center">
							<view>任务人：</view>
							<text>{{item.userName || '-'}}</text>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script lang="ts" setup>
	import {
		onMounted,
		ref,
		nextTick,
		getCurrentInstance
	} from 'vue'
	import * as echarts from 'echarts'
	const {
		proxy
	} = getCurrentInstance()
	import {
		getCircuitInfo,
		getCircuitList,
		getCircuitRecordList
	} from '@/common/api/rquipmentAssets'
	import {
		pieOption,
	} from './echartsData'

	const pieChart = ref < any > ({})
	const circuitInfo = ref < any > ({})
	const circuitList = ref < any > ([])
	const circuitRecordList = ref < any > ([])
	const props = defineProps({
		detail: {
			type: Object
		}
	})

	const refreshData = async (code: string) => {
		const res = await getCircuitInfo(code)
		circuitInfo.value = res.data.data
		console.log(res.data.data)
		const geadeCount = circuitInfo.value.nowYearCircuit.map((data: any) => {
			return {
				value: data.count,
				name: data.level
			}
		})
		initPie(geadeCount)
		const res2 = await getCircuitList(code, {
			page: 1,
			size: 9999
		})
		circuitList.value = res2.data?.data.data
		const record = await getCircuitRecordList({
			page: 1,
			size: 9999,
			startStartTime: '',
			endStartTime: ''
		})
		circuitRecordList.value = record.data?.data.data
	}

	onMounted(() => {
		nextTick(() => {
			refreshData(props.detail.deviceLabelCode)
		})

	})

	//饼图
	const initPie = (data: any) => {
		pieChart.value.init(echarts, (chart: any) => {
			const options = pieOption(data)
			chart.setOption(options);
		});
	}
</script>

<style lang="scss" scoped>
	.card-box {
		width: 686rpx;
		margin: 20rpx auto;
		border-radius: 16rpx;
		// padding: 22rpx 28rpx;
		padding: 0;
		background-color: #FFFFFF;

		.hand {
			padding: 22rpx 28rpx;

			.hand-title {
				height: 42rpx;
				line-height: 42rpx;

				.icon-box {
					text-align: center;
					line-height: 32rpx;
					height: 32rpx;
					width: 32rpx;
					background-color: #3862F8;
					border-radius: 4rpx;
				}

				text {
					font-size: 28rpx;
					margin-left: 16rpx;
					color: #060F27;
					font-weight: 600;
				}
			}

			.status {
				color: #91949F;
			}
		}

		.line {
			padding-bottom: 20rpx;
		}

		.table {
			margin-top: 24rpx;
			padding: 0rpx 28rpx;

			.info {
				font-size: 24rpx;
				padding: 24rpx 0rpx;
				display: flex;

				text {
					&:nth-child(1) {
						color: #000000;
					}

					&:nth-child(2) {
						flex: 1;
						color: #000000;
					}
				}


				.bg {
					padding: 2rpx 12rpx;
					background: rgba(56, 98, 248, 0.2);
					border-radius: 8rpx;

					text {
						color: #3862F8;
					}
				}
			}

			.files {
				font-size: 36rpx;
				font-weight: 600;

				text {
					color: #626262;
					padding-left: 20rpx;
				}
			}

			.list {
				background-color: #f1f1f1;
				padding: 24rpx;
				margin-top: 10rpx;
			}
		}

		.line-echarts {
			height: 400rpx;
			width: 100%;
		}

		.icon-img {
			width: 40rpx;
			height: 40rpx;
			padding-right: 20rpx;
		}
	}
</style>
