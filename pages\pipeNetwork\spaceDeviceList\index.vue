<template>
	<view class="main">
		<u-sticky bgColor="#FFFFFF">
			<view class="tabs" :change:pipeUrl="pipeQuery.setPipeUrl" :pipeUrl="pipeServerUrl"
				:change:geometry="pipeQuery.init" :geometry="geometry" :change:layerid="pipeQuery.query"
				:layerid="pipeTabs.currentTab">
				<u-tabs :list="pipeTabs.tabs" v-model="pipeTabs.currentTab"></u-tabs>
			</view>
			<view class="search-box flex-between">
				<u-search shape="square" :showAction="false" v-model="pipeTabs.filter" placeholder="请输入监测点名称"
					@change="pipeTabs.filterData">
				</u-search>
			</view>
		</u-sticky>
		<view class="card-list">
			<scroll-view v-if="pipeTabs.currentTabData.length" :lower-threshold="50" :refresher-threshold="50">
				<view class="card flex-between" v-for="(device,index) in pipeTabs.currentTabData" :key="index"
					@click="toDetail(device)">
					<view class="left flex-center">
						<view class="image">
							<text class="custom-icon custom-icon-gis-fm-paiqifa icon"></text>
						</view>
						<view class="info">
							<view class="name">
								编号 : {{device.SID}}
							</view>
						</view>
					</view>
					<view class="right flex-center">
						<u-icon name="arrow-right" color="#91949F" size="14"></u-icon>
					</view>
				</view>
			</scroll-view>
			<view v-else class="empty">
				<text>暂无相关信息！</text>
			</view>
		</view>
	</view>
</template>

<script lang="ts">
	import { gisConfig } from '../../../common/data/gisData';
import {
		usePipeTabs
	} from './hooks/usePipeTabs';
	export default {
		data() {
			return {
				pipeServerUrl: gisConfig().gisPipeDynamicService,
				pipeTabs: usePipeTabs(),
				barHeight: 0,
				geometry: '',
			}
		},
		methods: {
			// 查看详情
			toDetail(item : any) {
				const attributes = encodeURIComponent(JSON.stringify(item))
				const layername = encodeURIComponent(this.pipeTabs.currentTabName)
				const layerid = encodeURIComponent(this.pipeTabs.currentTab)
				uni.navigateTo({
					url: './detailData/index?layerid=' + layerid + '&layername=' + layername + '&attrs=' +
						attributes
				})
			},
			refreshData(data : any[]) {
				this.pipeTabs.refreshData(data)
			}
		},
		onLoad(options : any) {
			if (!options?.geometry || !options?.layerids) return
			this.geometry = options.geometry

			const layerIds = JSON.parse(decodeURIComponent(options.layerids));
			const layerNames = JSON.parse(decodeURIComponent(options.layerNames))
			this.pipeTabs.tabs = layerIds?.map((item : any, i : number) => {
				return {
					name: layerNames[i],
					value: item,
					count: 0
				}
			})
			this.pipeTabs.currentTab = layerIds?.[0]
		},
		onReachBottom() {
			this.pipeTabs.showMore()
		}
	}
</script>
<script lang="renderjs" module="pipeQuery">
	import {
		usePipeQuery
	} from '../../../common/hooks/map'
	const pipeQuery = usePipeQuery()
	export default {
		name: 'pipeQuery',
		data() {
			return {
				pipeUrl: '',
				queryParams: {}
			}
		},
		methods: {
			// 设置管线查询路径
			setPipeUrl(url) {
				this.pipeUrl = url
			},
			init(geometry) {
				const geo = JSON.parse(decodeURIComponent(geometry))
				this.queryParams = {
					geometry: {
						type: 'polygon',
						...(geo || {})
					},
					outFields: ['*']
				}
			},
			async query(newValue, oldValue, ownerVm, vm) {
				let data = []
				try {
					if (newValue === undefined || newValue === '') {} else {
						const res = await pipeQuery.excute(this.pipeUrl + '/' + newValue, this.queryParams)
						data = res.features?.map(item => item.attributes) || []
					}
				} catch (e) {
					//TODO handle the exception
					console.log(e);
				}
				this.$ownerInstance.callMethod('refreshData', data)

			}
		}
	}
</script>
<style lang="scss" scoped>
	.empty {
		display: flex;
		align-items: center;
		height: 200rpx;
		text-align: center;
		justify-content: center;
	}

	.search-box {
		padding: 20rpx 32rpx;
		background-color: #FFFFFF;
	}

	.scroll-wrapper {
		// height: calc(100vh - 200rpx);
	}

	.card-list {
		padding: 0 32rpx;

		.card {
			background-color: #FFFFFF;
			border-radius: 16rpx;
			padding: 22rpx 44rpx;

		}

		.left {
			.info {
				padding-left: 16rpx;

				.name {
					color: #060F27;
					font-size: 28rpx;
					font-weight: bold;
				}

				.time {
					color: #91949F;
					font-size: 20rpx;
				}
			}

			.image {
				background: #5991FF;
				border: 2rpx solid #1564FF;
				width: 56rpx;
				height: 56rpx;
				text-align: center;
				border-radius: 50%;
				line-height: 56rpx;

				.icon {
					font-size: 32rpx;
					color: #FFFFFF;
				}
			}
		}

		.right {
			text {
				&:nth-child(1) {
					color: #3862F8;
					font-weight: bold;
				}

				&:nth-child(2) {
					color: #91949F;
					padding-right: 10rpx;
				}
			}
		}
	}
</style>