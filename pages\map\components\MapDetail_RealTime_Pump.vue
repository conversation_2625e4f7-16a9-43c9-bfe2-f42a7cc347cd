<template>
	<view class="flow-detail">
		<!-- 供水量占比图表区域 -->
		<view class="chart-section">
			<view class="section-title">
				<text class="title-text">{{props.title}}供水量占比</text>
				<view class="title-line"></view>
			</view>
			<view class="chart-container">
				<l-echart ref="refLEchart_Status_Ratio"></l-echart>
			</view>
		</view>

		<!-- 列表区域 -->
		<view class="list-section">
			<view class="section-title">
				<text class="title-text">{{props.title}}列表</text>
				<view class="title-line"></view>
			</view>
			<view class="list-container">
				<view class="table-container">
					<view class="table-header">
						<view class="header-cell" v-for="column in state.listColumns" :key="column.prop">
							{{column.label}}
						</view>
					</view>
					<scroll-view class="table-body" scroll-y="true" :style="{height: '400rpx'}">
						<view 
							v-for="(item, index) in state.list" 
							:key="index"
							class="table-row"
							:class="{ 'row-hover': true }"
						>
							<view class="table-cell">
								<text class="cell-text">{{item.name}}</text>
							</view>
							<view class="table-cell">
								<text class="cell-text value">{{item.todayWaterSupply}} m³</text>
							</view>
							<view class="table-cell">
								<text class="cell-text time">{{item.lastTime}}</text>
							</view>
						</view>
						<view v-if="state.list.length === 0" class="empty-state">
							<text class="empty-icon">📊</text>
							<text class="empty-text">暂无数据</text>
						</view>
					</scroll-view>
				</view>
			</view>
		</view>
	</view>
</template>

<script lang="ts" setup>
	import * as echarts from 'echarts'
	import lEchart from '@/uni_modules/lime-echart/components/l-echart/l-echart.vue'
	import TableList from './TableList.vue'
	import { onMounted, reactive, ref } from 'vue'
	import { getPumpStationList } from '../../../common/api/stations'
	const props = defineProps<{
		title ?: string
	}>()
	const refLEchart_Status_Ratio = ref<InstanceType<typeof lEchart>>()
	const state = reactive<{
		list : any[]
		listColumns : { label : string; prop : string }[]
	}>({
		list: [],
		listColumns: [
			{ label: '名称', prop: 'name' },
			{ label: '今日供水量(m³)', prop: 'todayWaterSupply' },
			{ label: '更新时间', prop: 'lastTime' }
		]
	})
	const refreshStatus = async () => {
		try {
			let total = 0
			state.list.map(item => total += item.todayWaterSupply || 0)
			const data = state.list.map((item) => {
				return { name: item.name, value: item.todayWaterSupply }
			})
			refLEchart_Status_Ratio.value?.init(echarts, (chart : any) => {
				const option = {
					tooltip: {
						trigger: 'item',
						backgroundColor: 'rgba(0, 0, 0, 0.85)',
						borderColor: 'transparent',
						borderRadius: 12,
						textStyle: {
							color: '#fff',
							fontSize: 14,
							fontWeight: '500'
						},
						formatter: '{a} <br/>{b}: {c} m³ ({d}%)',
						extraCssText: 'box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.3);'
					},
					title: {
						text: '{name|合计(万m³)}\n{val|' + (total/10000).toFixed(2) + '}',
						top: 'center',
						left: '30%',
						textAlign: 'center',
						textStyle: {
							rich: {
								name: {
									fontSize: 14,
									fontWeight: 'normal',
									padding: [8, 0],
									align: 'center',
									color: '#666',
									width: 100
								},
								val: {
									fontSize: 24,
									fontWeight: 'bold',
									color: '#0073ff',
									textShadow: '0 2rpx 4rpx rgba(0, 115, 255, 0.3)',
									align: 'center',
									width: 100
								}
							}
						}
					},
					color: ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57', '#ff9ff3'],
					legend: {
						type: 'scroll',
						icon: 'circle',
						orient: 'vertical',
						left: 'right',
						top: 'center',
						align: 'left',
						itemGap: 20,
						itemWidth: 16,
						itemHeight: 16,
						symbolKeepAspect: true,
						textStyle: {
							color: '#333',
							fontSize: 14,
							fontWeight: '500',
							rich: {
								name: {
									align: 'left',
									width: 100,
									fontSize: 14,
									color: '#333',
									fontWeight: '500'
								},
								value: {
									align: 'left',
									width: 80,
									fontSize: 14,
									color: '#0073ff',
									fontWeight: 'bold'
								}
							}
						},
						data: data.map(item => item.name),
						formatter(name : any) {
							if (data && data.length) {
								for (let i = 0; i < data.length; i++) {
									if (name === data[i].name) {
										return '{name|' + (data[i].name || name) + '}\n{value|' + data[i].value + 'm³}'
									}
								}
							}
						}
					},
					series: [
						{
							name: props.title,
							type: 'pie',
							radius: ['40%', '70%'],
							center: ['30%', '50%'],
							data: data,
							itemStyle: {
								borderRadius: 8,
								borderColor: '#fff',
								borderWidth: 3,
								shadowBlur: 15,
								shadowOffsetX: 0,
								shadowOffsetY: 4,
								shadowColor: 'rgba(0, 0, 0, 0.15)'
							},
							label: {
								show: true,
								position: 'inside',
								textStyle: {
									fontSize: 14,
									color: '#fff',
									fontWeight: 'bold',
									textShadow: '0 1rpx 2rpx rgba(0, 0, 0, 0.3)'
								},
								formatter(param : any) {
									return (param.percent||0) + '%';
								}
							},
							labelLine: {
								show: false
							},
							emphasis: {
								itemStyle: {
									shadowBlur: 25,
									shadowOffsetX: 0,
									shadowOffsetY: 8,
									shadowColor: 'rgba(0, 0, 0, 0.25)',
									borderWidth: 4
								},
								label: {
									fontSize: 16,
									fontWeight: 'bold'
								}
							},
							animationType: 'scale',
							animationEasing: 'elasticOut',
							animationDelay: function (idx: any) {
								return Math.random() * 200;
							}
						}
					]
				}
				chart.setOption(option)
			})

		} catch (e) {
			console.log(e)
		}
	}

	const refreshTable = async () => {
		try {
			const res = await getPumpStationList({name: ''})

			if (res.data.code === 200) {
				state.list = res.data?.data?.map(item=>{
					return {
						...item,
						lastTime: item.lastTime?.split(' ')?.[1]
					}
				}) || []
			} else {
				console.log('统计失败');
			}
		} catch (e) {
			console.log(e)
		}
	}
	const refreshData = async () => {
		await refreshTable()
		refreshStatus()
	}
	onMounted(() => {

		refreshData()
	})
</script>

<style lang="scss" scoped>
	.flow-detail {
		// background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
		min-height: 100vh;
		padding: 12rpx;

		.section-title {
			display: flex;
			align-items: center;
			margin-bottom: 24rpx;
			
			.title-text {
				font-size: 32rpx;
				font-weight: bold;
				color: #333;
				margin-right: 16rpx;
			}
			
			.title-line {
				flex: 1;
				height: 2rpx;
				background: linear-gradient(90deg, #0073ff 0%, transparent 100%);
				border-radius: 1rpx;
			}
		}

		.chart-section {
			margin-bottom: 32rpx;
			
			.chart-container {
				background: #fff;
				border-radius: 16rpx;
				padding: 24rpx;
				box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
				height: 520rpx;
			}
		}

		.list-section {
			.list-container {
				background: #fff;
				border-radius: 16rpx;
				padding: 24rpx;
				box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
				display: flex;
				flex-direction: column;
				height: 520rpx;
			}

			.table-container {
				flex: 1;
				display: flex;
				flex-direction: column;
				border-radius: 12rpx;
				overflow: hidden;
				background: #fafafa;
			}

			.table-header {
				display: flex;
				background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
				border-bottom: 2rpx solid #dee2e6;
				padding: 20rpx 0;

				.header-cell {
					flex: 1;
					text-align: center;
					font-size: 28rpx;
					font-weight: bold;
					color: #495057;
					position: relative;

					&:not(:last-child)::after {
						content: '';
						position: absolute;
						right: 0;
						top: 50%;
						transform: translateY(-50%);
						width: 1rpx;
						height: 60%;
						background: #dee2e6;
					}
				}
			}

			.table-body {
				flex: 1;
				background: #fff;

				.table-row {
					display: flex;
					padding: 20rpx 0;
					border-bottom: 1rpx solid #f1f3f4;
					transition: all 0.3s ease;

					&:hover {
						background: #f8f9fa;
						transform: translateX(4rpx);
					}

					&:last-child {
						border-bottom: none;
					}

					.table-cell {
						flex: 1;
						display: flex;
						align-items: center;
						justify-content: center;
						padding: 0 16rpx;

						.cell-text {
							font-size: 26rpx;
							color: #333;
							text-align: center;
							overflow: hidden;
							text-overflow: ellipsis;
							white-space: nowrap;
							max-width: 100%;

							&.value {
								color: #0073ff;
								font-weight: bold;
							}

							&.time {
								color: #666;
								font-size: 24rpx;
							}
						}
					}
				}

				.empty-state {
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;
					height: 300rpx;
					color: #999;

					.empty-icon {
						font-size: 80rpx;
						margin-bottom: 16rpx;
					}

					.empty-text {
						font-size: 28rpx;
						color: #999;
					}
				}
			}
		}
	}

	// 响应式设计
	@media (max-width: 750rpx) {
		.flow-detail {
			padding: 16rpx;
		}
	}
</style>