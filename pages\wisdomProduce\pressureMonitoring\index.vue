<template>
	<view class="main">
		<u-sticky bgColor="#FFFFFF">
			<view class="search-box flex-between">
				<view style="width: 90%;">
					<u-search shape="square" :showAction="false" v-model="state.keyword" placeholder="请输入监测点名称"
						@change="searchKeyWord">
					</u-search>
				</view>
				<view @click="showArea">
					<image src="/static/img/icons/shaixuan-black.png" style="height: 48rpx;width: 48rpx;"></image>
				</view>
			</view>
		</u-sticky>
		<view class="card-list">
			<scroll-view scroll-y="false" scroll-with-animation="true" v-if="pressureList.length>0">
				<view class="card flex-between" v-for="(pressure,index) in pressureList" :key="index"
					@click="toDetail(pressure)">
					<view class="left flex-center">
						<view class="image">
							<image src="/static/img/icons/Group.png" style="height: 48rpx;width: 48rpx;"></image>
						</view>
						<view class="info">
							<view class="name">
								{{pressure.name}}
							</view>
							<view class="time">
								{{pressure.time}}
							</view>
						</view>
					</view>
					<view class="right flex-center">
						<text :style="{color:pressure.pressure_status?'#ff0000':''}">{{pressure.pressure?pressure.pressure:'-'}}</text><text>{{pressure.pressure_unit}}</text>
						<u-icon name="arrow-right" color="#91949F" size="28"></u-icon>
					</view>
				</view>
			</scroll-view>
			<view class="uempty" v-else>
				<u-empty></u-empty>
			</view>
		</view>
		
			<u-popup mode="right" v-model="state.showPopup" :closeOnClickOverlay="true" :overlay="true" @close="close">
				<view class="popup">
				<view class="area-list">
					<view class="area" v-for="(item,index) in areaGroup" :key="index">
						<view class="title">
							{{item.name}}
						</view>
						<view class="flex-between box-list">
							<view @click="state.currentProjectId = area.id" v-for="(area,i) in item.children" :key="i"
								:class="['box',{'check-box':state.currentProjectId===area.id,'max-box':area.name.length>5}]">
								{{area.name}}
							</view>
						</view>
					</view>
				</view>
				<view class="popupButton">
					<u-button type="primary" color="#3862F8" @click="submit">确定</u-button>
				</view>
				</view>
			</u-popup>
	</view>
</template>

<script lang="ts" setup>
	import {
		onMounted,
		reactive,
		ref
	} from "vue"
	import {
		getStationDynamicRealtimeData,
		getProjectRoot
	} from '@/common/api/monitoring'
	import {
		onPullDownRefresh
	} from '@dcloudio/uni-app'
	const state = reactive < {
		keyword: string,
		showPopup: boolean,
		checkAreaId: string,
		currentProjectId: string,
		pressureList: any,
	} > ({
		keyword: '',
		showPopup: false,
		checkAreaId: '11',
		currentProjectId: '',
		pressureList: []
	})
	let pressureList = ref([])
	let areaGroup = ref([])

	// 选择区域
	const showArea = () => {
		state.showPopup = true
	}
	// 关闭弹框
	const close = () => {
		state.showPopup = false
	}
	// 查看压力详情
	const toDetail = (item: any) => {
		uni.$u.route({
			url: 'pages/wisdomProduce/pressureMonitoring/dataDetail/index',
			params: {
				...item
			},
			animationType: true
		})
	}
	// 选择区域
	const submit = () => {
		// 查询列表
		refreshData()
		close()
	}

	// 查询
	const searchKeyWord = (val: string) => {
		console.log(val)
		if (val) {
			pressureList.value = state.pressureList.filter(pressure => pressure.name.includes(val))
		} else {
			pressureList.value = state.pressureList
		}
	}
	
	onPullDownRefresh(async()=>{
		refreshData()
	})
	// 刷新数据
	const refreshData = async () => {
		//接口获取区域压力监测列表
		if(state.currentProjectId){
			const params = {
				stationType: '压力监测站,测流压站',
				projectId: state.currentProjectId
			}
			const res = await getStationDynamicRealtimeData(params)
			pressureList.value = res.data
			state.pressureList = res.data
		}
		uni.stopPullDownRefresh()
	}

	onMounted(async () => {
		//获取区域
		const res = await getProjectRoot()
		const data = res.data? res.data : []
		areaGroup.value = data
		state.currentProjectId = data[0]?.id
		refreshData()
	})
</script>

<style lang="scss" scoped>
	.search-box {
		padding: 20rpx 32rpx;
		background-color: #FFFFFF;
	}

	.card-list {
		padding: 0 32rpx;

		.card {
			background-color: #FFFFFF;
			border-radius: 16rpx;
			padding: 22rpx 44rpx;
		}

		.left {
			.info {
				padding-left: 16rpx;

				.name {
					color: #060F27;
					font-weight: bold;
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
				}

				.time {
					color: #91949F;
					font-size: 20rpx;
				}
			}
		}

		.right {
			text {
				&:nth-child(1) {
					color: #3862F8;
					font-weight: bold;
				}

				&:nth-child(2) {
					color: #91949F;
					padding-right: 10rpx;
				}
			}
		}
	}

	.popup {
		width: 100%;
		height: 100%;
		position: relative;

		.area-list {
			width: 528rpx;

			.area {
				padding: 20rpx 24rpx;

				.title {
					font-weight: bold;
				}

				.box-list {
					flex-wrap: wrap;
					height: 90%;

					.box {
						min-width: 228rpx;
						height: 64rpx;
						background: #FFFFFF;
						border: 2rpx solid #EBEDF6;
						border-radius: 8rpx;
						margin: 20rpx 0rpx;
						text-align: center;
						line-height: 64rpx;
					}

					.max-box {
						width: 480rpx;
					}

					.check-box {
						border: 2rpx solid #3862F8;
						color: #3862F8;
					}
				}

			}
		}

		.popupButton {
			width: 94%;
			position: absolute;
			bottom: 40rpx;
			left: 0;
			right: 0;
			margin: 0 auto;
		}
	}
</style>
