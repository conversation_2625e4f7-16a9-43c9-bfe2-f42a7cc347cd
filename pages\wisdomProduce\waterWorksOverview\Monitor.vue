<template>
  <view>
    <view class="station-select" @click="state.stationShow = true">
      <text>{{ selectedStationName }}</text>
      <u-icon name="arrow-down" color="#91949F" size="28"></u-icon>
    </view>
    <view class="chart-view">
      <view class="chart-content">
        <l-echart ref="lineChart"></l-echart>
      </view>
    </view>

    <u-picker v-model="state.stationShow" mode="selector" :default-selector="[0]" :range="stationOptions" range-key="name"
			@confirm="selectStation"></u-picker>
  </view>
</template>
<script lang="ts" setup>
	import { onMounted, reactive, ref } from "vue"
  import { getStationList } from '@/common/api/waterplant'

  const selectedStationId = ref<string>('')
	const selectedStationName = ref<string>('请选择水厂')
  const stationOptions = ref<any[]>([]) // 水厂选择列表
	const lineChart = ref<any>({})
	const state = reactive<{
		screenShow : boolean,
		typeShow : boolean,
		stationList : string[],
		dateShow : boolean,
		stationShow: boolean // 控制水厂选择器显示隐藏
	}>({
		screenShow: false,
		typeShow: false,
		stationList: [],
		dateShow: false,
		stationShow: false
	})

  	// 选择水厂
	const selectStation = (index: any) => {
		selectedStationId.value = stationOptions.value[index].id
		selectedStationName.value = stationOptions.value[index].name
		state.stationShow = false
		// updateChartData()
	}

  const getStationOps = async () => {
    const params = {
			page: 1,
			size: 999,
			type: '水厂'
		}
		const res = await getStationList(params)
		console.log(res.data?.data)
		state.stationList = res.data?.data.map((d : any) => {
			return d.id
		})
		stationOptions.value = res.data?.data.map((d: any) => ({
			id: d.id,
			name: d.name
		}))
		if (stationOptions.value.length > 0) {
			selectedStationId.value = stationOptions.value[0].id
			selectedStationName.value = stationOptions.value[0].name
		}
  }
</script>
<style lang="scss" scoped>
.station-select {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0;
  font-size: 28rpx;
  color: #333;
  border-bottom: 1px solid #eee;
}
.chart-content {
  width: 100%;
  height: 35vh;
}
</style>