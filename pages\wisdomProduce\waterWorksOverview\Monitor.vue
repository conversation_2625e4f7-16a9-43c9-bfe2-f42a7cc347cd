<template>
  <view class="monitor-container">
    <!-- 水厂选择器 -->
    <view class="station-select" @click="state.stationShow = true">
      <text>{{ selectedStationName }}</text>
      <u-icon name="arrow-down" color="#91949F" size="28"></u-icon>
    </view>

    <!-- 工艺监控 -->
    <view class="process-monitor">
      <view class="process-title">工艺监控</view>
      <view class="monitor-content">
        <view class="process-image-container" v-if="processImageUrl">
          <image
            :src="processImageUrl"
            mode="aspectFill"
            class="process-image"
            @click="previewProcessImage"
          />
          <view class="image-title">污水处理厂工艺</view>
        </view>
        <view v-else class="no-image">
          <text>暂无工艺流程图</text>
        </view>
        <view class="process-image-container" v-if="processImageUrl">
          <image
            :src="deviceImageUrl"
            mode="aspectFill"
            class="process-image"
            @click="previewDeviceImage"
          />
          <view class="image-title">设备图片</view>
        </view>
        <view v-else class="no-image">
          <text>暂无设备图片</text>
        </view>
      </view>
    </view>

    <!-- 趋势分析 -->
    <view class="trend-analysis">
      <trend-chart
        :station-id="selectedStationId"
        title="趋势分析"
      />
    </view>

    <!-- 历史数据 -->
    <view class="history-data">
      <history-chart
        :station-id="selectedStationId"
        title="历史数据"
      />
    </view>

    <!-- 水厂选择器弹窗 -->
    <u-picker
      v-model="state.stationShow"
      mode="selector"
      :default-selector="[0]"
      :range="stationOptions"
      range-key="name"
      @confirm="selectStation"
    ></u-picker>
  </view>
</template>
<script lang="ts" setup>
	import { onMounted, reactive, ref } from "vue"
  import { getStationList } from '@/common/api/waterplant'
  import TrendChart from './components/TrendChart.vue'
  import HistoryChart from './components/HistoryChart.vue'

  const selectedStationId = ref<string>('')
	const selectedStationName = ref<string>('请选择水厂')
  const stationOptions = ref<any[]>([]) // 水厂选择列表
	const processImageUrl = ref<string>('') // 工艺流程图URL
  const deviceImageUrl = ref<string>('') // 设备图片URL

	const state = reactive<{
		screenShow : boolean,
		typeShow : boolean,
		stationList : string[],
		dateShow : boolean,
		stationShow: boolean, // 控制水厂选择器显示隐藏
		currentTab: number // 当前选中的tab
	}>({
		screenShow: false,
		typeShow: false,
		stationList: [],
		dateShow: false,
		stationShow: false,
		currentTab: 0
	})

  	// 选择水厂
	const selectStation = (index: any) => {
		selectedStationId.value = stationOptions.value[index].id
		selectedStationName.value = stationOptions.value[index].name
		state.stationShow = false
		// 切换水厂后，重新加载工艺流程图
		loadProcessImage()
	}

	// 预览工艺流程图
	const previewProcessImage = () => {
		if (processImageUrl.value) {
			uni.previewImage({
				urls: [processImageUrl.value],
				current: 0
			})
		}
	}

  // 预览设备图片
  const previewDeviceImage = () => {
    if (deviceImageUrl.value) {
      uni.previewImage({
        urls: [deviceImageUrl.value],
        current: 0
      })
    }
  }

	// 加载工艺流程图
	const loadProcessImage = () => {
		// 这里可以根据selectedStationId加载对应的工艺流程图
		// 暂时使用示例图片
		if (selectedStationId.value) {
			deviceImageUrl.value = '/static/img/temporary/Rectangle.png' // 示例图片路径
      processImageUrl.value = '/static/water-factory/flow.png' // 示例图片路径
		}
	}

  const getStationOps = async () => {
    const params = {
			page: 1,
			size: 999,
			type: '水厂'
		}
		const res: any = await getStationList(params)

		state.stationList = res.data?.data.map((d : any) => {
			return d.id
		})
		stationOptions.value = res.data?.data.map((d: any) => ({
			id: d.id,
			name: d.name
		}))
		if (stationOptions.value.length > 0) {
			selectedStationId.value = stationOptions.value[0].id
			selectedStationName.value = stationOptions.value[0].name
			// 加载默认水厂的工艺流程图
			loadProcessImage()
		}
  }

	// 组件挂载时初始化
	onMounted(async () => {
		await getStationOps()
	})
</script>
<style lang="scss" scoped>
.monitor-container {
  padding: 20rpx;
  background-color: #f8f9fa;
  min-height: 100vh;
}

.station-select {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
}

.process-monitor {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 20rpx ;
}

.process-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  text-align: left;
}

.monitor-content {
  display: flex;
  justify-content: space-between;
}

.process-image-container {
  position: relative;
  width: 48%;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 12rpx;
  overflow: hidden;
}

.process-image {
  width: 100%;
  max-width: 600rpx;
  height: 200rpx;
}

.image-title {
  position: absolute;
  bottom: 0;
  left: 0;
  padding: 4rpx 10rpx;
  width: 100%;
  color: #fff;
  background-color: rgba(0, 0, 0, 0.5);
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  font-size: 24rpx;
}

.no-image {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 48%;
  height: 200rpx;
  color: #999;
  font-size: 28rpx;
  background-color: #f5f5f5;
  border-radius: 12rpx;
}

.trend-analysis,
.history-data {
  margin-top: 20rpx;
  background-color: #fff;
  border-radius: 16rpx;
  // padding: 20rpx;
}
</style>