export interface applictionInc {
	title: string,
	muens: {
		icon: string,
		title: string,
		url: string
	}[]
}
export const applictions = () => {
	return [{
		icon: '/static/img/menus/shuichangzonglan.png',
		title: '水厂总览',
		url: 'pages/wisdomProduce/waterWorksOverview/index'
	},
	{
		icon: '/static/img/menus/guanwangyali.png',
		title: '管网压力',
		url: 'pages/wisdomProduce/pressureMonitoring/index'
	},
	{
		icon: '/static/img/menus/shuichangzonglan.png',
		title: '养护任务',
		url: 'pages/pipeNetwork/maintenanceTasks/index'

	},
	{
		icon: '/static/img/menus/yichangguanli.png',
		title: '异常管理',
		url: 'pages/wisdomProduce/exceptionalManageV2/index'
	},
	{
		icon: '/static/img/menus/yichangguanli.png',
		title: '异常管理',
		url: 'pages/wisdomProduce/exceptionalManageV3/index'
	},
	{
		icon: '/static/img/menus/dituliulan.png',
		title: '地图浏览',
		url: 'pages/pipeNetwork/mapBrowsing/index_leaflet'
	},
	{
		icon: '/static/img/menus/chaxuntongji.png',
		title: '查询统计',
		url: 'pages/revenueManage/queryStatistics/index'
	},
	{
		icon: '/static/img/menus/gongdanfaqi.png',
		title: '工单发起',
		url: 'pages/smartOperation/workOrderInitiation/index'
	},
	{
		icon: '/static/img/menus/gongdanfenpai.png',
		title: '工单分派',
		url: 'pages/smartOperation/workOrderAssignment/index'
	},
	{
		icon: '/static/img/menus/gongdanchuli.png',
		title: '工单处理',
		url: 'pages/smartOperation/workOrderTask/index'
	},
	{
		icon: '/static/img/menus/gongdanshenhe.png',
		title: '工单审核',
		url: 'pages/smartOperation/workOrderApproval/index'
	},
	{
		icon: '/static/img/menus/tuidanshenhe.png',
		title: '退单审核',
		url: 'pages/smartOperation/chargebackReviewed/index'
	},
	{
		icon: '/static/img/menus/gongdanchaxun.png',
		title: '工单查询',
		url: 'pages/smartOperation/workOrderQuery/index'
	},
	{
		icon: '/static/img/menus/gongdantongji.png',
		title: '工单统计',
		url: 'pages/smartOperation/workOrderStatistics/index'
	},
	{
		icon: '/static/img/menus/gongdanchaxun.png',
		title: '资产保养',
		url: 'pages/smartOperation/assetMaintainTask/index'
	},
	{
		icon: '/static/img/menus/gongdanchaxun.png',
		title: '资产巡检',
		url: 'pages/smartOperation/assetCircuitInspectionTask/index'
	},
	{
		icon: '/static/img/menus/gongdantongji.png',
		title: '设备资产',
		url: 'pages/smartOperation/rquipmentAssets/index'
	},
	{
		icon: '/static/img/menus/chaobiaopaixu.png',
		title: '抄表排序',
		url: 'pages/revenueManage/userSort/index'
	},
	{
		icon: '/static/img/menus/shangchuanxiazai.png',
		title: '上传下载',
		url: 'pages/revenueManage/upAndDownload/index'
	},
	{
		icon: '/static/img/menus/chaobiaorenwu.png',
		title: '抄表任务',
		url: 'pages/revenueManage/meterReadingTask/index'
	}]
}
