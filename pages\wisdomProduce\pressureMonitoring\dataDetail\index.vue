<template>
	<view class="main">
		<u-navbar :borderBottom="false" :background="{backgroundColor: '#151A2C'}" title="压力监测" titleColor="#FFFFFF"
			backIconColor="#FFFFFF">
		</u-navbar>
		<view class="top">
			<view class="flex-center title">
				<image src="/static/img/icons/kongxishuiyaliji.png" style="height:36rpx;width:36rpx"></image>
				<text>{{state.currentProperty.name}}</text>
			</view>
			<view class="ucharts">
				<view>
					<l-echart ref="gaugeChart"></l-echart>
				</view>
			</view>
		</view>
		<view class="detail">
			<view class="flex-between" style="margin-bottom: 20rpx;">
				<view class="title-bold">
					压力曲线
				</view>
				<view class="tabs flex-between">
					<view v-for="(tab,i) in state.tabs" :key="i"
						:class="['tab',{'check-tab':state.activeTab.value===tab.value}]" @click="checkActiveTab(tab)">
						{{tab.title}}
					</view>

				</view>
			</view>
			<view class="bottom">
				<view class="y-title flex-between">
					<view>
						单位：{{state.currentProperty.unit}}
					</view>
					<view class="flex-center">
						<view class="flex-center" style="padding: 0 8rpx;" v-for="(tab,i) in state.tabList" :key="i">
							<view class="color-view" :style="{'background-color':tab.color}"></view>
							<view style="color:#B2B7C7;">{{tab.title}}</view>
						</view>
					</view>
				</view>
				<view class="line-echarts">
					<view>
						<l-echart ref="lineChart"></l-echart>
					</view>
				</view>
			</view>
		</view>
		<view class="buttons flex-between">
			<u-button class="custom-style-left" @click="showHistoryData()">查看详情</u-button>
			<u-button class="custom-style-right" @click="showAlarmData()">报警详情</u-button>
		</view>
		<u-popup overlay mode="bottom" closeable v-model="showStatus" safeAreaInsetBottom border-radius="16"
			@close="close">
			<view v-if="state.popupType === 'data'" class="popup">
				<data-list :propertyList="propertyList" :currentProperty="state.currentProperty" title="查看详情"
					:headers="headers" :tableData="tableData"></data-list>
			</view>
			<view v-if="state.popupType === 'alarm'" class="popup">
				<alarm-list :stationId="state.currentProperty.stationId"></alarm-list>
			</view>
		</u-popup>
	</view>
</template>

<script lang="ts" setup>
	import {
		onMounted,
		reactive,
		ref
	} from "vue";
	import lEchart from '@/uni_modules/lime-echart/components/l-echart/l-echart.vue'
	import * as echarts from 'echarts'
	import dataList from '@/components/dataList/dataList.vue'
	import alarmList from '@/components/alarmList.vue'
	import {
		gaugeOption,
		lineOption
	} from '@/common/data/echartsData'
	import {
		tabs,
	} from '@/common/data/publicData'
	import {
		getThreeDaysData
	} from '@/common/api/monitoring'
	const showStatus = ref < boolean > (false)
	const state = reactive < {
		activeTab: any,
		tabList: any,
		tabs: any,
		popupType: string,
		currentProperty: any,
		today: any,
		yesterday: any,
		beforeYesterday: any,
		date: any,
	} > ({
		currentProperty: {},
		tabList: [tabs[1]],
		today: [],
		yesterday: [],
		beforeYesterday: [],
		date: [],
		activeTab: tabs[1],
		tabs: tabs,
		popupType: 'data'
	})
	const propertyList = ref < any > ([])
	const gaugeChart = ref < any > ({})
	const lineChart = ref < any > ({})
	const headers = ref < any > ([])
	const tableData = ref < any > ([])
	const initGauge = (val: number) => {
		gaugeChart.value.init(echarts, (chart: any) => {
			const options = gaugeOption(val)
			chart.setOption(options);
		});
	}

	const initLine = (dataX: string[], data: any, color ? : string) => {
		lineChart.value.init(echarts, (chart: any) => {
			const options = lineOption(dataX, data, color)
			chart.setOption(options);
		});
	}


	// 切换时间查询曲线
	const checkActiveTab = (tab: any) => {
		state.activeTab = tab
		if (state.activeTab.value === 'today') {
			initLine(state.date, {
				'今天': state.today
			}, state.activeTab.color)
			state.tabList = state.tabs.slice(1, 2)
		} else if (state.activeTab.value === 'yesterday') {
			initLine(state.date, {
				'昨天': state.yesterday
			}, state.activeTab.color)
			state.tabList = state.tabs.slice(2, 3)
		} else if (state.activeTab.value === 'beforeYesterday') {
			initLine(state.date, {
				'前天': state.beforeYesterday
			}, state.activeTab.color)
			state.tabList = state.tabs.slice(3, 4)
		} else {
			initLine(state.date, {
				'今天': state.today,
				'昨天': state.yesterday,
				'前天': state.beforeYesterday
			})
			state.tabList = state.tabs.slice(1, 4)
		}
	}

	// 显示报警详情
	const showAlarmData = async () => {
		state.popupType = 'alarm'
		showStatus.value = true
	}

	// 显示详情数据
	const showHistoryData = async () => {
		state.popupType = 'data'
		showStatus.value = true
	}
	// 关闭popup
	const close = () => {
		showStatus.value = false
	}

	// 显示详情数据
	// const showHistoryData = async (params ? : any) => {
	// 	params = params || {}
	// 	params = {
	// 		start: dayjs(params.start).startOf('day').valueOf(),
	// 		end: dayjs(params.end).endOf('day').valueOf(),
	// 		type: params.type || '15m',
	// 		attributes: [state.currentProperty.pressure_deviceId + '.pressure'],
	// 	}
	// 	const res = await getDeviceData(params)
	// 	const data = res.data
	// 	let newData = []
	// 	for (let key in data) {
	// 		newData.push({
	// 			'date': key,
	// 			'val': data[key][state.currentProperty.pressure_deviceId + '.pressure']
	// 		})
	// 	}
	// 	tableData.value = newData
	// 	state.popupType = 'data'
	// 	showStatus.value = true
	// }

	//查询历史数据
	// const onQuery = (query: any) => {
	// 	showHistoryData(query)
	// }
	//获取压力最近三天的数据 
	const getData = async () => {
		const params = {
			attr: 'pressure',
			deviceId: state.currentProperty.pressure_deviceId
		}
		const res = await getThreeDaysData(params)
		const data = res.data?.data
		state.date = data.todayDataList.map((data: any) => {
			return data.ts
		})
		state.today = data.todayDataList.map((data: any) => {
			return data.value
		})
		state.yesterday = data.yesterdayDataList.map((data: any) => {
			return data.value
		})
		state.beforeYesterday = data.beforeYesterdayDataList.map((data: any) => {
			return data.value
		})
		initLine(state.date, {
			'今天': state.today
		})
	}

	onMounted(async () => {
		var pages = getCurrentPages();
		var page = pages[pages.length - 1];
		state.currentProperty = {
			...page.$page.options,
			deviceId: page.$page.options.pressure_deviceId,
			property: 'pressure',
			propertyName: '压力',
			unit: 'Mpa'
		}
		propertyList.value = [state.currentProperty]
		initGauge(state.currentProperty?.pressure || 0)
		await getData()

	})
</script>

<style lang="scss" scoped>
	.top {
		width: 100%;
		height: 560rpx;
		background: #151A2C;
		font-size: 28rpx;
		color: #FFFFFF;

		.title {
			padding: 22rpx 32rpx;

			text {
				padding-left: 12rpx;
			}
		}

		.ucharts {
			width: 542rpx;
			height: 414rpx;
			margin: 0 auto;
		}
	}

	.detail {
		position: relative;
		top: -40rpx;
		background: #FBFBFB;
		border-radius: 32rpx 32rpx 0px 0px;
		padding: 24rpx 32rpx;

		.tabs {
			width: 60%;
			height: 46rpx;
			line-height: 46rpx;
			text-align: center;

			.tab {
				width: 88rpx;
				color: #91949F;
			}

			.check-tab {
				width: 88rpx;
				border-radius: 8rpx;
				background-color: #3862F8;
				color: #FFFFFF;
			}
		}

		.bottom {
			background-color: #FFFFFF;
			border-radius: 16rpx;
			padding-bottom: 98rpx;

			.y-title {
				color: #B2B7C7;
				padding: 20rpx 40rpx;

				.color-view {
					width: 20rpx;
					height: 20rpx;
					background-color: #3862F8;
					margin-right: 8rpx;
				}
			}

			.line-echarts {
				height: 600rpx;
				margin: 10rpx auto;
			}
		}
	}

	.buttons {
		width: 100%;
		height: 128rpx;
		background: #FFFFFF;
		box-shadow: 0px 4rpx 30rpx rgba(178, 183, 199, 0.5);
		position: fixed;
		bottom: 0;

		.custom-style-left {
			background-color: #3862F8;
			color: #FFFFFF;
			width: 45%;
		}

		.custom-style-right {
			background-color: #F88938;
			color: #FFFFFF;
			width: 45%;
		}
	}

	.popup {
		height: 85vh;
	}
</style>
