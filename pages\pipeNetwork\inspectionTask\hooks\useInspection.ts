import { computed, onBeforeUnmount, reactive, ref, toRefs } from 'vue'
import { GetDistrictPointsJson, getInspectionDevices, getInspectionLocus, GetKeyPoint, postInspectionCoordinates } from '../../../../common/api/inspection'
// import { useAddress } from './useAddress'

export const useInspection = (options ?: { getPosition : () => Promise<{ longitude : number, latitude : number }> }) => {
	const taskCode = ref<string>('')
	const areaId = ref<string>('')
	// 区域信息
	const districtData = ref<string | undefined>()
	const getDistrictData = async () => {
		if (!areaId.value) districtData.value = undefined
		else {
			const res = await GetDistrictPointsJson(areaId.value)
			districtData.value = res.data?.data
		}
	}
	// 全部关键点信息
	const allKeyPoints = ref<any[]>([])
	const getAllKeyPoints = async () => {
		if (!areaId.value) allKeyPoints.value = []
		else {
			const res = await GetKeyPoint({ areaId: areaId.value })
			allKeyPoints.value = res.data?.data?.data || []
		}
	}
	// 全部关键点和管网设备信息
	const allDevices = ref<any[]>([])
	const getAllDevices = async () => {
		isOk.value = false
		if (!taskCode.value) allDevices.value = []
		else {
			const res = await getInspectionDevices({
				taskCode: taskCode.value,
			})
			allDevices.value = res.data.data?.data || []
		}
		keyPoints.value = allDevices.value.filter(item => item.type === '关键点').map(item => {
			const keyPoint = allKeyPoints.value.find(o => o.id === item.deviceType)
			if (keyPoint) {
				item.lon = keyPoint.lon
				item.lat = keyPoint.lat
			}
			return item
		})
		devices.value = allDevices.value.filter(item => item.type === '设备')
		spetialDevices.value = allDevices.value.filter(item => item.type === '专项设备')
		setTimeout(() => {
			isOk.value = true
		}, 0)
	}
	// 刷新区域和点数据
	const refreshData = () => {
		viewHistoryLine()
		getDistrictData()
		// 先查所有的关键点，再更新关键点数据
		getAllKeyPoints().then(() => {
			getAllDevices()
		})

	}
	/**
	 * 关键点数据
	 */
	const keyPoints = ref<any[]>([])
	const keyPointsSettledCount = computed(() => {
		return keyPoints.value.filter(item => item.isSettle === true).length
	})
	/**
	 * 任务的设备数据,
	 */
	const devices = ref<any[]>([])
	const devicesSettledCount = computed(() => {
		return devices.value.filter(item => item.isSettle === true).length
	})
	/**
	 * 任务的专项设备
	 */
	const spetialDevices = ref<any[]>([])
	const spatialDevicesSettledCount = computed(() => spetialDevices.value.filter(item => item.isSettle === true).length)
	// 标记数据是否加载完
	const isOk = ref<boolean>(false)
	//刷新定位
	const refreshNum =  ref<any>(0)
	const lonLat = computed(() => {
		return inspectInfo.refreshNum
	})
	// 是否开始巡检
	const inspectInfo = reactive<{
		isStart: boolean
		timer: any
		startTime: number
		endTime: number
		hours: number
		minites: number
		historyPoints: any[]
		status: 'APPROVED' | ''
		refreshNum: any
	}>({
		isStart: false,
		timer: undefined,
		startTime: new Date().valueOf(),
		endTime: new Date().valueOf(),
		hours: 0,
		minites: 0,
		historyPoints: [],
		status: '',
		refreshNum: 'handle'+new Date().valueOf()
	})
	const _calcHourAndMinites = () => {
		inspectInfo.endTime = new Date().valueOf()
		const delt = (inspectInfo.endTime - inspectInfo.startTime) / 60000
		inspectInfo.hours = Math.floor(delt / 60)
		inspectInfo.minites = Math.ceil(delt % 60)
	}
	// const Address = useAddress()
	// 开始巡检
	const startInspect = async () => {
		if (inspectInfo.historyPoints.length) {
			inspectInfo.startTime = new Date(inspectInfo.historyPoints[0].createTime).valueOf()
		} else {
			inspectInfo.startTime = new Date().valueOf()
		}
		clearInterval(inspectInfo.timer)
		inspectInfo.timer = setInterval(() => {
			// console.log('开始定位');
			if (inspectInfo.isStart) {
				addHistoryPoint()
				_calcHourAndMinites()
				inspectInfo.refreshNum = 'auto'+new Date().valueOf()
			}
		}, 10000)// 每10秒更新一次时长信息
		addHistoryPoint()
		_calcHourAndMinites()
		inspectInfo.isStart = true
		inspectInfo.refreshNum = 'auto'+new Date().valueOf()
	}
	// 结束巡检
	const endInspect = () => {
		_calcHourAndMinites()
		inspectInfo.timer && clearInterval(inspectInfo.timer)
		inspectInfo.isStart = false
	}
	const toggleInspect = (readonly ?: boolean) => {
		if (readonly) return
		if (!inspectInfo.isStart) {
			startInspect()
		} else {
			endInspect()
		}
	}
	const viewHistoryLine = async () => {
		// console.log(taskCode.value)
		const res = await getInspectionLocus({
			taskCode: taskCode.value
		})
		inspectInfo.historyPoints = res.data.data?.data || []
		// console.log(inspectInfo.historyPoints)
	}
	const addHistoryPoint = () => {
		// Address.locaton(async () => {
		// 	try{
		// 		const res = await postInspectionCoordinates({
		// 			taskCode: taskCode.value,
		// 			x: Address.longitude.value,
		// 			y: Address.latitude.value
		// 		})
		// 		if (res.data?.data) {
		// 			inspectInfo.historyPoints = [...inspectInfo.historyPoints, res.data.data]
		// 		}
		// 	}catch(e){
		// 		//TODO handle the exception
		// 		console.log('添加定位失败',e.message);
		// 		// uni.showToast({
		// 		// 	title:'获取位置信息失败',
		// 		// 	icon:'fail'
		// 		// })
		// 	}
		// })

	}
	onBeforeUnmount(() => {
		endInspect()
	})
	return {
		districtData,
		getDistrictData,
		allKeyPoints,
		getAllKeyPoints,
		allDevices,
		getAllDevices,
		keyPoints,
		keyPointsSettledCount,
		devices,
		devicesSettledCount,
		spetialDevices,
		spatialDevicesSettledCount,
		isOk,
		refreshData,
		startInspect,
		endInspect,
		viewHistoryLine,
		addHistoryPoint,
		taskCode,
		areaId,
		lonLat,
		toggleInspect,
		_calcHourAndMinites,
		...toRefs(inspectInfo)
	}
}