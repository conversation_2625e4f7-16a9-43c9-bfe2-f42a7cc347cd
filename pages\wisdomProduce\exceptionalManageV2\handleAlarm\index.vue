<template>
	<view class="main">
		<view class="handle-info">
			<u-input type="textarea" input-align="left"  v-model="info" height="460" placeholder="请输入处理信息"  border color="#91949F">
			</u-input>
		</view>
		<view class="but-list">
			<u-button type="primary" :customStyle="customStyle" color="#3862F8" text="">提交
			</u-button>

			<u-button type="default" color="#91949F" style="margin-top: 32rpx;" :customStyle="customStyle" plain>取消
			</u-button>
		</view>
	</view>
</template>

<script lang="ts" setup>
	import {
		ref
	} from "vue";
	const info = ref < String > ('')
	const customStyle = ref < any > ({
		'border-radius': '8rpx',
		'margin-bottom': '32rpx',
		'width': '686rpx',
		'margin': '0 auto',
	})
</script>

<style lang="scss" scoped>
	.handle-info {
		min-height: 460rpx;
		width: 686rpx;
		border-radius: 16rpx;
		background-color: #FFFFFF;
		margin: 20rpx auto;
		padding: 30rpx 28rpx;
	}

	.but-list {
		margin-top: 32rpx;
	}

	::v-deep .u-textarea {
		background-color: #F9F9F9;
	}
</style>
