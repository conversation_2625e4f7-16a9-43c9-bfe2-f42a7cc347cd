<!DOCTYPE html>
<html>
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>定位</title>
		<script type="text/javascript" src="https://api.tianditu.gov.cn/api?v=4.0&tk=be4a83d8298d7aa84cb5e0ff3e1dad58">
		</script>
		<script type="text/javascript" src="https://js.cdn.aliyun.dcloud.net.cn/dev/uni-app/uni.webview.1.5.2.js">
		</script>
		<style>
			#mapDiv {
				width: 100%;
				height: 350px;
				position: relative;
			}

			.centerMarker {
				width: 32px;
				height: 32px;
				background: url('http://***************:9000/cloud-admin/2024/10/25/89c7951e26a145c4bc0c1243702e77a5.png') no-repeat center;
				background-size: contain;
				position: absolute;
				z-index: 99999;
				top: 50%;
				left: 50%;
				margin-left: -16px;
				/* 图标宽度的一半 */
				margin-top: -32px;
				/* 图标高度的一半 */
			}

			ul,
			li {
				list-style: none;
				padding: 8px;
				margin: 0px;
			}

			li {
				border-bottom: 1px solid #c4c4c4;
			}

			#addressList li.active {
				background-color: #d3d3d3;
				color: #007aff;
				/* 设置选中时的颜色 */
			}

			.searchClass {
				background-color: #007aff;
				border-radius: 5px;
				color: #fff;
				border: none;
				height: 30px;
			}

			.submitClass {
				background-color: #007aff;
				border-radius: 5px;
				border: none;
				color: #fff;
				position: absolute;
				z-index: 99999;
				top: 20px;
				right: 10px;
				height: 30px;
			}

			#searchInput {
				margin-top: 4px;
				width: 74%;
				height: 24px;
			}
		</style>
	</head>
	<body>
		<div id="mapDiv">
			<div class="centerMarker"></div> <!-- 中心点自定义图标 -->
			<button id="submit" class="submitClass">确定</button>
		</div>
		<!-- <p>中心点经纬度：<span id="centerLatLon"></span></p> -->
		<input type="text" id="searchInput" placeholder="请输入地址">
		<button id="searchBtn" class="searchClass">搜索地址</button>
		<div id="searchResult"></div>
		<ul id="addressList"></ul> <!-- 显示地址列表 -->
		<script>
			document.addEventListener('UniAppJSBridgeReady', function() {
				var location;
				var map = new T.Map('mapDiv'); // 初始化地图
				var geoc = new T.Geocoder();
				// 获取当前位置
				if (navigator.geolocation) {
					navigator.geolocation.getCurrentPosition(function(position) {
						var lng = position.coords.longitude;
						var lat = position.coords.latitude;

						// 显示经纬度
						// document.getElementById('address').innerText = lat + ", " + lng;
						map.centerAndZoom(new T.LngLat(lat, lng), 20); // 设置初始中心点和缩放级别
						// 逆地理编码获取地址

						geoc.getLocation(new T.LngLat(lng, lat), function(result) {
							if (result) {
								var address = result.getAddress();
								updateAddersss(address)
							}
						});

						// 将当前位置标注在地图上
						// var marker = new T.Marker(new T.LngLat(lng, lat));
						// map.addOverLay(marker);
						map.panTo(new T.LngLat(lng, lat)); // 平移到当前位置
					});
				} else {
					alert("浏览器不支持定位功能");
				}


				// 监听地图移动结束事件
				map.addEventListener('moveend', updateCenterCoordinates);


				// 获取并显示中心点经纬度信息
				function updateCenterCoordinates() {
					var center = map.getCenter(); // 获取地图中心点的经纬度
					// document.getElementById('centerLatLon').innerText = center.lat + ", " + center.lng;
					geoc.getLocation(new T.LngLat(center.lng, center.lat), function(result) {
						if (result) {
							updateAddersss(result)
						}
					});
				}

				function updateAddersss(result) {
					location = {
						address: result.getAddress(),
						coordinate: result.location
					}
					console.log(location)
					var addressList = document.getElementById('addressList');
					addressList.innerHTML = ''; // 清空列表
					var li = document.createElement('li');
					li.textContent = result.getAddress();
					// 移除其他列表项的 active 类
					var items = document.querySelectorAll(
						'#addressList li');
					items.forEach(function(item) {
						item.classList.remove('active');
					});

					// 为当前点击的列表项添加 active 类
					li.classList.add('active');

					// 将列表项添加到地址列表
					addressList.appendChild(li);
				}

				// 地址搜索功能
				document.getElementById('searchBtn').addEventListener('click', function() {
					var keyword = document.getElementById('searchInput').value;
					var localSearch = new T.LocalSearch(map, {
						pageCapacity: 5 // 每次查询最多返回5个结果
					});

					// 执行搜索
					localSearch.search(keyword);

					// 处理搜索结果
					localSearch.setSearchCompleteCallback(function(result) {
						var pois = result.getPois(); // 获取搜索到的POI点
						var addressList = document.getElementById('addressList');
						addressList.innerHTML = ''; // 清空列表

						if (pois && pois.length > 0) {
							pois.forEach(function(poi, index) {
								// 创建一个列表项
								var li = document.createElement('li');
								li.textContent = poi.name + ' - ' + poi.address;

								// 点击事件：点击后将地图平移到该POI的经纬度
								li.addEventListener('click', function() {
									var lnglat = poi.lonlat.split(','); // 获取POI的经纬度
									map.clearOverLays(); // 清除之前的标注
									// var marker = new T.Marker(lnglat);
									// map.addOverLay(marker);
									map.panTo(new T.LngLat(lnglat[0], lnglat[
										1])); // 平移到选中的位置

									// 移除其他列表项的 active 类
									var items = document.querySelectorAll(
										'#addressList li');
									items.forEach(function(item) {
										item.classList.remove('active');
									});
									// 为当前点击的列表项添加 active 类
									li.classList.add('active');
									location = {
										address: poi.name + ' - ' + poi.address,
										coordinate: poi.lonlat
									}
								});

								// 将列表项添加到地址列表
								addressList.appendChild(li);
							});
						} else {
							addressList.innerHTML = '<li>未找到相关结果</li>';
						}
					});
				});

				document.getElementById('submit').addEventListener('click', function() {
					uni.webView.postMessage({
						data: location
					})
					// uni.navigateBack()
				})
			})
		</script>

</html>