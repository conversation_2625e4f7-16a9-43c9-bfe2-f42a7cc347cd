export interface applicationInc {
  title: string;
  menus: {
    icon: string;
    title: string;
    url: string;
  }[];
}
export const applications = () => {
  return [
    // {
    // 	title: '考勤管理',
    // 	key: 'KQGL',
    // 	menus: [{
    // 		icon: '/static/img/menus/daka.png',
    // 		title: '打卡',
    // 		url: 'pages/mine/clockIn/index'
    // 	}, {
    // 		icon: '/static/img/menus/daka.png',
    // 		title: '值班签到',
    // 		url: 'pages/mine/clockIn/newIndex'
    // 	}]
    // },
    // {
    // 	title: '客服服务',
    // 	key: 'KFFW',
    // 	menus: [{
    // 		icon: '/static/img/menus/gongdanchaxun.png',
    // 		title: '综合查询',
    // 		url: 'pages/smartOperation/quannanInstall/comprehensiveQuery/index'
    // 	}, {
    // 		icon: '/static/img/menus/gongdanfaqi.png',
    // 		title: '报装申请',
    // 		url: 'pages/smartOperation/quannanInstall/install/index'
    // 	}]
    // },
    {
      title: "智慧生产",
      key: "ZHSC",
      menus: [
        // {
        // 	icon: '/static/img/menus/VR.png',
        // 	title: '',
        // 	url: 'pages/webView/index'
        // },
        {
          icon: "/static/img/menus/shuichangzonglan.png",
          title: "水厂总览",
          url: "pages/wisdomProduce/waterWorksOverview/index",
        },
        {
          icon: "/static/img/menus/ergongxunjian.png",
          title: "水厂巡检",
          url: "pages/wisdomProduce/secondSupplyInspection/index?type=水厂",
        },
        {
          icon: "/static/img/menus/ergongxunjian.png",
          title: "污水厂巡检",
          url: "pages/wisdomProduce/waterFactoryInspection/index?type=污水处理厂",
        },
        {
          icon: "/static/img/menus/ergongxunjian.png",
          title: "水源巡检",
          url: "pages/wisdomProduce/secondSupplyInspection/index?type=水源",
        },
        // {
        // 	icon: '/static/img/menus/ercigongshui.png',
        // 	title: '智慧泵房',
        // 	url: 'pages/smartPump/PumpsSingle'
        // },

        // {
        // 	icon: '/static/img/menus/shuichi.png',
        // 	title: '水池监测',
        // 	url: 'pages/wisdomProduce/poolMonitoring/index'
        // },
        // {
        // 	icon: '/static/img/menus/shuichi.png',
        // 	title: '站点监测',
        // 	url: 'pages/wisdomProduce/stationMonitoring/index'
        // },
        {
          icon: "/static/img/menus/ercigongshui.png",
          title: "二次供水",
          url: "pages/wisdomProduce/secondaryWaterSupply/index",
        },
        {
          icon: "/static/img/menus/ergongxunjian.png",
          title: "二供巡检",
          url: "pages/wisdomProduce/secondSupplyInspection/index?type=二供泵房",
        },
        {
          icon: "/static/img/menus/baobiao.png",
          title: "水厂报表",
          url: "pages/wisdomProduce/waterWorksReport/index",
        },
        {
          icon: "/static/img/menus/baobiao.png",
          title: "二供报表",
          url: "pages/wisdomProduce/secondSupplyReport/index",
        },
        // {
        // 	icon: '/static/img/menus/yichangguanli.png',
        // 	title: '异常管理',
        // 	url: 'pages/wisdomProduce/exceptionalManage/index'
        // },
        {
          icon: "/static/img/menus/yichangguanli.png",
          title: "异常管理",
          url: "pages/wisdomProduce/exceptionalManageV2/index",
        },
        // {
        // 	icon: '/static/img/menus/yichangguanli.png',
        // 	title: '异常管理',
        // 	url: 'pages/wisdomProduce/exceptionalManageV3/index'
        // },
        // {
        // 	icon: '/static/img/menus/write.png',
        // 	title: '数据填报',
        // 	url: 'pages/wisdomProduce/dataFill/index'
        // },
        // {
        // 	icon: '/static/img/menus/shexiangtou.png',
        // 	title: '摄像头监控',
        // 	url: 'pages/wisdomProduce/cameraMonitoring/cameraList'
        // },
        // {
        // 	icon: '/static/img/menus/write.png',
        // 	title: '安全任务',
        // 	url: 'pages/wisdomProduce/safeHomework/taskList'
        // },
        // {
        // 	icon: '/static/img/menus/write.png',
        // 	title: '隐患任务',
        // 	url: 'pages/wisdomProduce/safeHidden/index'
        // },
        // {
        // 	icon: '/static/img/menus/baobiao.png',
        // 	title: '环比分析',
        // 	url: "pages/wisdomProduce/chainAnalysis/index",
        // },
        // {
        // 	title: '同比分析',
        // 	icon: '/static/img/menus/baobiao.png',
        // 	url: "pages/wisdomProduce/monthOnMonthAnalusis/index",
        // },
      ],
    },
    {
      title: "智慧管网",
      key: "ZHGW",
      menus: [
        {
          icon: "/static/img/menus/guanwangyali.png",
          title: "管网压力",
          url: "pages/wisdomProduce/pressureMonitoring/index",
        },
        {
          icon: "/static/img/menus/guanwangliuliang.png",
          title: "管网流量",
          url: "pages/wisdomProduce/flowMonitoring/index",
        },
        {
          icon: "/static/img/menus/guanwangshuizhi.png",
          title: "管网水质",
          url: "pages/wisdomProduce/waterQualityMonitoring/index",
        },
        {
          icon: "/static/img/menus/yanghurenwu.png",
          title: "养护任务",
          url: "pages/pipeNetwork/maintenanceTasks/index",
        },
        {
          icon: "/static/img/menus/dituliulan.png",
          title: "地图浏览",
          url: "pages/pipeNetwork/mapBrowsing/index_leaflet",
        },
        {
          icon: "/static/img/menus/xunjianrenwu.png",
          title: "巡检任务",
          url: "pages/pipeNetwork/inspectionTask/index",
        },
        {
          icon: "/static/img/menus/xunjianfenpai.png",
          title: "巡检分派",
          url: "pages/pipeNetwork/inspectionTaskAssignment/index",
        },
        {
          icon: "/static/img/menus/xunjianchaxun.png",
          title: "巡检查询",
          url: "pages/pipeNetwork/inspectionTaskQuery/index",
        },
        {
          icon: "/static/img/menus/xunjianchaxun.png",
          title: "统计分析",
          url: "pages/pipeNetwork/statisticalAnalysis/index",
        },
        // {
        // 	icon: '/static/img/menus/xunjianchaxun.png',
        // 	title: '管网采集',
        // 	url: 'pages/pipeNetwork/pipeCollect/index'
        // },
        // {
        // 	icon: '/static/img/menus/xunjianchaxun.png',
        // 	title: '新建项目',
        // 	url: 'pages/pipeNetwork/project/createProject'
        // },
        // {
        // 	icon: '/static/img/menus/xunjianchaxun.png',
        // 	title: '汇总统计',
        // 	url: 'pages/pipeNetwork/statisticalAnalysis/summaryStatistics'
        // }
      ],
    },
    // {
    // 	title: '智慧营销',
    // 	key: 'ZHYX',
    // 	menus: [{
    // 		icon: '/static/img/menus/chaxuntongji.png',
    // 		title: '查询统计',
    // 		url: 'pages/revenueManage/queryStatistics/index'
    // 	},
    // 	{
    // 		icon: '/static/img/menus/chaobiaopaixu.png',
    // 		title: '抄表排序',
    // 		url: 'pages/revenueManage/userSort/index'
    // 	},
    // 	{
    // 		icon: '/static/img/menus/shangchuanxiazai.png',
    // 		title: '上传下载',
    // 		url: 'pages/revenueManage/upAndDownload/index'
    // 	},
    // 	{
    // 		icon: '/static/img/menus/chaobiaorenwu.png',
    // 		title: '抄表任务',
    // 		url: 'pages/revenueManage/meterReadingTask/index'
    // 	},
    // 	{
    // 		icon: '/static/img/menus/chaobiaorenwu.png',
    // 		title: '报装处理',
    // 		url: 'pages/smartOperation/quannanInstall/installTask/index'
    // 	},
    // 	{
    // 		icon: '/static/img/menus/chaobiaorenwu.png',
    // 		title: '报修处理',
    // 		url: 'pages/smartOperation/quannanInstall/repairTask/index'
    // 	},
    // 	{
    // 		icon: '/static/img/menus/valve.png',
    // 		title: '开关阀',
    // 		url: 'pages/revenueManage/onOffValve/on-off'
    // 	}]
    // },
    {
      title: "智慧运营",
      key: "ZHYY",
      menus: [
        // {
        // 	icon: '/static/img/menus/link.png',
        // 	title: 'webView',
        // 	url: 'pages/webView/index'
        // },
        {
          icon: "/static/img/menus/gongdanfaqi.png",
          title: "工单发起",
          url: "pages/smartOperation/workOrderInitiation/index",
        },
        {
          icon: "/static/img/menus/gongdanfenpai.png",
          title: "工单分派",
          url: "pages/smartOperation/workOrderAssignment/index",
        },
        {
          icon: "/static/img/menus/gongdanchuli.png",
          title: "工单处理",
          url: "pages/smartOperation/workOrderTask/index",
        },
        {
          icon: "/static/img/menus/gongdanshenhe.png",
          title: "工单审核",
          url: "pages/smartOperation/workOrderApproval/index",
        },
        // {
        // 	icon: '/static/img/menus/gongdanshenhe.png',
        // 	title: '转单审核',
        // 	url: 'pages/smartOperation/workOrderHandover/index'
        // }, {
        // 	icon: '/static/img/menus/gongdanxiezuo.png',
        // 	title: '工单协作',
        // 	url: 'pages/smartOperation/WorkOrderCollaboration/index'
        // },
        // {
        // 	icon: '/static/img/menus/yanqishenhe.png',
        // 	title: '延期审核',
        // 	url: 'pages/smartOperation/yanqishenhe/index'
        // },
        {
          icon: "/static/img/menus/tuidanshenhe.png",
          title: "退单审核",
          url: "pages/smartOperation/chargebackReviewed/index",
        },
        {
          icon: "/static/img/menus/gongdanchaxun.png",
          title: "工单查询",
          url: "pages/smartOperation/workOrderQuery/index",
        },
        {
          icon: "/static/img/menus/gongdantongji.png",
          title: "工单统计",
          url: "pages/smartOperation/workOrderStatistics/index",
        },
        {
          icon: "/static/img/menus/gongdanchaxun.png",
          title: "资产保养",
          url: "pages/smartOperation/assetMaintainTask/index",
        },
        {
          icon: "/static/img/menus/gongdanchaxun.png",
          title: "资产巡检",
          url: "pages/smartOperation/assetCircuitInspectionTask/index",
        },
        {
          icon: "/static/img/menus/gongdantongji.png",
          title: "设备资产",
          url: "pages/smartOperation/rquipmentAssets/index",
        },
        // {
        // 	icon: '/static/img/menus/gongdanchuli.png',
        // 	title: '预约管理',
        // 	url: 'pages/smartOperation/quannanInstall/reserveTask/index'
        // },
      ],
    },
    {
      title: "智慧营收",
      key: "ZHYS",
      menus: [
        {
          icon: "/static/img/menus/gongdanfaqi.png",
          title: "移动抄表",
          url: "pages/income/meterReading/index",
        },
        {
          icon: "/static/img/menus/lihuxinzeng.png",
          title: "立户新增",
          url: "pages/InstallationApply/User/userAdd/index",
        },
        {
          icon: "/static/img/menus/lihuxinxi.png",
          title: "立户信息",
          url: "pages/InstallationApply/User/userDetail/index",
        },
      ],
    },
  ];
};
