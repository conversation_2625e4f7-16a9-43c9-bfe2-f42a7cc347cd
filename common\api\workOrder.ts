import { http } from "../vmeitime-http/index";

export const saveWorkOrder = (params?: any) => {
  return http().post("api/workOrder", params);
};

export const saveWorkOrderV2 = (alarmId: string, params?: any) => {
  return http().post(
    `api/alarmV2/alarmCenter/createWorkOrder/${alarmId}`,
    params
  );
};

// 获取工单类型树
export const workOrderTypeList = (params?: { isDel?: number | null }) => {
  return http().get("api/ss/workOrder/type/tree", params);
};

// 获取紧急程度
export const urgencyList = (params?: { isDel?: number | null }) => {
  return http().get("api/ss/urgency", params);
};

// 组织列表
export const organizationList = (params?: {
  name?: String;
  parentId?: String;
}) => {
  return http().get("api/organization", params);
};

// 组织部门列表
export const departmentList = (params: { parentId: String }) => {
  return http().get("api/department", params);
};

// 获取完整的单位部门树结构
export const organizationTree = (depth: Number) => {
  return http().get(`api/organization/tree/${depth}`);
};

// 来源列表
export const sourceList = (params: { parentId: String }) => {
  return http().get("api/ss/kpi/norm/param", params);
};

// 来源列表
export const getAllByPid = (params?: {
  pid?: string;
  name?: string;
  page: number;
  size: number;
  status?: number;
}) => {
  return http().get("api/user/getAllByPid", params);
};
// 根据用户名获取用户信息
export const getAllByName = (params: { name?: string }) => {
  return http().get("api/user/getAllByName", params);
};

// 工单列表
export const workOrderList = (params: {
  status?: String;
  stepProcessUserId?: string;
  stageBetween?: String;
  stage?: String;
  self?: boolean;
  page: number;
  size: number;
}) => {
  return http().get("api/workOrder", params);
};

// 工单详情
export const workOrderDetailById = (orderId: String) => {
  return http().get(`api/workOrder/${orderId}`);
};

// 工单步骤清单
export const workOrderStagesById = (orderId: String) => {
  return http().get(`api/workOrder/${orderId}/stages`);
};

// 添加处理流程
export const workOrderHandle = (orderId: String, params: any) => {
  return http().post(`api/workOrder/${orderId}/stage`, params);
};

// 分派工单
export const assignWorkOrder = (orderId: String, params: any) => {
  return http().post(`api/workOrder/${orderId}/assign`, params);
};
// 接收工单
export const receiveWorkOrder = (orderId: String, params: any) => {
  return http().post(`api/workOrder/${orderId}/receive`, params);
};
// 申请退回订单
export const chargebackRequestWorkOrder = (orderId: String, params: any) => {
  return http().post(`api/workOrder/${orderId}/chargebackRequest`, params);
};

// 审核工单
export const verifyWorkOrder = (orderId: String, params: any) => {
  return http().post(`api/workOrder/${orderId}/verify`, params);
};

// 退回订单
export const chargebackWorkOrder = (orderId: String, params: any) => {
  return http().post(`api/workOrder/${orderId}/chargeback`, params);
};

// 个人工单任务
export const myWorkOrderList = (params: {
  status: String;
  page: number;
  size: number;
}) => {
  return http().get("api/workOrder/my", params);
};

// 工单统计接口
export const workOrderCount = (params: {
  timeUnit?: String;
  fromTime: number;
  toTime: number;
  statisticType?: boolean;
  statisticOrganizer?: boolean;
}) => {
  return http().get("api/workOrder/count", params);
};

// 工单统计数量接口
export const countOfStage = (params: {
  processUserId?: string;
  self?: boolean;
  fromTime?: number;
  toTime?: number;
}) => {
  return http().get("api/workOrder/countOfStatus", params);
};

// 上传文件
export const uploadFile = (params?: any) => {
  return http().post(`file/api/upload/file`, params);
};

// 待办事件数量
export const getTodoCount = () => {
  return http().get("api/shuiwu/common/commonTodoStatistic");
};

// 转单申请
export const handoverRequest = (
  id: string,
  params?: {
    processRemark?: string;
    processAdditionalInfo?: any;
    nextProcessUserId: string;
    expectUserId?: string;
  }
) => {
  return http().post(`api/workOrder/${id}/handoverRequest`, params);
};

// 转单审核
export const handover = (
  id: string,
  params?: {
    processRemark?: string;
    processAdditionalInfo?: any;
    stage: string;
  }
) => {
  return http().post(`api/workOrder/${id}/handover`, params);
};
/**
 * 工单完成率统计当年和当月
 */
export const workOrderCompleteRatio = () => {
  return http().get("api/workOrder/completeCount");
};

//工单协作列表
export const getWorkOrderCollaborations = (params: {
  page: number;
  size: number;
  status: string;
}) => {
  return http().get("api/workOrder/collaborations", params);
};

// 审核 工单协作
export const collaborateVerify = (id: string, params?: any) => {
  return http().post(`api/workOrder/${id}/collaborateVerify`, params);
};

// 工单协作申请
export const collaborate = (
  id: string,
  params?: {
    collaborationRemark?: String;
    isDirectDispatch: boolean;
    nextProcessUserId: String;
    processRemark: String;
    remark?: String;
    type: String;
  }
) => {
  return http().post(`api/workOrder/${id}/collaborate`, params);
};

/**
 * 分页查询工单来源列表
 * @param params
 * @returns
 */
export const getWorkOrderResourceList = (params: {
  page: string | number;
  size: string | number;
  status?: string;
}) => {
  return http().get("api/workOrderResource/list", params);
};

/**
 * 查询工单类型
 * @returns
 */
export const getWorkOrderTypeList = (status?: string) => {
  return http().get(`api/workOrderType/list`, {
    status,
  });
};

/**
 * 查询工单紧急程度列表
 * @returns
 */
export const getWorkOrderEmergencyLevelList = (status?: string) => {
  return http().get(`api/workOrderEmergencyLevel/list`, {
    status,
  });
};

/**
 * 查询工单处理级别列表
 * @returns
 */
export const getWorkOrderProcessLevelList = (status?: string) => {
  return http().get(`api/workOrderProcessLevel/list`, {
    status,
  });
};

export const getFaultKnowledgeSerch = (params?: {
  size: number;
  page: number;
  name?: string;
  deviceTypeId?: string;
}) => {
  return http().get(`api/fault/type`, params);
};
