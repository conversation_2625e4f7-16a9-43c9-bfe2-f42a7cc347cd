<template>
  <view class="main">
		<view class="card-box" v-for="(data,index) in tableData" :key="index" @click="openFill(data)">
			<view class="table">
				<view class="info">
					<text>巡检地点：</text> <text>{{data.locationArea}}</text>
				</view>
				<view class="info">
          <text>巡检位置：</text> <text>{{data.position}}</text>
				</view>
				<view class="info">
					<text>检验内容：</text> <text>{{data.content}}</text>
				</view>
				<view class="info">
					<text>检验时间：</text> <text>{{data.checkTime}}</text>
				</view>
        <view class="info flex-center">
          <text>检验结果：</text>
          <view class="bg">
            <text>{{data.result === 'NOT_CHECKED' ? '未检查' : data.result === 'NORMAL'?'正常':'异常'}}</text>
          </view>
        </view>
				<view class="info">
					<text>结果描述：</text> <text>{{data.resultDesc}}</text>
				</view>
			</view>
		</view>
    <u-popup v-model="showFill" mode="bottom" border-radius="20">
      <view class="fill-form">
        <view class="form-title">填写巡检结果</view>
        <u-form :model="fillForm" ref="fillFormRef" label-width="150">
          <u-form-item label="检验结果">
            <u-radio-group v-model="fillForm.result">
              <u-radio name="NORMAL">正常</u-radio>
              <u-radio name="ABNORMAL">异常</u-radio>
            </u-radio-group>
          </u-form-item>
          <u-form-item label="结果描述">
            <u-input v-model="fillForm.resultDescription" placeholder="请输入描述" />
          </u-form-item>
        </u-form>
        <view class="form-actions">
          <u-button type="primary" @click="submitFill">提交</u-button>
          <u-button @click="showFill=false" type="default">取消</u-button>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import {
  getInspectionWorkTaskList,
  postInspectionWorkTask
} from '../../../../common/api/inspection'

const tableData = ref<any[]>([])
const showFill = ref(false)
const fillForm = reactive({
  id: '',
  result: '',
  resultDescription: ''
})
const fillFormRef = ref()
let currentRow: any = null
let taskId = ''

onLoad((options) => {
  taskId = options.taskId
  fetchList()
})

const fetchList = async () => {
  if (!taskId) return
  const res = await getInspectionWorkTaskList(taskId)
  if (res && res.data) {
    tableData.value = res.data.data.map((item: any) => ({
      id: item.id,
      locationArea: item.location || '',
      position: item.position || '',
      content: item.content || '',
      result: item.result,
      resultDesc: item.resultDescription,
      checkTime: item.checkTimeName,
    }))
  }
}

const openFill = (row: any) => {
  // 状态为未检查时，才能填写
  if (row.result !== 'NOT_CHECKED') return
  
  fillForm.id = row.id
  fillForm.result = row.result || ''
  fillForm.resultDescription = row.resultDesc || ''
  showFill.value = true
  currentRow = row
}

const submitFill = async () => {
  if (!fillForm.result) {
    uni.$u.toast('请选择检验结果')
    return
  }
  await postInspectionWorkTask(fillForm.id, {
    result: fillForm.result,
    resultDescription: fillForm.resultDescription
  })
  showFill.value = false
  fetchList()
  uni.$u.toast('提交成功')
}
</script>

<style lang="scss" scoped>
.fill-btn {
  color: $u-type-primary;
  cursor: pointer;
}
.fill-form {
  padding: 32rpx 24rpx 24rpx 24rpx;
  background: #fff;
  border-radius: 20rpx 20rpx 0 0;
}
.form-title {
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 24rpx;
  color: $uni-text-color;
}
.form-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 32rpx;
}
.card-box {
  width: 686rpx;
  margin: 20rpx auto;
  border-radius: 16rpx;
  padding: 22rpx 28rpx;
  background-color: #FFFFFF;

  .table {
    margin-top: 24rpx;

    .info {
      font-size: 24rpx;
      padding-bottom: 18rpx;

      text {
        &:nth-child(1) {
          color: #91949F;
        }

        &:nth-child(2) {
          flex: 1;
        }
      }

      .bg {
        padding: 2rpx 12rpx;
        background: rgba(56, 98, 248, 0.2);
        border-radius: 8rpx;

        text {
          color: #3862F8;
        }
      }
    }
  }
}
</style>