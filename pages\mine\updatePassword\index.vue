<template>
	<view class="main">
		<view class="form-content">
			<u-form :model="form" ref="refForm" :label-style="{'color':'#91949F'}" borderBottom labelWidth="180"
				input-align="right" :error-type="['toast']">
				<u-form-item label="旧密码" labelWidth="150" prop="currentPassword">
					<u-input v-model="form.currentPassword" placeholder="请输入旧密码">
					</u-input>
				</u-form-item>
				<u-form-item label="新密码" labelWidth="150" prop="newPassword">
					<u-input v-model="form.newPassword" placeholder="请输入新密码,不能少于8位">
					</u-input>
				</u-form-item>
			</u-form>
		</view>
		<view class="button">
			<u-button type="primary" @click="updatePassword">修改密码</u-button>
		</view>
		<u-toast ref="refToast"></u-toast>
	</view>
</template>

<script lang="ts" setup>
	import {
		reactive,
		ref
	} from "vue";
	import {
		onReady
	} from '@dcloudio/uni-app';
	import {
		changePassword
	} from '@/common/api/login'
	const refForm = ref < any > ({})
	const refToast = ref < any > ()
	const form = reactive({
		currentPassword: '',
		newPassword: ''
	})
	const rules = reactive < any > ({
		currentPassword: [{
			required: true,
			message: '请输入旧密码',
			trigger: ['blur', 'change']
		}],
		newPassword: [{
			required: true,
			message: '请输入新密码',
			trigger: ['blur', 'change']
		}, {
			min: 8,
			message: '请输入新密码,不能少于8位',
			trigger: ['blur', 'change']
		}],
	})
	// 修改密码
	const updatePassword = () => {
		refForm.value?.validate(valid => {
			console.log(valid)
			if (valid) {
				if (form.currentPassword === form.newPassword) {
					uni.$u.toast('新密码与原密码一致，请重新输入')
					return false
				}
				changePassword(form.currentPassword, form.newPassword)
					.then(() => {
						refToast.value.show({
							title: '修改成功',
							type: 'success',
							callback: () => {
								uni.removeStorageSync("userInfo")
								uni.removeStorageSync("authority")
								uni.removeStorageSync("token")
								uni.$u.route({
									type: 'redirectTo',
									url: '/pages/login/index'
								})
							}
						})
					}).catch(err => {
						refToast.value.show({
							title: err.data.message,
							type: 'error ',
						})
					})
			}
		})
	}

	onReady(async () => {
		console.log('ready')
		refForm.value.setRules(rules)
	})
</script>

<style lang="scss" scoped>
	.main {
		height: 90vh;
		padding-top: 21rpx;

		.form-content {
			padding: 20rpx;
			background-color: #FFF;
			border-radius: 30rpx;
			width: 696rpx;
			min-height: 300rpx;
			margin: 0 auto;
		}
	}
</style>
