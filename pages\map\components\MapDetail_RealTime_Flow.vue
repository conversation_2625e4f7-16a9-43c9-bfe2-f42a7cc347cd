<template>
	<view class="flow-detail">
		<!-- 统计卡片区域 -->
		<view class="stats-section">
			<view class="section-title">
				<text class="title-text">{{props.title}}统计</text>
				<view class="title-line"></view>
			</view>
			<view class="stats-cards">
				<view class="stat-card" v-for="(item, i) in state.total" :key="i">
					<view class="card-icon">
						<text class="iconfont" :class="i === 0 ? 'icon-device' : 'icon-alert'"></text>
					</view>
					<view class="card-content">
						<view class="card-value">{{item.value}}<text class="card-unit">{{item.unit}}</text></view>
						<view class="card-title">{{item.title}}</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 状态占比图表区域 -->
		<view class="chart-section">
			<view class="section-title">
				<text class="title-text">{{props.title}}状态占比统计</text>
				<view class="title-line"></view>
			</view>
			<view class="chart-container">
				<l-echart ref="refLEchart_Status_Ratio"></l-echart>
			</view>
		</view>

		<!-- 列表区域 -->
		<view class="list-section">
			<view class="section-title">
				<text class="title-text">{{props.title}}列表</text>
				<view class="title-line"></view>
			</view>
			<view class="list-container">
				<view class="tabs-container">
					<view class="custom-tabs">
						<view 
							v-for="(tab, index) in state.statusList" 
							:key="index"
							class="tab-item"
							:class="{ active: state.currentStatus === index }"
							@click="chooseTab(index)"
						>
							<text class="tab-text">{{tab.name}}</text>
							<view class="tab-badge" v-if="tab.badge.value > 0">{{tab.badge.value}}</view>
						</view>
					</view>
				</view>
				<view class="table-container">
					<view class="table-header">
						<view class="header-cell" v-for="column in state.listColumns" :key="column.prop">
							{{column.label}}
						</view>
					</view>
					<scroll-view class="table-body" scroll-y="true" :style="{height: '400rpx'}">
						<view 
							v-for="(item, index) in state.list" 
							:key="index"
							class="table-row"
							:class="{ 'row-hover': true }"
						>
							<view class="table-cell">
								<text class="cell-text">{{item.name}}</text>
							</view>
							<view class="table-cell">
								<view class="status-badge" :class="getStatusClass(item.statusAlia)">
									{{item.statusAlia}}
								</view>
							</view>
							<view class="table-cell">
								<text class="cell-text time">{{item.lastTime}}</text>
							</view>
						</view>
						<view v-if="state.list.length === 0" class="empty-state">
							<text class="empty-icon">📊</text>
							<text class="empty-text">暂无数据</text>
						</view>
					</scroll-view>
				</view>
			</view>
		</view>
	</view>
</template>

<script lang="ts" setup>
	import * as echarts from 'echarts'
	import lEchart from '@/uni_modules/lime-echart/components/l-echart/l-echart.vue'
	import { onMounted, reactive, ref } from 'vue'
	import { getFlowMonitorStatusList } from '../../../common/api/stations'
	const props = defineProps<{
		title ?: string
	}>()
	const refLEchart_Status_Ratio = ref<InstanceType<typeof lEchart>>()
	const state = reactive<{
		total : { value : number; title : string; unit ?: string }[],
		list : { name : string; status : string; lastTime : string }[]
		listColumns : { label : string; prop : string }[]
		statusList : { name : string; badge : { value : number } }[],
		currentStatus : number
	}>({
		total: [{ value: 0, title: props.title + '总数', unit: '个' }, { value: 0, title: '报警率', unit: '%' }],
		list: [],
		listColumns: [
			{ label: '名称', prop: 'name' },
			{ label: '状态', prop: 'statusAlia' },
			{ label: '更新时间', prop: 'lastTime' }
		],
		statusList: [
			{ name: '全部', badge: { value: 0 } },
			{ name: '离线', badge: { value: 0 } },
			{ name: '报警', badge: { value: 0 } },
			{ name: '正常', badge: { value: 0 } },
		],
		currentStatus: 0
	})
	const refreshStatus = async () => {
		try {
			let total = state.list.length
			const statusList = [...state.statusList].splice(1,4)
			const data = statusList.map((item) => {
				return { name: item.name, value: item.badge.value }
			})
			refLEchart_Status_Ratio.value?.init(echarts, (chart : any) => {
				const option = {
					tooltip: {
						trigger: 'item',
						backgroundColor: 'rgba(0, 0, 0, 0.85)',
						borderColor: 'transparent',
						borderRadius: 12,
						textStyle: {
							color: '#fff',
							fontSize: 14,
							fontWeight: '500'
						},
						formatter: '{a} <br/>{b}: {c} ({d}%)',
						extraCssText: 'box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.3);'
					},
					title: {
						text: '{name|合计(个)}\n{val|' + total + '}',
						top: 'center',
						left: '35%',
						textAlign: 'center',
						textStyle: {
							rich: {
								name: {
									fontSize: 14,
									fontWeight: 'normal',
									padding: [8, 0],
									align: 'center',
									color: '#666'
								},
								val: {
									fontSize: 24,
									fontWeight: 'bold',
									color: '#0073ff',
									textShadow: '0 2rpx 4rpx rgba(0, 115, 255, 0.3)'
								}
							}
						}
					},
					color: ['#ff4757', '#ffa502', '#2ed573'],
					legend: {
						type: 'scroll',
						icon: 'circle',
						orient: 'vertical',
						left: 'right',
						top: 'center',
						align: 'left',
						itemGap: 20,
						itemWidth: 16,
						itemHeight: 16,
						symbolKeepAspect: true,
						textStyle: {
							color: '#333',
							fontSize: 14,
							fontWeight: '500',
							rich: {
								name: {
									align: 'left',
									width: 50,
									fontSize: 14,
									color: '#333',
									fontWeight: '500'
								},
								value: {
									align: 'left',
									width: 40,
									fontSize: 14,
									color: '#0073ff',
									fontWeight: 'bold'
								}
							}
						},
						data: data.map(item => item.name),
						formatter(name : any) {
							if (data && data.length) {
								for (let i = 0; i < data.length; i++) {
									if (name === data[i].name) {
										return '{name|' + (data[i].name || name) + '}\n{value|' + data[i].value + '个}'
									}
								}
							}
						}
					},
					series: [
						{
							name: props.title,
							type: 'pie',
							radius: ['40%', '70%'],
							center: ['35%', '50%'],
							data: data,
							itemStyle: {
								borderRadius: 8,
								borderColor: '#fff',
								borderWidth: 3,
								shadowBlur: 15,
								shadowOffsetX: 0,
								shadowOffsetY: 4,
								shadowColor: 'rgba(0, 0, 0, 0.15)'
							},
							label: {
								show: true,
								position: 'inside',
								textStyle: {
									fontSize: 14,
									color: '#fff',
									fontWeight: 'bold',
									textShadow: '0 1rpx 2rpx rgba(0, 0, 0, 0.3)'
								},
								formatter(param : any) {
									return (param.percent||0) + '%';
								}
							},
							labelLine: {
								show: false
							},
							emphasis: {
								itemStyle: {
									shadowBlur: 25,
									shadowOffsetX: 0,
									shadowOffsetY: 8,
									shadowColor: 'rgba(0, 0, 0, 0.25)',
									borderWidth: 4
								},
								label: {
									fontSize: 16,
									fontWeight: 'bold'
								}
							},
							animationType: 'scale',
							animationEasing: 'elasticOut',
							animationDelay: function (idx: any) {
								return Math.random() * 200;
							}
						}
					]
				}
				chart.setOption(option)
			})

		} catch (e) {
			console.log(e)
		}
	}

	const chooseTab = (index : number) => {
		state.currentStatus = index
		refreshTable(index)
	}
	const refreshTable = async (index : number = 0) => {
		try {
			const curStatus = state.statusList[index]?.name
			const status = curStatus === '离线' ? 'offline' : curStatus === '报警' ? 'alarm' : curStatus === '正常' ? 'online' : ''
			const res = await getFlowMonitorStatusList(status)

			if (res.data.code === 200) {
				state.list = res.data?.data?.map((item : any) => {
					item.lastTime = item.lastTime?.split(' ')?.[1]
					item.statusAlia = item.status === 'offline' ? '离线' : item.status === 'alarm' ? '报警' : item.status === 'online' ? '正常' : ''
					return item
				}) || []
			} else {
				console.log('统计失败');
			}
		} catch (e) {
			console.log(e)
		}
	}
	const refreshData = async () => {
		await refreshTable()
		state.statusList[0].badge.value = state.total[0].value = state.list.length
		state.statusList[1].badge.value = state.list.filter(item => item.status === 'offline').length
		state.statusList[2].badge.value = state.list.filter((item) => item.status === 'alarm').length
		state.statusList[3].badge.value = state.list.filter(item => item.status === 'online').length
		state.total[1].value = state.list.length === 0 ? 0 : Number((state.statusList[2].badge.value / state.list.length * 100).toFixed(2))
		refreshStatus()
	}

	const getStatusClass = (status: string) => {
		switch (status) {
			case '正常':
				return 'status-normal'
			case '报警':
				return 'status-alarm'
			case '离线':
				return 'status-offline'
			default:
				return 'status-normal'
		}
	}

	onMounted(() => {
		refreshData()
	})
</script>

<style lang="scss" scoped>
	.flow-detail {
		// background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
		min-height: 100vh;
		padding: 12rpx;

		.section-title {
			display: flex;
			align-items: center;
			margin-bottom: 24rpx;
			
			.title-text {
				font-size: 32rpx;
				font-weight: bold;
				color: #333;
				margin-right: 16rpx;
			}
			
			.title-line {
				flex: 1;
				height: 2rpx;
				background: linear-gradient(90deg, #0073ff 0%, transparent 100%);
				border-radius: 1rpx;
			}
		}

		.stats-section {
			margin-bottom: 32rpx;
			
			.stats-cards {
				display: flex;
				gap: 20rpx;
				
				.stat-card {
					flex: 1;
					background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
					border-radius: 16rpx;
					padding: 24rpx;
					box-shadow: 0 12rpx 40rpx rgba(102, 126, 234, 0.4);
					display: flex;
					align-items: center;
					transition: all 0.3s ease;
					
					&:hover {
						transform: translateY(-6rpx);
						box-shadow: 0 20rpx 60rpx rgba(102, 126, 234, 0.5);
					}
					
					.card-icon {
						width: 80rpx;
						height: 80rpx;
						background: rgba(255, 255, 255, 0.2);
						border-radius: 50%;
						display: flex;
						align-items: center;
						justify-content: center;
						margin-right: 20rpx;
						
						.iconfont {
							font-size: 36rpx;
							color: #fff;
						}
					}
					
					.card-content {
						flex: 1;
						
						.card-value {
							font-size: 36rpx;
							font-weight: bold;
							color: #fff;
							margin-bottom: 8rpx;
							
							.card-unit {
								font-size: 24rpx;
								font-weight: normal;
								opacity: 0.8;
							}
						}
						
						.card-title {
							font-size: 24rpx;
							color: rgba(255, 255, 255, 0.9);
						}
					}
					
					&:nth-child(2) {
						background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
						box-shadow: 0 12rpx 40rpx rgba(240, 147, 251, 0.4);
						
						&:hover {
							box-shadow: 0 20rpx 60rpx rgba(240, 147, 251, 0.5);
						}
					}
				}
			}
		}

		.chart-section {
			margin-bottom: 32rpx;
			
			.chart-container {
				background: #fff;
				border-radius: 16rpx;
				padding: 24rpx;
				box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
				height: 520rpx;
			}
		}

		.list-section {
			.list-container {
				background: #fff;
				border-radius: 16rpx;
				padding: 24rpx;
				box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
				display: flex;
				flex-direction: column;
				height: 520rpx;
			}

			.tabs-container {
				display: flex;
				justify-content: space-around;
				margin-bottom: 24rpx;
				padding-bottom: 20rpx;
				border-bottom: 1rpx solid #eee;
			}

			.custom-tabs {
				display: flex;
				gap: 20rpx;
			}

			.tab-item {
				display: flex;
				align-items: center;
				padding: 12rpx 24rpx;
				border-radius: 20rpx;
				background: #f5f5f5;
				transition: all 0.3s ease;
				position: relative;
				cursor: pointer;

				&.active {
					background: linear-gradient(135deg, #0073ff 0%, #0056b3 100%);
					color: #fff;
					box-shadow: 0 4rpx 12rpx rgba(0, 115, 255, 0.3);

					.tab-text {
						color: #fff;
					}

					.tab-badge {
						background: rgba(255, 255, 255, 0.2);
						color: #fff;
					}
				}

				&:hover:not(.active) {
					background: #e8f2ff;
					transform: translateY(-2rpx);
				}

				.tab-text {
					font-size: 28rpx;
					font-weight: 500;
					color: #666;
					margin-right: 8rpx;
				}

				.tab-badge {
					background: #ff4757;
					color: #fff;
					font-size: 20rpx;
					padding: 4rpx 8rpx;
					border-radius: 10rpx;
					min-width: 32rpx;
					text-align: center;
					font-weight: bold;
				}
			}

			.table-container {
				flex: 1;
				display: flex;
				flex-direction: column;
				border-radius: 12rpx;
				overflow: hidden;
				background: #fafafa;
			}

			.table-header {
				display: flex;
				background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
				border-bottom: 2rpx solid #dee2e6;
				padding: 20rpx 0;

				.header-cell {
					flex: 1;
					text-align: center;
					font-size: 28rpx;
					font-weight: bold;
					color: #495057;
					position: relative;

					&:not(:last-child)::after {
						content: '';
						position: absolute;
						right: 0;
						top: 50%;
						transform: translateY(-50%);
						width: 1rpx;
						height: 60%;
						background: #dee2e6;
					}
				}
			}

			.table-body {
				flex: 1;
				background: #fff;

				.table-row {
					display: flex;
					padding: 20rpx 0;
					border-bottom: 1rpx solid #f1f3f4;
					transition: all 0.3s ease;

					&:hover {
						background: #f8f9fa;
						transform: translateX(4rpx);
					}

					&:last-child {
						border-bottom: none;
					}

					.table-cell {
						flex: 1;
						display: flex;
						align-items: center;
						justify-content: center;
						padding: 0 16rpx;

						.cell-text {
							font-size: 26rpx;
							color: #333;
							text-align: center;
							overflow: hidden;
							text-overflow: ellipsis;
							white-space: nowrap;
							max-width: 100%;

							&.time {
								color: #666;
								font-size: 24rpx;
							}
						}

						.status-badge {
							padding: 8rpx 16rpx;
							border-radius: 20rpx;
							font-size: 24rpx;
							font-weight: 500;
							text-align: center;
							min-width: 80rpx;

							&.status-normal {
								background: linear-gradient(135deg, #2ed573 0%, #1e90ff 100%);
								color: #fff;
								box-shadow: 0 2rpx 8rpx rgba(46, 213, 115, 0.3);
							}

							&.status-alarm {
								background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%);
								color: #fff;
								box-shadow: 0 2rpx 8rpx rgba(255, 71, 87, 0.3);
							}

							&.status-offline {
								background: linear-gradient(135deg, #747d8c 0%, #57606f 100%);
								color: #fff;
								box-shadow: 0 2rpx 8rpx rgba(116, 125, 140, 0.3);
							}
						}
					}
				}

				.empty-state {
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;
					height: 300rpx;
					color: #999;

					.empty-icon {
						font-size: 80rpx;
						margin-bottom: 16rpx;
					}

					.empty-text {
						font-size: 28rpx;
						color: #999;
					}
				}
			}
		}

		// 响应式设计
		@media (max-width: 750rpx) {
			.flow-detail {
				padding: 16rpx;
				
				.stats-cards {
					flex-direction: column;
					gap: 16rpx;
					
					.stat-card {
						padding: 20rpx;
						
						.card-icon {
							width: 60rpx;
							height: 60rpx;
							
							.iconfont {
								font-size: 28rpx;
							}
						}
						
						.card-content {
							.card-value {
								font-size: 32rpx;
							}
							
							.card-title {
								font-size: 22rpx;
							}
						}
					}
				}
			}
		}
	}
</style>