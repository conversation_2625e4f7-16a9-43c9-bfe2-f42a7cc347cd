import { onBeforeUnmount, ref } from "vue"

export const useUniBluetooth = () => {

	// 搜索到的蓝牙设备列表
	const blueDeviceList = ref([])

	// 【1】初始化蓝牙
	const initBlue = () => {
		uni.openBluetoothAdapter({
			success(res) {
				console.log('初始化蓝牙成功')
				console.log(res)
			},
			fail(err) {
				console.log('初始化蓝牙失败')
				console.error(err)
			}
		})
	}

	// 【2】开始搜寻附近设备
	const discovery = () => {
		stopDiscovery()
		uni.startBluetoothDevicesDiscovery({
			allowDuplicatesKey: false,
			// services:['0000FDAA-0000-1000-8000-00805F9B34FB'],
			success(res) {
				console.log('开始搜索')
				// 开启监听回调
				uni.onBluetoothDeviceFound(onFound)
			},
			fail(err) {
				console.log('搜索失败')
				console.error(err)
			}
		})
	}

	// 【3】找到新设备就触发该方法
	const onFound = (res) => {
		// console.log(res)
		res.devices.map(item => {
			if (item.advertisServiceUUIDs?.length || item.deviceId == '75:BA:59:24:17:FA' || item.deviceId == '74:76:5B:AE:43:57') {
				// 	console.log(item);
				// if (item.deviceId == '75:BA:59:24:17:FA') {

				console.log(JSON.stringify(item))
				blueDeviceList.value.push(item)
			}

			// }
		})
	}

	// 蓝牙设备的id
	const deviceId = ref('')

	// 【4】连接设备
	const connect = (data) => {
		console.log(data)

		deviceId.value = data.deviceId // 将获取到的设备ID存起来
		console.log(deviceId.value)
		uni.createBLEConnection({
			deviceId: deviceId.value,
			success(res) {
				console.log('连接成功')
				console.log(res)
				// 停止搜索
				stopDiscovery()
				uni.showToast({
					title: '连接成功'
				})
			},
			fail(err) {
				console.log('连接失败')
				console.error(err)
				uni.showToast({
					title: '连接失败',
					icon: 'error'
				})
			}
		})
	}

	// 【5】停止搜索
	const stopDiscovery = () => {
		uni.stopBluetoothDevicesDiscovery({
			success(res) {
				console.log('停止成功')
				console.log(res)
			},
			fail(err) {
				console.log('停止失败')
				console.error(err)
			}
		})
	}

	// 【6】获取服务
	const getServices = () => {
		// 如果是自动链接的话，uni.getBLEDeviceServices方法建议使用setTimeout延迟1秒后再执行
		uni.getBLEDeviceServices({
			deviceId: deviceId.value,
			success(res) {
				console.log(res) // 可以在res里判断有没有硬件佬给你的服务
				serviceId.value = res.services[0].uuid
				uni.showToast({
					title: '获取服务成功'
				})
			},
			fail(err) {
				console.error(err)
				uni.showToast({
					title: '获取服务失败',
					icon: 'error'
				})
			}
		})
	}

	// 硬件提供的服务id，开发中需要问硬件佬获取该id
	const serviceId = ref('0000FFE0-0000-1000-8000-00805F9B34FB')

	// 【7】获取特征值
	const getCharacteristics = () => {
		// 如果是自动链接的话，uni.getBLEDeviceCharacteristics方法建议使用setTimeout延迟1秒后再执行
		uni.getBLEDeviceCharacteristics({
			deviceId: deviceId.value,
			serviceId: serviceId.value,
			success(res) {
				console.log(res) // 可以在此判断特征值是否支持读写等操作，特征值其实也需要提前向硬件佬索取的
				characteristicId.value = res.characteristics[0].uuid
				characteristicProperties.value = res.characteristics[0].properties
				uni.showToast({
					title: '获取特征值成功'
				})
			},
			fail(err) {
				console.error(err)
				uni.showToast({
					title: '获取特征值失败',
					icon: 'error'
				})
			}
		})
	}

	const characteristicId = ref('0000FFE1-0000-1000-8000-00805F9B34FB')
	const characteristicProperties = ref({
		"read": true,
		"write": false,
		"notify": false,
		"indicate": false
	})

	// 【8】开启消息监听
	const notify = () => {
		uni.notifyBLECharacteristicValueChange({
			deviceId: deviceId.value, // 设备id
			serviceId: serviceId.value, // 监听指定的服务
			characteristicId: characteristicId.value, // 监听对应的特征值
			state: true,
			success(res) {
				console.log(res)
				listenValueChange()
				uni.showToast({
					title: '已开启监听'
				})
			},
			fail(err) {
				console.error(err)
				uni.showToast({
					title: '监听失败',
					icon: 'error'
				})
			}
		})
	}

	// ArrayBuffer转16进度字符串示例
	const ab2hex = (buffer) => {
		const hexArr = Array.prototype.map.call(
			new Uint8Array(buffer),
			function (bit) {
				return ('00' + bit.toString(16)).slice(-2)
			}
		)
		return hexArr.join('')
	}

	// 将16进制的内容转成我们看得懂的字符串内容
	const hexCharCodeToStr = (hexCharCodeStr) => {
		var trimedStr = hexCharCodeStr.trim();
		var rawStr = trimedStr.substr(0, 2).toLowerCase() === "0x" ? trimedStr.substr(2) : trimedStr;
		var len = rawStr.length;
		if (len % 2 !== 0) {
			alert("存在非法字符!");
			return "";
		}
		var curCharCode;
		var resultStr = [];
		for (var i = 0; i < len; i = i + 2) {
			curCharCode = parseInt(rawStr.substr(i, 2), 16);
			resultStr.push(String.fromCharCode(curCharCode));
		}
		return resultStr.join("");
	}

	// 监听到的内容
	const message = ref('')
	const messageHex = ref('') // 十六进制

	// 【9】监听消息变化
	const listenValueChange = () => {
		uni.onBLECharacteristicValueChange(res => {
			console.log(res)
			let resHex = ab2hex(res.value)
			console.log(resHex)
			messageHex.value = resHex
			let result = hexCharCodeToStr(resHex)
			console.log(String(result))
			message.value = String(result)
		})
	}

	// 【10】发送数据
	const send = () => {
		// 向蓝牙设备发送一个0x00的16进制数据
		let msg = 'hello'

		const buffer : any = new ArrayBuffer(msg.length)
		const dataView = new DataView(buffer)
		// dataView.setUint8(0, 0)

		for (var i = 0; i < msg.length; i++) {
			dataView.setUint8(i, msg.charAt(i).charCodeAt(0))
		}

		uni.writeBLECharacteristicValue({
			deviceId: deviceId.value,
			serviceId: serviceId.value,
			characteristicId: characteristicId.value,
			value: buffer,
			success(res) {
				console.log('writeBLECharacteristicValue success', res.errMsg)
				uni.showToast({
					title: 'write指令发送成功'
				})
			},
			fail(err) {
				console.error(err)
				uni.showToast({
					title: 'write指令发送失败',
					icon: 'error'
				})
			}
		})
	}

	// 【11】读取数据
	const read = () => {
		uni.readBLECharacteristicValue({
			deviceId: deviceId.value,
			serviceId: serviceId.value,
			characteristicId: characteristicId.value,
			success(res) {
				console.log(res)
				uni.showToast({
					title: 'read指令发送成功'
				})
			},
			fail(err) {
				console.error(err)
				uni.showToast({
					title: 'read指令发送失败',
					icon: 'error'
				})
			}
		})
	}
	onBeforeUnmount(() => {
		stopDiscovery()
	})
	return {
		blueDeviceList,
		initBlue,
		discovery,
		deviceId,
		connect,
		stopDiscovery,
		getServices,
		serviceId,
		getCharacteristics,
		characteristicId,
		notify,
		send,
		read,
		message,
		messageHex,
	}
}


// <view>
// <p>使用uniapp蓝牙接口连接BLE低功耗蓝牙：</p>
// <scroll-view scroll-y class="box">
// 	<view class="item" v-for="item in bluetooth.blueDeviceList.value" @click="bluetooth.connect(item)">
// 		<view>
// 			<text>id: {{ item.deviceId }}</text>
// 		</view>
// 		<view>
// 			<text>name: {{ item.name }}</text>
// 		</view>
// 	</view>
// </scroll-view>

// <button @click="bluetooth.initBlue">1 初始化蓝牙</button>

// <button @click="bluetooth.discovery">2 搜索附近蓝牙设备</button>

// <button @click="bluetooth.getServices">3 获取蓝牙服务</button>

// <button @click="bluetooth.getCharacteristics">4 获取特征值</button>

// <button @click="bluetooth.notify">5 开启消息监听</button>

// <button @click="bluetooth.send">6 发送数据</button>

// <button @click="bluetooth.read">7 读取数据</button>
// <button @click="bluetooth.stopDiscovery">8 手动停止蓝牙搜索</button>

// <view class="msg_x">
// 	<view class="msg_txt">
// 		监听到的内容：{{ bluetooth.message.value }}
// 	</view>
// 	<view class="msg_hex">
// 		监听到的内容（十六进制）：{{ bluetooth.messageHex.value }}
// 	</view>
// </view>
// <h1>使用Native.js连接蓝牙：</h1>
// <button @click="bluetooth.openBluetooth">1 打开蓝牙</button>
// <button @click="bluetooth.searchBoundedDevices">2 查看已配对设备</button>
// <button @click="bluetooth.searchDevices">3 搜索附近蓝牙设备</button>

// <div>
// 	已配对成功蓝牙设备：
// 	<ul id="list1">
// 		<li v-for="(item,i) in bluetooth.patchedDevices.value" :key="i" @click="()=>bluetooth.print(item)">
// 			{{item.name}}</li>
// 	</ul>
// </div>
// <div>
// 	搜索到的未配对设备：
// 	<ul id="list1">
// 		<li v-for="(item,i) in bluetooth.devices.value" :key="i" @click="bluetooth.connect(item)">
// 			{{item.name}}</li>
// 	</ul>
// </div>

// <div>
// 	搜索周边已配对设备：

// 	<ul id="list2">
// 		<li v-for="(item,i) in bluetooth.connectedDevices.value" :key="i" @click="()=>bluetooth.print(item)">{{item.name}}</li>

// 	</ul>
// </div>

// <p>操作记录：</p>
// <ul>
// 	<li v-for="(item,i) in bluetooth.actionsRecords.value" :key="i">Action: {{item}}</li>
// </ul>
// ​
// </view>