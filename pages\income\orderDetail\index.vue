<template>
  <view class="container">
    <view class="description-box">
      <!-- 用户信息 -->
      <view class="section-title">用户信息</view>
      <UserInfo :userInfo="userInfo" />

      <!-- 账单明细 -->
      <view class="section-title">账单明细</view>
      <view class="order-detail">
        <OrderDetailItem label="水表编号" value="WM00015797" />
        <OrderDetailItem label="上次抄表时间" value="2024-08-10 10:44:51" />
        <OrderDetailItem label="上次抄表读数" value="245" />
        <OrderDetailItem label="本次抄表时间" value="2024-09-10 10:44:51" />
        <OrderDetailItem label="本次抄表读数" value="298" />
        <OrderDetailItem label="旧表止度" value="0" />
        <OrderDetailItem label="用水量" value="53" />
        <OrderDetailItem label="价格名称" value="居民标准价格" />
        <OrderDetailItem label="单价" value="2.50" />
        <OrderDetailItem label="违约金" value="0.00" />
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive } from 'vue';
import {  getUserInfo } from '@/common/api/income'; // 引入获取用户信息的API
import UserInfo from "@/components/UserInfo.vue"; // 引入用户信息组件
import OrderDetailItem from "@/components/OrderDetailItem.vue"; // 引入订单明细项组件

const userInfo = reactive({
  businessArea: "城南片区",
  meterReadingManual: "抄表手册001",
  userNo: "10017166",
  userName: "王雪琪",
  phoneNumber: "***********",
  address: "广东省深圳市南山区粤海街道科苑路15号",
});

// 从url参数获取用户编号
const urlParams = new URLSearchParams(window.location.search);
const userNo = urlParams.get('id');

// 获取用户信息
const getUserInfoData = async () => {
  try {
    const res = await getUserInfo(userNo);
    if (res.code === 0) {
      userInfo.businessArea = res.data.businessArea;
      userInfo.meterReadingManual = res.data.meterReadingManual;
      userInfo.userNo = res.data.userNo;
      userInfo.userName = res.data.userName;
      userInfo.phoneNumber = res.data.phoneNumber;
      userInfo.address = res.data.address;
    } else {
      uni.showToast({
        title: res.msg,
        icon: 'none'
      });
    }
  } catch (error) {
    uni.showToast({
      title: '获取用户信息失败',
      icon: 'none'
    });
  }
}

getUserInfoData();
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background: #F5F6F7;
  
  .description-box {
    margin: 20rpx;
    background: #FFFFFF;
    padding: 20rpx;
    border-radius: 16rpx;
  }
  .section-title {
    font-size: 32rpx;
    font-weight: 500;
    color: #333333;
    margin: 26rpx 0 16rpx;
    padding-left: 16rpx;
    border-left: 8rpx solid #3862F8;
  }
}

.user-info {
  margin-bottom: 40rpx;
}

.info-item {
  display: flex;
  margin-bottom: 16rpx;
}

.info-item .label {
  width: 200rpx;
  font-weight: bold;
}

.order-detail {
  margin-bottom: 20rpx;
}
</style>
