<template>
	<view class="main">
		<view class="logo">
			<image src="/static/img/logo.png" style="margin: 0 auto;height: 180rpx;width: 180rpx;"></image>
			<view class="version">
				<view>智慧水务</view>
				<view>V1.0</view>
			</view>
		</view>
		<view class="cell">
			<u-cell-group :border="false">
				<u-cell-item bg-color="#FBFBFB" title="版本更新" :value="version" :titleStyle="titleStyle" :arrow="false"
					@click="updateShow=true">
					<template #icon>
						<u-icon name="custom-icon-24px" customPrefix="custom-icon" size="46" color="#e98f36"></u-icon>
					</template>
				</u-cell-item>
				<u-cell-item bg-color="#FBFBFB" title="功能介绍" :titleStyle="titleStyle" :arrow="true">
					<template #icon>
						<u-icon name="custom-icon-gongnengdingyi" customPrefix="custom-icon" size="46" color="#2979ff">
						</u-icon>
					</template>
				</u-cell-item>
				<u-cell-item bg-color="#FBFBFB" title="官网" :titleStyle="titleStyle" :arrow="true"
					@click="toWebView('深龙软件','http://www.siloon.com/')">
					<template #icon>
						<u-icon name="custom-icon-guanwang" customPrefix="custom-icon" size="46" color="#2979ff">
						</u-icon>
					</template>
				</u-cell-item>
				<u-cell-item bg-color="#FBFBFB" title="帮助" :titleStyle="titleStyle" :arrow="true">
					<template #icon>
						<u-icon name="custom-icon-bangzhu" customPrefix="custom-icon" size="46" color="#2979ff">
						</u-icon>
					</template>
				</u-cell-item>
				<u-cell-item bg-color="#FBFBFB" title="反馈" :titleStyle="titleStyle" :arrow="true">
					<template #icon>
						<u-icon name="custom-icon-fankui" customPrefix="custom-icon" size="46" color="#2979ff"></u-icon>
					</template>
				</u-cell-item>
			</u-cell-group>
		</view>
		<u-modal v-model="updateShow" content="发现新版本" show-cancel-button confirm-text="新版升级" cancel-text="取消">
		</u-modal>
	</view>
</template>

<script lang="ts" setup>
	import {
		reactive,
		ref
	} from "vue";
	const titleStyle = reactive({
		'marginLeft': '20rpx',
		'fontSize': '28rpx'
	})
	const updateShow = ref < boolean > (false)
	const version = ref<any>('')
	const toWebView = (title:string,path:string) => {
		uni.$u.route({
			url: 'pages/mine/aboutUs/webView',
			params: {
				title:title,
				url:path
			}
		})
	}
</script>

<style lang="scss" scoped>
	.logo {
		width: 100%;
		padding-top: 40rpx;
		text-align: center;

		.version {
			color: #91949F;
			padding-top: 10rpx;
			font-size: 28rpx;
			line-height: 40rpx;

		}
	}

	.cell {
		margin-top: 30rpx;
	}
</style>
