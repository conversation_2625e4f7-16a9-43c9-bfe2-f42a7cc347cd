<template>
	<view class="main">
		<view class="w-card" style="background-image: url('static/img/icons/waterWorks-bg1.png')"
			@click="toWaterVolume">
			<text>水量报表</text>
		</view>
		<view class="w-card" style="background-image: url('static/img/icons/waterWorks-bg2.png')"
			@click="toEnergyConsumption">
			<text>能耗报表</text>
		</view>
		<view class="w-card" style="background-image: url('static/img/icons/waterWorks-bg3.png')"
			@click="toUnitConsumption">
			<text>单耗报表</text>
		</view>
	</view>
</template>

<script lang="ts" setup>
	// 水量报表
	const toWaterVolume = () => {
		uni.$u.route({
			url: 'pages/wisdomProduce/secondSupplyReport/waterVolumeReport/index'
		})
	}
	// 能耗报表 
	const toEnergyConsumption = () => {
		uni.$u.route({
			url: 'pages/wisdomProduce/secondSupplyReport/energyConsumptionReport/index'
		})
	}
	// 单耗报表 
	const toUnitConsumption = () => {
		uni.$u.route({
			url: 'pages/wisdomProduce/secondSupplyReport/unitConsumptionReport/index'
		})
	}
</script>

<style lang="scss" scoped>
	.w-card {
		height: 240rpx;
		width: 686rpx;
		border-radius: 16rpx;
		background-color: red;
		margin: 10rpx auto;
		background-size: 100% 100%;
		line-height: 240rpx;
		text-align: center;

		text {
			font-size: 34rpx;
			color: #FFFFFF;
		}
	}
</style>
