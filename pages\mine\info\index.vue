<template>
	<view class="main" style="overflow: hidden;">
		<view class="info-content">
			<u-form ref="uForm" labelWidth="180" :labelStyle="{color:'#91949F'}">
				<u-form-item label="人员账号：">
					{{userInfo.email}}
				</u-form-item>
				<u-form-item label="人员名称：">
					{{userInfo.firstName}}
				</u-form-item>
				<u-form-item label="所属部门：">
					{{userInfo.departmentName || '-'}}
				</u-form-item>
				<u-form-item label="所属单位：">
					{{userInfo.organizationName || '-'}}
				</u-form-item>
				<u-form-item label="联系方式：">
					{{ userInfo.phone || '-'}}
					<template #right>
						<view class="flex-between" style="width: 140rpx;">
							<view class="icon-button " @click="callPhone">
								<u-icon name="phone" color="#FFFFFF" size="40"></u-icon>
							</view>
							<view class="icon-button " @click="show=true">
								<u-icon name="edit-pen" color="#FFFFFF" size="40"></u-icon>
							</view>
						</view>
					</template>
				</u-form-item>
			</u-form>
		</view>
		<u-toast ref="refToast" />
		<u-modal ref="refuModal" v-model="show" title="修改手机号" :async-close="true" cancel-text="取消" show-cancel-button confirm-text="修改" cancel="show=false"
			@confirm="updatePhone">
			<u-input v-model="phone" placeholder="请输入手机号" style="width: 90%;margin: 40rpx auto;" border
				maxlength="11"></u-input>
		</u-modal>
	</view>
</template>

<script lang="ts" setup>
	import {
		onMounted,
		ref
	} from "vue";
	import {
		updateUser
	} from '@/common/api/login'
	const userInfo = ref<any>({})
	const show = ref<boolean>(false)
	const phone = ref<any>({})
	const refToast = ref<any>()
	const refuModal = ref<any>()
	onMounted(async () => {
		userInfo.value = uni.getStorageSync("userInfo")
		phone.value = userInfo.value.phone
	})
	// 打电话
	const callPhone = () => {
		uni.makePhoneCall({
			phoneNumber: userInfo.value.phone, //仅为示例
			success: (res) => {
				//uni.$u.toast(res)

			},
			fail: (err) => {
				//uni.$u.toast(err)
			}
		});
	}
	// 修改手机号
	const updatePhone = () => {
		console.log(userInfo.value)
		const phoneRegex = /^1[3-9]\d{9}$/; // 正则表达式匹配中国大陆手机号
		if (phoneRegex.test(phone.value)) {
			updateUser({ ...userInfo.value, phone: phone.value }).then(res => {
				show.value = false;
				refToast.value?.show({
					title: '保存成功',
					position: 'bottom',
					type: 'success'
				})
				userInfo.value.phone = phone.value
				uni.setStorageSync("userInfo", { ...userInfo.value, phone: phone.value })
			}).catch(res => {
				refToast.value?.show({
					title: '保存失败',
					position: 'bottom',
					type: 'error'
				})
			})
		}else{
			refToast.value?.show({
				title: '填写正确的手机号',
				position: 'bottom',
				type: 'warning'
			})
		}
		refuModal.value.clearLoading();
	}
</script>

<style lang="scss" scoped>
	.main {
		padding: 20rpx 32rpx;

		.info-content {
			background-color: #FFFFFF;
			min-height: 300rpx;
			padding: 20rpx;
		}

		.icon-button {
			width: 60rpx;
			height: 60rpx;
			text-align: center;
			line-height: 62rpx;
			border-radius: 50%;

			&:nth-child(1) {
				background-color: #2979ff;
			}

			&:nth-child(2) {
				background-color: #ffaa00;
			}
		}
	}
</style>