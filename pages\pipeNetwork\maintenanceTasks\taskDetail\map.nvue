<template>
	<view class="map">
		<map id="map" :enable-satellite="state.enableSatellite" ref="map" :scale="state.scale" :polygons="markerPolygon"
			:polyline="markerPolyline" :style="{height: state.windowHeight}" :markers="markerPoints"
			:latitude="state.latitude" :longitude="state.longitude" :show-location="false" @markertap="markertap"
			@tap="poitap" enable-rotate enable-overlooking enable-poi>

			<view class="cover-view-plus" :style="{top:state.barHeight + 20}">
				<block v-for="(menu,index) in menus" :key="index">
					<view class="plus-btn" @click="chooseMapType(menu)">
						<text class="iconfont btn-img"
							:style="{'color':state.mapType === menu.value?'#3862F8':''}">{{menu.icon}}</text>
						<text style="font-size: 24rpx;"
							:style="{'color':state.mapType === menu.value?'#3862F8':''}">{{menu.name}}</text>
					</view>
				</block>
				<view class="btn-line"></view>
				<view class="plus-btn" @click="clearMap">
					<text class="iconfont btn-img">&#xe622;</text>
					<text style="font-size: 24rpx;">清空</text>
				</view>
			</view>

			<view class="cover-view-loca" :style="{'bottom':localIcon}" @click="getLocation">
				<view class="cover-loca-image">
					<image src="/static/img/icons/location-black.png" mode="widthFix"></image>
				</view>
			</view>

			<cover-view class="cover-view-menu border-box" v-if="state.showDefaultMenus">
				<view class="search-box">

				</view>
				<view class="menu-list flex-around">
					<view v-for="(layer,index) in defaultMenus" :key="index" class="layer-box" style="">
						<text class="layer-title">
							{{layer.name}}
						</text>
						<view class="layer-menu">
							<view style="width: 138rpx;align-items: center;" v-for="(menu,i) in layer.list" :key="i"
								@click="selectLayer(menu,layer)">
								<text
									:class="['layer-menu-icon','iconfont',{'checked-menu-icon':selectMenus.includes(menu.name) || state.activeAll}]">{{menu.iconUnicode}}</text>
								<text class="layer-menu-text">{{menu.name}}</text>
							</view>
						</view>
					</view>
					<!-- <view v-for="(menu,index) in defaultMenus" :key="index" class="menu" @click="chooseMapType(menu)">
						<image class="menu-btn-img" :src="menu.icon" mode="widthFix"></image>
						<text :class="['menu-text',{'menu-text-color':menu.value === state.mapType}]">{{menu.name}}</text>
					</view> -->
				</view>
			</cover-view>
			<!-- 显示底图 -->
			<view class="cover-view-drawing border-box" v-if="state.showDrawing">
				<view class="title-close">
					<text class="d-title">选择底图</text>
					<view style="width: 50rpx;" @click="state.showDrawing=false">
						X
					</view>
				</view>
				<view class="drawing-box" style="">
					<view @click="state.enableSatellite=false">
						<image :class="['drawing-img',{'check-border':!state.enableSatellite}]"
							src="/static/img/mapIcons/rectangle-1.png" mode="widthFix"></image>
						<view class="d-text">
							<text style="font-size: 24rpx;line-height: 48rpx;">标准</text>
						</view>
					</view>
					<view @click="state.enableSatellite=true">
						<image :class="['drawing-img',{'check-border':state.enableSatellite}]"
							src="/static/img/mapIcons/rectangle-2.png" mode="widthFix"></image>
						<view class="d-text">
							<text style="font-size: 24rpx;line-height: 48rpx;">卫星</text>
						</view>
					</view>
				</view>
			</view>
			<!-- 显示图层 -->
			<view class="cover-view-layer border-box" @touchmove.stop.prevent="true" v-if="state.showLayer">
				<view class="title-close">
					<text class="d-title">图层</text>
					<view style="width: 50rpx;" @click="state.showLayer=false">
						X
					</view>
				</view>
				<view class="check-box">
					<!-- <u-checkbox-group v-model="state.allLayer" @change="checkboxChange">
					<u-checkbox size="14" labelSize="14" labelColor="#91949F" label="全选" name="all"></u-checkbox>
				</u-checkbox-group> -->
				</view>
				<view v-for="(layer,index) in layerMenusList" :key="index" class="layer-box" style="">
					<text class="layer-title">
						{{layer.name}}
					</text>
					<view class="layer-menu">
						<view style="width: 138rpx;align-items: center;" v-for="(menu,i) in layer.list" :key="i"
							@click="selectLayer(menu,layer)">
							<text
								:class="['layer-menu-icon','iconfont',{'checked-menu-icon':selectMenus.includes(menu.name) || state.activeAll}]">{{menu.iconUnicode}}</text>
							<text class="layer-menu-text">{{menu.name}}</text>
						</view>
					</view>
				</view>
			</view>
		</map>
	</view>
</template>

<script setup>
	import {
		layerList,
		defaultMenuList
	} from './data'
	import {
		computed,
		nextTick,
		onMounted,
		reactive
	} from "vue";

	let selectMenus = reactive < Array > ([])
	let layerMenusList = reactive < any > ([])
	let polygons = reactive < any > ([])
	layerMenusList = layerList()
	const domModule = weex.requireModule('dom')
	domModule.addRule('fontFace', {
		fontFamily: 'iconfont',
		src: "url('/static/font/iconfont.ttf')"
	})

	const state = reactive < {
		barHeight: Number, //动态高度初始值
		windowHeight: Number,
		needBack: Boolean,
		latitude: String,
		mapType: String,
		longitude: String,
		value: String,
		scale: Number,
		activeAll: Boolean,
		showDrawing: Boolean,
		showDefaultMenus: Boolean,
		showLayer: Boolean,
		polyline: Boolean,
		polygons: Boolean,
		enableSatellite: Boolean
	} > ({
		barHeight: null, //动态高度初始值
		windowHeight: null,
		needBack: true,
		latitude: '你的坐标',
		mapType: '',
		longitude: '你的坐标',
		value: '1223',
		scale: 16,
		activeAll: false,
		showDrawing: false,
		showDefaultMenus: false,
		showLayer: false,
		polyline: true,
		polygons: false,
		enableSatellite: false
	})

	const menus = reactive < any > ([{
		name: '底图',
		value: 'drawing',
		icon: '\ue631',
	}, {
		name: '图层',
		value: 'layer',
		icon: '\ue612',
	}, {
		name: '面积',
		value: 'area',
		icon: '\ue6a2',
	}, {
		name: '距离',
		value: 'distance',
		icon: '\uea07',
	}, ])

	const localIcon = computed(() => {
		if (state.showDrawing) {
			return '350rpx'
		} else if (state.showDefaultMenus) {
			return '600rpx'
		} else if (state.showLayer) {
			return '660rpx'
		} else {
			return '200rpx'
		}
	})
	const defaultMenus = reactive < any > (defaultMenuList())

	// 标记点
	const markerPoints = computed(() => {
		if (polygons.length <= 0) {
			return [];
		}
		let distance = 0
		const len = polygons.length;
		// 1.多边形区域端点
		const endPointMarkers = polygons.map(item => {
			return {
				id: Math.random()
					.toString()
					.substr(2),
				width: 16,
				height: 16,
				anchorX: 0.5,
				anchorY: 0.5,
				iconPath: '../../../static/img/icons/dw.png',
				...item
			};
		});

		if (state.mapType === 'buffer') {
			console.log('dddd')
		} else if (state.mapType === 'distance' || state.mapType === 'area') {
			// 2.多边形区域线段中心点
			let lineCenterMarkers = polygons.map((item, index) => {
				// 计算线段中心点经纬度
				let currentPoint = item,
					nextPoint,
					lineCenterLongitude,
					lineCenterLatitude,
					distance1;

				if (state.polyline) {
					if (index === len - 1) {
						nextPoint = polygons[index];
					} else {
						nextPoint = polygons[index + 1];
					}
					lineCenterLongitude = (currentPoint.longitude + nextPoint.longitude) / 2;
					lineCenterLatitude = (currentPoint.latitude + nextPoint.latitude) / 2;
					distance1 = calculateDistance(currentPoint.longitude, currentPoint.latitude, nextPoint
						.longitude, nextPoint.latitude);
					distance += Number(distance1)
					// distance = distance.toFixed(2)
					//显示线中间部分xxx米距离
					return {
						id: Math.random()
							.toString()
							.substr(2),
						label: {
							content: index === len - 1 ? `总长${distance.toFixed(2)} 米` : `${distance1} 米`,
							color: '#ff1166',
						},
						width: 2,
						height: 2,
						anchorX: 0.5,
						anchorY: 0.5,
						longitude: lineCenterLongitude,
						latitude: lineCenterLatitude
					};
				} else if (state.polygons) {
					if (index === len - 1) {
						nextPoint = polygons[index];
					} else {
						nextPoint = polygons[index + 1];
					}
					// 区域面积
					const area = calculateArea(
						polygons.map(item => {
							return {
								lat: item.latitude,
								lng: item.longitude
							};
						})
					)
					// const lalg = polygons.map(res => {
					// 	return [res.latitude, res.longitude]
					// })
					const centerPoint = center(polygons)
					if (area > 0) {

					}
					return {
						id: Math.random()
							.toString()
							.substr(2),
						label: {
							content: area > 0 ? `面积${(area.toFixed(2)/1000).toFixed(2)} 平方千米` : ' ',
							color: '#ff1166',
						},
						alpha: 0,
						width: 2,
						height: 2,
						anchorX: 0.5,
						anchorY: 0.5,
						longitude: centerPoint.lng,
						latitude: centerPoint.lat
					};
				} else {
					return {}
				}
			});
			return [...endPointMarkers, ...lineCenterMarkers];
		}
		return [...endPointMarkers]
	})
	//计算属性地图路线
	const markerPolyline = computed(() => {
		if (polygons.length >= 2 && state.polyline) {
			return [{
				points: [...polygons],
				color: '#ff0004',
				width: 4
			}];
		}
		return [];
	})

	//计算属性地图区域
	const markerPolygon = computed(() => {
		if (polygons.length >= 3 && state.polygons) {
			return [{
				points: [...polygons],
				fillColor: '#c89d6633', // 填充颜色
				strokeColor: '#ff0004', // 边框颜色
				strokeWidth: 4
			}];
		}
		return [];
	})

	const center = (pointArray) => {
		let lat = 0
		let lng = 0
		const num_coords = pointArray.length
		for (let i in pointArray) {
			lat += pointArray[i].latitude
			lng += pointArray[i].longitude
		}

		lat /= num_coords;
		lng /= num_coords;
		return {
			lat: lat,
			lng: lng
		}
	}
	//计算长度
	const calculateDistance = (lng1, lat1, lng2, lat2) => {
		lat1 = lat1 || 0;
		lng1 = lng1 || 0;
		lat2 = lat2 || 0;
		lng2 = lng2 || 0;

		var rad1 = (lat1 * Math.PI) / 180.0;
		var rad2 = (lat2 * Math.PI) / 180.0;
		var a = rad1 - rad2;
		var b = (lng1 * Math.PI) / 180.0 - (lng2 * Math.PI) / 180.0;
		var r = 6378137;
		var distance = r * 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(rad1) * Math.cos(rad2) *
			Math.pow(Math.sin(b / 2), 2)));
		return distance.toFixed(2);
	}
	//计算面积
	const calculateArea = (path) => {
		let radius = 6371009;
		let len = path.length;
		if (len < 3) return 0;
		let total = 0;
		let prev = path[len - 1];
		let prevTanLat = Math.tan((Math.PI / 2 - (prev.lat / 180) * Math.PI) / 2);
		let prevLng = (prev.lng / 180) * Math.PI;
		for (let i in path) {
			let tanLat = Math.tan((Math.PI / 2 - (path[i].lat / 180) * Math.PI) / 2);
			let lng = (path[i].lng / 180) * Math.PI;
			total += polarTriangleArea(tanLat, lng, prevTanLat, prevLng);
			prevTanLat = tanLat;
			prevLng = lng;
		}
		return Math.abs(total * (radius * radius));
	}
	// 面积辅助
	const polarTriangleArea = (tan1, lng1, tan2, lng2) => {
		let deltaLng = lng1 - lng2;
		let t = tan1 * tan2;
		return 2 * Math.atan2(t * Math.sin(deltaLng), 1 + t * Math.cos(deltaLng));
	}


	const markeMove = () => {
		nextTick(() => {
			state.scale = 16
			// 更新Markers点
			uni.createMapContext('map', this).translateMarker({
				markerId: 'myId',
				destination: {
					latitude: state.latitude,
					longitude: state.longitude
				},
			});
		})
	}
	// 获取手机信息及位置
	const fitNav = () => {
		// 获取手机信息
		let info = uni.getSystemInfoSync()
		//顶部高度
		state.barHeight = info.statusBarHeight
		state.windowHeight = info.windowHeight
		// console.log('尺寸', info)
	}
	//返回
	const back = () => {
		uni.navigateBack({
			delta: 1
		})
	}
	//保存
	const saveBtn = () => {
		console.log('保存')
	}
	//获取当前位置
	const getLocation = () => {
		uni.getLocation({
			type: 'gcj02',
			success: (res) => {
				state.longitude = res.longitude
				state.latitude = res.latitude
				// state.move()
				markeMove()
			}
		});
	}
	//移动到此位置
	const move = () => {
		nextTick(() => {
			state.scale = 16
			uni.createMapContext('map', this).moveToLocation({
				longitude: state.longitude,
				latitude: state.latitude,
			});
		})
	}
	// 选地图操作类型
	const chooseMapType = (data) => {
		state.mapType = data.value
		if (state.mapType === 'drawing') {
			handleShowDrawing()
		} else if (state.mapType === 'layer') {
			handleShowLayer()
		} else if (data.value === 'distance') {
			state.polygons = false
			state.polyline = true
			polygons.splice(0, polygons.length)
		} else if (data.value === 'area') {
			state.polygons = true
			state.polyline = false
			polygons.splice(0, polygons.length)
		}
	}

	const clearMap = () => {
		state.mapType = 'reset'
		polygons.splice(0, polygons.length)
	}
	// 显示图层列表
	const handleShowLayer = () => {
		state.showDrawing = false
		state.showLayer = true
	}
	// 切换底图类型
	const handleShowDrawing = () => {
		state.showLayer = false
		state.showDrawing = true
	}

	const checkboxChange = (item) => {
		if (item.length > 0) {
			layerMenusList.forEach(data => {
				data.list.forEach(item => {
					selectMenus.push(item.name)
				})
			})
		} else {
			selectMenus = reactive < Array > ([])
		}
	}

	const selectLayer = (item, layer) => {
		const i = selectMenus.indexOf(item.name)
		if (i === -1) {
			selectMenus.push(item.name)
		} else {
			selectMenus.splice(i, 1)
		}
		// const lay = layerMenusList.find(data => data.name === layer.name)
		// let data = lay.list.find(data => data.name === item.name)
		// data.checked = !item.checked
		// console.log(layerMenusList)
	}
	//缩小
	const sub = () => {
		state.scale -= 1
	}
	//标记点的点击事件
	const markertap = (e) => {
		// console.log('点击markertap', e)
		uni.openLocation({
			latitude: state.latitude,
			longitude: state.longitude,
			success: function() {
				// console.log('success');
			}
		});
	}

	let polyline = reactive < any > ([{
		points: [],
		color: '#000000',
		width: '2'
	}])

	//地图的点击事件
	const poitap = (e) => {
		// state.longitude = e.detail.longitude
		// state.latitude = e.detail.latitude
		if (state.polygons || state.polyline) {
			polygons.push(e.detail)
		}
		// markeMove()
	}

	onMounted(async () => {
		fitNav()
		getLocation()
	})
</script>


<style scoped>
	.iconfont {
		font-family: iconfont;
	}

	.my-top-nvue {
		width: 750rpx;
		height: 88rpx;
		background-color: #ffffff;
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: center;
		padding: 0 30rpx;

		/* 		border-width: 1px;
		border-style: solid;
		border-color: red; */
	}

	.my-top-nvue-back {
		width: 20rpx;
		height: 35rpx;
	}

	.my-top-nvue-words {
		font-size: 36rpx;
		color: #323232;
	}

	.my-top-nvue-save {
		font-size: 30rpx;
		color: #42c2a4;
	}

	.cover-view-loca {
		width: 80rpx;
		height: 80rpx;
		background-color: #ffffff;
		border-radius: 16rpx;
		position: absolute;
		bottom: 200rpx;
		right: 32rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.cover-loca-image {
		width: 40rpx;
		height: 40rpx;
	}


	.cover-view-plus {
		width: 90rpx;
		min-height: 192rpx;
		background-color: #ffffff;
		border-radius: 20rpx;
		position: absolute;
		top: 62rpx;
		right: 32rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.plus-btn {
		width: 90rpx;
		height: 90rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.btn-line {
		width: 80rpx;
		height: 1rpx;
		border-width: 1rpx;
		border-style: solid;
		border-color: #EBEDF6;
	}

	.btn-img {
		font-size: 48rpx;
	}

	.cover-view-menu {
		/* width: 750rpx; */
		z-index: 99999;
	}

	.search-box {
		height: 80rpx;
		width: 686rpx;
		position: relative;
		background-color: #FFFFFF;
		bottom: 60rpx;
		border-radius: 24rpx;
		border: 2rpx solid #EBEDF6;
	}

	.border-box {
		width: 750rpx;
		padding-top: 30rpx;
		z-index: 99999;
		background-color: #FFFFFF;
		position: absolute;
		bottom: 0;
		align-items: center;
		justify-content: space-around;
		box-shadow: 0 4rpx 30rpx rgba(178, 183, 199, 0.5);
		border-radius: 16rpx 16rpx 0rpx 0;
	}

	.menu-list {
		flex-direction: row;
		display: flex;
		justify-content: space-around;
		width: 750rpx;
	}

	.menu {
		text-align: center;
	}

	.menu-btn-img {
		width: 80rpx;
		height: 80rpx;
	}

	.menu-text {
		font-size: 24rpx;
		padding-top: 8rpx;
		text-align: center;
	}

	.menu-text-color {
		color: #3862F8;
		font-weight: bold;
		font-size: 24rpx;
	}

	.cover-view-drawing {
		/* width: 750rpx; */
	}

	.title-close {
		width: 692rpx;
		flex-direction: row;
		display: flex;
		justify-content: space-between;
	}

	.d-title {
		font-size: 34rpx;
		font-weight: 700;
	}

	.drawing-box {
		padding: 20rpx 32rpx;
		width: 750rpx;
		flex-direction: row;
		display: flex;
		justify-content: space-between;
	}

	.check-border {
		border: 2rpx solid #3862F8;
	}

	.drawing-img {
		height: 192rpx;
		width: 328rpx;
		border-radius: 16rpx;
		margin-top: 20rpx;
	}

	.d-text {
		width: 328rpx;
		height: 48rpx;
		position: absolute;
		bottom: 0rpx;
		padding-left: 18rpx;
		font-size: 10px;
		background: rgba(255, 255, 255, 0.8);
		border-radius: 0px 0px 16rpx 16rpx;
	}

	.cover-view-layer {
		/* width: 750rpx; */
	}

	.check-box {
		position: absolute;
		right: 32rpx;
		top: 60rpx;
	}

	.layer-box {
		width: 692rpx;
		position: relative;
	}

	.layer-title {
		font-size: 28rpx;
		font-weight: 400;
		padding-bottom: 20rpx;
		color: #91949F;
	}

	.layer-menu {
		border-radius: 16rpx;
		flex-direction: row;
		flex-wrap: wrap;
		padding-top: 20rpx;
		background-color: #FFFFFF;
	}

	.layer-menu-icon {
		font-size: 60rpx;
		color: #000;
		text-align: center;
		line-height: 80rpx;
		width: 80rpx;
		height: 80rpx;
		border-radius: 50%;
		background: #E2E3E5;
	}

	.checked-menu-icon {
		background: #3862F8;
		color: #FFF;
	}

	.layer-menu-text {
		font-size: 24rpx;
		color: #060F27;
		padding: 8rpx 0 28rpx 0;
		text-align: center;
	}
</style>
