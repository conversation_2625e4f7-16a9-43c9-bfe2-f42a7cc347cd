<template>
	<view class="main">
		<web-view :src="url"></web-view>
	</view>
</template>

<script lang="ts" setup>
import {
	onMounted,
	ref
} from 'vue'
const url = ref<string>()

onMounted(() => {
	var pages = getCurrentPages();
	var page = pages[pages.length - 1];
	const options = page.$page.options
	if (options.url) {
		url.value = decodeURIComponent(options.url) 
	}
})
</script>

<style>

</style>
