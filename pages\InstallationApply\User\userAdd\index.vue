<!-- 用户新增 -->
<template>
	<view class="i-main">
		<view class="detail">
			<u-form :model="form" ref="refForm" :label-style="{'color':'#91949F'}" borderBottom labelWidth="180"
				input-align="right" :error-type="['toast']">
				<view class="card-box">
					<u-form-item label="户号：" required prop="userNumber" borderBottom>
						<u-input placeholder="输入内容" v-model="form.userNumber" input-align="right">
						</u-input>
					</u-form-item>
					<u-form-item label="客户性质：" required prop="customerNature" borderBottom>
						<template #right>
							<u-icon name="arrow-right" size="28" color="#91949F"></u-icon>
						</template>
						<input inputmode="none" placeholder="请选择" v-model="form.customerNature" input-align="right"
							placeholder-class="placeholderClass" @click="clickShow('customerNatureIndex','customerNatureShow')">
						</input>
					</u-form-item>

					<u-form-item label="用水性质：" required prop="waterNature" borderBottom>
						<template #right>
							<u-icon name="arrow-right" size="28" color="#91949F"></u-icon>
						</template>
						<input inputmode="none" v-model="form.waterNature" placeholder="请选择" input-align="right"
							placeholder-class="placeholderClass" @click="clickShow('waterNatureIndex','waterNatureShow')">
						</input>
					</u-form-item>

					<u-form-item label="用水人数：" required prop="waterUsers" borderBottom>
						<u-input v-model="form.waterUsers" placeholder="输入内容" input-align="right">
						</u-input>
					</u-form-item>

					<u-form-item label="用户姓名：" required prop="userName" borderBottom>
						<u-input v-model="form.userName" placeholder="请输入" input-align="right">
						</u-input>
					</u-form-item>

					<u-form-item label="手机号码：" required prop="phone" borderBottom>
						<u-input v-model="form.phone" placeholder="输入内容" input-align="right">
						</u-input>
					</u-form-item>
					<u-form-item label="证件号码：" required prop="idNumber" borderBottom>
						<u-input v-model="form.idNumber" placeholder="输入内容" input-align="right">
						</u-input>
					</u-form-item>

					<u-form-item label="证件类型：" required prop="idType" borderBottom>
						<template #right>
							<u-icon name="arrow-right" size="28" color="#91949F"></u-icon>
						</template>
						<input inputmode="none" v-model="form.idType" placeholder="请选择" input-align="right"
							placeholder-class="placeholderClass" @click="clickShow('idTypeIndex','idTypeShow')">
						</input>
					</u-form-item>

					<u-form-item label="用户状态：" required prop="userStatus" borderBottom>
						<template #right>
							<u-icon name="arrow-right" size="28" color="#91949F"></u-icon>
						</template>
						<input inputmode="none" v-model="form.userStatus" placeholder="请选择" input-align="right"
							placeholder-class="placeholderClass" @click="clickShow('userStatusIndex','userStatusShow')">
						</input>
					</u-form-item>

					<u-form-item label="营业区域：" required prop="businessArea" borderBottom>
						<u-input v-model="form.businessArea" placeholder="请输入" input-align="right">
						</u-input>
					</u-form-item>

					<u-form-item label="小区名称：" required prop="communityName" borderBottom>
						<u-input v-model="form.communityName" placeholder="请输入" input-align="right">
						</u-input>
					</u-form-item>

					<u-form-item label="单元房号：" required prop="unitNumber" borderBottom>
						<u-input v-model="form.unitNumber" placeholder="请输入" input-align="right">
						</u-input>
					</u-form-item>

					<u-form-item label="用水地址：" required prop="waterAddress" borderBottom>
						<u-input v-model="form.waterAddress" placeholder="请输入" input-align="right">
						</u-input>
					</u-form-item>

					<u-form-item label="电子邮箱：" prop="email" borderBottom>
						<u-input v-model="form.email" placeholder="请输入" input-align="right">
						</u-input>
					</u-form-item>

					<u-form-item label="纳税人识别号：" prop="taxNumber" borderBottom>
						<u-input v-model="form.taxNumber" placeholder="请输入" input-align="right">
						</u-input>
					</u-form-item>

					<u-form-item label="供水日期：" required prop="waterSupplyDate" borderBottom>
						<template #right>
							<u-icon name="calendar" size="28" color="#91949F"></u-icon>
						</template>
						<input inputmode="none" v-model="form.waterSupplyDate" placeholder="请选择日期" input-align="right"
							placeholder-class="placeholderClass" @click="state.dateShow = true">
						</input>
					</u-form-item>

					<u-form-item label="计费方式：" required prop="billingMethod" borderBottom>
						<template #right>
							<u-icon name="arrow-right" size="28" color="#91949F"></u-icon>
						</template>
						<input inputmode="none" v-model="form.billingMethod" placeholder="请选择" input-align="right"
							placeholder-class="placeholderClass" @click="clickShow('billingMethodIndex','billingMethodShow')">
						</input>
					</u-form-item>
				</view>
			</u-form>
			<view class="card-box" style="padding: 0">
				<file-upload ref="refFileUpload" :hide-audio="true" :hide-video="true"></file-upload>
			</view>
		</view>

		<view class="button">
			<u-button type="default" color="#91949F" @click="cancel">取消</u-button>
			<u-button type="primary" color="#3862F8" @click="submit">确认</u-button>
		</view>
		
		<!-- 客户性质 -->
		<u-picker v-model="state.customerNatureShow" mode="selector" :default-selector="[0]" :range="customerNatures"
			range-key="name" @confirm="selectCustomerNature"></u-picker>
		<!-- 用水性质 -->
		<u-picker v-model="state.waterNatureShow" mode="selector" :default-selector="[0]" :range="waterNatures"
			range-key="name" @confirm="selectWaterNature"></u-picker>
		<!-- 证件类型 -->
		<u-picker v-model="state.idTypeShow" mode="selector" :default-selector="[0]" :range="idTypes"
			range-key="name" @confirm="selectIdType"></u-picker>
		<!-- 用户状态 -->
		<u-picker v-model="state.userStatusShow" mode="selector" :default-selector="[0]" :range="userStatuses"
			range-key="name" @confirm="selectUserStatus"></u-picker>
		<!-- 计费方式 -->
		<u-picker v-model="state.billingMethodShow" mode="selector" :default-selector="[0]" :range="billingMethods"
			range-key="name" @confirm="selectBillingMethod"></u-picker>
		<!-- 供水日期 -->
		<u-picker v-model="state.dateShow" mode="date" @confirm="selectDate"></u-picker>
		
		<u-toast ref="refToast" />
	</view>
</template>

<script lang="ts" setup>
	import {
		onReady,
		onShow
	} from '@dcloudio/uni-app';
	import {
		onBeforeMount,
		computed,
		onMounted,
		reactive,
		ref
	} from "vue";
	import fileUpload from '@/components/fileUpload/fileUpload.vue';

	// 声明uni全局变量
	declare const uni: any;

	const state = reactive<{
		customerNatureShow : boolean,
		waterNatureShow : boolean,
		idTypeShow : boolean,
		userStatusShow : boolean,
		billingMethodShow : boolean,
		dateShow : boolean,
		customerNatureIndex : any,
		waterNatureIndex : any,
		idTypeIndex : any,
		userStatusIndex : any,
		billingMethodIndex : any,
	}>({
		customerNatureShow: false,
		waterNatureShow: false,
		idTypeShow: false,
		userStatusShow: false,
		billingMethodShow: false,
		dateShow: false,
		customerNatureIndex: [0],
		waterNatureIndex: [0],
		idTypeIndex: [0],
		userStatusIndex: [0],
		billingMethodIndex: [0],
	})

	const rules = reactive<any>({
		userNumber: [{
			required: true,
			message: '请输入户号',
		}],
		customerNature: [{
			required: true,
			message: '请选择客户性质',
		}],
		waterNature: [{
			required: true,
			message: '请选择用水性质',
		}],
		waterUsers: [{
			required: true,
			message: '请输入用水人数',
		}],
		userName: [{
			required: true,
			message: '请输入用户姓名',
		}],
		phone: [{
			required: true,
			message: '请输入手机号码',
		}],
		idNumber: [{
			required: true,
			message: '请输入证件号码',
		}],
		idType: [{
			required: true,
			message: '请选择证件类型',
		}],
		userStatus: [{
			required: true,
			message: '请选择用户状态',
		}],
		businessArea: [{
			required: true,
			message: '请输入营业区域',
		}],
		communityName: [{
			required: true,
			message: '请输入小区名称',
		}],
		unitNumber: [{
			required: true,
			message: '请输入单元房号',
		}],
		waterAddress: [{
			required: true,
			message: '请输入用水地址',
		}],
		waterSupplyDate: [{
			required: true,
			message: '请选择供水日期',
		}],
		billingMethod: [{
			required: true,
			message: '请选择计费方式',
		}],
	})
	
	const refToast = ref<any>();
	const refForm = ref<any>({})
	const refFileUpload = ref<any>({})
	
	// 下拉数据
	const customerNatures = ref<any>([
		{ name: '居民', value: '1' },
		{ name: '商业', value: '2' },
		{ name: '工业', value: '3' },
	])
	
	const waterNatures = ref<any>([
		{ name: '生活用水', value: '1' },
		{ name: '商业用水', value: '2' },
		{ name: '工业用水', value: '3' },
	])
	
	const idTypes = ref<any>([
		{ name: '身份证', value: '1' },
		{ name: '护照', value: '2' },
		{ name: '营业执照', value: '3' },
	])
	
	const userStatuses = ref<any>([
		{ name: '正常', value: '1' },
		{ name: '停用', value: '2' },
		{ name: '暂停', value: '3' },
	])
	
	const billingMethods = ref<any>([
		{ name: '抄表计费', value: '1' },
		{ name: '定额计费', value: '2' },
		{ name: '包干计费', value: '3' },
	])

	const form = reactive<any>({
		userNumber: '',
		customerNature: '',
		waterNature: '',
		waterUsers: '',
		userName: '',
		phone: '',
		idNumber: '',
		idType: '',
		userStatus: '',
		businessArea: '',
		communityName: '',
		unitNumber: '',
		waterAddress: '',
		email: '',
		taxNumber: '',
		waterSupplyDate: '',
		billingMethod: '',
	})

	const clickShow = (indexName: string, typeName: string) => {
		state[indexName] = [0]
		state[typeName] = true
	}

	// 选择客户性质
	const selectCustomerNature = (val : any) => {
		state.customerNatureIndex = val
		const type = customerNatures.value[val[0]]
		form.customerNature = type.name
	}
	
	// 选择用水性质
	const selectWaterNature = (val : any) => {
		state.waterNatureIndex = val
		const type = waterNatures.value[val[0]]
		form.waterNature = type.name
	}
	
	// 选择证件类型
	const selectIdType = (val : any) => {
		state.idTypeIndex = val
		const type = idTypes.value[val[0]]
		form.idType = type.name
	}
	
	// 选择用户状态
	const selectUserStatus = (val : any) => {
		state.userStatusIndex = val
		const type = userStatuses.value[val[0]]
		form.userStatus = type.name
	}
	
	// 选择计费方式
	const selectBillingMethod = (val : any) => {
		state.billingMethodIndex = val
		const type = billingMethods.value[val[0]]
		form.billingMethod = type.name
	}
	
	// 选择日期
	const selectDate = (val : any) => {
		console.log('选择的日期:', val)
		form.waterSupplyDate = val
	}

	// 取消
	const cancel = () => {
		uni.navigateBack()
	}

	//提交用户
	const submit = () => {
		refForm.value.validate((valid : boolean) => {
			if (valid) {
				uni.showModal({
					title: '提示',
					content: '确定提交',
					success: function (res) {
						if (res.confirm) {
							const params = {
								...form,
								// 只包含文件和图片上传，不包含录音
								files: refFileUpload.value.fileList?.join(',') || '',
								images: refFileUpload.value.imageList?.join(',') || '',
							}
							console.log('提交数据', params)
							// 这里调用API提交数据
							refToast.value.show({
								title: '提交成功',
								type: 'success',
								position: 'bottom',
								back: true
							})
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			} else {
				console.log('验证失败');
			}
		})
	}

	onReady(() => {
		refForm.value.setRules(rules);
	})

	onShow(async () => {
		// 页面显示时的逻辑
	})
</script>

<style lang="scss" scoped>
	.i-main {
		background: #F9F9F9;
		font-size: 24rpx;
		color: #000000;
	}

	.detail {
		padding-bottom: 100rpx;
		background-color: #F9F9F9;
		height: 100%;

		.card-box {
			width: 686rpx;
			margin: 0 auto;
			margin-top: 10px;
			border-radius: 16rpx;
			padding: 12rpx 28rpx;
			background-color: #FFFFFF;
			min-height: 112rpx;

			.icon {
				color: #3862F8;
			}

		}

		.content-card {
			width: 686rpx;
			margin: 20rpx auto;
			background-color: #FFFFFF;
			min-height: 224rpx;
			border-radius: 8px;
			padding: 24rpx 0 0 28rpx;

			.title-text {
				color: #91949F;
				font-size: 28rpx;
			}

			.file-s {
				width: 116rpx;
				height: 116rpx;
				display: flex;
				justify-content: center;
				flex-direction: column;
				text-align: center;
				background: #F9F9F9;
				position: relative;

				text {
					color: #91949F;
					font-size: 20rpx;
				}

				.close-icon {
					border-radius: 50%;
					width: 32rpx;
					height: 32rpx;
					background-color: red;
					line-height: 32rpx;
					text-align: center;
					position: absolute;
					right: 0;
					top: 0;
				}
			}

			.margin-center {
				margin: 0 auto;

				.icon {
					font-size: 48rpx;
				}
			}
		}

		.cc {
			width: 160rpx;
			height: 80rpx;
			background-color: #dedede;
			margin: 4rpx;
		}
	}

	.button {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		padding: 16rpx 32rpx;
		background: #FFFFFF;
		display: flex;
		gap: 16rpx;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
		
		u-button {
			flex: 1;
		}
	}

	// .address {
	// 	width: 50%;
	// 	overflow: hidden;
	// 	white-space: nowrap;
	// 	text-overflow: ellipsis;
	// }
	.map {
		height: 80rpx;
		width: 100%;
	}

	::v-deep .u-form-item__body {
		padding: 16rpx;
	}

	::v-deep .u-form-item {
		padding: 0;
	}
</style>