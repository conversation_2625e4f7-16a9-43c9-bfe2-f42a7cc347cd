<template>
	<view class="main">
		<view class="card-box">
			<u-form v-if="uploadContent.length" ref="form1" :label-style="{'color':'#91949F'}" borderBottom
				labelWidth="180">
				<u-form-item :label="'设备类型:'"
					:prop="'layer'">
					<u-input disabled :placeholder="'--'" v-model="state.uploadInfo.layer" inputAlign="right">
					</u-input>
				</u-form-item>
				<u-form-item v-for="(field,i) in uploadContent" :key="i" :label="(field.alia||field.name)+':'"
					:prop="field.name">
					<u-input disabled  :placeholder="'--'" v-model="field.newvalue" inputAlign="right">
					</u-input>
				</u-form-item>
				<u-form-item :label="'备注:'"
					:prop="'remark'">
					<u-input disabled :placeholder="'--'" v-model="state.uploadInfo.remark" inputAlign="right">
					</u-input>
				</u-form-item>

			</u-form>
			<u-empty v-else></u-empty>
		</view>
		<view class="button">
			<u-button type="primary" color="#3862F8" @click="goBack">返回</u-button>
		</view>
	</view>
</template>

<script lang="ts" setup>
	import {
		onLoad
	} from '@dcloudio/uni-app'
	import {
		computed,
		reactive
	} from "vue";
	const state = reactive<{
		uploadInfo : any
	}>({
		uploadInfo: {}
	})
	const uploadContent = computed(() => {
		return state.uploadInfo.uploadContent && JSON.parse(state.uploadInfo.uploadContent) || []
	})
	const goBack = () => {
		uni.navigateBack()
	}
	onLoad((options : any) => {
		state.uploadInfo = JSON.parse(decodeURIComponent(options.uploadInfo))
	})
</script>

<style lang="scss" scoped>
	.main {
		padding-top: 20rpx;

		.card-box {
			width: 686rpx;
			border-radius: 8px;
			padding: 20rpx 28rpx;
			margin: 0 auto;
			color: #FFFFFF;
			background-color: #FFFFFF;

			.form {
				padding: 0 28rpx 10rpx 28rpx;
				color: #060F27;
			}

			.l-file {
				padding: 10rpx 0;
			}
		}

		::v-deep.u-form-item {
			padding: 0;
		}
	}
</style>