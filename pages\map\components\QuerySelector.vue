<template>
  <view class="query-selector-overlay" @click="$emit('close')">
    <view class="query-selector" @click.stop>
      <view class="selector-header">
        <text class="selector-title">选择查询方式</text>
        <text class="close-btn" @click="$emit('close')">×</text>
      </view>
      <view class="query-options">
        <view 
          class="query-option"
          @click="selectQuery('point')"
        >
          <view class="option-icon">
            <text class="custom-icon custom-icon-dianji"></text>
          </view>
          <text class="option-text">点击查询</text>
          <text class="option-desc">点击地图任意位置查询管网数据</text>
        </view>
        <view 
          class="query-option"
          @click="selectQuery('range')"
        >
          <view class="option-icon">
            <text class="custom-icon custom-icon-fanwei"></text>
          </view>
          <text class="option-text">范围查询</text>
          <text class="option-desc">指定范围内管网数据查询</text>
        </view>
        <view 
          class="query-option"
          @click="selectQuery('condition')"
        >
          <view class="option-icon">
            <text class="custom-icon custom-icon-tiaojian"></text>
          </view>
          <text class="option-text">条件查询</text>
          <text class="option-desc">指定条件下管网数据查询</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'QuerySelector',
  emits: ['close', 'select'],
  methods: {
    selectQuery(type) {
      this.$emit('select', type)
      this.$emit('close')
    }
  }
}
</script>

<style scoped lang="scss">
.query-selector-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  z-index: 1000;
}

.query-selector {
  width: 100%;
  background-color: #ffffff;
  border-radius: 16rpx 16rpx 0 0;
  padding: 40rpx 32rpx 60rpx;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.selector-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40rpx;
  
  .selector-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #060F27;
  }
  
  .close-btn {
    font-size: 48rpx;
    color: #91949F;
    width: 48rpx;
    height: 48rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.query-options {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.query-option {
  display: flex;
  align-items: center;
  padding: 32rpx 24rpx;
  border-radius: 16rpx;
  background-color: #F7F8FA;
  
  &:active {
    background-color: #E2E3E5;
  }
  
  .option-icon {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    background-color: #3862F8;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 24rpx;
    flex-shrink: 0;
    
    .custom-icon {
      color: #ffffff;
      font-size: 40rpx;
    }
  }
  
  .option-text {
    font-size: 32rpx;
    font-weight: 600;
    color: #060F27;
    margin-bottom: 8rpx;
  }
  
  .option-desc {
    font-size: 24rpx;
    color: #91949F;
    line-height: 1.4;
  }
}
</style>