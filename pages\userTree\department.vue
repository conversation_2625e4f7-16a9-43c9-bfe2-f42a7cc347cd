<template>
	<view class="main">
		<u-navbar :border-bottom="false">
			<u-search placeholder="可输入人员信息查询" v-model="state.name" @search="searchUser" @custom="searchUser"></u-search>
		</u-navbar>
		<view class="content">
			<u-cell-group v-if="departments.length>0">
				<u-cell-item :title="item.name" v-for="(item,index) in departments" :key="index"
					@click="showPersonnel(item)">
				</u-cell-item>
			</u-cell-group>
			<u-empty v-else></u-empty>
		</view>
		<u-popup v-model="state.show" mode="center" height="700rpx" width="550rpx">
			<scroll-view scroll-y>
				<u-cell-group v-if="users.length>0">
					<u-cell-item v-for="(item,index) in users" :key="index" @click="chooseUser(item)">
						<template #title>
							{{item.organizationName}}/{{item.departmentName}}/{{item.firstName}}
						</template>
					</u-cell-item>
				</u-cell-group>
			</scroll-view>
		</u-popup>
	</view>
</template>

<script lang="ts" setup>
	import {
		storeToRefs
	} from 'pinia'
	import {
		useStore
	} from '@/store/index'
	import {
		onMounted,
		reactive,
		ref
	} from "vue";
	import {
		departmentList,
		getAllByPid,
	} from '@/common/api/workOrder'
	const store = useStore()
	const departments = ref < any > ([])
	const users = ref < any > ([])
	const state = reactive < {
		show: boolean,
		pid: string,
		key: string,
		name: string,
	} > ({
		show: false,
		pid: '',
		key: '',
		name: ''
	})

	// 显示组织下的部门
	const getDepartmentList = async (pid: string) => {
		const res = await departmentList({
			parentId: pid
		})
		departments.value = res.data?.data
	}

	const searchUser = async () => {
		const res = await getAllByPid({
			pid: state.pid,
			name: state.name,
			page: 1,
			size: 9999,
			status: 1
		})
		users.value = res.data?.data?.data
		console.log(users.value)
		if (users.value.length > 0) {
			state.show = true
		} else {
			uni.$u.toast('无此人员')
		}
	}

	//
	const chooseUser = (data: any) => {
		// console.log(data)
		// const pages = getCurrentPages();
		// console.log(pages)
		// const page = pages[pages.length - 4]
		// page.$vm.chooseUser(data)
		let { userData }  = storeToRefs(store);
		userData.value = data
		state.show = false
		uni.navigateBack({
			delta: 2
		})
	}

	// 
	const showPersonnel = (data: any) => {
		uni.$u.route({
			url: 'pages/userTree/personnel',
			params: {
				pid: data.id,
				key: state.key
			}
		})
	}

	onMounted(() => {
		var pages = getCurrentPages();
		var page = pages[pages.length - 1];
		state.pid = page.$page.options.pid
		state.key = page.$page.options.key || ''
		getDepartmentList(state.pid)
	})
</script>

<style lang="scss" scoped>
	.content {
		height: 90vh;
		padding: 20rpx 32rpx;

	}
</style>
