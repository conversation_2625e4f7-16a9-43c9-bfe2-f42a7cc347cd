<template>
	<view>
		<u-navbar fixed placeholder safeAreaInsetTop bgColor="#FFFFFF" title="工单查询" :autoBack="true" leftIconSize="20">
			<template #right>
				<view class="nv-right" @click="showScreen">
					<image src="/static/img/icons/shaixuan-black.png" style="height: 48rpx;width: 48rpx;"></image>
				</view>
			</template>
		</u-navbar>
		<!-- <scroll-view style="height: 100vh;" :lower-threshold="50" :refresher-threshold="50" scroll-y refresher-enabled
			refresher-default-style="white" :refresher-triggered="triggered" @refresherrefresh="onRefresh"
			@scrolltolower="showMoreData"> -->
		<view class="card-box" v-for="(data,index) in tableData" :key="index" @click="toDetail(data)">
			<view class="flex-between hand">
				<view class="hand-title flex-center">
					<view class="icon-box">
						<u-icon name="custom-icon-gongdan" customPrefix="custom-icon" color="#FFFFFF" size="20">
						</u-icon>
					</view>
					<text>{{data.title}}</text>
				</view>
			</view>
			<view class="steps">
				<u-steps :list="numList" :current="getCurrentStep(data.status)"></u-steps>
			</view>
			<view class="table">
				<view class="info">
					<text>工单编号：</text> <text>{{data.serialNo}}</text>
				</view>
				<view class="info">
					<text>发起人员：</text> <text>{{data.uploadUserName}}</text>
				</view>
				<view class="info">
					<text>地址：</text> <text>{{data.address}}</text>
				</view>
			</view>
		</view>
		<u-loadmore bg-color="F9F9F9" :status="state.status" loading-text="努力加载中" loadmore-text="加载更多"
			nomore-text="没有了更多" />
		<!-- </scroll-view> -->
		<u-popup mode="right" width="528" v-model="state.screenShow" closeOnClickOverlay overlay
			@close="state.screenShow = false">
			<view class="popup">
				<view class="screen-list">
					<u-form :model="screenForm" ref="form1" :label-style="{'color':'#91949F'}" border-bottom input-align="right"
						label-width="180">
						<u-form-item label="快速查找：" prop="screenForm.keyword">
							<u-input placeholder="编号/标题/摘要/地址/电话" v-model="screenForm.keyword">
							</u-input>
						</u-form-item>
						<u-form-item label="开始时间：" prop="screenForm.fromTime">
							<input inputmode="none" placeholder-class="placeholderClass" placeholder="点击选择"
								v-model="screenForm.fromTime" @click="chooseDate('start')">
							</input>
							<template #right>
								<u-icon name="calendar" color="#91949F" size="34"></u-icon>
							</template>
						</u-form-item>
						<u-form-item label="结束时间：" prop="screenForm.toTime">
							<input inputmode="none" placeholder-class="placeholderClass" placeholder="点击选择"
								v-model="screenForm.toTime" @click="chooseDate('end')">
							</input>
							<template #right>
								<u-icon name="calendar" color="#91949F" size="34"></u-icon>
							</template>
						</u-form-item>

						<u-form-item label="工单状态：" prop="screenForm.statusName" :borderBottom="false">
						</u-form-item>
						<u-form-item label="" prop="screenForm.statusName" :borderBottom="false">
							<u-grid :col="3" :border="false">
								<u-grid-item :custom-style="{'padding-bottom': '6rpx'}"
									v-for="(item,index) in state.searchWorkSteps" :key="index">
									<view :class="['grid-box',{'grid-box-checked':currentStep===item.value}] "
										@click="checkStep(item)">{{item.name}}</view>
								</u-grid-item>
							</u-grid>
						</u-form-item>
					</u-form>
				</view>
				<view class="popup-button">
					<u-button type="primary" color="#3862F8" @click="submitScreen">确定</u-button>
				</view>
			</view>
		</u-popup>

		<!-- 日期选择 -->
		<u-calendar :minDate="minDate" v-model="state.calendarShow" ref="calendar" @change="confirmDate">
		</u-calendar>
	</view>
</template>

<script lang="ts" setup>
	import {
		onMounted,
		reactive,
		ref,
		getCurrentInstance
	} from "vue"
	import {
		onPullDownRefresh,
		onReachBottom
	} from '@dcloudio/uni-app'
	import dayjs from 'dayjs'
	import {
		workOrderSteps
	} from '@/common/data/publicData'
	import {
		workOrderList
	} from '@/common/api/workOrder'
	const state = reactive<{
		screenShow : boolean,
		calendarShow : boolean,
		showDateType : string,
		status : string,
		searchWorkSteps : any,
	}>({
		screenShow: false,
		calendarShow: false,
		showDateType: '',
		status: 'loadmore',
		searchWorkSteps: workOrderSteps
	})

	const {
		proxy
	} = getCurrentInstance()
	const triggered = ref<boolean>()
	const minDate = ref<string>(proxy.$startDate)
	const currentStep = ref<String>('')
	const numList = ref<any>([{
		name: '上报',
		value: 'PENDING'
	}, {
		name: '分派',
		value: 'ASSIGN'
	}, {
		name: '接收',
		value: 'RESOLVING'
	}, {
		name: '到场',
		value: 'ARRIVING'
	}, {
		name: '处理',
		value: 'PROCESSING'
	}, {
		name: '完成',
		value: 'SUBMIT'
	}, {
		name: '审核通过',
		value: 'APPROVED'
	}])

	const getCurrentStep = (step : String) => {
		let stepNum : number = 0
		numList.value.map((item : any, index : number) => {
			if (item.value === step) {
				stepNum = index
			}
		})

		return stepNum
	}

	// 选择区域
	const screenForm = reactive<any>({
		fromTime: dayjs().add(-30, 'day').format('YYYY-MM-DD'),
		toTime: dayjs().format('YYYY-MM-DD'),
		page: 1,
		size: 10
	})
	const tableData = ref<any>([])

	const checkStep = (step : any) => {
		currentStep.value = step.value
		// const index = checkSteps.value.indexOf(step.name)
		// console.log(index)
		// if (index != -1) {
		// 	checkSteps.value.splice(index, 1)
		// } else {
		// 	checkSteps.value.push(step.name)
		// }
	}
	const chooseDate = (type : string) => {
		state.calendarShow = true
		if (type === 'end') {
			minDate.value = screenForm.fromTime
		} else {
			minDate.value = proxy.$startDate
		}
		state.showDateType = type
	}

	// 加载更多
	const showMoreData = async () => {
		state.status = 'loading'
		await getWorkOrderList()
	} ///

	// 下拉刷新
	const onRefresh = async () => {
		triggered.value = true
		screenForm.page = 1
		await getWorkOrderList()
	}

	// 确定搜索
	const submitScreen = () => {
		state.screenShow = false
		tableData.value = []
		screenForm.page = 1
		getWorkOrderList()
	}

	const confirmDate = (date : any) => {
		state.calendarShow = false
		if (state.showDateType === 'start') {
			screenForm.fromTime = date.result
			screenForm.toTime = ''
		} else {
			screenForm.toTime = date.result
		}
	}
	// 选择区域
	const showScreen = () => {
		state.screenShow = true
	}

	const toDetail = (detail : any) => {
		uni.$u.route({
			url: 'pages/smartOperation/workOrderQuery/detail/index',
			params: {
				id: detail.id,
				fromType: 'query',
				status: detail.status,
				statusName: detail.statusName,
			}
		})
	}

	// 工单列表
	const getWorkOrderList = async () => {
		state.status = 'loadmore'
		const params = {
			...screenForm,
			fromTime: dayjs(screenForm.fromTime).startOf('day').valueOf(),
			toTime: dayjs(screenForm.toTime).endOf('day').valueOf(),
			status: currentStep.value
		}
		const res = await workOrderList(params)
		const data = res.data?.data?.data
		const total = res.data?.data?.total
		// if (screenForm.page === 1) {
		// 	tableData.value = data
		// } else {
		// 	tableData.value = tableData.value.concat(data)
		// }
		// if (data.length > 0 && total >= tableData.value.length) {
		// 	screenForm.page += 1
		// 	state.status = 'loadmore'
		// } else {
		// 	state.status = 'nomore'
		// }

		if (data.length > 0) {
			if (screenForm.page === 1) {
				tableData.value = data
			} else {
				tableData.value = tableData.value.concat(data)
			}
			screenForm.page += 1
			state.status = 'loadmore'
			if (data.length === total) { state.status = 'nomore' }
		} else {
			state.status = 'nomore'
		}

		triggered.value = false
		uni.stopPullDownRefresh()
	}

	onReachBottom(async () => {
		showMoreData()
	})

	onPullDownRefresh(async () => {
		onRefresh()
	})
	onMounted(async () => {
		getWorkOrderList()
	})
</script>

<style lang="scss" is="">
	.card-box {
		width: 686rpx;
		margin: 20rpx auto;
		border-radius: 16rpx;
		padding: 22rpx 28rpx;
		background-color: #FFFFFF;

		.hand {
			.hand-title {
				height: 42rpx;
				line-height: 42rpx;

				.icon-box {
					text-align: center;
					line-height: 32rpx;
					height: 32rpx;
					width: 32rpx;
					background-color: #3862F8;
					border-radius: 4rpx;
				}

				text {
					font-size: 28rpx;
					margin-left: 16rpx;
					color: #060F27;
					font-weight: 600;
				}
			}

			.status {
				color: #91949F;
			}
		}

		.steps {
			width: auto;
			min-height: 86rpx;
			border-radius: 8rpx;
			background: #F9F9F9;
			padding-top: 12rpx;
			font-size: 20rpx;
			margin: 24rpx 0;

			::v-deep .u-steps .u-steps__item {
				min-width: auto;
			}
		}

		.table {
			margin-top: 24rpx;

			.info {
				font-size: 24rpx;
				padding-bottom: 18rpx;
				display: flex;

				text {
					&:nth-child(1) {
						color: #91949F;
					}

					&:nth-child(2) {
						flex: 1;
					}
				}


				.bg {
					padding: 2rpx 12rpx;
					background: rgba(56, 98, 248, 0.2);
					border-radius: 8rpx;

					text {
						color: #3862F8;
					}
				}
			}
		}
	}

	.popup {
		width: 100%;
		height: 100%;
		position: relative;
		padding: 0 34rpx;


		.popup-button {
			width: 94%;
			position: absolute;
			bottom: 40rpx;
			left: 0;
			right: 0;
			margin: 0 auto;
		}

		::v-deep .placeholderClass {
			font-size: 28rpx;
		}

		.screen-list {
			padding-top: 222rpx;
		}
	}

	.grid-box {
		height: 56rpx;
		width: 144rpx;
		border-radius: 8rpx;
		border: 2rpx solid #EBEDF6;
		font-size: 24rpx;
		text-align: center;
		line-height: 56rpx;
	}

	.grid-box-checked {
		border: 2rpx solid #3862F8;
		color: #3862F8;
	}

	::v-deep .u-form-item__body {
		padding: 8rpx 34rpx;
	}

	::v-deep.u-form-item {
		padding: 0;
	}
</style>