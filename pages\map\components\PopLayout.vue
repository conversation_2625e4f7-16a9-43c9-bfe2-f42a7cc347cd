<template>
	<view class="pop-layout">
		<view class="status-bar"></view>
		<view class="pop-layout_header">
			<view class="icon-bg" @click="goBack">
				<text :class="['layer-menu-icon','custom-icon','custom-icon-right']"></text>
			</view>
			<view class="title-wrapper">
				<view class="title">{{props.title}}</view>
			</view>
		</view>
		<scroll-view class="pop-layout__wrapper" scroll-y>
			<slot></slot>
		</scroll-view>
	</view>
</template>

<script lang="ts" setup>
	const emit = defineEmits(['back'])
	const props = defineProps<{ title ?: string }>()
	const goBack = () => {
		emit('back')
	}
</script>

<style scoped lang="scss">
	.pop-layout {
		width: 100vw;
		height: 100vh;
		// 状态栏占位高度
		.status-bar {
			height: var(--status-bar-height);
		}

		.pop-layout_header {
			position: relative;
			height: 70rpx;
			padding: 14rpx 6rpx;
			background-color: #FBFBFB;

			.icon-bg {
				width: 60rpx;
				height: 100%;
				display: grid;
				place-items: center;
				color: #000;
			}

			.title-wrapper {
				position: absolute;
				left: 140rpx;
				right: 140rpx;
				top: 0;
				height: 100%;
				display: grid;
				place-items: center;
				user-select: auto;

				.title {
					overflow: hidden;
					font-size: 32rpx;
					font-weight: bold;
					text-align: center;
					white-space: nowrap;
					text-overflow: ellipsis;
				}
			}
		}

		.pop-layout__wrapper {
			background-color: #ffffff;
			height: calc(100vh - var(--status-bar-height));
		}
	}
</style>