import DB from '@/common/utils/sqlLite'
export const db = () => {
	const createTable = () => {
		let open = DB.isOpen();
		if (open) {
			plus.sqlite.selectSql({
				name: "meterReading",
				sql: "SELECT count(*) as userT FROM sqlite_master WHERE type='table' AND name='UserT'",
				success: function(data) {
					console.log('selectSql success: ', data[0]);
					if (data[0]?.userT === 0 || !data[0]) {
						let sql =
							'"id" TEXT PRIMARY KEY, "code" TEXT, "ym" TEXT, "ymTime" TEXT, "userCode" TEXT, "orgId" TEXT, "meterId" TEXT,' +
							' "pointId" TEXT, "meterCopyNumber" INTEGER, "lastReadNum" INTEGER, "lastReadWater" TEXT, "thisReadNum" INTEGER, ' +
							'"thisReadWater" TEXT, "appendWater" TEXT, "totalWater" TEXT, "readStatus" TEXT, "sendStatus" TEXT, "exceptionType" TEXT, ' +
							'"lastReadDate" TEXT, "thisReadDate" TEXT, "dataTime" TEXT, "recordType" TEXT, "type" TEXT, "meterAddress" TEXT, "meterCopyUser" TEXT, ' +
							'"remark" TEXT, "imgs" TEXT, "tenantId" TEXT, "userId" TEXT, "userName" TEXT, "meterCopyUserName" TEXT, "userAddress" TEXT, "meterBookId" TEXT, ' +
							'"meterBookCode" TEXT, "meterBookName" TEXT, "waterCategory" TEXT, "waterCategoryName" TEXT, "meterCode" TEXT, "meterType" TEXT, "meterTypeName" TEXT, ' +
							'"caliber" TEXT, "orgName" TEXT, "executeUser" TEXT'
						// 创建表 DB.createTable(表名, 表的列)
						DB.createTable("UserT", sql).then(res => {
							console.log("创建userT表成功", res);
						}).catch(error => {
							console.log("创建表失败", error);
						});
					}
				},
				fail: function(e) {
					console.log('selectSql failed: ' + JSON.stringify(e));
				}
			})
			plus.sqlite.selectSql({
				name: "meterReading",
				sql: "SELECT count(*) as bookT FROM sqlite_master WHERE type='table' AND name='BookT'",
				success: function(data) {
					console.log('selectSql success: ', data[0]);
					if (data[0]?.bookT === 0 || !data[0]) {
						let sql =
							'"id" TEXT PRIMARY KEY, "orgId" TEXT, "bookCode" TEXT, "code" TEXT, "eventType" TEXT, "ym" TEXT, "executor" TEXT,' +
							' "lastMeterCopyDate" TEXT, "planDate" TEXT, "planStatus" TEXT, "status" TEXT, "calculateDate" TEXT, "sendDate" TEXT, "remark" TEXT,' +
							' "tenantId" TEXT, "orgName" TEXT, "bookName" TEXT, "dayOfMonth" TEXT, "meterBookType" TEXT, "cycle" TEXT, "bookId" TEXT, "copyBookId" TEXT,' +
							' "custCount" TEXT, "copiedCount" TEXT, "totalReadWater" TEXT, "totalAppendWater" TEXT, "totalWater" TEXT, "totalBaseMoney" TEXT,' +
							' "totalSignPriceMoney" TEXT, "totalAdditionalMoney" TEXT, "totalReceivableMoney" TEXT, "meterCopyUser" TEXT, "meterCopyUserName" TEXT, ' +
							'"accountantsUser" TEXT, "accountantsUserName" TEXT, "checked" TEXT, readNum INTEGER'
						// 创建表 DB.createTable(表名, 表的列)
						DB.createTable("BookT", sql).then(res => {
							console.log("创建BookT表成功", res);
						}).catch(error => {
							console.log("创建表失败", error);
						});
					}
				},
				fail: function(e) {
					console.log('selectSql failed: ' + JSON.stringify(e));
				}
			})
			return true
		} else {
			return false
		}
	}
	return {
		createTable
	}
}
