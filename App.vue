<script>
	import {
		versionUpdate
	} from '@/common/utils/version'
	import { initEnvConfig } from './common/config/env'

	export default {
		onLaunch: function() {
			// 初始化环境配置
			initEnvConfig()

			// #ifdef APP-PLUS
			 versionUpdate(true); // 版本更新 不显示提示信息
			// enablPermission()
			// #endif
			// uni.hideTabBar()
		},
		onShow: function() {
			console.log('App Show')
			// uni.hideTabBar()
		},
		onHide: function() {
			console.log('App Hide')
		},
		methods: {
			enablPermission() {
				let main = plus.android.runtimeMainActivity();
				let pkName = main.getPackageName();
				let uid = main.getApplicationInfo().plusGetAttribute("uid");
				let NotificationManagerCompat = plus.android.importClass(
					"android.support.v4.app.NotificationManagerCompat");
				//android.support.v4升级为androidx
				if (NotificationManagerCompat == null) {
					NotificationManagerCompat = plus.android.importClass("androidx.core.app.NotificationManagerCompat");
				}
				let areNotificationsEnabled = NotificationManagerCompat.from(main).areNotificationsEnabled();
				// 未开通‘允许通知'权限，则弹窗提醒开通，并点击确认后，跳转到系统设置页面进行设置  
				if (!areNotificationsEnabled) {
					uni.showModal({
						title: '通知权限开启提醒',
						content: '您还没有开启通知权限，无法接受到消息通知，请前往设置！',
						showCancel: false,
						confirmText: '去设置',
						success: function(res) {
							if (res.confirm) {
								let Intent = plus.android.importClass('android.content.Intent');
								let Build = plus.android.importClass("android.os.Build");
								//android 8.0引导  
								if (Build.VERSION.SDK_INT >= 26) {
									let intent = new Intent('android.settings.APP_NOTIFICATION_SETTINGS');
									intent.putExtra('android.provider.extra.APP_PACKAGE', pkName);
								} else if (Build.VERSION.SDK_INT >= 21) { //android 5.0-7.0  
									let intent = new Intent('android.settings.APP_NOTIFICATION_SETTINGS');
									intent.putExtra("app_package", pkName);
									intent.putExtra("app_uid", uid);
								} else { //(<21)其他--跳转到该应用管理的详情页  
									intent.setAction(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
									let uri = Uri.fromParts("package", mainActivity.getPackageName(), null);
									intent.setData(uri);
								}
								// 跳转到该应用的系统通知设置页  
								main.startActivity(intent);
							}
						}
					});
				}
			}
		}
	}
</script>

<style lang="scss">
	/*每个页面公共css */
	// @import "@/uni_modules/uview-plus/index.scss";
	@import "@/static/style/index.scss";
	/* #ifdef APP-VUE || H5 */
	// @import url("//at.alicdn.com/t/c/font_3772558_ccvv5563s18.css");
	@import "./static/font/iconfont.css";
	@import "@/uni_modules/vk-uview-ui/index.scss";
	/* #endif */
</style>
