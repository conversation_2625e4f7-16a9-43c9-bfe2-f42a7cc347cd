<template>
  <view class="query-results">
    <!-- 图层筛选标签 -->
    <view class="layer-filter-section">
      <scroll-view class="layer-tags" scroll-x>
        <view class="layer-tags-inner">
          <view
            class="layer-tag"
            :class="{ active: selectedLayerFilter === 'all' }"
            @click="setLayerFilter('all')"
          >
            <view class="tag-indicator all"></view>
            <text class="tag-text">全部 ({{ normalizedResults.length }})</text>
          </view>
          <view
            v-for="layer in availableLayers"
            :key="layer.name"
            class="layer-tag"
            :class="{ active: selectedLayerFilter === layer.name }"
            @click="setLayerFilter(layer.name)"
          >
            <view class="tag-indicator" :style="{ backgroundColor: layer.color }"></view>
            <text class="tag-text">{{ layer.displayName }} ({{ layer.count }})</text>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 统计图表区域：仅在选择具体图层且有数据时显示 -->
    <query-result-stats
      v-if="selectedLayerFilter !== 'all' && filteredResults.length > 0"
      :features="filteredResults"
      v-model:show="showStats"
    />

    <!-- 扁平列表：去掉分组折叠，保持每项清晰紧凑 -->
    <scroll-view class="results-content" scroll-y>
      <view
        v-for="(item, idx) in flatResults"
        :key="item.id || idx"
        class="result-item"
        :class="{
          expanded: expandedItems.includes(itemKey(item.properties?.layer, idx)),
        }"
      >
        <view class="item-main">
          <view class="item-header" @click="toggleItemExpand(itemKey(item.properties?.layer, idx))">
            <view
              class="geom-badge"
              :style="{
                backgroundColor: getLayerColor(item.properties?.layer, item.geometry?.type),
              }"
            >
              <text class="geom-icon">{{
                getLayerIcon(item.properties?.layer, item.geometry?.type)
              }}</text>
            </view>
            <view class="title-and-quick">
              <view class="title-content">
                <text class="item-name">{{ item.properties?.name || item.id || 'N/A' }}</text>
              </view>
              <view class="item-quick">
                <view v-for="f in getSummaryFields(item.properties)" :key="f.key" class="kv-inline">
                  <text class="k">{{ f.label }}:</text>
                  <text class="v">{{ f.value }}</text>
                </view>
              </view>
            </view>
            <view class="item-actions">
              <text class="action-btn" @click.stop="navigateToItem(item)" title="导航">🧭</text>
              <text class="action-btn" @click.stop="locateToItem(item)" title="定位">📍</text>
            </view>
          </view>

          <view
            v-if="expandedItems.includes(itemKey(item.properties?.layer, idx))"
            class="item-details"
          >
            <view class="details-grid">
              <view
                v-for="field in getDetailFields(item.properties)"
                :key="field.key"
                class="detail-row"
                :style="{
                  borderLeftColor: getLayerColor(item.properties?.layer, item.geometry?.type),
                }"
              >
                <text class="detail-label">{{ field.label }}：</text>
                <text class="detail-value">{{ field.value }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <view v-if="filteredResults.length === 0" class="empty-state">
        <text class="empty-text">
          {{ searchKeyword ? '没有找到匹配的结果' : '暂无查询结果' }}
        </text>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import QueryResultStats from './QueryResultStats.vue';
import { useLocation } from '@/common/hooks';
import { webMercatorToGeographic, geographicToWebMercator } from '@/common/utils/arcMapHelper.js';
import { getNavigationRoute } from '@/common/api/navigation';

export default {
  name: 'QueryResultsEnhanced',
  components: {
    QueryResultStats,
  },
  props: {
    results: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      // 筛选和展开
      selectedLayerFilter: 'all',
      expandedItems: [], // 存储 itemKey(layerName-index)
      showStats: true, // 控制统计图表的显示/隐藏

      // 图层配置
      layerConfig: {
        pipe_layer: {
          displayName: '管道',
          color: '#1890ff',
          icon: '🔵',
        },
        valve_layer: {
          displayName: '阀门',
          color: '#52c41a',
          icon: '🟢',
        },
        hydrant_layer: {
          displayName: '消防栓',
          color: '#f5222d',
          icon: '🔴',
        },
        meter_layer: {
          displayName: '水表',
          color: '#fa8c16',
          icon: '🟠',
        },
        unknown_layer: {
          displayName: '未知',
          color: '#d9d9d9',
          icon: '⚪',
        },
      },
    };
  },
  computed: {
    // 标准化为 GeoJSON Features
    normalizedResults() {
      if (!this.results) return [];

      // 已经是 GeoJSON FeatureCollection
      if (this.results.type === 'FeatureCollection' && Array.isArray(this.results.features)) {
        return this.results.features.map(feature => {
          if (!feature.properties) feature.properties = {};
          // 从 ID 解析图层信息并添加到 properties
          const layerInfo = this.parseLayerFromId(feature.id);
          feature.properties.layer = layerInfo;
          return feature;
        });
      }

      // 数组转换为 GeoJSON Features
      if (Array.isArray(this.results)) {
        return this.results.map(item => {
          // 如果已经是 Feature 格式就直接返回
          if (item.type === 'Feature') {
            if (!item.properties) item.properties = {};
            item.properties.layer = this.parseLayerFromId(item.id);
            return item;
          }

          // 否则构造 Feature 对象
          return {
            type: 'Feature',
            id: item.id,
            geometry: item.geometry || null,
            properties: {
              ...item.properties,
              layer: this.parseLayerFromId(item.id),
              name: item.name || item.properties?.name || item.id,
            },
          };
        });
      }

      return [];
    },

    // 可用图层统计
    availableLayers() {
      const layerCounts = {};
      this.normalizedResults.forEach(feature => {
        const layer = feature.properties?.layer || 'unknown_layer';
        layerCounts[layer] = (layerCounts[layer] || 0) + 1;
      });

      return Object.keys(layerCounts).map(layerName => ({
        name: layerName,
        displayName: this.getLayerDisplayName(layerName),
        color: this.getLayerColor(layerName),
        count: layerCounts[layerName],
      }));
    },

    // 按图层过滤的结果
    filteredResults() {
      if (this.selectedLayerFilter === 'all') return this.normalizedResults;

      return this.normalizedResults.filter(
        feature => feature.properties?.layer === this.selectedLayerFilter,
      );
    },

    // 用于显示的排序结果

    flatResults() {
      return [...this.filteredResults].sort((a, b) => {
        // 首先按图层名称排序
        const layerA = a.properties?.layer || '';
        const layerB = b.properties?.layer || '';
        if (layerA !== layerB) return layerA.localeCompare(layerB);

        // 然后按ID数字后缀排序
        const getNumber = id => {
          const match = String(id || '').match(/(\d+)$/);
          return match ? parseInt(match[1], 10) : -1;
        };
        return getNumber(b.id) - getNumber(a.id);
      });
    },
  },

  mounted() {
    // 设置最大高度
    this.updateMaxHeight();
    if (typeof window !== 'undefined') {
      window.addEventListener('resize', this.updateMaxHeight);
    }
    // 初始化时显示所有结果
    this.$emit('displayResults', this.normalizedResults);
  },

  watch: {
    // 监听筛选的图层变化，更新高亮
    filteredResults: {
      handler(newResults) {
        this.$emit('clearHighlight'); // 清除现有高亮
        this.$emit('displayResults', newResults); // 显示筛选后的结果
      },
      deep: true,
    },
  },

  beforeDestroy() {
    if (typeof window !== 'undefined') {
      window.removeEventListener('resize', this.updateMaxHeight);
    }
  },

  methods: {
    // 从 feature.id 中解析图层名（以点分隔，取前缀）
    parseLayerFromId(id) {
      if (!id) return 'unknown_layer';
      const i = String(id).indexOf('.');
      return i > -1 ? String(id).slice(0, i) : String(id);
    },

    // 复合键，避免不同组 index 冲突
    itemKey(groupName, index) {
      return `${groupName}@@${index}`;
    },
    updateMaxHeight() {
      try {
        const info = uni.getSystemInfoSync && uni.getSystemInfoSync();
        if (info && info.windowHeight) {
          this.maxHeight = info.windowHeight * 0.8;
          return;
        }
      } catch (e) {}
      if (typeof window !== 'undefined') {
        this.maxHeight = window.innerHeight * 0.8;
      }
    },

    // 图层筛选
    setLayerFilter(layerName) {
      this.selectedLayerFilter = layerName;
    },

    // 项目展开/收起
    toggleItemExpand(key) {
      const expandedIndex = this.expandedItems.indexOf(key);
      if (expandedIndex > -1) {
        this.expandedItems.splice(expandedIndex, 1);
      } else {
        this.expandedItems.push(key);
      }
    },

    // 图层/几何类型相关方法
    getLayerColor(layerName, geometryType) {
      // 优先按图层名映射，否则按几何类型上色
      const byLayer = this.layerConfig[layerName]?.color;
      if (byLayer) return byLayer;
      const byGeom = {
        Point: '#52c41a',
        LineString: '#1890ff',
        Polygon: '#fa8c16',
      };
      return byGeom[geometryType] || this.layerConfig['unknown_layer'].color;
    },

    getLayerIcon(layerName, geometryType) {
      const byLayer = this.layerConfig[layerName]?.icon;
      if (byLayer) return byLayer;
      const byGeom = {
        Point: '📍',
        LineString: '📏',
        Polygon: '🟦',
      };
      return byGeom[geometryType] || this.layerConfig['unknown_layer'].icon;
    },

    getLayerDisplayName(layerName) {
      return this.layerConfig[layerName]?.displayName || layerName;
    },

    locateToItem(feature) {
      // 清除现有高亮
      this.$emit('clearHighlight');
      // 定位并高亮闪烁选中项
      if (feature.geometry) {
        // 如果有几何数据，使用highlightFeature
        this.$emit('highlightFeature', feature);
      }
    },

    async navigateToItem(feature) {
      // 检查是否有几何数据
      if (!feature.geometry) {
        uni.showToast({
          title: '无法获取要素位置',
          icon: 'none',
        });
        return;
      }

      // 根据几何类型获取目标坐标
      let coordinates;
      if (feature.geometry.type === 'Point') {
        coordinates = feature.geometry.coordinates;
      } else if (feature.geometry.type === 'LineString') {
        // 对于线，取中点
        const midIndex = Math.floor(feature.geometry.coordinates.length / 2);
        coordinates = feature.geometry.coordinates[midIndex];
      } else if (feature.geometry.type === 'Polygon') {
        // 对于面，取第一个环的中心点
        const ring = feature.geometry.coordinates[0];
        let sumX = 0,
          sumY = 0;
        ring.forEach(coord => {
          sumX += coord[0];
          sumY += coord[1];
        });
        coordinates = [sumX / ring.length, sumY / ring.length];
      } else {
        uni.showToast({
          title: '不支持的几何类型',
          icon: 'none',
        });
        return;
      }
      // 获取用户当前位置
      const [userLocation, hasPermission] = await this.getCurrentLocation();
      if (!hasPermission) {
        uni.showToast({
          title: '需要定位权限才能导航',
          icon: 'none',
        });
        return;
      }

      // 目标点坐标（从3857转换为WGS84）
      const [targetX, targetY] = coordinates;
      try {
        // 使用工具方法进行坐标转换
        const { x: wgs84Lng, y: wgs84Lat } = await webMercatorToGeographic(targetX, targetY);

        // 用户位置（已经是GCJ02）
        const userLng = userLocation.longitude;//95.78315533648099;
        const userLat = userLocation.latitude;//40.5177779215204;

        uni.showLoading({
          title: '获取导航路线...',
        });

        // 获取导航路线(驾车导航)
        getNavigationRoute([userLng, userLat], [wgs84Lng, wgs84Lat], 'drive')
          .then(async routeData => {
            uni.hideLoading();
            // 触发导航事件，发送转换后的路线数据
            this.$emit('showNavigation', {
              route: routeData,
              start: {
                lng: userLng,
                lat: userLat,
              },
              end: {
                lng: wgs84Lng,
                lat: wgs84Lat,
              },
              type: 'drive',
            });
          })
          .catch(error => {
            uni.hideLoading();
            uni.showToast({
              title: '获取导航路线失败',
              icon: 'none',
            });
          });
      } catch (err) {
        console.error('导航错误:', err);
        uni.showToast({
          title: '导航发生错误',
          icon: 'none',
        });
      }
    },
    // 获取当前位置
    async getCurrentLocation() {
      try {
        const locationHook = useLocation();
        const location = await locationHook.getLocationWithoutLoading();
        if (location && location.longitude && location.latitude) {
          return [
            {
              longitude: location.longitude,
              latitude: location.latitude,
            },
            true,
          ];
        }
        return [null, false];
      } catch (error) {
        console.error('获取位置失败:', error);
        return [null, false];
      }
    },

    // 获取简要显示字段（仅保留管材、管径、备注）
    getSummaryFields(properties) {
      if (!properties) return [];
      const normalize = val => {
        if (val === null || val === undefined) return '-';
        const s = String(val).trim();
        return s === '' ? '-' : s;
      };
      return [
        { key: '管材', label: '管材', value: normalize(properties['管材']) },
        { key: '管径', label: '管径', value: normalize(properties['管径']) },
        { key: '备注', label: '备注', value: normalize(properties['备注']) },
      ];
    },

    // 获取详细显示字段（全部属性展开显示）
    getDetailFields(properties) {
      if (!properties) return [];
      const fields = [];
      const normalize = val => {
        if (val === null || val === undefined) return '-';
        const s = String(val).trim();
        return s === '' ? '-' : s;
      };
      // 将 properties 全量展开（空值使用 '-' 占位）
      Object.keys(properties).forEach(k => {
        // 跳过内部使用的 layer 字段
        if (k === 'layer') return;
        fields.push({ key: k, label: k, value: normalize(properties[k]) });
      });
      return fields;
    },
  },
};
</script>
<style scoped lang="scss">
.query-results {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.layer-filter-section {
  padding: 12px 20px;
  border-bottom: 1px solid #f0f0f0;
  position: relative;
  background: #fff;

  /* 左右渐变遮罩，提示可横向滑动 */
  &:before,
  &:after {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    width: 16px;
    pointer-events: none;
    z-index: 1;
  }

  &:before {
    left: 0;
    background: linear-gradient(90deg, #fff 0%, rgba(255, 255, 255, 0) 100%);
  }

  &:after {
    right: 0;
    background: linear-gradient(270deg, #fff 0%, rgba(255, 255, 255, 0) 100%);
  }

  .layer-tags {
    /* 使用 inline-block 模式，兼容各端 scroll-view 横向滚动规范 */
    display: block;
    white-space: nowrap;
    overflow-x: auto;
    overflow-y: hidden;
    // padding-bottom: 4px;
    scrollbar-width: none;
    &::-webkit-scrollbar {
      display: none;
    }

    /* 关键：内部容器禁止换行，按内容总宽度横向滚动 */
    .layer-tags-inner {
      display: inline-block;
      white-space: nowrap;
    }

    .layer-tag {
      display: inline-flex; /* 关键：子项内联，保证不换行 */
      flex: 0 0 auto; /* 不换行、不缩小 */
      align-items: center;
      gap: 6px;
      padding: 6px 10px;
      background: #f5f5f5;
      border-radius: 12px;
      cursor: pointer;
      transition: all 0.2s ease;
      border: 1px solid transparent;
      margin-right: 6px;

      &:last-child {
        margin-right: 0;
      }

      &.active {
        background: #e6f7ff;
        border-color: #1890ff;

        .tag-text {
          color: #1890ff;
          font-weight: 500;
        }
      }

      .tag-indicator {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        flex-shrink: 0;

        &.all {
          background: linear-gradient(45deg, #1890ff, #52c41a, #f5222d, #fa8c16);
        }
      }

      .tag-text {
        font-size: 12px;
        color: #595959;
        white-space: nowrap;
      }
    }
  }
}

/* quick-stats removed */

/* filter-section removed */

.results-content {
  flex: 1;
  overflow-y: auto;
}

.result-item {
  position: relative;
  margin: 8px 20px;
  background: #fff;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.2s ease;

  &.selected {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
  }

  &.expanded {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
}

.item-main {
  padding: 4px 8px 4px 8px;
  .item-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 8px;
    cursor: pointer;

    .title-and-quick {
      flex: 1;
      min-width: 0;
    }
    .title-content {
      display: flex;
      gap: 8px;
      align-items: baseline;
      flex-wrap: wrap;
    }
    .item-name {
      font-size: 15px;
      font-weight: 600;
      color: #262626;
    }

    .item-actions {
      display: flex;
      gap: 8px;

      .action-btn {
        width: 28px;
        height: 28px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
        font-size: 16px;
        color: #1890ff;
        transition: all 0.2s ease;
        cursor: pointer;

        &:hover {
          background-color: #f0f5ff;
        }

        &:active {
          background-color: #e6f7ff;
        }
      }
    }
  }

  .item-quick {
    display: flex;
    flex-wrap: wrap;
    gap: 6px 12px;
    margin-top: 6px;
    align-items: baseline;
  }
  .kv-inline {
    display: inline-flex;
    align-items: baseline;
    gap: 4px;
    max-width: 46%;
  }
  .kv-inline .k {
    font-size: 12px;
    color: #8c8c8c;
    line-height: 1.2;
  }
  .kv-inline .v {
    font-size: 12px;
    color: #262626;
    font-weight: 500;
    line-height: 1.2;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.item-details {
  border-top: 1px solid #f0f0f0;
  padding: 4px 8px;
  background: transparent;

  .details-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .details-title {
      font-size: 15px;
      font-weight: 600;
      color: #262626;
    }
  }

  .details-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 6px 10px;
    margin-bottom: 2px;

    .detail-row {
      display: flex;
      flex-direction: row; /* 标签和值同一行，更紧凑 */
      align-items: baseline;
      gap: 6px;
      // padding: 6px 8px;
      background: transparent;
      border-radius: 0;
      border: none;
      box-shadow: none;
      transition: none;

      .detail-label {
        font-size: 12px;
        color: #8c8c8c;
        letter-spacing: 0.2px;
      }

      .detail-value {
        font-size: 13px;
        color: #000;
        word-break: break-word;
        line-height: 1.5;
        display: inline;
        max-width: 100%;
        font-weight: bold;
      }
    }
  }
}

.empty-state {
  padding: 40px 20px;
  text-align: center;

  .empty-text {
    font-size: 14px;
    color: #8c8c8c;
  }
}

/* 滚动条样式 */
.results-content::-webkit-scrollbar {
  width: 6px;
}

.results-content::-webkit-scrollbar-track {
  background: #f0f0f0;
  border-radius: 3px;
}

.results-content::-webkit-scrollbar-thumb {
  background: #d9d9d9;
  border-radius: 3px;
}

/* 隐藏横向滚动条，但保留滚动功能 */
.layer-tags {
  scrollbar-width: none;
  -ms-overflow-style: none;
}
.layer-tags::-webkit-scrollbar {
  height: 0;
  display: none;
}

/* 若需显示滚动条，恢复以上注释代码并移除此隐藏设置 */

@media (max-width: 768px) {
  .layer-filter-section {
    padding: 8px 16px;
  }

  .result-item {
    margin: 6px 16px;
  }

  .details-grid {
    grid-template-columns: 1fr !important;
  }

  /* 始终保持单行横向滚动 */
  .layer-tags {
    flex-wrap: nowrap;
    white-space: nowrap;
  }

  .item-actions {
    gap: 2px;

    .detail-expand-btn {
      width: 24px;
      height: 24px;
      font-size: 12px;
    }
  }
}
</style>
