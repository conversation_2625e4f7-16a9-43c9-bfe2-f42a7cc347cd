<template>
  <div>
    <AttrPop
      v-for="(item, i) in state.pumps"
      :key="i"
      :style="{ left: item.left, top: item.top }"
      :status="item.status"
      :text="item.name"
    ></AttrPop>
    <img
      v-for="(item, i) in state.pans"
      :key="i"
      :style="item.styles"
      class="img"
      :class="item.status"
      :src="item.status === 'online' ? onlinePng : offlinePng"
      alt="1"
    />
  </div>
</template>
<script lang="ts" setup>
import onlinePng from '../../imgs/fsyx.png'
import offlinePng from '../../imgs/fstz.png'
import AttrPop from '../AttrPop.vue'

const props = defineProps<{
  data?: any[]
}>()
const state = reactive<{
  pumps: { name: string; status: 'online' | 'offline'; left: string; top: string }[]
  pans: { styles: any; status: 'online' | 'offline' }[]
}>({
  pumps: [
    { name: '1#泵', status: 'offline', left: '1140px', top: '200px' },
    { name: '2#泵', status: 'offline', left: '1300px', top: '200px' }
  ],
  pans: [
    { styles: { left: '1173px', top: '265px' }, status: 'offline' },
    { styles: { left: '1333px', top: '265px' }, status: 'offline' }
  ]
})
const refreshData = async () => {
  state.pumps.map((item, i) => {
    const value = props.data?.find(attr => attr.property === 'status' + (i + 1))?.value
    item.status = value === '0' ? 'offline' : 'online'
  })
  state.pans.map((item, i) => {
    const value = props.data?.find(attr => attr.property === 'status' + (i + 1))?.value
    item.status = value === '0' ? 'offline' : 'online'
  })
}
watch(
  () => props.data,
  () => refreshData()
)
onMounted(() => {
  refreshData()
})
</script>
<style lang="scss" scoped>
.img {
  position: absolute;
  width: 17px;
  height: 17px;
  &.online {
    transform: rotate(360deg);
    animation: rotation 3s linear infinite;
  }
}
@keyframes rotation {
  from {
    -webkit-transform: rotate(0deg);
  }

  to {
    -webkit-transform: rotate(360deg);
  }
}
</style>
