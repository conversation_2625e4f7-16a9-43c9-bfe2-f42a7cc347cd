<template>
  <view class="order-item">
    <text class="order-date">{{ order.orderDate }}</text>
    <UserInfo :userInfo="order.userInfo" />
    <text class="order-amount">{{ order.orderAmount }}</text>
    <text class="order-time">{{ order.orderTime }}</text>
    <uni-button @click="viewDetail">查看详情</uni-button>
  </view>
</template>

<script setup>
import UserInfo from "@/components/UserInfo.vue";
defineProps({
  order: {
    type: Object,
    default: () => ({}),
  },
});

const viewDetail = () => {
  // TODO: 跳转到订单详情页面
  console.log("查看订单详情");
};
</script>

<style lang="scss" scoped>
.order-item {
  display: flex;
  flex-direction: column;
  margin-bottom: 10px;
  border: 1px solid #ccc;
  padding: 10px;
}
</style>
