
export const gaugeOption = (val:number) => {
	return {
		series: [
			{
				type: 'gauge',
				startAngle: 180,
				endAngle: 0,
				center: ['50%', '40%'],
				radius: '60%',
				min: 0,
				max: 1,
				splitNumber: 5,
				axisLine: {
					lineStyle: {
						width: 2,
						color: [
							[val, '#3862F8'],
							[1, '#F9F9F9']
						]
					}
				},
				splitLine: {
					show: false,
				},
				pointer: {
					show: false,
				},
				axisTick: {
					distance: -28,
					length: 20,
					lineStyle: {
						color: 'auto',
						width: 6
					}
				},
				axisLabel: {
					show: false,
				},
				title: {
					offsetCenter: [0, '-50%'],
					fontSize: 16,
					fontWeight: 'bold',
				},
				detail: {
					fontSize: 24,
					fontWeight: 'bold',
					offsetCenter: [0, '-10%'],
					valueAnimation: true,
					formatter: function(value: number) {
						return Math.round(value * 100) + '';
					},
					color: 'auto'
				},
				data: [
					{
						value: val,
						name: '产销差（%）'
					}
				]
			}
		]
	}
}