<!-- 工单详情 -->
<template>
	<view class="main">
		<u-navbar title="工单详情">
			<!-- <template #right>
				<view class="nv-right">
					二次审核
				</view>
			</template> -->
		</u-navbar>
		<work-order-detail :fromType="detail.fromType" :workOrderId="detail.id" v-if="detail"></work-order-detail>
		<view class="button flex-around">
			<u-button type="primary" @click="pass(detail)">通过</u-button>
			<u-button type="primary" @click="reject(detail)">退回</u-button>
		</view>
		<u-toast ref="refToast"></u-toast>
	</view>
</template>

<script lang="ts" setup>
	import workOrderDetail from '../../workOrderDetail/index.vue'
	import {
		onMounted,
		reactive,
		ref
	} from "vue";
	import {
		verifyWorkOrder
	} from '@/common/api/workOrder'
	const state = reactive < {
		current: number
	} > ({
		current: 0
	})
	const refToast = ref < any > ()
	const detail = ref < any > (null)
	// 通过审核
	const pass = async (detail: any) => {
		uni.showModal({
			title: '提示',
			content: '确定审核通过吗',
			success: (res) => {
				if (res.confirm) {
					verify(detail.id, {
						stage: 'APPROVED'
					})
				}
			}
		})
	}

	// 通过审核
	const reject = (detail: any) => {
		uni.$u.route({
			url: 'pages/smartOperation/workOrderApproval/detail/returnHandle/index',
			params: {
				id: detail.id
			},
			animationType: true
		})
	}

	const verify = (id: String, params: any) => {
		verifyWorkOrder(id, params).then(res => {
			if (res.data?.code === 200) {
				refToast.value.show({
					title: '提交成功',
					type: 'success',
					callback: () => {
						uni.navigateBack({
							delta: 1
						})
					}
				})
			} else {
				refToast.value.show({
					title: res.data?.err,
					type: 'error'
				})
			}
		}).catch(() => {
			refToast.value.show({
				title: '提交失败',
				type: 'error'
			})
		})
	}
	const onRefresh = () => {
		detail.value = null
		var pages = getCurrentPages();
		var page = pages[pages.length - 1];
		detail.value = page.$page.options
	}

	onMounted(() => {
		onRefresh()
	})
</script>

<style lang="scss" scoped>
	.main {
		padding-bottom: 100rpx;

		.card-box {
			width: 686rpx;
			margin: 0 auto;
			border-radius: 16rpx;
			background-color: #FFFFFF;

			.hand {
				.hand-title {
					height: 42rpx;
					line-height: 42rpx;

					text {
						font-size: 28rpx;
						color: #060F27;
						font-weight: 600;
					}
				}

				.status {
					color: #91949F;
				}
			}

			.table {
				margin-top: 24rpx;

				.info {
					font-size: 24rpx;
					padding-bottom: 18rpx;
					display: flex;

					text {
						&:nth-child(1) {
							color: #91949F;
						}

						&:nth-child(2) {
							flex: 1;
						}
					}


					.bg {
						padding: 2rpx 12rpx;
						background: rgba(56, 98, 248, 0.2);
						border-radius: 8rpx;

						text {
							color: #3862F8;
						}
					}
				}
			}


			.card-title {
				padding: 14rpx 28rpx;

				.label {
					padding-left: 10rpx;
					font-style: normal;
					font-weight: 600;
					font-size: 28rpx;
					color: #060F27;
				}
			}

			.list {
				padding: 0 28rpx 10rpx 28rpx;
				color: #060F27;

				.l-c {
					padding: 10rpx 0;

					text {
						&:nth-child(1) {
							color: #91949F;
						}

						&:nth-child(3) {
							color: #060F27;
						}
					}

					.address {
						color: #FBB934;
						overflow: hidden;
						white-space: nowrap;
						width: 84%;
						text-overflow: ellipsis;
					}

					.navigation {
						text-align: right;
						font-size: 24rpx;
						color: #3862F8;
					}
				}

				.l-file {
					padding: 10rpx 0;
				}
			}

			.time-line {
				padding: 20rpx 28rpx;
			}

			.u-node {
				width: 32rpx;
				height: 32rpx;
				border-radius: 50%;
				display: flex;
				justify-content: center;
				align-items: center;
				background: rgba(56, 98, 248, 0.2);

				.circle-center {
					height: 16rpx;
					width: 16rpx;
					border-radius: 50%;
					background-color: #3862F8;
				}
			}

			.u-order-title {
				color: #91949F;
				font-size: 24rpx;
			}

			.u-order-desc {
				color: rgb(150, 150, 150);
				font-size: 28rpx;
				margin: 16rpx 0;
				font-size: 24rpx;

				text {
					&:nth-child(2) {
						color: #060F27;
						padding-left: 10rpx;
					}
				}
			}

			.u-order-time {
				color: rgb(200, 200, 200);
				font-size: 26rpx;
			}
		}

		.button {
			width: 100%;
			background-color: #FFFFFF;
			position: fixed;
			bottom: 0rpx;
			height: 120rpx;
			z-index: 9999;
			box-shadow: 0px 4rpx 30rpx rgba(178, 183, 199, 0.5);

			button {
				border-radius: 8rpx;
				width: 343rpx;
				margin: 20rpx auto;
			}
		}
	}
</style>
