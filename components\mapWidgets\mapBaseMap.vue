<template>
	<view v-if="visible" class="map-layers">
		<view class="cover-header">
			<text class="title">选择底图</text>
			<text class="icon" @click="close">x</text>
		</view>
		<view class="cover-main">
			<view class="item">
				<image class="item-image" @click="()=>changeBaseMap('vec')"
					src="http://t4.tianditu.gov.cn/vec_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=vec&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX=13&TILEROW=3457&TILECOL=6548&tk=e4e98a7455967290863f2f1bb245f7b5"
					mode=""></image>
				<text class="item-text">标准</text>
			</view>
			<view class="item">
				<image class="item-image" @click="()=>changeBaseMap('img')"
					src="http://t4.tianditu.gov.cn/img_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=img&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX=14&TILEROW=6916&TILECOL=13100&tk=e4e98a7455967290863f2f1bb245f7b5"
					mode=""></image>
				<text class="item-text">卫星</text>
			</view>
		</view>
	</view>
</template>

<script lang="ts" setup>
	import { ref } from 'vue';
	defineProps<{ modelValue : string }>()
	const emit = defineEmits(['update:modelValue', 'close'])
	const visible = ref<boolean>(true)
	const changeBaseMap = (val : string) => {
		emit('update:modelValue', val)
	}
	const close = () => {
		visible.value = false
		emit('close')
	}
	const open = () => {
		visible.value = true
	}
	defineExpose({
		open,
		close
	})
</script>

<style lang="scss" scoped>
	.map-layers {
		position: absolute;
		bottom: 0;
		background-color: #FBFBFB;
		height: 320rpx;
		width: 100%;
		padding: 0 32rpx;
		border-radius: 16rpx 16rpx 0rpx 0;

		.cover-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			height: 86rpx;

			.title {
				text-align: left;
				word-break: keep-all;
			}

			.icon {
				font-size: 1.2em;
			}
		}

		.cover-main {
			display: flex;
			justify-content: space-between;
			flex-wrap: nowrap;
			height: 200rpx;

			.item {
				width: calc(50% - 20rpx);
				height: 100%;
				position: relative;

				.item-image {
					width: 100%;
					height: 100%;
					border-radius: 8px;
				}

				.item-text {
					background: rgba(255, 255, 255, 0.8);
					border-radius: 0px 0px 8px 8px;
					width: 100%;
					position: absolute;
					bottom: 0;
					left: 0;
					height: 48rpx;
					line-height: 48rpx;
					padding: 0 20rpx;
					font-size: 24rpx;
				}
			}
		}
	}
</style>