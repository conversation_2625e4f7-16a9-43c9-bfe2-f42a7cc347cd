export function groupBy(list, key) {
	const obj = {};
	list.forEach(item => {
		const rowKey = item[key];
		if (!obj[rowKey]) {
			obj[rowKey] = [];
		}
		obj[rowKey].push(item);
	});
	return obj;
}

// 比较两个数组内容是否相等
export function arrayEqual(arr1, arr2) {
	if (!Array.isArray(arr1) || !Array.isArray(arr2)) return false;
	if (arr1.length != arr2.length) return false;
	for (let i = 0, len = arr1.length; i < len; i++) {
		if ((arr1[i] instanceof Array) && (arr2[i] instanceof Array)) {
			if (!arrayEqual(arr1[i], arr2[i])) return false;
		} else if (arr1[i] !== arr2[i]) {
			return false;
		}
	}
	return true;
}

/**
 * @tutorial https://www.cnblogs.com/-867259206/p/6795354.html
 * @param {Object} obj1
 * @param {Object} obj2
 */
export function dataIsEqual(obj1, obj2) {
	for (const propName in obj1) {
		if (obj1.hasOwnProperty(propName) != obj2.hasOwnProperty(propName)) {
			return false;
		} else if (typeof obj1[propName] != typeof obj2[propName]) {
			return false;
		}
	}
	for (const propName in obj2) {
		if (obj1.hasOwnProperty(propName) != obj2.hasOwnProperty(propName)) {
			return false;
		} else if (typeof obj1[propName] != typeof obj2[propName]) {
			return false;
		}
		if (!obj1.hasOwnProperty(propName)) continue;
		if (obj1[propName] instanceof Array && obj2[propName] instanceof Array) {
			if (!dataIsEqual(obj1[propName], obj2[propName])) return false;
		} else if (obj1[propName] instanceof Object && obj2[propName] instanceof Object) {
			if (!dataIsEqual(obj1[propName], obj2[propName])) return false;
		} else if(obj1[propName] != obj2[propName]) {
			return false;
		}
	}
	return true;
}

/**
 * 保留小数位
 * @param {Number} val 要转换的数值
 * @param {String, Number} accuracy 精度
 */
export function keepDecimals(val, accuracy = 2) {
	if ((val !== 0 && !val) || typeof(val) === 'undefined') return '';
	if (!accuracy && accuracy !== 0) {
		// 默认保留两位小数
		// 0 就... 0, 不保留小数位
		accuracy = 2;
	}
	const number = parseFloat(val);
	if (!isNaN(number)) {
		return number.toFixed(accuracy);
	}
	return val;
}

/**
 * 封装 setTimeout
 * @param {Number} duration 
 */
export function delay(duration = 1000) {
	return new Promise((resolve) => {
		setTimeout(resolve, duration);
	});
}

/**
 * 节流 throttle
 * 栗子：对疯狂点击行为的节流
 * @param {*} fn 
 * @param {*} delay 
 */
export function throttle (fn, delay = 500) {
	let last = 0;
	return function (...args) {
		const now = Date.now();
		if (now - last > delay) {
			fn.apply(this, args); // 和上一次点击时间差大于0.5s, 才执行函数
			last = now;
		}
	}
}

/**
 * 防抖 debounce
 * 栗子：实时搜索，拖拽
 */
export function debounce (fn, delay = 500) {
	let timer;
	return function (...args) {
		clearTimeout(timer);
		timer = setTimeout(() => {
			fn.apply(this, args);
		}, delay);
	}
}

/**
 * @description 版本号比较
 * @param {String} v1
 * @param {String} v2
 * @return {Boolean} v2 大于 v1 返回true, 否则返回false
 */
export function compareVersion(v1, v2) {
	v1 = v1.split('.');
	v2 = v2.split('.');
	const len = Math.max(v1.length, v2.length);

	while (v1.length < len) {
		v1.push('0');
	}
	while (v2.length < len) {
		v2.push('0');
	}

	for (let i = 0; i < len; i++) {
		const num1 = parseInt(v1[i]);
		const num2 = parseInt(v2[i]);

		if (num1 < num2) {
			return true;
		} else if (num1 > num2) {
			return false;
		}
	}

	return false;
}
