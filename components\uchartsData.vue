<template>
	<view class="detail">
		<view class="flex-between" style="margin-bottom: 20rpx;">
			<view class="title-bold" v-if="uchartsConfig.title">
				压力曲线
			</view>
			<view class="title-bold" v-if="uchartsConfig.titleType">
				压力曲线
			</view>
			<view class="tabs flex-between">
				<view v-for="(tab,i) in props.uchartsConfig.tabs" :key="i" :class="['tab',{'check-tab':activeTab.value===tab.value}]"
					@click="checkActiveTab(tab)">
					{{tab.title}}
				</view>

			</view>
		</view>
		<view class="bottom">
			<view class="y-title flex-between">
				<view>
					单位:Mpa
				</view>
				<view class="flex-center">
					<view class="color-view" :style="{'background-color':activeTab.color}"></view>
					<view style="color:#B2B7C7;">{{activeTab.title}}</view>
				</view>
			</view>
			<view class="line-ucharts" :style="{height:props.height}">
				<qiun-data-charts type="area" background="##FFFFFF" :onzoom="true" :opts="opts" :ontouch="true"
					:canvas2d="true" :chartData="props.chartData"></qiun-data-charts>
			</view>
		</view>
	</view>
</template>

<script lang="ts" setup>
	import {
		reactive,
	} from "vue";
	const props = defineProps({
		chartData: Object,
		uchartsConfig:Object,
		height: String
	})

	let activeTab = reactive < any > (props.uchartsConfig.tabs[1])
	const opts = reactive < any > ({
		enableScroll: true,
		legend: {
			show: false
		},
		xAxis: {
			scrollShow: true,
			itemCount: 5,
		},
		extra: {
			area: {
				opacity: 0.5,
				gradient: true
			}
		}
	})

	const checkActiveTab = (tab: any) => {
		activeTab = tab
	}
</script>

<style lang="scss" scoped>
	.detail {
		height: 788rpx;
		background: #FBFBFB;
		margin: 0 auto;
		border-radius: 32rpx 32rpx 0px 0px;
		padding: 0 32rpx;

		.tabs {
			width: 60%;
			height: 46rpx;
			line-height: 46rpx;
			text-align: center;

			.tab {
				width: 88rpx;
				color: #91949F;
			}

			.check-tab {
				width: 88rpx;
				border-radius: 8rpx;
				background-color: #3862F8;
				color: #FFFFFF;
			}
		}

		.bottom {
			background-color: #FFFFFF;
			border-radius: 16rpx;
			padding-bottom: 98rpx;

			.y-title {
				color: #B2B7C7;
				padding: 20rpx 40rpx;

				.color-view {
					width: 20rpx;
					height: 20rpx;
					background-color: #3862F8;
					margin-right: 8rpx;
				}
			}

			.line-ucharts {
				height: 30vh;
				margin: 10rpx auto;
			}
		}
	}
</style>
