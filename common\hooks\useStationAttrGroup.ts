import {
	getAllStationGroup
} from '@/common/api/waterplant'
import {
	ref
} from 'vue'
export const useStationAttrGroup = () => {
	const group = ref<{ type: string; attrList: any[] }[]>([])
	const initAttrGroupData = async (stationId?: string) => {
		if (stationId === undefined) {
			group.value = []
		} else {
			const res = await getAllStationGroup({
				stationId: stationId || ''
			})
			group.value = res.data || []
		}
		group.value.length === 0
			&& group.value.push({ type: '请添加分组', attrList: [] })
		return group.value
	}
	const removeAttrGroup = (type?: string) => {
		const index = group.value.findIndex(item => item.type === type)
		if (index > -1) {
			group.value.splice(index, 1)
		}
	}
	return {
		removeAttrGroup,
		initAttrGroupData,
		group
	}
}
