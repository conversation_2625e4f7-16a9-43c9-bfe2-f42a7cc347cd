<template>
  <div class="pump-pics">
    <img
      class="img"
      :src="plantPng"
      alt="1"
    />
    <JINSHUICHANG></JINSHUICHANG>
    <RealTimeList
      :station-id="stationId1"
      :position="'left'"
      style="top: 60%;"
    ></RealTimeList>
    <RealTimeList :station-id="stationId2"></RealTimeList>
  </div>
</template>
<script lang="ts" setup>
	import{
		ref
	} from 'vue'
import JINSHUICHANG from './components/JINSHUICHANG.vue'
import RealTimeList from './components/RealTimeList.vue'
const plantPng = ref<string>('/static/pump/imgs/plants/shuichang.jpg')
const stationId1 = ref<string>('402880b18a3aec86018a40a42a2a00db')
const stationId2 = ref<string>('402880b18a3aec86018a40a45b4c00dc')
</script>
<style lang="scss" scoped>
.pump-pics {
  width: 100%;
  height: 100%;
  width: 1535px;
  height: 980px;
  position: relative;
  margin: auto;
  // background: url('./imgs/plants/水厂.jpg') 0 0 /100% 100% no-repeat;
}
.img {
  width: 100%;
  object-fit: contain;
  position: absolute;
}
</style>
