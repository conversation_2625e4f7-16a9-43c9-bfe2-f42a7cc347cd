<!-- 无效处理 -->
<template>
	<view class="main">
		<view class="card-box">
			<u-form :model="form" ref="form1" :label-style="{'color':'#91949F'}" labelWidth="180">
				<u-form-item required label="退回原因：" prop="form.val1" :borderBottom="false">
				</u-form-item>
				<u-form-item prop="form.processRemark" :borderBottom="false">
					<u-input type="textarea" input-align="left"  placeholder="请输入退回原因" v-model="form.processRemark" height="400" border>
					</u-input>
				</u-form-item>
			</u-form>
		</view>
		<view class="button">
			<u-button type="primary" color="#3862F8" @click="submit">提交</u-button>
		</view>
		<u-toast ref="refToast"></u-toast>
	</view>
</template>

<script lang="ts" setup>
	import {
		onMounted,
		reactive,
		ref
	} from "vue";
	import {
		verifyWorkOrder
	} from '@/common/api/workOrder'
	const refToast = ref < any > ()
	const form = reactive < any > ({})
	const wordOrder = ref < any > ({})
	const submit = () => {
		if(form.processRemark){
			uni.showModal({
				title: '提示',
				content: '确定提交',
				success: (res) => {
					if (res.confirm) {
						let param = {
							...form,
							stage: 'REJECTED'
						}
						verifyWorkOrder(wordOrder.value.id, param).then(res => {
							if (res.data?.code === 200) {
								refToast.value.show({
									title: '提交成功',
									type: 'success',
									callback: () => {
										uni.navigateBack({
											delta: 2
										})
									}
								})
							} else {
								refToast.value.show({
									title: res.data?.err,
									type: 'error'
								})
							}
						}).catch(() => {
							refToast.value.show({
								title: '提交失败',
								type: 'error'
							})
						})
					}
				},
			})
		}else{
			uni.$u.toast('请填写退回原因')
			return
		}
		
	}

	onMounted(async () => {
		var pages = getCurrentPages();
		var page = pages[pages.length - 1];
		wordOrder.value = page.$page.options
	})
</script>

<style lang="scss" scoped>
	.main {
		padding-top: 20rpx;
		height: 90vh;
	}

	.card-box {
		width: 686rpx;
		border-radius: 8px;
		min-height: 80rpx;
		padding: 20rpx 28rpx;
		margin: 0 auto 20rpx auto;
		color: #FFFFFF;
		background-color: #FFFFFF;

		.form {
			padding: 0 28rpx 10rpx 28rpx;
			color: #060F27;
		}

		.l-file {
			padding: 10rpx 0;
		}
	}
</style>
