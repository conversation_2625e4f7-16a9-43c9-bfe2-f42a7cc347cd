{"compilerOptions": {"baseUrl": ".", "paths": {"@/*": ["./*"]}, "target": "esnext", "module": "esnext", "strict": true, "jsx": "preserve", "importHelpers": true, "moduleResolution": "node", "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "sourceMap": true, "types": ["webpack-env", "jest", "node"], "lib": ["esnext", "dom", "dom.iterable", "scripthost"], "allowJs": true}, "include": ["**/*.ts", "**/*.tsx", "**/*.vue", "**/*.js", "**/*.jsx"], "exclude": ["node_modules"]}