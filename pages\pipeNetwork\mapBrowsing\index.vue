<template>
	<view class="map-browsing">
		<view class="main-wrapper">
			<view id="arcgisView" ref="arcgisView" class="map-view" :change:center="setCenter"
				:center="pipelayer?.center" :change:currentMenu="currentMenuChanged"
				:currentMenu="bottomBars?.curMenu.value" :change:currentVMenu="currentVerticalMenuChanged"
				:currentVMenu="verticalBar?.currentBar.value" :change:cleanMark="clean" :cleanMark="cleanMark"
				:change:locateMark="locate" :locateMark="location?.lonLat" :change:basemap="toggleBaseMap"
				:basemap="basemap" :change:pipevisible="togglePipeVisible" :pipevisible="pipelayer?.currToggled"
				:change:pipeUrl="setPipeUrl" :pipeUrl="pipelayer?.pipeServerUrl" />
			<view class="cover-view-plus">
				<view v-for="(menu, index) in verticalBar?.menus.value" :key="index">
					<view v-if="index < 2" class="plus-btn" @click="verticalBar.setCurrent(menu.value)">
						<text class="custom-icon btn-img" :class="menu.icon"
							:style="{ 'color': verticalBar.currentBar.value === menu.value ? '#3862F8' : '#060F27' }"></text>
						<text style="font-size: 24rpx;"
							:style="{ 'color': verticalBar.currentBar.value === menu.value ? '#3862F8' : '#060F27' }">{{ menu.name }}</text>
					</view>
					<view v-if="index === 0" class="btn-line"></view>
				</view>
			</view>
			<view class="cover-view-loca" @click="getLocation">
				<button class="cover-loca-image" :loading="location?.loading.value">
					<image class="loca-btn" src="/static/img/icons/location-black.png" mode="widthFix"></image>
				</button>
			</view>
			<view class="cover-view-menu bottom-menu border-box flex-around">
				<view class="menu-item" v-for="(menu, i) in bottomBars?.menus.value" :key="i"
					@click="bottomBars?.setCurrent(menu?.id)">
					<view class="icon-bg round-rect" :style="{ 'background': menu?.color.value || '#3862F8' }">
						<text :class="['layer-menu-icon', 'custom-icon', menu?.icon]" :style="{ 'color': '#ffffff' }"></text>
					</view>
					<text class="layer-menu-text"
						:style="{ 'color': menu?.id === bottomBars?.current ? '#3862F8' : '#060F27' }">{{ menu?.alias ||
							menu?.name}}</text>
				</view>
			</view>
			<view v-if="verticalBar.currentBar.value === 'baselayer'" class="cover-view-menu baselayer">
				<view class="cover-header">
					<text class="title">选择底图</text>
					<text class="icon" @click="closeCover">x</text>
				</view>
				<view class="cover-main">
					<view class="item">
						<image class="item-image" @click="() => toggleBaseMap('vec')"
							src="http://t4.tianditu.gov.cn/vec_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=vec&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX=13&TILEROW=3457&TILECOL=6548&tk=e4e98a7455967290863f2f1bb245f7b5"
							mode=""></image>
						<text class="item-text">标准</text>
					</view>
					<view class="item">
						<image class="item-image" @click="() => toggleBaseMap('img')"
							src="http://t4.tianditu.gov.cn/img_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=img&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX=14&TILEROW=6916&TILECOL=13100&tk=e4e98a7455967290863f2f1bb245f7b5"
							mode=""></image>
						<text class="item-text">卫星</text>
					</view>
					<view class="item">
						<image class="item-image" @click="() => toggleBaseMap('arcgis')"
							src="https://services.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/13/3457/6548"
							mode=""></image>
						<text class="item-text">ArcGIS卫星</text>
					</view>
				</view>
			</view>
			<view v-if="verticalBar.currentBar.value === 'layer'" class="cover-view-menu border-box layer">
				<view class="cover-header">
					<text class="title">选择图层</text>
					<text class="icon" @click="closeCover">x</text>
				</view>
				<scroll-view class="menu-list flex-around" :scroll-y="true">
					<view class="layer-box">
						<text class="layer-title">管点类</text>
						<view class="layer-menu">
							<view class="menu-item" v-for="(menu, i) in pipelayer?.pointLayers.value" :key="i"
								@click="pipelayer.toggle(menu.layerid)">
								<view class="icon-bg"
									:style="{ 'background-color': pipelayer.selected.value.indexOf(menu.layerid) !== -1 ? '#3862F8' : '#E2E3E5' }">
									<text :class="['layer-menu-icon', 'custom-icon', menu.icon]"
										:style="{ 'color': pipelayer.selected.value.indexOf(menu.layerid) !== -1 ? '#ffffff' : '#060F27' }"></text>
								</view>
								<text class="layer-menu-text"
									:style="{ 'color': pipelayer.selected.value.indexOf(menu.layerid) !== -1 ? '#3862F8' : '#060F27' }">{{ menu.layername }}</text>
							</view>
						</view>
					</view>
					<view class="layer-box">
						<text class="layer-title">管线类</text>
						<view class="layer-menu">
							<view class="menu-item" v-for="(menu, i) in pipelayer?.lineLayers.value" :key="i"
								@click="pipelayer.toggle(menu.layerid)">
								<view class="icon-bg"
									:style="{ 'background-color': pipelayer.selected.value.indexOf(menu.layerid) !== -1 ? '#3862F8' : '#E2E3E5' }">
									<text :class="['layer-menu-icon', 'custom-icon', menu.icon]"
										:style="{ 'color': pipelayer.selected.value.indexOf(menu.layerid) !== -1 ? '#ffffff' : '#060F27' }"></text>
								</view>
								<text class="layer-menu-text"
									:style="{ 'color': pipelayer.selected.value.indexOf(menu.layerid) !== -1 ? '#3862F8' : '#060F27' }">{{ menu.layername }}</text>
							</view>
						</view>
					</view>
				</scroll-view>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import {
	useLocation,
	useVerticalBar,
	usePipeLayers
} from '../../../common/hooks'
import {
	useBottomMenus
} from './hooks/useBottomMenus'
import {
	initView,
	loadEsriModules,
	changeBaseMap as toggleBaseMapUtil,
	getLayerById,
	setSymbol,
	queryBufferPolygon,
	initBufferParams,
	locationIcon,
	userMarker,
	loadGeoServerWMSLayer
} from '../../../common/utils/arcMapHelper'
import {
	usePipeQuery,
	useMeasure,
	useSketch
} from '../../../common/hooks/map'
import { gisConfig } from '../../../common/data/gisData'
const verticalBar = useVerticalBar()
const location = useLocation()
const bottomBars = useBottomMenus()
const pipelayer = usePipeLayers()

console.log(pipelayer, 'pipelayer------')
const measure = useMeasure()
const pipeQuery = usePipeQuery()
const sketch = useSketch()
let Point, Graphic, Symbol, SimpleMarkerSymbol, Geometry, PictureMarkerSymbol, MapImageLayer

const basemap = ref('vec')
const cleanMark = ref(0)

// 地图相关变量
let view = null
let graphicsLayer = null

const curOper = ref('')
const subLayers = ref([])
const pipeUrl = ref('')
const arcgisView = ref(null)

// 方法定义
const setCenter = (center) => {
	if (!view) return
	view.goTo(center)
}

const toggleBaseMap = (type) => {
	try {
		basemap.value = type
		if (!view?.map) return
		toggleBaseMapUtil(view.map, type)
	} catch (e) {
		console.error('切换底图失败:', e)
		uni.showToast({
			title: '切换底图失败',
			icon: 'none'
		})
	}
}


const closeCover = () => {
	verticalBar.setCurrent('')
}

const clearMap = () => {
	cleanMark.value++
}

const getLocation = () => {
	location.refreshNum.value++
}

const getLayerInfoFromRenderjs = async (params) => {
	console.log(params,'paramsparams-----')
	await pipelayer.getLayerInfo(params)
}
const loadPipeLayer = async () => {
	// 使用GeoServer WMS服务替换ArcGIS服务
	try {
		// 加载GeoServer WMS服务图层，并传入中心点进行定位
		const pipeLayer = await loadGeoServerWMSLayer(view, 'pipe-layer', {
			center: gisConfig().center,
			zoom: gisConfig().zoom
		});
		
		// 获取子图层信息
		subLayers.value = pipeLayer?.sublayers?.items?.map(item => {
			return {
				layername: item.title || item.name,
				layerid: item.name  // 使用name作为GeoServer WMS图层的ID
			}
		}) || [];
		
		console.log(subLayers, 'subLayers-----');
		getLayerInfoFromRenderjs(subLayers.value.map(item => item.layerid));
	} catch (error) {
		console.error('加载GeoServer图层失败:', error);
		uni.showToast({
			title: '加载图层失败',
			icon: 'none'
		});
	}
}

const alertMessage = () => {
	const current = bottomBars.current
	const message = current === 'space' ? '请绘制查询区域' : current === 'buffer' ? '请绘制缓冲中心' : ''
	message &&
		uni.showToast({
			title: message,
			icon: 'none',
			duration: 1000
		})
}

const navigateTo = (url) => {
	uni.navigateTo({
		url: url
	})
}

const showLoading = (config) => {
	uni.showToast({
		title: '加载中...',
		icon: 'loading',
		...(config || {})
	})
}

const hideLoading = () => {
	uni.hideToast()
}

const setPipeUrl = (url) => {
	// if (!view?.map) return
	// const pipeLayer = new MapImageLayer({
	// 	id: 'pipe-layer',
	// 	url: url
	// })
	// view.map.add(pipeLayer)
	pipeUrl.value = url
}

const togglePipeVisible = (params) => {
	// 处理GeoServer WMS图层的可见性
	const layer = view?.map?.findLayerById('pipe-layer');
	const subLayer = layer?.sublayers?.find(item => item.name === params?.id);
	subLayer && (subLayer.visible = params.visible);
}

const locate = () => {
	if (!navigator?.geolocation) {
		uni.showToast({
			title: '当前环境不支持定位',
			icon: 'none'
		})
		return
	}

	showLoading({
		title: '定位中...'
	})

	let options = {
		enableHighAccuracy: true,
		timeout: 5000,
		maximumAge: 0,
	}

	const success = (position) => {
		try {
			const lng = position.coords.longitude
			const lat = position.coords.latitude

			if (!lng || !lat) {
				throw new Error('获取位置信息失败')
			}

			const g = new Graphic({
				geometry: {
					type: 'point',
					longitude: lng,
					latitude: lat,
					spatialReference: view?.spatialReference
				},
				symbol: new PictureMarkerSymbol({
					width: 25,
					height: 25,
					url: userMarker,
					yoffset: 13
				})
			})

			view?.graphics?.removeAll()
			view?.graphics?.add(g)

			view?.goTo({
				zoom: 18,
				target: g
			})
		} catch (e) {
			console.error('定位失败:', e)
			uni.showToast({
				title: '定位失败',
				icon: 'none'
			})
		} finally {
			hideLoading()
		}
	}

	const error = (err) => {
		console.warn("定位错误:", err)
		hideLoading()
		uni.showToast({
			title: '定位失败',
			icon: 'none'
		})
	}

	navigator.geolocation.getCurrentPosition(success, error, options)
}

const clean = () => {
	graphicsLayer?.removeAll()
	measure?.clear()
}

const currentVerticalMenuChanged = (newVal) => {
	if (['area', 'distance'].includes(newVal)) {
		measure.start(view, newVal)
	} else {
		measure.clear()
	}
}

const currentMenuChanged = async (newValue) => {
	if (!view || !graphicsLayer) return
	graphicsLayer?.removeAll()
	const menuId = newValue?.id
	if (!menuId) {
		measure?.clear()
		sketch?.staticState?.sketch?.cancel()
		return
	}
	if (['area', 'distance'].includes(menuId)) {
		measure?.start(view, menuId)
	} else {
		measure?.clear()
	}
	alertMessage()
	curOper.value = menuId
	if (menuId === 'space' || menuId === 'buffer') {
		sketch?.staticState?.sketch?.create(curOper.value === 'space' ? 'polygon' : 'point')
	} else {
		sketch?.staticState?.sketch?.cancel()
	}
	if (menuId === 'reset') {
		measure?.clear()
		sketch?.staticState?.sketch?.cancel()
	}
}

const drawEnd = async (result) => {
	if (result.state === 'complete') {
		let g = result.graphics[0].geometry
		if (curOper.value === 'buffer') {
			if (g.type === 'point') {
				const polygon = await queryBufferPolygon(g, 1000, 'meters')
				g = polygon
				const graphic = new Graphic({
					geometry: g,
					symbol: setSymbol('polygon', {
						color: [0, 182, 153, 0.2],
						outlineColor: '#00B699',
						outlineWidth: 1
					})
				})
				graphicsLayer?.add(graphic)
			}
		}
		const geometry = g?.toJSON()
		const layerIds = subLayers.value.map(item => item.layerid)
		const layerNames = subLayers.value.map(item => item.layername)
		navigateTo('../spaceDeviceList/index?layerNames=' +
			encodeURIComponent(JSON.stringify(layerNames)) + '&layerids=' + encodeURIComponent(
				JSON.stringify(layerIds)) + '&geometry=' + encodeURIComponent(
					JSON.stringify(geometry)))
	}
}

// 生命周期钩子
onMounted(async () => {
	const container = 'arcgisView'
	try {
		showLoading()
		// 初始化地图视图，但不设置中心点，让loadPipeLayer处理定位
		view = await initView(container);

		const [p, g, s, sms, geo, pms, mil] = await loadEsriModules([
			'esri/geometry/Point',
			'esri/Graphic',
			'esri/symbols/Symbol',
			'esri/symbols/SimpleMarkerSymbol',
			'esri/geometry/Geometry',
			'esri/symbols/PictureMarkerSymbol',
			'esri/layers/MapImageLayer'
		])

		Point = p
		Graphic = g
		Symbol = s
		SimpleMarkerSymbol = sms
		Geometry = geo
		PictureMarkerSymbol = pms
		MapImageLayer = mil
		
		// 加载管网图层，并在内部处理定位
		await loadPipeLayer()
		
		// 初始化设备图层
		graphicsLayer = await getLayerById(view, 'device-layer')

		await Promise.all([
			sketch.init(view, graphicsLayer, {
				createCallBack: drawEnd,
				updateCallBack: drawEnd
			}),
			measure.init(),
			pipeQuery.init()
		])

		// 初始化完成后启用底部菜单
		bottomBars?.enable?.()
	} catch (e) {
		console.error('地图初始化失败:', e)
		uni.showToast({
			title: '地图加载失败，请重试',
			icon: 'none'
		})
	} finally {
		hideLoading()
	}
})

</script>
<style scoped lang="scss">
.map-browsing {
	// width: 750rpx;
	height: calc(100vh - 100rpx);
	/* #ifdef APP-PLUS */
	height: 100vh;

	/* #endif */

	// 主体内容
	.main-wrapper {
		position: relative;
		height: calc(100% - var(--status-bar-height));

		.map-view {
			height: 100%;
		}
	}

	.cover-view-plus {
		width: 80rpx;
		background-color: #ffffff;
		border-radius: 20rpx;
		position: absolute;
		top: 62rpx;
		right: 32rpx;
		display: flex;
		flex-wrap: wrap;
		align-items: center;
		justify-content: center;
		padding: 8rpx 16rpx;

		.plus-btn {
			width: 48rpx;
			height: 96rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;

			.btn-img {
				font-size: 48rpx;
			}
		}

		.btn-line {
			width: 80rpx;
			height: 1rpx;
			border-width: 1rpx;
			border-style: solid;
			border-color: #EBEDF6;
		}

		.btn-img {
			font-size: 48rpx;
		}
	}

	.cover-view-loca {
		width: 80rpx;
		height: 80rpx;
		background-color: #ffffff;
		border-radius: 16rpx;
		position: absolute;
		bottom: 208rpx;
		right: 32rpx;
		display: flex;
		align-items: center;
		justify-content: center;

		.cover-loca-image {
			width: 40rpx;
			height: 40rpx;
			background-color: transparent;
			border: none;
			padding: 0;
			line-height: 40rpx;

			&::after {
				border: none;
				width: 40rpx;
				height: 40rpx;
				transform: scale(1);
			}

			.loca-btn {
				width: 40rpx;
				height: 40rpx;
			}
		}
	}

	.cover-view-menu {
		/* width: 750rpx; */
		position: absolute;
		bottom: 0;
		background-color: #FBFBFB;
		// z-index: 99999;
		height: 350rpx;

		&.bottom-menu {
			height: 176rpx;
		}

		&.baselayer,
		&.layer {
			height: 320rpx;
			width: 100%;
			padding: 0 32rpx;
			border-radius: 16rpx 16rpx 0rpx 0;



			.cover-main {
				display: flex;
				justify-content: space-between;
				flex-wrap: nowrap;
				height: 200rpx;

				.item {
					width: calc(50% - 20rpx);
					height: 100%;
					position: relative;

					.item-image {
						width: 100%;
						height: 100%;
						border-radius: 8px;
					}

					.item-text {
						background: rgba(255, 255, 255, 0.8);
						border-radius: 0px 0px 8px 8px;
						width: 100%;
						position: absolute;
						bottom: 0;
						left: 0;
						height: 48rpx;
						line-height: 48rpx;
						padding: 0 20rpx;
						font-size: 24rpx;
					}
				}
			}
		}

		&.baselayer {
			height: 320rpx;
		}

		&.layer {
			height: 800rpx;
			overflow: hidden;

			.menu-list {
				height: 700rpx;
			}
		}

		.cover-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			height: 86rpx;

			.title {
				text-align: left;
				word-break: keep-all;
			}

			.icon {
				font-size: 1.2em;
			}
		}

		&.layer {}

		&.area {}

		&.distance {}

		transition: all 0.5s ease-in-out;
	}

	.bottom-menu {}

	.border-box {
		width: 100%;
		padding-top: 30rpx;
		// z-index: 99999;
		background-color: #FBFBFB;
		position: absolute;
		bottom: 0;
		align-items: center;
		justify-content: space-around;
		// box-shadow: 0 4rpx 30rpx rgba(178, 183, 199, 0.5);
		border-radius: 16rpx 16rpx 0rpx 0;

		.menu-list {
			flex-direction: column;
			display: flex;
			justify-content: space-around;
			width: 100%;

			.layer-box {
				width: 100%;
				padding: 0 32rpx 25rpx;

				.layer-title {
					color: #91949F;
				}

				.layer-menu {
					display: flex;
					justify-content: flex-start;
					align-items: center;
					flex-wrap: wrap;
					border-radius: 8px;
					background-color: #ffffff;
					margin-top: 20rpx;
					padding-bottom: 20rpx;




				}
			}
		}
	}

}

.menu-item {
	width: 20%;
	align-items: center;
	padding: 0;
	text-align: center;
	display: flex;
	justify-content: center;
	flex-direction: column;

	.icon-bg {
		border-radius: 50%;
		width: 80rpx;
		height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: center;

		&.round-rect {
			border-radius: 16rpx;
		}
	}

	.layer-menu-text {
		word-break: keep-all;

	}
}

:deep(.esri-view .esri-view-surface--inset-outline) {
	&:focus::after {
		display: none;
	}
}
</style>