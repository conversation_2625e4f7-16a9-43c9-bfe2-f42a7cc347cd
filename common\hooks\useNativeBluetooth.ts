import { ref } from 'vue'

export const useNativeBluetooth = () => {

	const actionsRecords = ref<string[]>([])
	const openBluetooth = () => {

		var main = plus.android.runtimeMainActivity();
		var BluetoothAdapter = plus.android.importClass("android.bluetooth.BluetoothAdapter");
		var BAdapter = new BluetoothAdapter.getDefaultAdapter();
		// var resultDiv = document.getElementById('output');
		var receiver = plus.android.implements('io.dcloud.android.content.BroadcastReceiver', {
			onReceive: function (context, intent) { //实现onReceiver回调函数  
				plus.android.importClass(intent);
				console.log(intent.getAction());
				actionsRecords.value.push(intent.getAction());
				main.unregisterReceiver(receiver);
			}
		});
		var IntentFilter = plus.android.importClass('android.content.IntentFilter');
		var filter = new IntentFilter();
		filter.addAction(BAdapter.ACTION_STATE_CHANGED); //监听蓝牙开关  
		main.registerReceiver(receiver, filter); //注册监听  

		if (!BAdapter.isEnabled()) {
			BAdapter.enable(); //启动蓝牙  
		} else {
			// BAdapter.disable();  
		}
	}
	const patchedDevices = ref<any[]>([])
	const searchBoundedDevices = () => {
		patchedDevices.value = []
		var main = plus.android.runtimeMainActivity();
		var Context = plus.android.importClass("android.content.Context");
		var BManager = main.getSystemService(Context.BLUETOOTH_SERVICE);
		plus.android.importClass(BManager); //引入相关的method函数
		BAdapter = BManager.getAdapter();
		plus.android.importClass(BAdapter);//引入相关的method函数，这样之后才会有isEna
		var lists = BAdapter.getBondedDevices();
		plus.android.importClass(lists);
		// var resultDiv = document.getElementById('bluetooth_list');
		var iterator = lists.iterator();
		plus.android.importClass(iterator);
		while (iterator.hasNext()) {
			var d = iterator.next();
			plus.android.importClass(d);
			console.log(d)
			patchedDevices.value.push({ deviceId: d.getAddress(), name: d.getName() })
			console.log("名称：" + d.getName() + "，地址：" + d.getAddress());
		}
	}
	const devices = ref<any[]>([])
	const connectedDevices = ref<any[]>([])
	//address=""搜索蓝牙//address=设备mac地址，自动配对给出mac地址的设备  
	function searchDevices() {
		var address = '74:76:5B:AE:43:57'
		// var address = '75:BA:59:24:17:FA'
		//注册类  
		var main = plus.android.runtimeMainActivity();
		var IntentFilter = plus.android.importClass('android.content.IntentFilter');
		var BluetoothAdapter = plus.android.importClass("android.bluetooth.BluetoothAdapter");
		var BluetoothDevice = plus.android.importClass("android.bluetooth.BluetoothDevice");
		var BAdapter = BluetoothAdapter.getDefaultAdapter();
		if (BAdapter == null) {
			console.log('设备不支持蓝牙');
			return
		}
		console.log("开始搜索设备");
		var filter = new IntentFilter();
		var bdevice = new BluetoothDevice();
		var on = null;
		var un = null;
		// var vlist1 = document.getElementById('list1'); //注册容器用来显示未配对设备  
		// vlist1.innerHTML = ''; //清空容器  
		// var vlist2 = document.getElementById('list2'); //注册容器用来显示未配对设备  
		// vlist2.innerHTML = ''; //清空容器  
		// var button1 = document.getElementById('bt1');  
		// button1.disabled=true;  
		// button1.value='正在搜索请稍候';  
		BAdapter.startDiscovery(); //开启搜索  
		var BleDevice
		var receiver = plus.android.implements('io.dcloud.android.content.BroadcastReceiver', {
			onReceive: function (context, intent) { //实现onReceiver回调函数  
				// console.log('onReceived', context, intent);
				plus.android.importClass(intent); //通过intent实例引入intent类，方便以后的‘.’操作  
				var action = intent.getAction()
				actionsRecords.value.push(action)
				// console.log(action); //获取action  
				if (action == "android.bluetooth.adapter.action.DISCOVERY_FINISHED") {
					main.unregisterReceiver(receiver);//取消监听  
					// button1.disabled=false;  
					// button1.value='搜索设备';  
					console.log("搜索结束")
				} else {
					BleDevice = intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE);
					// console.log(BleDevice);
					//判断是否配对  
					if (BleDevice.getBondState() == bdevice.BOND_NONE) {
						// console.log("未配对蓝牙设备：" + BleDevice.getName() + '    ' + BleDevice.getAddress());
						//参数如果跟取得的mac地址一样就配对  
						if (address == BleDevice.getAddress()) {
							if (BleDevice.createBond()) { //配对命令.createBond()  
								console.log("配对成功");
								console.log('services:' + BleDevice.getServices());
								connectedDevices.value.push({ deviceId: BleDevice.getAddress(), click: print, name: BleDevice.getName() })
								var gatt = BleDevice.connectGatt(context, false, (res) => {
									actionsRecords.value.push('尝试获取设备信息')
								})
								var services = gatt.getServices()
								console.log(services);
								// actionsRecords.value.push()
								// var li2 = document.createElement('li'); //注册  
								// li2.setAttribute('id', BleDevice.getAddress()); //打印机mac地址  
								// li2.setAttribute('onclick', 'print(id)'); //注册click点击列表进行打印  
								// li2.innerText = BleDevice.getName();  
								// vlist2.appendChild(li2);  
							} else {
								console.log('not createBond');
							}

						} else {
							if (BleDevice.getName() != on) { //判断防止重复添加  
								// var li1 = document.createElement('li'); //注册  
								// li1.setAttribute('id', BleDevice.getAddress()); //打印机mac地址  
								// li1.setAttribute('onclick', 'searchDevices(id)'); //注册click点击列表进行配对  
								on = BleDevice.getName();
								// li1.innerText = on;  
								// vlist1.appendChild(li1);  
								// console.log('pushed:' + on);
								devices.value.push({ id: BleDevice.getAddress(), click: searchDevices, name: on })

							} else {
								// console.log('is on');
							}

						}
					} else {
						if (BleDevice.getName() != un) { //判断防止重复添加  
							var resultaddress = BleDevice.getAddress()
							console.log("已配对蓝牙设备：" + BleDevice.getName() + '    ' + resultaddress);
							// var li2 = document.createElement('li'); //注册  
							// li2.setAttribute('id',resultaddress); //打印机mac地址  
							// li2.setAttribute('onclick', 'print(id)'); //注册click点击列表进行打印  
							un = BleDevice.getName();
							console.log('pushed:' + un);
							// li2.innerText = un;  
							// vlist2.appendChild(li2);}  
							connectedDevices.value.push({ deviceId: resultaddress, click: print, name: un })
							if (address == resultaddress) {

								console.log("配对成功");
								console.log('services:' + BleDevice.getServices);
								if (BleDevice.getUuids) {
									console.log(BleDevice.getUuids());
									const services = BleDevice.getUuids()
									console.log(services.length);
									for (let j = 0; j < services.length; j++) {
										console.log("设备服务 UUID：" + services[j].toString());
									}
								}
								// connectedDevices.value.push({ id:resultaddress, click: print, name: BleDevice.getName() })
								if (BleDevice.connectGatt) {
									console.log(BleDevice.connectGatt);
									var gatt = BleDevice.connectGatt(context, false, (res) => {
										actionsRecords.value.push('尝试获取设备信息')
									})
									var services = gatt.getServices()
									console.log(services);
								}

							} else {
								console.log(address, resultaddress);
							}
						} else {
							console.log('name: ' + BleDevice.getName());
						}
					}

				}
			}
		})

		filter.addAction(bdevice.ACTION_FOUND);
		filter.addAction(BAdapter.ACTION_DISCOVERY_STARTED);
		filter.addAction(BAdapter.ACTION_DISCOVERY_FINISHED);
		filter.addAction(BAdapter.ACTION_STATE_CHANGED);

		main.registerReceiver(receiver, filter); //注册监听  
	}
	// const searchV3 = () => {
	// 	if (plus.os.name == 'Android') {
	// 	  const BluetoothAdapter = plus.android.importClass("android.bluetooth.BluetoothAdapter");
	// 	  const BluetoothDevice = plus.android.importClass("android.bluetooth.BluetoothDevice");

	// 	  function searchDevices() {
	// 	    const bluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
	// 	    if (bluetoothAdapter.isDiscovering()) {
	// 	      bluetoothAdapter.cancelDiscovery();
	// 	    }

	// 	    // 注册蓝牙设备搜索监听事件
	// 	    const receiver = plus.android.implements('android.bluetooth.BluetoothAdapter$LeScanCallback', {
	// 	      onLeScan: function (device, rssi, scanRecord) {
	// 	        // 处理搜索到的设备
	// 	        console.log("Found device: " + device.getName() + " (" + device.getAddress() + ")");
	// 	      }
	// 	    });

	// 	    bluetoothAdapter.startLeScan(receiver);

	// 	    // 设置搜索时间，例如搜索10秒后停止
	// 	    setTimeout(function () {
	// 	      bluetoothAdapter.stopLeScan(receiver);
	// 	    }, 10000);
	// 	  }

	// 	  function connectDevice(deviceAddress) {
	// 	    const bluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
	// 	    const device = bluetoothAdapter.getRemoteDevice(deviceAddress);

	// 	    // 建立蓝牙设备连接
	// 	    const socket = device.createRfcommSocketToServiceRecord(UUID.fromString("00001101-0000-1000-8000-00805F9B34FB"));
	// 	    socket.connect();

	// 	    // 在此处可以进行数据通讯操作，发送和接收数据等
	// 	    // ...

	// 	    // 断开蓝牙设备连接
	// 	    socket.close();
	// 	  }

	// 	  // 调用上述函数进行设备搜索和连接
	// 	  searchDevices();
	// 	  // connectDevice("设备地址");
	// 	}
	// }

	function searchNativeDevices() {
		const bluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
		if (bluetoothAdapter.isDiscovering()) {
			bluetoothAdapter.cancelDiscovery();
		}

		// 注册蓝牙设备搜索监听事件
		const receiver = plus.android.implements('android.bluetooth.BluetoothAdapter$LeScanCallback', {
			onLeScan: function (device, rssi, scanRecord) {
				// 处理搜索到的设备
				console.log("Found device: " + device.getName() + " (" + device.getAddress() + ")");
			}
		});

		bluetoothAdapter.startLeScan(receiver);

		// 设置搜索时间，例如搜索10秒后停止
		setTimeout(function () {
			bluetoothAdapter.stopLeScan(receiver);
		}, 10000);
	}
	let socket : any
	const isConnected = ref<boolean>(false)
	const curDevice = ref()
	const connectDevice = (item) => {
		const BluetoothAdapter = plus.android.importClass("android.bluetooth.BluetoothAdapter");
		const deviceAddress = item.deviceId
		var UUID = plus.android.importClass("java.util.UUID");
		uuid = UUID.fromString("00001101-0000-1000-8000-00805F9B34FB");
		const bluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
		const device = bluetoothAdapter.getRemoteDevice(deviceAddress);
		plus.android.importClass(device);
		// 建立蓝牙设备连接
		socket = device.createRfcommSocketToServiceRecord(uuid);
		plus.android.importClass(socket);
		if (!socket.isConnected()) {
			console.log('检测到设备未连接，尝试连接....');
			socket.connect();
		}
		console.log('设备连接中，请稍候...');
		console.log('连接状态：' + socket.isConnected());
		if (socket.isConnected()) {
			console.log('设备已连接');
			isConnected.value = true
			curDevice.value = item
			// var outputStream = socket.getOutputStream();
			// plus.android.importClass(outputStream);
			// var string = "打印测试\r\n";
			// var bytes = plus.android.invoke(string, 'getBytes', 'gbk');
			// outputStream.write(bytes);
			// outputStream.flush();
			// device = null //这里关键 
			// bluetoothSocket.close(); //必须关闭蓝牙连接否则意外断开的话打印错误 

		} else {
			console.log('连接失败');
			isConnected.value = false
			curDevice.value = undefined
			// console.log(socket.isConnected());
		}

	}
	const closeConnect = () => {

		if (!socket) {
			return
		}
		socket.close()
		curDevice.value = undefined
		isConnected.value = false
	}
	const sendData = (data='') => {
		if (!socket) {
			console.log('socket未创建，发送失败：' + data);
			return
		}
		const outputStream = socket.getOutputStream();
		plus.android.importClass(outputStream);
		outputStream.write(data.getBytes()); // 将数据转化为字节数组并发送出去
	}

	const receiveData = () => {
		if (!socket) {
			console.log('socket未创建，接收失败');
			return
		}
		const inputStream = socket.getInputStream();
		plus.android.importClass(inputStream);
		const buffer = new ArrayBuffer(1024);
		console.log('buffer:' + buffer);
		const bytes = inputStream.read(buffer); // 从输入流中读取数据到字节数组中
		console.log('bytes:' + bytes);
		const data = new Uint8Array(buffer, 0, bytes); // 将字节数组转换为Uint8Array
		console.log('data:' + data);
		const receivedData = new TextDecoder().decode(data); // 将Uint8Array转换为字符串
		return receivedData;
	}

	var device = null,
		BAdapter = null,
		BluetoothAdapter = null,
		uuid = null,
		main = null,
		bluetoothSocket = null;
	function print(device) {

		console.log(device);
		const mac_address = device.deviceId
		if (!mac_address) {
			uni.showToast({
				title: '请选择蓝牙打印机',
				icon: 'error'
			})
			return;
		}

		main = plus.android.runtimeMainActivity();
		BluetoothAdapter = plus.android.importClass("android.bluetooth.BluetoothAdapter");
		var UUID = plus.android.importClass("java.util.UUID");
		// uuid = UUID.fromString("00000000-0000-1000-8000-00805f9b34fb".toUpperCase());
		uuid = UUID.fromString("00001101-0000-1000-8000-00805F9B34FB");
		// uuid = UUID.fromString("0000110e-0000-1000-8000-00805f9b34fb".toUpperCase());
		// uuid = UUID.fromString("0000110e-0000-1000-8000-00805f9b34fb".toUpperCase());
		BAdapter = BluetoothAdapter.getDefaultAdapter();
		device = BAdapter.getRemoteDevice(mac_address);
		console.log(device);
		plus.android.importClass(device);
		bluetoothSocket = device.createRfcommSocketToServiceRecord(uuid);
		plus.android.importClass(bluetoothSocket);

		if (!bluetoothSocket.isConnected()) {
			console.log('检测到设备未连接，尝试连接....');
			bluetoothSocket.connect();
		}

		console.log('设备连接中，请稍候...');
		console.log(bluetoothSocket.isConnected);
		if (bluetoothSocket.isConnected()) {
			console.log('设备已连接');
			var outputStream = bluetoothSocket.getOutputStream();
			plus.android.importClass(outputStream);
			var string = "打印测试\r\n";
			var bytes = plus.android.invoke(string, 'getBytes', 'gbk');
			outputStream.write(bytes);
			outputStream.flush();
			device = null //这里关键 
			bluetoothSocket.close(); //必须关闭蓝牙连接否则意外断开的话打印错误 

		} else {
			console.log('连接失败');
			console.log(bluetoothSocket.isConnected());
		}

	}
	return {
		searchDevices,
		print,
		devices,
		connectedDevices,
		searchBoundedDevices,
		openBluetooth,
		actionsRecords,
		patchedDevices,
		searchNativeDevices,
		connectDevice,
		isConnected,
		closeConnect,
		sendData,
		receiveData,
		curDevice

	}
}