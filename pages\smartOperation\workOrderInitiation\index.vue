<!-- 工单发起 -->
<template>
	<view class="i-main">
		<view class="detail">
			<u-form :model="form" ref="refForm" :label-style="{'color':'#91949F'}" borderBottom labelWidth="180"
				input-align="right" :error-type="['toast']">
				<view class="card-box">
					<u-form-item label="事件标题：" required prop="title" borderBottom>
						<u-input placeholder="请输入标题" v-model="form.title" input-align="right">
						</u-input>
					</u-form-item>
					<u-form-item label="事件来源：" required prop="source" borderBottom>
						<template #right>
							<u-icon name="arrow-right" size="28" color="#91949F"></u-icon>
						</template>
						<input inputmode="none" placeholder="请选择事件来源" v-model="form.source" input-align="right"
							placeholder-class="placeholderClass" @click="clickShow('sourceIndex','sourceShow')">
						</input>
					</u-form-item>

					<u-form-item label="事件类型：" required prop="type" borderBottom>
						<template #right>
							<u-icon name="arrow-right" size="28" color="#91949F"></u-icon>
						</template>
						<input inputmode="none" v-model="form.type" placeholder="请选择事件类型" input-align="right"
							placeholder-class="placeholderClass" @click="clickShow('typeIndex','typesShow')">
						</input>
					</u-form-item>

					<!-- <u-form-item label="事件内容：" prop="form.val2" borderBottom @click="typesContentShow=true">
						<template #right>
							<u-icon name="arrow-right" size="28" color="#91949F"></u-icon>
						</template>
						<input v-model="form.val2" inputmode="none" placeholder-class="placeholderClass"  placeholder="请选择事件内容" input-align="right">
						</input>
					</u-form-item> -->

					<u-form-item label="紧急程度：" required prop="levelName" borderBottom>
						<template #right>
							<u-icon name="arrow-right" size="28" color="#91949F"></u-icon>
						</template>
						<input inputmode="none" v-model="form.levelName" placeholder="请选择紧急程度" input-align="right"
							placeholder-class="placeholderClass" @click="clickShow('urgencyIndex','urgencyShow')">
						</input>
					</u-form-item>

					<u-form-item label="事件地址：" required prop="address" borderBottom>
						<u-input v-model="form.address" placeholder="请输入事件地址" input-align="right" @blur="changeAddress">
						</u-input>
						<template #right>
							<view @click="chooseAddress" class="dw">
								<image src="/static/img/icons/dw.png" style="height:40rpx;width:40rpx">
								</image>
							</view>
						</template>
					</u-form-item>
					<!-- <u-form-item label="事件地址：" required prop="address" borderBottom>
						<u-input v-model="form.address" placeholder="请输入事件地址" input-align="right">
						</u-input>
						<template #right>
							<image src="/static/img/icons/location.png" @click="locaton"
								style="margin-bottom:20rpx;height:36rpx;width:36rpx">
							</image>
						</template>
					</u-form-item>
					<u-form-item label="" borderBottom>
						<view style="width: 100%; height: 80px;">
							<map v-show="!mapShow" style="width: 100%; height: 80px;" id="maps" :controls="controls"
								:latitude="state.latitude" :longitude="state.longitude" @regionchange="regionchange">
							</map>
						</view>
					</u-form-item> -->
					<u-form-item label="详细描述：" required :borderBottom="false">
					</u-form-item>
					<u-form-item label="" prop="remark" borderBottom>
						<u-input type="textarea" input-align="left" v-model="form.remark" placeholder="请输入详细描述" border
							color="#91949F">
						</u-input>
					</u-form-item>

					<u-form-item label="处理级别：" required prop="processLevelLabel" borderBottom>
						<template #right>
							<u-icon name="arrow-right" size="28" color="#91949F"></u-icon>
						</template>
						<input inputmode="none" v-model="form.processLevelLabel" placeholder="请选择处理级别"
							input-align="right" placeholder-class="placeholderClass"
							@click="clickShow('processLevelIndex','processLevelShow')">
						</input>
					</u-form-item>

					<u-form-item label="处理时长：" required borderBottom>
						<u-input v-model="form.processLevelName" disabled placeholder="处理时长" input-align="right">
						</u-input>
					</u-form-item>

					<u-form-item label="抄送人员：" prop="ccUserId">
						<view class="flex-center" @click.stop="toChooseUser">
							<view class="flex-center" style="flex-wrap: wrap;margin: 4rpx;">
								<u-tag :text="user" type="primary" closeable v-for="(user,index) in users" :key="index"
									:index="index" @close="removeCC" />
								<!-- <text>{{user}}</text>
								<u-icon name="close-circle-fill" size="32" color="#FFFFFF" @click="removeCC(index)">
								</u-icon> -->
							</view>
						</view>
						<template #right>
							<u-icon name="arrow-right" size="28" color="#91949F"></u-icon>
						</template>
					</u-form-item>
				</view>

				<view class="card-box">
					<u-form-item required label="发起人员：" prop="currentUserType">
						<template #right>
							<u-subsection fontSize="24" height="60" activeColor="#3862F8" inactiveColor="#91949F"
								mode="subsection" :list="state.userTypes" v-model="state.currentUserType">
							</u-subsection>
						</template>
					</u-form-item>
					<block v-if="state.currentUserType===1">
						<u-form-item label="姓名：" prop="uploadUserId" required>
							<u-input v-model="form.uploadUserId" placeholder="发起人姓名">
							</u-input>
						</u-form-item>
						<u-form-item label="联系方式：" prop="uploadPhone" required>
							<u-input v-model="form.uploadPhone" placeholder="发起人联系方式">
							</u-input>
						</u-form-item>
						<u-form-item label="地址：" prop="uploadAddress">
							<u-input v-model="form.uploadAddress" placeholder="发起人地址">
							</u-input>
						</u-form-item>
					</block>
				</view>
				<view class="card-box">
					<u-form-item label="分派：">
						<template #right>
							<u-subsection fontSize="24" height="60" buttonColor="#3862F8" activeColor="#3862F8"
								inactiveColor="#91949F" bgColor="#F9F9F9" mode="subsection"
								:list="state.assignmentTypes" v-model="state.currentassignmentType">
							</u-subsection>
						</template>
					</u-form-item>

					<u-form-item label="处理组织：" prop="organization" borderBottom>
						<template #right>
							<u-icon name="arrow-right" size="28" color="#91949F"></u-icon>
						</template>
						<input inputmode="none" v-model="form.organizationName" placeholder="请选择" input-align="right"
							placeholder-class="placeholderClass"
							@click="clickShow('organizationIndex','organizationShow')">
						</input>
					</u-form-item>
					<u-form-item label="处理部门：" prop="receiveDepartmentName" borderBottom>
						<template #right>
							<u-icon name="arrow-right" size="28" color="#91949F"></u-icon>
						</template>
						<input inputmode="none" v-model="form.receiveDepartmentName" placeholder="请选择"
							input-align="right" placeholder-class="placeholderClass"
							@click="clickShow('departmentIndex','departmentShow')">
						</input>
					</u-form-item>

					<u-form-item label="处理人员：" v-if="state.currentassignmentType===1" prop="stepProcessUserName">
						<template #right>
							<u-icon name="arrow-right" size="28" color="#91949F"></u-icon>
						</template>
						<input v-model="form.stepProcessUserName" inputmode="none" placeholder="选择处理人员"
							input-align="right" placeholder-class="placeholderClass"
							@click="clickShow('processUserIndex','stepProcessUserShow')">
						</input>
					</u-form-item>

					<!-- <u-form-item label="备注：" prop="remark">
						<u-input v-model="form.remark" placeholder="请输入备注">
						</u-input>
					</u-form-item> -->
					<!-- <u-form-item label="完成时间：" prop="form.val4">
						<template #right>
							<u-icon name="calendar" size="36"></u-icon>
						</template>
						<input v-model="form.time" inputmode="none" placeholder-class="placeholderClass" placeholder="完成时间">
						</input>
					</u-form-item> -->
				</view>
			</u-form>
			<view class="card-box" style="padding: 0">
				<file-upload ref="refFileUpload"></file-upload>
			</view>
		</view>

		<view class="button">
			<u-button type="primary" color="#3862F8" @click="submit">提交</u-button>
		</view>
		<!-- 事件来源 -->
		<u-picker v-model="state.sourceShow" mode="selector" :default-selector="[0]" :range="sourceTypes"
			range-key="name" @confirm="selectSource"></u-picker>
		<!-- 事件类型 -->
		<u-picker v-model="state.typesShow" mode="selector" :default-selector="[0]" :range="workOrderTypes"
			range-key="name" @confirm="selectType"></u-picker>
		<!-- <u-select v-model="state.typesShow" mode="mutil-column-auto" :list="workOrderTypes" @confirm="selectType">
		</u-select> -->
		<!-- 事件类型 -->
		<!-- <u-picker valueName="id" labelName="name" v-model="state.typesShow" :defaultSelector="state.typeIndex"
			:range="userTypes" @confirm="selectType" range-key="name"></u-picker> -->
		<!-- 紧急程度 -->
		<u-picker v-model="state.urgencyShow" mode="selector" :default-selector="[0]" :range="urgencys" range-key="name"
			@confirm="selectUrgency"></u-picker>
		<!-- 处理级别 -->
		<u-picker v-model="state.processLevelShow" mode="selector" :default-selector="[0]" :range="processLevels"
			range-key="name" @confirm="selectProcessLevel"></u-picker>
		<!-- 组织列表 -->
		<u-picker v-model="state.organizationShow" mode="selector" :default-selector="[0]" :range="organizations"
			range-key="name" @confirm="selectOrganization"></u-picker>
		<!-- 部门列表 -->
		<u-picker v-model="state.departmentShow" mode="selector" :default-selector="[0]" :range="departments"
			range-key="name" @confirm="selectDepartment"></u-picker>
		<!-- 处理人员 -->
		<u-picker v-model="state.stepProcessUserShow" mode="selector" :default-selector="[0]" :range="processUsers"
			range-key="firstName" @confirm="selectProcessUser"></u-picker>
		<u-toast ref="refToast" />
	</view>
</template>

<script lang="ts" setup>
	import {
		useStore
	} from '@/store/index'
	import {
		onReady,
		onShow
	} from '@dcloudio/uni-app';
	import {
		onBeforeMount,
		computed,
		onMounted,
		reactive,
		ref
	} from "vue";
	import fileUpload from '@/components/fileUpload/fileUpload.vue'
	import {
		userTypes,
		assignmentTypes
	} from '@/common/data/publicData'
	import {
		queryGeocoder
	} from '@/common/api/map'
	import {
		removeSlash
	} from '@/common/utils/removeIdSlash'
	import {
		workOrderTypeList,
		getOrganization,
		workOrderResourceList,
		getDepartmentList,
		workOrderProcessLevelList,
		getStepProcessUser,
		workOrderEmergencyLevelList
	} from '@/common/data/workOrderData'
	import {
		saveWorkOrder
	} from '@/common/api/workOrder'
	import {
		storeToRefs
	} from 'pinia'
	const store = useStore();
	const state = reactive<{
		typesShow : boolean,
		windowHeight : Number,
		urgencyShow : boolean,
		sourceShow : boolean,
		processLevelShow : boolean,
		organizationShow : boolean,
		departmentShow : boolean,
		stepProcessUserShow : boolean,
		typeIndex : any,
		organizationIndex : any,
		sourceIndex : any,
		departmentIndex : any,
		urgencyIndex : any,
		processUserIndex : any,
		processLevelIndex : any,
		userTypes : any,
		assignmentTypes : any,
		latitude : Number,
		currentUserType : Number,
		currentassignmentType : Number,
		longitude : Number
	}>({
		windowHeight: 1000,
		typesShow: false,
		organizationShow: false,
		departmentShow: false,
		stepProcessUserShow: false,
		sourceShow: false,
		urgencyShow: false,
		processLevelShow: false,
		typeIndex: [0],
		urgencyIndex: [0],
		processUserIndex: [0],
		organizationIndex: [0],
		sourceIndex: [0],
		departmentIndex: [0],
		processLevelIndex: [0],
		userTypes: userTypes,
		assignmentTypes: assignmentTypes,
		currentUserType: 0,
		currentassignmentType: 0,
		longitude: 0,
		latitude: 0,
	})

	const rules = reactive<any>({
		title: [{
			required: true,
			message: '请输入事件标题',
		}],
		source: [{
			required: true,
			message: '请选择事件来源',
		}],
		type: [{
			required: true,
			message: '请选择事件类型',
			trigger: ['change', 'blur'],
		}],
		levelName: [{
			required: true,
			message: '请选择紧急程度',
			trigger: ['change', 'blur'],
		}],
		address: [{
			required: true,
			message: '请输入地址',
			trigger: ['change', 'blur'],
		}],
		remark: [{
			required: true,
			message: '请输入详细描述',
			trigger: ['change', 'blur'],
		}],
		processLevelLabel: [{
			required: true,
			message: '处理级别',
			trigger: ['change', 'blur'],
		}],
		uploadUserId: [{
			required: true,
			message: '发起人姓名',
			trigger: ['change', 'blur'],
		}],
		uploadPhone: [{
			required: true,
			message: '发起人联系方式',
			trigger: ['change', 'blur'],
		}]
	})
	const refToast = ref<any>();
	const refForm = ref<any>({})
	const refFileUpload = ref<any>({})
	const workOrderTypes = ref<any>([])
	const sourceTypes = ref<any>([])
	const urgencys = ref<any>([])
	const departments = ref<any>([])
	const processUsers = ref<any>([])
	const organizations = ref<any>([])
	const users = ref<any>([])
	const controls = ref<any>([{
		id: '1',
		iconPath: '../../../static/img/icons/dw.png',
		position: { //控件在地图的位置
			left: 140,
			top: 30,
			width: 20,
			height: 20,
		},
	}])

	const processLevels = ref<any>()
	const mapShow = computed(() => {
		return state.typesShow ||
			state.organizationShow ||
			state.departmentShow ||
			state.stepProcessUserShow ||
			state.sourceShow ||
			state.urgencyShow || state.processLevelShow

	})
	const toChooseUser = () => {
		uni.$u.route({
			url: 'pages/userTree/organization',
		})
	}
	const chooseAddress = () => {
		uni.navigateTo({
			url: '/pages/map/tianMap'
		})
	}
	const form = reactive<any>({
		address: '',
		time: '',
		source: '',
		ccUserId: []
	})
	// 获取当前定位
	// const locaton = () => {
	// 	uni.getLocation({
	// 		type: 'gcj02',
	// 		altitude: true,
	// 		geocode: true,
	// 		isHighAccuracy: true,
	// 		success: (info) => {
	// 			console.log(info)
	// 			state.latitude = info.latitude
	// 			state.longitude = info.longitude
	// 			form.address = info.address.street + info.address.streetNum
	// 		},
	// 		fail: () => {
	// 			uni.$u.toast('获取定位失败')
	// 		}
	// 	})
	// }
	const changeAddress = () => {
		queryGeocoder({ keyWord: form.address }).then((res : any) => {
			const location = res.data?.location
			state.latitude = location.lat
			state.longitude = location.lon
		})
	}
	// 新增抄送人员
	const chooseUser = (user : any) => {
		if (user && users.value.indexOf(user.firstName) === -1) {
			users.value.push(user.firstName)
			form.ccUserId.push(removeSlash(user.id?.id))
		}
	}
	// 移除抄送人员
	const removeCC = (index : any) => {
		console.log(index, users.value)
		users.value.splice(index, 1)
		console.log(users.value)
		form.ccUserId.splice(index, 1)
	}

	const clickShow = (indexName, typeName) => {
		state[indexName] = [0]
		state[typeName] = true
	}

	// 选择事件来源
	const selectSource = (val : any) => {
		state.sourceIndex = val
		const type = sourceTypes.value[val[0]]
		form.source = type.name
	}
	// 选择事件类型
	const selectType = (val : any) => {
		console.log(val)
		state.typeIndex = val
		const type = workOrderTypes.value[val[0]]
		form.type = type.name
	}
	// 选择紧急程度
	const selectUrgency = (val : any) => {
		state.urgencyIndex = val
		const level = urgencys.value[val[0]]
		form.level = level.id
		form.levelName = level.name
	}
	// 选择处理级别
	const selectProcessLevel = (val : any) => {
		state.processLevelIndex = val
		const processLevel = processLevels.value[val[0]]
		form.processLevelLabel = processLevel.name
		form.processLevel = processLevel.dayTime * 1440 + processLevel.hourTime * 60 + processLevel.minuteTime
		form.processLevelName = processLevel.dayTime + '天' + processLevel.hourTime + '小时' + processLevel.minuteTime +
			'分钟'
		console.log('选择处理级别', form.processLevel)
	}
	// 选择组织
	const selectOrganization = (val : any) => {
		state.organizationIndex = val
		const organization = organizations.value[val[0]]
		form.organizationName = organization.name
		getDepartment(organization.id)
	}
	// 选择部门
	const selectDepartment = (val : any) => {
		state.departmentIndex = val
		const department = departments.value[val[0]]
		form.receiveDepartmentName = department.name
		form.receiveDepartmentId = department.id
		getProcessUser(department.id)
	}
	// 选择人员
	const selectProcessUser = (val : any) => {
		state.processUserIndex = val
		const processUser = processUsers.value[val[0]]
		form.stepProcessUserName = processUser.firstName
		form.stepProcessUserId = removeSlash(processUser.id?.id)
	}
	// 获取组织部门列表
	const getDepartment = async (id : String) => {
		departments.value = await getDepartmentList(id)
		console.log(departments.value)
	}
	// 获取组织部门用户列表
	const getProcessUser = async (id : String) => {
		processUsers.value = await getStepProcessUser(id)
	}

	//提交工单
	const submit = () => {
		refForm.value.validate((valid : boolean) => {
			if (valid) {
				uni.showModal({
					title: '提示',
					content: '确定提交',
					success: function (res) {
						if (res.confirm) {
							const params = {
								...form,
								videoUrl: refFileUpload.value.videoList.join(','),
								audioUrl: refFileUpload.value.voiceList.join(','),
								imgUrl: refFileUpload.value.imageList.join(','),
								otherFileUrl: refFileUpload.value.fileList.join(','),
								ccUserId: form.ccUserId.join(','),
								// coordinate: state.latitude + ',' + state.longitude,
								isDirectDispatch: state.currentassignmentType === 1
							}
							console.log('提交数据', params)
							saveWorkOrder(params).then(res => {
								if (res.data?.code === 200) {
									refToast.value.show({
										title: '提交成功',
										type: 'success',
										position: 'bottom',
										back: true
									})
									let {
										userData
									} = storeToRefs(store);
									userData.value = null
								} else {
									refToast.value.show({
										title: res.data?.err,
										type: 'error',
									})
								}
							}).catch(() => {
								refToast.value.show({
									title: '提交失败',
									position: 'bottom',
									type: 'error',
								})
							})
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			} else {
				console.log('验证失败');
			}
		})
	}

	const onLoadUpdate = item => {
		form.address = item.address
		form.coordinate = item.coordinate.lat + ',' + item.coordinate.lon
	}

	onBeforeMount(async () => {
		sourceTypes.value = await workOrderResourceList()
		workOrderTypes.value = await workOrderTypeList()
		urgencys.value = await workOrderEmergencyLevelList()
		organizations.value = await getOrganization()
		processLevels.value = await workOrderProcessLevelList()
	})

	onMounted(async () => {
		let { userData } = storeToRefs(store);
		userData.value = null
		// locaton()
		// 获取手机信息
		let info = uni.getSystemInfoSync()
		//顶部高度
		state.windowHeight = info.windowHeight
	})

	onReady(() => {
		refForm.value.setRules(rules);
	})

	onShow(async () => {
		// let {
		// 	userData
		// } = store;
		// if (userData) {
		// 	chooseUser(userData)
		// }
		uni.$on("updateLocation", onLoadUpdate);
		uni.$on("chooseUserData", chooseUser);
	})
</script>

<style lang="scss" scoped>
	.i-main {
		background: #F9F9F9;
		font-size: 24rpx;
		color: #000000;
	}

	.detail {
		padding-bottom: 100rpx;
		background-color: #F9F9F9;
		height: 100%;

		.card-box {
			width: 686rpx;
			margin: 0 auto;
			margin-top: 10px;
			border-radius: 16rpx;
			padding: 12rpx 28rpx;
			background-color: #FFFFFF;
			min-height: 112rpx;

			.icon {
				color: #3862F8;
			}

		}

		.content-card {
			width: 686rpx;
			margin: 20rpx auto;
			background-color: #FFFFFF;
			min-height: 224rpx;
			border-radius: 8px;
			padding: 24rpx 0 0 28rpx;

			.title-text {
				color: #91949F;
				font-size: 28rpx;
			}

			.file-s {
				width: 116rpx;
				height: 116rpx;
				display: flex;
				justify-content: center;
				flex-direction: column;
				text-align: center;
				background: #F9F9F9;
				position: relative;

				text {
					color: #91949F;
					font-size: 20rpx;
				}

				.close-icon {
					border-radius: 50%;
					width: 32rpx;
					height: 32rpx;
					background-color: red;
					line-height: 32rpx;
					text-align: center;
					position: absolute;
					right: 0;
					top: 0;
				}
			}

			.margin-center {
				margin: 0 auto;

				.icon {
					font-size: 48rpx;
				}
			}
		}

		.cc {
			width: 160rpx;
			height: 80rpx;
			background-color: #dedede;
			margin: 4rpx;
		}
	}

	// .address {
	// 	width: 50%;
	// 	overflow: hidden;
	// 	white-space: nowrap;
	// 	text-overflow: ellipsis;
	// }
	.map {
		height: 80rpx;
		width: 100%;
	}

	::v-deep .u-form-item__body {
		padding: 16rpx;
	}

	::v-deep .u-form-item {
		padding: 0;
	}
</style>