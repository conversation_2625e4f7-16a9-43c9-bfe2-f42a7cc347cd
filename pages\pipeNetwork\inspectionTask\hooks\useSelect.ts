import { ref } from 'vue'
export const useSelect = (defaultValues ?: any) => {

	const list = ref<any[]>([])
	const selected = ref<string | number | any[]>(defaultValues)

	const show = ref<boolean>(false)
	const toggle = (flag : boolean, callBacK ?: () => any) => {
		show.value = flag
		callBacK()
	}
	const setList = async (set : any[] | (() => any)) => {
		try {
			list.value = typeof set === 'function' ? await set() : set
			console.log('setList', list.value);
		} catch (e) {
			//TODO handle the exception
			console.log('获取列表错误');
		}
	}
	/**
	 * 会对指定的字段进行赋值
	 */
	const change = (indexs : any, callBack : (val : any) => any) => {
		selected.value = indexs
		console.log(indexs);
		const result = list.value[indexs[0]]
		console.log(indexs, result);
		callBack(result)
	}
	const optionChange = (selects : any, callBack : (val : any) => any) => {
		selected.value = selects
		const result = selected.value[0]
		callBack(result)
	}
	return {
		setList,
		list,
		selected,
		show,
		change,
		toggle,
		optionChange
	}
}