<!-- 巡检上报 -->
<template>
	<view class="main">
		<view class="card-box">
			<u-form :model="form" ref="uForm1" :label-style="{'color':'#91949F'}" borderBottom :error-type=" ['border-bottom']" labelWidth="180"  input-align="right" >
				<u-form-item label="关键点名称：" prop="name" required>
					<u-input v-model="form.name" inputAlign="right">
					</u-input>
				</u-form-item>
				<u-form-item label="有无隐患：" prop="val" required>
					<template #right>
						<u-switch slot="right" v-model="form.val"></u-switch>
					</template>
				</u-form-item>
			</u-form>
		</view>
		<view class="detail">
			<view class="content-card">
				<text>巡检图片：</text>
				<view class="camera">
					<view class="margin-center">
						<text class="custom-icon custom-icon-xiangji icon"></text>
						<!-- <u-image src="/static/img/icons/camera.png" width="48" height="48"></u-image> -->
					</view>
					<text>拍照上传</text>
				</view>
			</view>
			<view class="content-card">
				<text>添加录音：</text>
				<view class="camera">
					<view class="margin-center">
						<text class="custom-icon custom-icon-htmal5icon13 icon"></text>
					</view>
					<text>添加录音</text>
				</view>
			</view>
			<view class="content-card">
				<text>添加视频：</text>
				<view class="camera">
					<view class="margin-center">
						<text class="custom-icon custom-icon-shipinvideo icon"></text>
					</view>
					<text>添加视频</text>
				</view>
			</view>
			<view class="content-card">
				<text>添加文件：</text>
				<view class="camera">
					<view class="margin-center">
						<text class="custom-icon custom-icon-wenjianjia icon"></text>
					</view>
					<text>添加文件</text>
				</view>
			</view>
		</view>
		<view class="button">
			<u-button type="primary" color="#3862F8" @click="submit">提交</u-button>
		</view>
	</view>
</template>

<script lang="ts" setup>
	import {
		onMounted,
		reactive,
		ref
	} from "vue";
	const uForm1 = ref < any > ({})
	const form = reactive < any > ({})
	const rules = reactive < any > ({
		name: [{
			required: true,
			message: '请输入关键点名称',
			// 可以单个或者同时写两个触发验证方式 
			trigger: ['change', 'blur'],
		}]
	})

	//
	const submit = () => {
		uForm1.value.validate(valid => {
			if (valid) {
				console.log('验证通过');
			} else {
				console.log('验证失败');
			}
		});
	}
	onMounted(() => {
		uForm1.value.setRules(rules)
	})
</script>

<style lang="scss" scoped>
	.main {
		padding-top: 20rpx;

		.card-box {
			width: 686rpx;
			border-radius: 8px;
			padding: 20rpx 28rpx;
			margin: 0 auto;
			color: #FFFFFF;
			background-color: #FFFFFF;

			.form {
				padding: 0 28rpx 10rpx 28rpx;
				color: #060F27;
			}

			.l-file {
				padding: 10rpx 0;
			}
		}

		::v-deep.u-form-item {
			padding: 10rpx;
		}

		.detail {
			.content-card {
				width: 686rpx;
				margin: 20rpx auto;
				background-color: #FFFFFF;
				min-height: 224rpx;
				border-radius: 8px;
				padding: 24rpx 0 0 28rpx;

				text {
					color: #91949F;
					font-size: 28rpx;
				}

				.camera {
					width: 112rpx;
					height: 112rpx;
					margin-top: 20rpx;
					display: flex;
					justify-content: center;
					flex-direction: column;
					text-align: center;
					background: #F9F9F9;

					text {
						color: #91949F;
						font-size: 20rpx;
					}
				}

				.margin-center {
					margin: 0 auto;

					.icon {
						font-size: 48rpx;
					}
				}
			}
		}
	}
</style>
