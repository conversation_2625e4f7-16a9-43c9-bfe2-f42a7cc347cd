declare namespace UniApp {
  interface RequestSuccessCallbackResult {
    data: any;
    statusCode: number;
    header: any;
    cookies: string[];
    config?: any; // 添加 config 属性
  }

  interface RequestOptions {
    url: string;
    data?: any;
    header?: any;
    method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
    timeout?: number;
    dataType?: string;
    responseType?: 'text' | 'arraybuffer';
    success?: (result: RequestSuccessCallbackResult) => void;
    fail?: (err: any) => void;
    complete?: (res: any) => void;
  }

  interface ShowModalRes {
    cancel: boolean;
    confirm: boolean;
  }

  interface NavigateToSuccessCallbackResult {
    errMsg: string;
  }

  function request(options: RequestOptions): void;
  function getStorageSync(key: string): any;
  function showModal(options: any): void;
  function reLaunch(options: any): void;
  function showLoading(options: any): void;
  function hideLoading(): void;
  function showToast(options: any): void;
}

declare const uni: UniApp;
