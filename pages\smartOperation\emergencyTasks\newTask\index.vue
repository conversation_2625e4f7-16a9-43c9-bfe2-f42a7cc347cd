<!-- 新增应急 -->
<template>
	<view>
		<u-navbar fixed placeholder safeAreaInsetTop bgColor="#FFFFFF" title="应急任务" :autoBack="true" leftIconSize="20"
			input-align="right">
			<template #right>
				<view class="nv-right">
					<span class="primaryColor" @click="submitForm">提交</span>
				</view>
			</template>
		</u-navbar>
		<view class="i-main">
			<view class="detail">
				<u-form :model="form" ref="refForm" :label-style="{'color':'#91949F'}" borderBottom labelWidth="220"
					input-align="right" :error-type="['toast']">
					<view class="card-box">
						<u-form-item label="应急任务名称：" required prop="name" borderBottom>
							<u-input placeholder="请输入应急任务名称" v-model="form.name" input-align="right">
							</u-input>
						</u-form-item>
						<u-form-item label="应急任务分类：" required prop="typeName" borderBottom>
							<template #right>
								<u-icon name="arrow-right" size="28" color="#91949F"></u-icon>
							</template>
							<input placeholder="请选择应急任务分类" placeholder-class="placeholderClass" inputmode="none"
								v-model="form.typeName" input-align="right" @click="state.typesShow=true">
							</input>
						</u-form-item>
						<u-form-item label="点位：" required prop="stationName" borderBottom>
							<template #right>
								<u-icon name="arrow-right" size="28" color="#91949F"></u-icon>
							</template>
							<input v-model="form.stationName" placeholder-class="placeholderClass" inputmode="none"
								placeholder="请选择点位" input-align="right" @click="chooseStation">
							</input>
						</u-form-item>
						<u-form-item label="应急预案：" prop="planName" borderBottom>
							<template #right>
								<u-icon name="arrow-right" size="28" color="#91949F"></u-icon>
							</template>
							<input v-model="form.planName" placeholder-class="placeholderClass" inputmode="none"
								placeholder="请选择应急预案" input-align="right" @click="state.planShow=true">
							</input>
						</u-form-item>
					</view>
					<u-divider color="#3862F8" bg-color="#F9F9F9" fontSize="36" margin-top="20"
						v-if="state.currentType===0">预案内容</u-divider>
					<view class="card-box">
						<u-form-item label="事件地址：" required prop="address" borderBottom>
							<u-input v-model="form.address" placeholder="请输入事件地址" input-align="right"
								@blur="changeAddress">
							</u-input>
							<template #right>
								<view @click="chooseAddress" class="dw">
									<image src="/static/img/icons/dw.png" style="height:40rpx;width:40rpx">
									</image>
								</view>
							</template>
						</u-form-item>
						<u-form-item label="处理责任人：" prop="circuitUserName" required labelWidth="300rpx">
							<template #right>
								<u-icon name="arrow-right" size="28" color="#91949F"></u-icon>
							</template>
							<input v-model="form.circuitUserName" placeholder-class="placeholderClass" inputmode="none"
								placeholder="选择处理责任人" input-align="right" @click="chooseAUser">
							</input>
						</u-form-item>
						<u-form-item label="联系电话：" prop="circuitUserPhone" required>
							<u-input v-model="form.circuitUserPhone" placeholder="联系电话">
							</u-input>
						</u-form-item>
						<u-form-item label="应急原因：" prop="pondingReason" borderBottom>
							<u-input placeholder="请输入应急原因" v-model="form.pondingReason" input-align="right">
							</u-input>
						</u-form-item>
						<u-form-item label="解决措施或建议：" :borderBottom="false" labelWidth="300rpx">
						</u-form-item>
						<u-form-item label="" prop="solution" borderBottom>
							<u-input type="textarea" input-align="left" v-model="form.solution" placeholder="请输入解决措施或建议"
								border color="#91949F">
							</u-input>
						</u-form-item>
						
						<!-- <u-form-item label="巡查责任人工作职责：" :borderBottom="false" labelWidth="300rpx">
						</u-form-item>
						<u-form-item label="" prop="circuitDuty" borderBottom>
							<u-input type="textarea" input-align="left" v-model="form.circuitDuty"
								placeholder="请输入巡查责任人工作职责" border color="#91949F">
							</u-input>
						</u-form-item> -->
					</view>
				</u-form>
			</view>
			<u-picker v-model="state.planShow" mode="selector" :default-selector="state.planIndex" :range="planListData"
				range-key="name" @confirm="selectPlan"></u-picker>

			<u-picker v-model="state.typesShow" mode="selector" :default-selector="state.typeIndex"
				:range="taskTypeData" range-key="name" @confirm="selectType"></u-picker>
			<!-- 防汛车辆 -->
			<u-picker v-model="state.carShow" mode="selector" :default-selector="state.carIndex" :range="taskCarData"
				range-key="licensePlate" @confirm="selectCar"></u-picker>
			<!-- 选择水司人员 -->
			<next-tree :selectParent="false" :checkStrictly="false" :ifSearch="false" ref="refWUser"
				@cancel="state.hideMap=false" :treeData="organizations" :loadData="loadData"
				@confirm="chooseWUserEvent" />
			<!-- 选择镇街人员 -->
			<next-tree :selectParent="false" :checkStrictly="false" :ifSearch="false" ref="refAUser"
				@cancel="state.hideMap=false" :treeData="organizations" :loadData="loadData"
				@confirm="chooseAUserEvent" />
			<!-- 内涝点 -->
			<next-tree :selectParent="false" labelKey="name" valueKey="id" :checkStrictly="false" :ifSearch="false"
				@cancel="state.hideMap=false" ref="refStation" :treeData="stationTreeData"
				@confirm="chooseStationEvent" />
			<!-- 镇街 -->
			<next-tree :selectParent="false" labelKey="name" valueKey="id" :checkStrictly="false" :ifSearch="false"
				@cancel="state.hideMap=false" ref="refArea" :treeData="areaTreeData" @confirm="chooseAreaEvent" />
			<!-- 物资选择 -->
			<next-tree :multiple="true" labelKey="name" valueKey="id" :ifSearch="false" @cancel="state.hideMap=false"
				ref="refMaterial" :treeData="materialData" @confirm="chooseMaterialEvent" />
			<u-toast ref="refToast" />
		</view>
	</view>
</template>

<script lang="ts" setup>
	import {
		useStore
	} from '@/store/index'
	import {
		onReady,
		onShow
	} from '@dcloudio/uni-app';
	import {
		onBeforeMount,
		computed,
		onMounted,
		reactive,
		ref,
		unref,
		watch,
		nextTick
	} from "vue";
	// import fileUpload from '@/components/fileUpload/fileUpload.vue'
	import nextTree from '@/uni_modules/next-tree/components/next-tree/next-tree.vue'
	import {
		userTypes,
	} from '@/common/data/publicData'
	import {
		createdTask,
		materialStoreList,
		planList,
		areaTree,
		taskTypeList,
		stationTree,
		circuitCarList
	} from '@/common/api/emergencyTasks'
	import {
		getOrganization
	} from '@/common/data/workOrderData'
	import {
		queryGeocoder
	} from '@/common/api/map'
	import {
		storeToRefs
	} from 'pinia'
	import {
		removeSlash
	} from '@/common/utils/removeIdSlash';
	const store = useStore();
	const refAUser = ref()
	const refStation = ref()
	const refWUser = ref()
	const refArea = ref()
	const refMaterial = ref()
	const planListData = ref([])
	const materialData = ref([])
	const taskCarData = ref([])
	const areaTreeData = ref([])
	const stationTreeData = ref([])
	const state = reactive<{
		types : any,
		typesShow : boolean,
		windowHeight : Number,
		planShow : boolean,
		carShow : boolean,
		organizationShow : boolean,
		hideMap : boolean,
		typeIndex : any,
		carIndex : any,
		planIndex : any,
		userTypes : any,
		latitude : Number,
		currentType : Number,
		currentassignmentType : Number,
		longitude : Number
	}>({
		types: [{
			value: '1',
			name: '曾发生内涝点',
		}, {
			value: '2',
			name: '新发生内涝点',
		}],
		windowHeight: 1000,
		typesShow: false,
		organizationShow: false,
		hideMap: false,
		planShow: false,
		carShow: false,
		typeIndex: [0],
		planIndex: [0],
		carIndex: [0],
		userTypes: userTypes,
		currentType: 0,
		currentassignmentType: 0,
		longitude: 0,
		latitude: 0,
	})
	const rules = reactive<any>({
		name: [{
			required: true,
			message: '请输入应急任务名称',
		}],
		typeName: [{
			required: true,
			message: '请选择应急分类',
			trigger: ['change', 'blur'],
		}],
		stationName: [{
			required: true,
			message: '内涝点不能为空',
		}],
		stationId: [{
			required: true,
			message: '内涝点不能为空',
		}],
		// address: [{
		// 	required: true,
		// 	message: '请输入地址',
		// 	trigger: ['change', 'blur'],
		// }],
		// planName: [{
		// 	required: true,
		// 	message: '请选择防汛预案',
		// 	trigger: ['change', 'blur'],
		// }],
		// pondingReason: [{
		// 	required: true,
		// 	message: '请输入积水原因',
		// }],
		// solution: [{
		// 	required: true,
		// 	message: '请输入解决措施或建议',
		// }],
		// areaName: [{
		// 	required: true,
		// 	message: '所属镇街不能为空'
		// }],
		// mainUserName: [{
		// 	required: true,
		// 	message: '水务局责任人不能为空',
		// }],
		// mainUserPhone: [{
		// 	required: true,
		// 	message: '水务局责任人电话不能为空',
		// }],
		circuitUserName: [{
			required: true,
			message: '镇街巡查责任人不能为空',
		}],
		circuitUserPhone: [{
			required: true,
			message: '镇街巡查责任人电话不能为空',
		}],
		// circuitDuty: [{
		// 	required: true,
		// 	message: '巡查责任人工作职责不能为空',
		// }]
	})
	const refToast = ref<any>();
	const refForm = ref<any>()
	const taskTypeData = ref<any>([])
	const organizations = ref<any>([])
	const controls = ref<any>([{
		id: '1',
		iconPath: '../../../static/img/icons/dw.png',
		position: { //控件在地图的位置
			left: 140,
			top: 30,
			width: 20,
			height: 20,
		},
	}])

	const mapShow = computed(() => {
		return state.typesShow ||
			state.organizationShow ||
			state.planShow ||
			state.hideMap ||
			state.carShow
	})
	const chooseAddress = () => {
		uni.navigateTo({
			url: '/pages/map/tianMap'
		})
	}
	watch(() => state.currentType,
		() => {
			if (state.currentType === 1) {
				form.value = {
					name: form.value.name,
					type: form.value.type,
					typeName: form.value.typeName,
					stationName: '',
					stationId: ''
				}
			}
		})

	const data = ref<any>({
		name: '',
		type: '',
		typeName: ''
	})
	const form = ref<any>({
		address: '',
		time: '',
		source: '',
		ccUserId: []
	})

	//地址转经纬度
	const changeAddress = () => {
		queryGeocoder({ keyWord: form.value.address }).then((res : any) => {
			const location = res.data?.location
			state.latitude = location.lat
			state.longitude = location.lon
		})
	}
	//选择镇街人员弹框
	const chooseAUser = () => {
		// state.hideMap = true
		// unref(refAUser).showTree = true
		uni.$u.route({
			url: 'pages/userTree/organization',
		})
	}

	//内涝点弹框
	const chooseStationEvent = async (val : any) => {
		console.log(val);
		// form.value.stationId = val[0]?.id
		// form.value.stationName = val[0]?.name
		// form.value.address = val[0]?.nodeDetail?.address
		const res = await planList({
			page: 1,
			size: 99999,
			stationId: form.value.stationId
		})
		form.value = {
			stationId: val[0]?.id,
			stationName: val[0]?.name,
			address: val[0]?.nodeDetail?.address,
			name: form.value.name,
			type: form.value.type,
			typeName: form.value.typeName,

		}
		planListData.value = res.data?.data?.data
		console.log(planListData.value);
		state.hideMap = false
	}

	// 选择区域
	const chooseAreaEvent = async (val : any) => {
		console.log(val);
		form.value.areaId = val[0]?.id
		form.value.areaName = val[0]?.name
		const res = await materialStoreList({
			page: 1,
			size: 99999,
			areaId: form.value.areaId
		})
		materialData.value = res.data?.data?.data
	}

	//选择水务局责任人
	const chooseWUserEvent = (val : any) => {
		form.value.mainUserId = val[0]?.userId
		form.value.mainUserName = val[0]?.nickName
		form.value.mainUserPhone = val[0]?.phonenumber
	}
	// 选择部门
	const chooseAUserEvent = (val : any) => {
		console.log(val);
		form.value.circuitUserName = val[0]?.nickName
		form.value.circuitUserId = val[0]?.id
		form.value.circuitUserPhone = val[0]?.phonenumber
	}
	//添加物资
	const chooseMaterialEvent = (val : any) => {
		console.log(val);
		form.value.items = val.map(data => {
			return {
				...data,
				totalNum: data.num,
				num: 0,
				id: ''
			}
		})
		// form.value.circuitUserId = val[0]?.id
	}
	//异步加载部门用户列表
	const loadData = (val : any) => {
		console.log('loadData', val);
		return userTree.loadUserData(val)
	}

	// 选择事件类型
	const selectType = (val : any) => {
		console.log(taskTypeData.value)
		state.typeIndex = val
		const type = taskTypeData.value[val[0]]
		form.value.typeName = type.name
		form.value.type = type.id

	}
	// 选防汛车辆
	const selectCar = (val : any) => {
		state.carIndex = val
		const car = taskCarData.value[val[0]]
		form.value.licensePlate = car.licensePlate
		form.value.carId = car.id
	}
	// 选择应急预案
	const selectPlan = (val : any) => {
		state.planIndex = val
		const plan = planListData.value[val[0]]
		form.value.planName = plan.name
		form.value.planId = plan.id
		form.value = {
			...form.value,
			...plan,
			name: form.value.name,
			type: form.value.type,
			typeName: form.value.typeName,
		}

		console.log(form)
	}

	//提交工单
	const submitForm = () => {
		console.log(form.value)
		refForm.value.validate((valid : boolean) => {
			if (valid) {
				uni.showModal({
					title: '提示',
					content: '确定提交',
					success: function (res) {
						if (res.confirm) {
							const params = {
								...data.value,
								...form.value
							}
							console.log('提交数据', params)
							createdTask(params).then(res => {
								if (res.data?.code === 200) {
									refToast.value.show({
										title: '提交成功',
										type: 'success',
										back: true
									})
									let {
										userData
									} = storeToRefs(store);
									userData.value = null
								} else {
									refToast.value.show({
										title: res.data?.msg,
										type: 'error',
									})
								}
							}).catch(() => {
								refToast.value.show({
									title: '提交失败',
									type: 'error',
								})
							})
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			} else {
				console.log('验证失败');
			}
		})
	}
	const getConfig = async () => {
		const res1 = await stationTree()
		const treeData = res1.data?.data
		for (const i in treeData) {
			changeParentProperty(treeData[i])
		}
		stationTreeData.value = treeData
		const res2 = await areaTree()
		areaTreeData.value = res2.data?.data
		const res3 = await taskTypeList({
			page: 1,
			size: 99999
		})

		taskTypeData.value = res3.data?.data?.data
		const res4 = await circuitCarList({
			page: 1,
			size: 99999,
			type: '2'
		})
		taskCarData.value = res4.data?.data?.data
	}

	const changeParentProperty = (node) => {
		if (node.children?.length == 0) {
			return; // 递归终止条件：没有子节点
		}

		node.children.forEach(child => {
			child.showChecked = child.type === 'station'; // 更改子节点的父属性为当前节点
			changeParentProperty(child); // 递归调用处理子节点的子节点
		});
	}
	onBeforeMount(async () => {
		organizations.value = await getOrganization()
		await getConfig()
	})

	onMounted(async () => {
		// locaton()
		// 获取手机信息
		let info = uni.getSystemInfoSync()
		//顶部高度
		state.windowHeight = info.windowHeight
	})

	onReady(() => {
		refForm.value.setRules(rules);
	})

	const onLoadUpdate = item => {
		form.value.address = item.address
		form.value.coordinate = item.coordinate.lat + ',' + item.coordinate.lon
	}
	// 新增抄送人员
	const chooseUser = (user : any) => {
		form.value.circuitUserName = user?.firstName
		form.value.circuitUserId = removeSlash(user.id?.id)
		form.value.circuitUserPhone = user.phone
	}
	onShow(() => {
		uni.$on("updateLocation", onLoadUpdate);
		uni.$on("chooseUserData", chooseUser);
	})
</script>

<style lang="scss" scoped>
	.i-main {
		background: #F9F9F9;
		font-size: 24rpx;
		color: #000000;
	}

	.detail {
		// padding-bottom: 100rpx;
		background-color: #F9F9F9;
		height: 100%;

		.card-box {
			width: 686rpx;
			margin: 0 auto;
			margin-top: 10px;
			border-radius: 16rpx;
			padding: 12rpx 28rpx;
			background-color: #FFFFFF;
			min-height: 112rpx;

			.icon {
				color: #3862F8;
			}

		}

		.content-card {
			width: 686rpx;
			margin: 20rpx auto;
			background-color: #FFFFFF;
			min-height: 224rpx;
			border-radius: 8px;
			padding: 24rpx 0 0 28rpx;

			.title-text {
				color: #91949F;
				font-size: 28rpx;
			}

			.file-s {
				width: 116rpx;
				height: 116rpx;
				display: flex;
				justify-content: center;
				flex-direction: column;
				text-align: center;
				background: #F9F9F9;
				position: relative;

				text {
					color: #91949F;
					font-size: 20rpx;
				}

				.close-icon {
					border-radius: 50%;
					width: 32rpx;
					height: 32rpx;
					background-color: red;
					line-height: 32rpx;
					text-align: center;
					position: absolute;
					right: 0;
					top: 0;
				}
			}

			.margin-center {
				margin: 0 auto;

				.icon {
					font-size: 48rpx;
				}
			}
		}

		.cc {
			width: 160rpx;
			height: 80rpx;
			background-color: #dedede;
			margin: 4rpx;
		}
	}

	// .address {
	// 	width: 50%;
	// 	overflow: hidden;
	// 	white-space: nowrap;
	// 	text-overflow: ellipsis;
	// }
	.map {
		height: 80rpx;
		width: 100%;
	}

	::v-deep .u-form-item__body {
		padding: 16rpx;
	}

	::v-deep .u-form-item {
		padding: 0;
	}
</style>