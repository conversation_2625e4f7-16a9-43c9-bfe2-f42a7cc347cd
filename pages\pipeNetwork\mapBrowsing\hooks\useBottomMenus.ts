import { computed, ref } from 'vue'
export const useBottomMenus = () => {
	const disabled = ref<boolean>(true)
	const menus = ref<{
		name: string, alias?: string, icon: string, id: string, color?: string
	}[]>([
		{
			id: 'space',
			name: '空间',
			icon: 'custom-icon-tijikongjian',
			color: '#6091F7'
		},
		{
			id: 'buffer',
			name: '缓冲区',
			icon: 'custom-icon-huanchongqufenxi',
			color: '#FBB934'
		},
		{
			id: 'area',
			name: '面积',
			icon: 'custom-icon-mianji',
			color: '#24D8AA'
		},
		{
			id: 'distance',
			name: '距离',
			icon: 'custom-icon-map-ruler-full',
			color: '#1FD3A7'
		},
		{
			id: 'reset',
			name: '重置',
			icon: 'custom-icon-shuaxin-zhongzhi-07',
			color: '#FC797C'
		}
	])
	const current = ref<string>('')
	const cleanMark = ref<any>(0)
	const setCurrent = (value: string) => {
		// 点击相同按钮则取消选中
		if(disabled.value) return
		if (current.value === value || value === 'reset') reset()
		else {
			current.value = value
		}
	}
	const curMenu = computed(() => {
		return menus.value.find(item => item.id === current.value)
	})
	const reset = () => {
		current.value = ''
		cleanMark.value++
	}
	const enable = () => {
		disabled.value = false
	}
	return {
		disabled,
		enable,
		curMenu,
		reset,
		setCurrent,
		current,
		cleanMark,
		menus
	}
}