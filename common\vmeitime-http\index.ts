import { createRequest } from './interface'

export const http = (data?: any) => {
	const token = uni.getStorageSync('token')
	const feeToken = uni.getStorageSync('feeToken')
	const request = createRequest({
		header: {
			// 'X-Authorization': 'bearer ' + uni.getStorageSync('token'),
			// 'Authorization':'Basic cm9vdDpJbmRhcmsxMTYhISE=',
			'Clientid': 'e5cd7e4891bf95d1d19206ce24a7b32e',
			'Content-Type': 'application/json'
		}
	})
	// request.config.baseUrl = 'http://127.0.0.1:8081' //'https://qnsw.sikezdh.com/' //接口地址
	// request.config.baseUrl = 'http://10.0.31.224:8848/'
	// request.config.baseUrl = ''

	// 根据环境设置baseUrl
	// 开发环境使用空字符串，由Vite代理处理
	// 生产环境使用实际API地址
	const isProd = process.env.NODE_ENV === 'production'
	request.config.baseUrl = isProd 
			? (uni.getStorageSync('apiBaseUrl') || '') 
			: ''

	request.interceptor.request = (config: any) => {
		config.header['X-Authorization'] = 'bearer ' + token
		config.header['Authorization'] = 'Bearer ' + feeToken
		config.header['Content-Type'] = 'application/json'

		// 如果是生产环境，需要确保URL正确
		if (isProd && config.url) {
				// 根据URL前缀决定使用哪个服务地址
				if (config.url.startsWith('server/')) {
						config.url = config.url.replace('server/', uni.getStorageSync('serverBaseUrl') || '')
				} else if (config.url.startsWith('api/')) {
						config.url = config.url.replace('api/', uni.getStorageSync('apiBaseUrl') || '')
				}
		}
		
		return config
	}

	request.interceptor.response = (response: any) => {
		if(response.data.code === 401){
			uni.showModal({
				title: '提示',
				content: '登录失效，请重新登录',
				success: function (res) {
					if (res.confirm) {
						 uni.reLaunch({
							url: '/pages/login/index',
							success(result) {
							}
						})
					} else if (res.cancel) {
						console.log('用户点击取消');
					}
				}
			});
			console.log(response.data.msg)
		}
		return response;
	},
		(Error: any) => {
			console.log('err: ' + Error)
		}
	return request
}


export default {
	http
}

