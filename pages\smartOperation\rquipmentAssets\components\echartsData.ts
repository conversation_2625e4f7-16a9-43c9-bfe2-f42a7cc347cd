export const pieOption = (data:any) => {
	const option = {
		toolbox: {
			show: false,
			feature: {
				mark: { show: true },
				dataView: { show: true, readOnly: false },
				restore: { show: true },
				saveAsImage: { show: true }
			}
		},
		series: [
			{
				name: 'Nightingale Chart',
				type: 'pie',
				radius: [60, 85],
				center: ['50%', '50%'],
				data: data
			}
		]
	}
	return option
}

export const lineOption = (dataX:string[],data:any) => {
	const option = {
		xAxis: {
			type: 'category',
			boundaryGap: false,
			data: dataX
		},
		yAxis: {
			type: 'value'
		},
		grid: {
			left: 50,
			right: 10,
			top: 20,
			bottom: 30
		},
		series: [
			{
				data: data,
				type: 'line',
				areaStyle: {}
			}
		]
	};

	return option
}