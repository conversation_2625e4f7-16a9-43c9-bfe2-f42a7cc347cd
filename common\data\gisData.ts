const gisService = "/geoserver/guazhou";
// const uni.getStorageSync('gisUtilitiesService') = "http://58.42.239.112:6080/arcgis/rest/services/Utilities/";
export const gisConfig = () => {
	const systemConfig = uni.getStorageSync('systemConfig')
	const gisCenter = systemConfig.gisCenter
	const gisUtilitiesService = systemConfig.gisUtilitiesService
	const gisApi = systemConfig.gisApi
	return {
		gisApi: gisApi,
		// center: [95.787329, 40.516879],
		center: [95.78315533648099, 40.5177779215204],
		zoom: 16,
		gisApiKey: 'AAPKbf4e22c18798464f9f15f46254975c13V9DFM0Tj5VyxUNjISsHJoWwb0OgwvhLNnh1HcRrP0-KYTPP3QWTys-xKqahU2Qac',
		// gisTdtToken: 'b861ce78777821c257068e757d41d878',
		// gisTdtToken: '0ce8cc64b83ec6a6c375b4250bf71cbf',
		// gisTdtToken: 'b67eed4cf99977d6412a29db83049e0c',
		// gisTdtToken: 'd6d795370b0f82267e9cc3694cd9b56e',
		// 企业
		// gisTdtToken: 'e4e98a7455967290863f2f1bb245f7b5',
		// 企业
		gisTdtToken: 'be4a83d8298d7aa84cb5e0ff3e1dad58',
		gisService: gisService,
		gisDefaultBaseMap: 'vec_w',
		// gisDefaultBaseMap: 'img_w',
		gisDefaultPoi: 'cva_w',
		// gisDefaultBaseMapFilterColor: 'rgba(255, 255, 255, 0.0)',
		gisDefaultCenter: gisCenter || undefined,
		gisProxyService: "/arcgis/rest/services/ANQING/",
		gisUtilitiesService: gisUtilitiesService,
		gisPipeDataService: gisService + "PIPE_QY_ANQING/MapServer",
		gisPipeDynamicService: gisService ,
		gisBurstGPService:
			gisService + "BurstPipeAnalyzenanbu/GPServer/BurstPipeAnalyzenanbu",
		gisConnectGPService:
			gisService + "ConnectedAnalyzenanbu/GPServer/ConnectedAnalyzenanbu",
		gisPathAnalysGPService:
			gisService + "PathAnalyzenanbu/GPServer/PathAnalyzenanbu",
		gisShutValveAnalysGPService:
			gisService +
			"ShutOffValveAnalyzebinzhou/GPServer/ShutOffValveAnalyzebinzhou",
		gisExtendShutValveAnalysGPService:
			gisService +
			"ShutOffValveExtendbinzhou/GPServer/ShutOffValveExtendbinzhou",
		gisFangDaGPService: gisService + "PIPE_QY_DEYANG_FANGDA/MapServer",
		gisGeometryService: gisUtilitiesService + "Geometry/GeometryServer",
		gisPrintTemplatePath:
			"D:/installation/gis/gisdata/geotool/tfprint/printtemplates",
		gisPrintGPService: gisService + "TFPrint/GPServer/TFPrint",
		gisPrintingToolsGPService:
			gisUtilitiesService + "Utilities/PrintingTools/GPServer",
		gisPipeFeatureServiceFeatureServer:
			gisService + "FeatureService/FeatureServer",
		gisPipeFeatureServiceMapServer: gisService + "FeatureService/MapServer",
	}

}