<template>
  <view class="cover-view-loca" @click="getLocation">
    <button class="cover-loca-image" :loading="location.loading.value">
      <image class="loca-btn" src="/static/img/icons/location-black.png" mode="widthFix"></image>
    </button>
  </view>
</template>

<script lang="ts" setup>
import { useLocation } from '@/common/hooks';
const location = useLocation();
defineProps<{ modelValue: string }>();
const emit = defineEmits(['update:modelValue']);
const getLocation = () => {
  console.log('locating');
  location.getLocation(() => {
    console.log('坐标已更新:', location.lonLatStr.value);
    emit('update:modelValue', location.lonLatStr.value);
  });
};
</script>

<style lang="scss" scoped>
.cover-view-loca {
  width: 80rpx;
  height: 80rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;

  .cover-loca-image {
    width: 40rpx;
    height: 40rpx;
    background-color: transparent;
    border: none;
    padding: 0;
    line-height: 40rpx;

    &::after {
      border: none;
      width: 40rpx;
      height: 40rpx;
      transform: scale(1);
    }

    .loca-btn {
      width: 40rpx;
      height: 40rpx;
    }
  }
}
</style>
