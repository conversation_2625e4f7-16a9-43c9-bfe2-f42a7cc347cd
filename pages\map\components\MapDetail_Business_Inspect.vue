<template>
	<view class="pipe-detail">
		<view class="pipe-detail-item pipe-detail-total">

			<view class="title">
				{{props.title}}统计
			</view>
			<view class="total-blocks">
				<view class="total-block" v-for="(item, i) in state.total" :key="i" :style="{
					backgroundColor: item.color
				}">
					<view class="value">{{item.value}} {{item.unit}}</view>
					<view class="text">{{item.title}}</view>
				</view>
			</view>
		</view>
		<view class="pipe-detail-item pipe-detail-list">
			<view class="title">{{props.title}}列表</view>
			<TableList :list="state.list" :list-columns="state.listColumns"></TableList>
		</view>
	</view>

</template>

<script lang="ts" setup>
	import TableList from './TableList.vue'
	import { onMounted, reactive } from 'vue'
	import { getInspectionTaskComplete, getInspectionTaskList } from '../../../common/api/inspection'
	const props = defineProps<{
		title ?: string
	}>()
	const state = reactive<{
		total : { color ?: string; value : number; title : string; unit ?: string }[],
		list : { name : string; status : string; lastTime : string }[]
		listColumns : { label : string; prop : string }[]
	}>({
		total: [
			{ color: 'rgb(82, 143, 248)', value: 0, title: '任务总数', unit: '条' },
			{ color: 'rgb(115, 203, 113)', value: 0, title: '完成率', unit: '%' },
		],
		list: [],
		listColumns: [
			{ label: '任务编号', prop: 'code' },
			{ label: '巡检人', prop: 'receiveUserName' },
			{ label: '状态', prop: 'statusName' }
		]
	})
	const refreshTable = async () => {
		try {
			const res = await getInspectionTaskList({ page: 1, size: 999 })

			if (res.data.code === 200) {
				state.list = res.data?.data?.data?.map((item : any) => {
					// item.lastTime = item.lastTime?.split(' ')?.[1]
					// item.statusAlia = item.status === 'offline' ? '离线' : item.status === 'alarm' ? '报警' : item.status === 'online' ? '正常' : ''
					return item
				}) || []
			} else {
				console.log('统计失败');
			}
		} catch (e) {
			console.log(e)
		}
	}
	const refreshData = async () => {
		refreshTable()
		getInspectionTaskComplete().then((res : any) => {
			const data = res.data?.data || {}
			state.total[0].value = data.total
			state.total[1].value = data.percent

		}).catch(() => {
		})
	}
	onMounted(() => {

		refreshData()
	})
</script>

<style lang="scss" scoped>
	.pipe-detail {
		padding-bottom: 210rpx;

		.pipe-detail-item {
			.title {
				font-size: 32rpx;
				margin: 32rpx 0 0;

			}

			&:first-child {
				.title {
					margin-top: 0;
				}
			}
		}

		.pipe-detail-total {
			.total-blocks {
				border-radius: 4rpx;
				width: 100%;
				display: flex;
				padding: 24rpx;
				flex-wrap: wrap;
				justify-content: flex-start;

				.total-block {
					background-color: #0073ff;
					padding: 10rpx;
					color: #fff;
					width: calc(50% - 24rpx);
					height: 120rpx;
					margin: 12rpx;
					text-align: center;

					.text,
					.value {
						line-height: 50rpx;
					}
				}
			}
		}

		.pipe-detail-chart {
			.chart-box {
				height: 480rpx;
			}
		}

	}
</style>