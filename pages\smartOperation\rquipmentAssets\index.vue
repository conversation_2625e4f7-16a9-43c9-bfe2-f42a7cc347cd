<template>
	<view>
		<u-navbar title="设备资产" sticky>
			<template #right>
				<view style="padding-right: 24rpx;" @click="scanCode">
					<u-icon name="custom-icon-iconfontscan" customPrefix="custom-icon" size="48" color="#2979ff">
					</u-icon>
				</view>
			</template>
		</u-navbar>
		<u-sticky bgColor="#FFFFFF">
			<view class="search-box flex-between">
				<view style="width: 90%;">
					<u-search shape="square" :showAction="false" v-model="state.keyword" placeholder="请输入设备名称"
						@change="searchKeyWord"></u-search>
				</view>
				<view @click="state.showPopup = true">
					<image src="/static/img/icons/shaixuan-black.png" style="height: 48rpx;width: 48rpx;"></image>
				</view>
			</view>
		</u-sticky>
		<view class="card-list">
			<scroll-view scroll-y style="height: 90vh;" scroll-with-animation="true" v-if="deviceList.length>0"
				@scrolltolower="scrolltolower">
				<view class="card flex-between" v-for="(device,index) in deviceList" :key="index"
					@click="toDetail(device.id,device.deviceLabelCode)">
					<view class="left flex-center">
						<view class="image">
							<image src="/static/img/icons/Group4.png" style="height: 48rpx;width: 48rpx;"></image>
						</view>
						<view class="info">
							<view class="name">
								{{device.name}}
							</view>
						</view>
					</view>
					<view class="right flex-center">
						<u-icon name="arrow-right" color="#91949F" size="28"></u-icon>
					</view>
				</view>
				<u-loadmore :status="state.loadmore"></u-loadmore>
			</scroll-view>
			<view class="uempty" v-else>
				<u-empty></u-empty>
			</view>
		</view>
		<u-popup mode="right" v-model="state.showPopup" :closeOnClickOverlay="true" :overlay="true" @close="close">
			<!-- <view class="area-list">
					<view class="area" v-for="(item,index) in areaGroup" :key="index">
						<view class="title">
							{{item.name}}
						</view>
						<view class="flex-between box-list">
							<view @click="state.currentProjectId = area.id" v-for="(area,i) in item.children" :key="i"
								:class="['box',{'check-box':state.currentProjectId===area.id,'max-box':area.name.length>5}]">
								{{area.name}}
							</view>
						</view>
					</view>
				</view> -->
			<view class="popup">
				<view class="screen-list">
					<u-form :model="screenForm" ref="form1" :label-style="{'color':'#91949F'}" border-bottom
						label-width="180">
						<u-form-item label="我的收藏：" prop="screenForm.queryTypeName">
							<template #right>
								<u-switch v-model="state.isCollect" active-value="1" inactive-value="0"></u-switch>
							</template>
						</u-form-item>
						<u-form-item label="查询类型：" prop="screenForm.queryTypeName">
							<input fontSize="14" placeholderClass="placeholderClass" inputmode="none"
								placeholder-class="placeholderClass" placeholder="点击选择"
								v-model="screenForm.deviceTypeName" @click="state.typeShow=true" clearable>
							</input>
							<template #right>
								<u-icon name="arrow-down" color="#91949F" size="34"></u-icon>
							</template>
						</u-form-item>
					</u-form>
				</view>
				<view class="popupButton">
					<u-button type="primary" color="#3862F8" @click="submit">确定</u-button>
				</view>
			</view>
		</u-popup>
		<u-select v-model="state.typeShow" mode="mutil-column-auto" valueName="id" labelName="name" :list="areaGroup"
			@confirm="confirm"></u-select>
	</view>
</template>

<script lang="ts" setup>
	import {
		onMounted,
		reactive,
		ref
	} from "vue"
	import {
		onPullDownRefresh
	} from '@dcloudio/uni-app'
	import {
		getDeviceTypeTree,
		getDeviceStorageJournalList
	} from '@/common/api/rquipmentAssets'
	const state = reactive<{
		keyword : string,
		showPopup : boolean,
		checkAreaId : string,
		currentProjectId : string,
		secondaryWaterList : any,
		typeShow : boolean,
		loadmore : string,
		isCollect : any
	}>({
		keyword: '',
		showPopup: false,
		checkAreaId: '11',
		currentProjectId: '',
		secondaryWaterList: [],
		typeShow: false,
		loadmore: 'nomore',
		isCollect: false
	})
	let deviceList = ref([])
	let areaGroup = ref([])
	const screenForm = reactive<any>({
		page: 1,
		size: 20,
		deviceTypeId: '',
		deviceTypeName: '',
	})
	// 选择区域
	const scanCode = () => {
		uni.scanCode({
			onlyFromCamera: false,
			autoZoom: true,
			success: function (res) {
				uni.$u.route({
					url: 'pages/smartOperation/rquipmentAssets/deviceDetail',
					params: {
						id: res.result
					}
				})
			}
		});
		// state.showPopup = true
	}
	// 关闭弹框
	const close = () => {
		state.showPopup = false
	}
	const confirm = (e) => {
		const type = e[e.length - 1]
		console.log(type)
		screenForm.deviceTypeName = type.label
		screenForm.deviceTypeId = type.value
	}
	// 查看压力详情
	const toDetail = (id : string) => {
		uni.$u.route({
			url: 'pages/smartOperation/rquipmentAssets/deviceDetail',
			params: {
				id
			}
		})
	}
	// 选择区域
	const submit = () => {
		// 查询列表
		screenForm.isCollect = state.isCollect ? '1' : ''
		screenForm.page = 1
		refreshData()
		close()
	}

	const scrolltolower = () => {
		screenForm.page += 1
		refreshData()
	}

	// 查询
	const searchKeyWord = (val : string) => {
		screenForm.name = val
		screenForm.page = 1
		refreshData()
	}
	onPullDownRefresh(async () => {
		refreshData()
	})
	// 刷新数据
	const refreshData = async () => {
		uni.showLoading({
			"title": "加载中",
			"mask": true
		})
		try {
			const res = await getDeviceStorageJournalList(screenForm)
			const data = res.data?.data?.data
			const total = res.data?.data?.total
			state.loadmore = 'loading'
			if (screenForm.page === 1) {
				deviceList.value = data
			} else {
				deviceList.value = deviceList.value.concat(data)
			}
			if (data?.length > 0 && total > deviceList.value.length) {
				screenForm.page += 1
				state.loadmore = 'loadmore'
			} else {
				state.loadmore = 'nomore'
			}
			uni.hideLoading()
		} catch (e) {
			//TODO handle the exception
			uni.hideLoading()
		}
		uni.stopPullDownRefresh()
	}

	onMounted(async () => {
		uni.showLoading({
			"title": "加载中",
			"mask": true
		})
		//获取区域
		try {
			const res = await getDeviceTypeTree()
			const data = res.data?.data
			areaGroup.value = data
			console.log(data)
			// state.currentProjectId = data[0]?.children[0]?.id
			refreshData()
		} catch (e) {
			uni.hideLoading()
		}

	})
</script>

<style lang="scss" scoped>
	.search-box {
		padding: 20rpx 32rpx;
	}

	.card-list {
		padding: 0 32rpx;

		.card {
			background-color: #FFFFFF;
			border-radius: 16rpx;
			padding: 22rpx 44rpx;
		}

		.left {
			.info {
				padding-left: 16rpx;

				.name {
					color: #060F27;
					font-weight: bold;
				}

				.time {
					color: #91949F;
					font-size: 20rpx;
				}
			}
		}

		.right {
			text {
				&:nth-child(1) {
					color: #3862F8;
					font-weight: bold;
				}

				&:nth-child(2) {
					color: #91949F;
					padding-right: 10rpx;
				}
			}
		}
	}

	// .popup {
	// 	width: 100%;
	// 	height: 100%;
	// 	position: relative;

	// 	.area-list {
	// 		width: 528rpx;

	// 		.area {
	// 			padding: 20rpx 24rpx;

	// 			.title {
	// 				font-weight: bold;
	// 			}

	// 			.box-list {
	// 				flex-wrap: wrap;
	// 				height: 90%;

	// 				.box {
	// 					min-width: 228rpx;
	// 					height: 64rpx;
	// 					background: #FFFFFF;
	// 					border: 2rpx solid #EBEDF6;
	// 					border-radius: 8rpx;
	// 					margin: 20rpx 0rpx;
	// 					text-align: center;
	// 					line-height: 64rpx;
	// 				}

	// 				.max-box {
	// 					width: 480rpx;
	// 				}

	// 				.check-box {
	// 					border: 2rpx solid #3862F8;
	// 					color: #3862F8;
	// 				}
	// 			}

	// 		}
	// 	}
	// }
	.popup {
		width: 100%;
		height: 100%;
		position: relative;

		.screen-list {
			padding: 222rpx 34rpx;
		}

		.popup-button {
			width: 94%;
			position: absolute;
			bottom: 40rpx;
			left: 0;
			right: 0;
			margin: 0 auto;
		}

		::v-deep .placeholderClass {
			font-size: 28rpx;
		}
	}
</style>