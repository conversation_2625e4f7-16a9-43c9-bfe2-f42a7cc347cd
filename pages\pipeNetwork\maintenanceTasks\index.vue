<template>
	<view class="main">
		<u-sticky bg-color="#FFFFFF">
			<u-tabs :list="state.tabs" active-color="#3862F8" v-model="state.currentTab" bg-color="#FFFFFF"
				:offset="[0,120]" :is-scroll="false" @change="changeStatus" count="count">
			</u-tabs>
		</u-sticky>
		<!-- <scroll-view :lower-threshold="50" :refresher-threshold="50" scroll-y refresher-enabled
			refresher-default-style="white" :refresher-triggered="triggered" @refresherrefresh="onRefresh"
			@scrolltolower="showMoreData"> -->
		<view class="card-box" v-for="(data,index) in tableData" :key="index" @click="toDetail(data)">
			<view class="flex-between hand">
				<view class="hand-title">
					<text>{{data.name}}</text>
				</view>
				<view class="status">
					{{data.statusName || '处理中'}}
				</view>
			</view>
			<view class="table">
				<view class="info flex-center">
					<text>养护设备类型：</text>
					<view class="bg">
						<text>{{data.deviceName}}</text>
					</view>
				</view>
				<view class="info">
					<text>开始时间：</text> <text>{{data.beginTime}}</text>
				</view>
				<view class="info">
					<text>结束时间：</text> <text>{{data.endTime}}</text>
				</view>
				<view class="info flex-between">
					<view>
						<text>已养护：</text> <text>{{data.completedTaskItemCount || 0}}</text>
					</view>
					<view>
						<text>未养护：</text> <text>{{(data.totalTaskItemCount-data.completedTaskItemCount) || 0}}</text>
					</view>
				</view>
			</view>
		</view>

		<u-loadmore bg-color="F9F9F9" :status="state.status" loading-text="努力加载中" loadmore-text="加载更多"
			nomore-text="没有了更多" />
		<!-- </scroll-view> -->
		<!-- <view class="">
			<u-calendar :max-date="maxDate" v-model="state.dateShow" ref="calendar" @close="state.dateShow=false"
				@change="chooseDate"></u-calendar>
		</view> -->
	</view>
</template>

<script lang="ts" setup>
	import {
		onMounted,
		reactive,
		ref
	} from "vue"
	import {
		onPullDownRefresh,
		onReachBottom
	} from '@dcloudio/uni-app'
	import {
		maintainTaskList
	} from '@/common/api/maintenanceTasks'
	// import {
	// 	maxDate
	// } from '@/common/data/publicdata'
	import {
		removeSlash
	} from '@/common/utils/removeIdSlash'
	// 选择区域
	const state = reactive<{
		tabs : any,
		activceTab : any,
		// dateShow: boolean,
		status : string,
		query : any,
		currentTab : number,
	}>({
		currentTab: 0,
		tabs: [{
			name: '待处理',
			value: false,
			count: 0
		},
		{
			name: '已完成',
			value: true,
			count: 0
		},
		],
		activceTab: {},
		// dateShow: false,
		status: 'loadmore',
		query: {
			page: 1,
			size: 10,
			isComplete: false
		}
	})
	const triggered = ref<boolean>()
	const tableData = ref<any>([])
	// 
	const toDetail = (params ?: any) => {
		uni.navigateTo({
			url: './taskDetail/index?id=' + params.id + '&layerid=' + params.device + '&layername=' + params
				.deviceName + '&status=' + params.status
		})
	}
	//选择日期
	// const chooseDate = () => {}

	// 加载更多
	const showMoreData = () => {
		console.log('dddddd')
		state.status = 'loading'
		taskList()
	} ///

	// 下拉刷新
	const onRefresh = () => {
		triggered.value = true
		state.query.page = 1
		taskList()
	}
	// 切换养护状态数据
	const changeStatus = (index : number) => {
		state.activceTab = state.tabs[index]
		state.query.page = 1
		state.query.isComplete = state.activceTab.value
		tableData.value = []
		taskList()
	}
	// 选择日期
	// const showDate = () => {
	// 	state.dateShow = true
	// }

	// 养护列表
	const taskList = async () => {
		state.status = 'loadmore'
		const userInfo = uni.getStorageSync('userInfo')
		const id = userInfo?.id.id
		state.query = {
			...state.query,
			maintainUser: removeSlash(id)
		}
		const res = await maintainTaskList(state.query)
		const data = res.data?.data?.data
		console.log(data[0]);
		const total = res.data?.data?.total
		if (state.currentTab === 0) {
			state.tabs[0].count = total
		}
		if (state.query.page === 1) {
			tableData.value = data
		} else {
			tableData.value = tableData.value.concat(data)
		}
		if (data.length > 0 && total > tableData.value.length) {
			state.query.page += 1
			state.status = 'loadmore'
		} else {
			state.status = 'nomore'
		}
		triggered.value = false
		uni.stopPullDownRefresh()
	}


	onReachBottom(() => {
		console.log('onReachBottom');
		showMoreData()
	})

	onPullDownRefresh(() => {
		console.log('onPullDownRefresh');
		onRefresh()
	})
	onMounted(() => {
		console.log('onMounted');
		taskList()
	})
</script>

<style lang="scss" scoped>
	.main {
		padding: 0 20rpx 20rpx 20rpx;
		min-height: 100%;
		// #ifdef APP-PLUS
		min-height: 100vh;
		// #endif
	}

	.card-box {
		// width: 686rpx;
		margin: 20rpx auto;
		border-radius: 16rpx;
		padding: 22rpx 28rpx;
		background-color: #FFFFFF;

		.hand {
			border-left: 4rpx solid #3862F8;

			.hand-title {
				height: 42rpx;
				line-height: 42rpx;

				text {
					font-size: 28rpx;
					margin-left: 16rpx;
					color: #060F27;
					font-weight: 600;
				}
			}

			.status {
				color: #91949F;
			}
		}

		.table {
			margin-top: 24rpx;

			.info {
				font-size: 24rpx;
				padding-bottom: 18rpx;

				text {
					&:nth-child(1) {
						color: #91949F;
					}

					&:nth-child(2) {
						flex: 1;
					}
				}

				.bg {
					padding: 2rpx 12rpx;
					background: rgba(56, 98, 248, 0.2);
					border-radius: 8rpx;

					text {
						color: #3862F8;
					}
				}
			}
		}
	}
</style>