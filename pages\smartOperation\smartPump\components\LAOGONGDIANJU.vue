<template>
	<div>
		<!-- <AttrPop style="left: 710px; top: 400px" :status="'online'" :text="'1#循环泵'"></AttrPop>
    <AttrPop style="left: 830px; top: 400px" :status="'online'" :text="'2#循环泵'"></AttrPop>
    <img style="left: 795px; top: 460px" class="img online" :src="onlinePng" alt="1" />
    <img style="left: 835px; top: 460px" class="img online" :src="onlinePng" alt="1" /> -->
		<AttrPop v-for="(item, i) in state.pumps" :key="i" :style="{ left: item.left, top: item.top }"
			:status="item.status" :text="item.name"></AttrPop>
		<image v-for="(item, i) in state.pans" :key="i" :style="item.styles" class="img" :class="item.status"
			:src="item.status === 'online' ? onlinePng : offlinePng" alt="1" />
	</div>
</template>
<script lang="ts" setup>
	import {
		reactive,
		ref,
		onMounted
	} from 'vue'
	import {
		useStationRealTimeData
	} from '@/common/hooks/useStations'
	import AttrPop from './AttrPop.vue'
	import {
		useStationAttrGroup
	} from '@/common/hooks'

	const onlinePng = ref < string > ('/static/pump/imgs/fsyx.png')
	const offlinePng = ref < string > ('/static/pump/imgs/fstz.png')
	const props = defineProps < {
		stationId ? : string
	} > ()
	const state = reactive < {
		pumps: {
			name: string;status: 'online' | 'offline';left: string;top: string
		} []
		pans: {
			styles: any;status: 'online' | 'offline'
		} []
	} > ({
		pumps: [{
				name: '1#PLC-1#泵',
				status: 'offline',
				left: '690px',
				top: '400px'
			},
			{
				name: '1#PLC-2#泵',
				status: 'offline',
				left: '830px',
				top: '400px'
			}
		],
		pans: [{
				styles: {
					left: '795px',
					top: '460px'
				},
				status: 'offline'
			},
			{
				styles: {
					left: '835px',
					top: '460px'
				},
				status: 'offline'
			}
		]
	})

	const realtime = useStationRealTimeData()
	const group = useStationAttrGroup()
	const refreshData = async () => {
		if (!props.stationId) return
		await group.initAttrGroupData(props.stationId)
		await realtime.getRealTimeData(props.stationId)
		group.group.value
			.filter(item => item.type?.indexOf('泵') !== -1)
			.map((item, i) => {
				if (i >= state.pumps.length) return
				state.pumps[i].name = item.type
				const attrs = item.attrList ?? []
				const deviceId = attrs.find(item => item.attr === 'status')?.deviceId
				const status = realtime.realtimeList.value.find(
					attr => attr.property === 'status' && attr.deviceId === deviceId
				)?.value
				state.pumps[i].status = status === '1.0' ? 'online' : 'offline'
				state.pans[i].status = status === '1.0' ? 'online' : 'offline'
			})
	}
	onMounted(() => {
		refreshData()
	})
</script>
<style lang="scss" scoped>
	.img {
		position: absolute;
		width: 15px;
		height: 15px;

		&.online {
			transform: rotate(360deg);
			animation: rotation 3s linear infinite;
		}
	}

	@keyframes rotation {
		from {
			-webkit-transform: rotate(0deg);
		}

		to {
			-webkit-transform: rotate(360deg);
		}
	}
</style>
