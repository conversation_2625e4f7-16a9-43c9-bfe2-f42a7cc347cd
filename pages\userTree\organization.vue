<template>
	<view class="main">
		<u-navbar :border-bottom="false">
			<u-search placeholder="可输入人员信息查询" v-model="state.name" @search="searchUser" @custom="searchUser"></u-search>
		</u-navbar>
		<view class="content">
			<u-cell-group v-if="organizations.length>0">
				<u-cell-item :title="item.name" v-for="(item,index) in organizations" :key="index"
					@click="chooseOrganization(item)"></u-cell-item>
			</u-cell-group>
			<u-empty v-else></u-empty>
		</view>
		<u-popup v-model="state.show" mode="center" height="700rpx" width="550rpx">
			<scroll-view scroll-y>
				<u-cell-group v-if="users.length>0">
					<u-cell-item v-for="(item,index) in users" :key="index" @click="chooseUser(item)">
						<template #title>
							{{item.organizationName}}/{{item.departmentName}}/{{item.firstName}}
						</template>
					</u-cell-item>
				</u-cell-group>
			</scroll-view>
		</u-popup>
	</view>
</template>

<script lang="ts" setup>
	import {
		storeToRefs
	} from 'pinia'
	import {
		useStore
	} from '@/store/index'
	import {
		onMounted,
		reactive,
		ref
	} from "vue";
	import {
		organizationList,
		getAllByName,
	} from '@/common/api/workOrder'
	const state = reactive < {
		show: boolean,
		pid: string,
		key: string,
		name: string,
	} > ({
		show: false,
		pid: '',
		name: '',
		key:''
	})
	const store = useStore()
	const organizations = ref < any > ([])
	const users = ref < any > ([])
	const getOrganizationList = async () => {
		const res = await organizationList()
		organizations.value = res.data?.data
		console.log(res.data?.data)
	}


	const searchUser = async () => {
		const res = await getAllByName({
			name: state.name,
		})
		users.value = res.data?.data
		console.log(users.value)
		if (users.value.length > 0) {
			state.show = true
		} else {
			uni.$u.toast('无此人员')
		}
	}
	// 显示组织下的部门
	const chooseOrganization = async (data: any) => {
		uni.$u.route({
			url: 'pages/userTree/department',
			params: {
				pid: data.id,
				key: state.key
			}
		})
	}
	//
	const chooseUser = (data: any) => {
		// console.log(data)
		// const pages = getCurrentPages();
		// console.log(pages)
		// const page = pages[pages.length - 4]
		// page.$vm.chooseUser(data)
		let {
			userData
		} = storeToRefs(store);
		userData.value = data
		state.show = false
		uni.navigateBack({
			delta: 1
		})
	}
	onMounted(() => {
		const pages = getCurrentPages();
		const page = pages[pages.length - 1]
		state.key = page.$page.options?.key || ''
		getOrganizationList()
	})
</script>

<style lang="scss" scoped>
	.content {
		height: 90vh;
		padding: 20rpx 32rpx;

	}
</style>
