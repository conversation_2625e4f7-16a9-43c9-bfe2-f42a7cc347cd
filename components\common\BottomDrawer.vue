<template>
  <view class="bottom-drawer" :style="{ zIndex: String(containerZIndex) }">
    <view
      v-if="mask && open"
      class="drawer-mask"
      @click="onMaskClick"
      :style="{ zIndex: String(containerZIndex), bottom: bottomOffset + 'px' }"
    />

    <view
      class="drawer-panel"
      :class="{
        open,
        collapsed: internalHeight === collapsedHeight,
      }"
      :style="{
        height: internalHeight + 'px',
        zIndex: String(zIndexPanel),
        bottom: open ? bottomOffset + 'px' : '-100%',
      }"
    >
      <view
        v-if="draggable"
        class="drawer-handle"
        @touchstart.stop.prevent="handleTouchStart"
        @touchmove.stop.prevent="handleTouchMove"
        @touchend.stop.prevent="handleTouchEnd"
      >
        <view class="handle-bar"></view>
      </view>
      <!-- header (slot overrideable) -->
      <view class="drawer-header">
        <slot name="header">
          <view class="default-header" v-if="title || showClose">
            <text class="drawer-title">{{ title }}</text>
            <view class="header-right">
              <slot name="header-right" />
              <text
                v-if="showClose"
                class="drawer-close"
                @click="$emit('close')"
                aria-label="close"
                role="button"
                >×</text
              >
            </view>
          </view>
        </slot>
      </view>

      <!-- content -->
      <view class="drawer-content">
        <slot />
      </view>

      <!-- actions slot -->
      <view class="drawer-actions">
        <slot name="actions" />
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'BottomDrawer',
  props: {
    open: { type: Boolean, default: false },
    height: { type: Number, default: 240 },
    minHeight: { type: Number, default: 200 },
    maxHeight: { type: Number, default: 0 }, // 0 表示不限制
    draggable: { type: Boolean, default: true },
    mask: { type: Boolean, default: false },
    closeOnMask: { type: Boolean, default: true },
    // 分离容器和面板的层级，默认面板高于容器
    containerZIndex: { type: Number, default: 2 },
    zIndexPanel: { type: [Number, String], default: 3 },
    // 距离底部的偏移（px），用于避开常显底部菜单/TabBar
    bottomOffset: { type: Number, default: 50 },
    // 标题与关闭按钮（可通过 header 插槽自定义覆盖）
    title: { type: String, default: '' },
    showClose: { type: Boolean, default: true },
  },
  emits: ['close', 'update:height'],
  data() {
    return {
      internalHeight: this.height,
      startY: 0,
      startHeight: 0,
      dragging: false,
      collapsedHeight: 20, // 完全收起时的高度，只显示把手
      lastDragDirection: null, // 记录最后的拖动方向
    };
  },
  watch: {
    height(val) {
      if (typeof val === 'number' && val > 0) {
        this.internalHeight = val;
      }
    },
    open(val) {
      if (val) {
        // 打开时设置为中间级别（minHeight）
        this.internalHeight = this.minHeight;
        this.$emit('update:height', this.minHeight);
      }
    },
  },
  methods: {
    onMaskClick() {
      if (this.closeOnMask) this.$emit('close');
    },
    handleTouchStart(e) {
      const touch = e.changedTouches && e.changedTouches[0];
      if (!touch) return;
      this.dragging = true;
      this.startY = touch.clientY;
      this.startHeight = this.internalHeight;
    },
    handleTouchMove(e) {
      if (!this.dragging) return;
      const touch = e.changedTouches && e.changedTouches[0];
      if (!touch) return;
      const delta = this.startY - touch.clientY;
      let next = this.startHeight + delta;

      // 计算拖动方向
      const currentDelta = touch.clientY - this.startY;
      this.lastDragDirection = currentDelta < 0 ? 'up' : 'down';

      // 允许完全收起（collapsedHeight）
      const maxH = this.maxHeight > 0 ? this.maxHeight : Number.MAX_SAFE_INTEGER;
      if (next < this.collapsedHeight) next = this.collapsedHeight;
      if (next > maxH) next = maxH;

      this.internalHeight = next;
      this.$emit('update:height', next);
    },
    handleTouchEnd() {
      if (!this.dragging) return;
      this.dragging = false;

      // 获取当前高度和可用的高度级别
      const currentHeight = this.internalHeight;
      const minH = this.minHeight > 0 ? this.minHeight : 0;
      const maxH = this.maxHeight > 0 ? this.maxHeight : Number.MAX_SAFE_INTEGER;

      // 三个档位的高度
      const levels = [
        this.collapsedHeight, // 收起状态
        minH, // 正常状态
        maxH, // 展开状态
      ];

      // 根据拖动方向找到最近的目标高度
      let targetHeight;
      if (this.lastDragDirection === 'up') {
        // 向上拖动时，找到大于当前高度的最近档位
        targetHeight = levels.find(h => h > currentHeight) || maxH;
      } else {
        // 向下拖动时，找到小于当前高度的最近档位（从后往前找）
        targetHeight = [...levels].reverse().find(h => h < currentHeight) || this.collapsedHeight;
      }

      // 更新高度
      this.internalHeight = targetHeight;
      this.$emit('update:height', targetHeight);
      this.lastDragDirection = null; // 重置拖动方向
    },
  },
};
</script>

<style scoped lang="scss">
.bottom-drawer {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none; // 让地图等底层可操作
}

.drawer-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.45);
}

.drawer-panel {
  position: fixed;
  left: 0;
  right: 0;
  bottom: -100%;
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
  box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.1);
  transition: all 0.25s ease-out;
  pointer-events: auto; // 面板内可交互
  display: flex;
  flex-direction: column;
  transform: translateY(0);
  will-change: transform, height;
}

.drawer-panel.open {
  bottom: 0;
}

.drawer-panel.collapsed {
  .drawer-content,
  .drawer-header,
  .drawer-actions {
    display: none;
  }

  .drawer-handle {
    height: 20px; // 与 collapsedHeight 对应
    // padding: 16rpx 0;
    background-color: #fff;
    border-radius: 24rpx 24rpx 0 0;
    box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.1);
  }
}

.drawer-handle {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16rpx 0;
}

.handle-bar {
  width: 80rpx;
  height: 8rpx;
  background-color: #e2e3e5;
  border-radius: 4rpx;
}

.drawer-content {
  flex: 1;
  overflow-y: auto;
}

.drawer-header {
  padding: 0rpx 32rpx 0 32rpx; /* 顶部有拖拽把手，减小上边距 */
}

.default-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 8rpx;
  border-bottom: 1px solid #f0f0f0;
}

.header-right {
  // display: inline-flex;
  // align-items: center;
  // gap: 12rpx;
}

.drawer-title {
  font-size: 34rpx;
  font-weight: 600;
  color: #1f1f1f;
}

.drawer-close {
  font-size: 40rpx;
  line-height: 1;
  color: #8c8c8c;
}

.drawer-actions {
  display: block; /* 由使用方自行布局与内边距 */
  padding: 0;
  background: transparent;
}
</style>
