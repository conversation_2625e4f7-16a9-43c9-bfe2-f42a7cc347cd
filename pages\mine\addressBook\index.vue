<template>
	<view class="page-main">
		<next-indexed-xlist :dataList="dataList" :showAvatar="true" @itemclick="itemclick" :showCall="false"
			:showVideo="false">
		</next-indexed-xlist>
	</view>
</template>
<script setup lang="ts">
	import { ref } from "vue";
	import { onShow } from "@dcloudio/uni-app";
	import {
		getAllUser
	} from '@/common/api/login'
	const currentUser = ref<any>()
	const dataList = ref([])
	const getUserList = () => {
		currentUser.value = uni.getStorageSync('userInfo')
		getAllUser().then((res : any) => {
			const allUser = res.data.data.filter((item : any) => item.id.id !== currentUser.value.id.id)
			dataList.value = allUser.map(data => {
				return {
					name: data.firstName + (data.departmentName? '(' + data.departmentName + ')':''),
					phone: data.phone,
					departmentName: data.departmentName,
					id: data.id.id
				}
			})
			console.log('dataList.value',dataList.value)
		})
	}

	onShow(() => {
		getUserList()
	})
	function itemclick(e) {
		console.log('点击列表回调：', e)
	}
</script>
<style lang="scss">
	.content-block {
		display: flex;
		flex-direction: row;
		justify-content: flex-start;
		background-color: #fff;

		.title {
			color: #333;
			padding: 20rpx;
			margin-right: 20rpx;
			display: flex;
			flex-direction: column;
			justify-content: center;
		}

		.btn {
			color: #ccc;
			padding: 10rpx;
			border: 1rpx solid #ccc;
			border-radius: 10rpx;
			margin: 20rpx 10rpx;
			font-size: 28rpx;
			display: flex;
			flex-direction: column;
			justify-content: center;

		}
	}
</style>