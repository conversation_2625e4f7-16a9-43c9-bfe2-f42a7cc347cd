<template>
	<view class="workorder-list">
		<template v-if="tableData.length">
			<view class="card-box" v-for="(data,index) in tableData" :key="index">
				<view class="flex-between hand">
					<view class="hand-title flex-center">
						<view class="icon-box">
							<u-icon name="custom-icon-gongdan" customPrefix="custom-icon" color="#FFFFFF" size="20">
							</u-icon>
						</view>
						<text>{{data.content}}</text>
					</view>
				</view>
				<view class="steps">
					<u-steps :list="numList" :current="getCurrentStep(data.status)"></u-steps>
				</view>
				<view class="table">
					<view class="info">
						<text>工单编号：</text> <text>{{data.code}}</text>
					</view>
					<view class="info">
						<text>发起人员：</text> <text>{{data.uploadUserName}}</text>
					</view>
					<view class="info">
						<text>地址：</text> <text>{{data.coordinateName}}</text>
					</view>
				</view>
			</view>
		</template>
		<u-empty v-else text="暂无内容" mode="list"></u-empty>

	</view>
</template>

<script lang="ts" setup>
	import { onMounted, reactive, ref, watch } from "vue";

	import { getInspectionWorkOrder } from "../../../common/api/inspection";
	const props = defineProps<{
		user : any
	}>()


	const tableData = ref<any>([])
	const numList = ref<any>([{
		name: '上报',
		value: 'PENDING'
	}, {
		name: '分派',
		value: 'ASSIGN'
	}, {
		name: '接收',
		value: 'RESOLVING'
	}, {
		name: '到场',
		value: 'ARRIVING'
	}, {
		name: '处理',
		value: 'PROCESSING'
	}, {
		name: '完成',
		value: 'SUBMIT'
	}, {
		name: '审核通过',
		value: 'APPROVED'
	}])
	const getCurrentStep = (step : String) => {
		let stepNum : number = 0
		numList.value.map((item : any, index : number) => {
			if (item.value === step) {
				stepNum = index
			}
		})

		return stepNum
	}
	// 工单列表
	const refreshData = async () => {
		if (!props.user) {
			tableData.value = []
			return
		}
		const res = await getInspectionWorkOrder({
			statusStage: '',
			processUserId: props.user.id
		})
		const data = res.data?.data?.data || []
		tableData.value = data
	}
	watch(() => props.user, () => {
		refreshData()
	})
	onMounted(() => {
		refreshData()
	})
	defineExpose({
		refreshData
	})
</script>

<style lang="scss" scoped>
	.workorder-list{
		padding: 12rpx 24rpx;
	}
	.card-box {
		margin: 20rpx auto;
		border-radius: 16rpx;
		padding: 22rpx 28rpx;
		background-color: #FFFFFF;

		.hand {
			.hand-title {
				height: 42rpx;
				line-height: 42rpx;

				.icon-box {
					text-align: center;
					line-height: 32rpx;
					height: 32rpx;
					width: 32rpx;
					background-color: #3862F8;
					border-radius: 4rpx;
				}

				text {
					font-size: 28rpx;
					margin-left: 16rpx;
					color: #060F27;
					font-weight: 600;
				}
			}

			.status {
				color: #91949F;
			}
		}

		.steps {
			width: auto;
			min-height: 86rpx;
			border-radius: 8rpx;
			background: #F9F9F9;
			padding-top: 12rpx;
			font-size: 20rpx;
			margin: 24rpx 0;

			::v-deep .u-steps .u-steps__item {
				min-width: auto;
			}
		}

		.table {
			margin-top: 24rpx;

			.info {
				font-size: 24rpx;
				padding-bottom: 18rpx;
				display: flex;

				text {
					&:nth-child(1) {
						color: #91949F;
					}

					&:nth-child(2) {
						flex: 1;
					}
				}


				.bg {
					padding: 2rpx 12rpx;
					background: rgba(56, 98, 248, 0.2);
					border-radius: 8rpx;

					text {
						color: #3862F8;
					}
				}
			}
		}
	}
</style>