import { reactive, ref, computed } from "vue";
import {
  useLocation,
  useVerticalBar,
  useTouch,
  usePipeLayers,
  useStations,
  useUsers,
  useWorkOrders,
} from "../../../common/hooks";
import { useOneMapMenus } from "../hooks/useOneMapMenus";

export function useMapData() {
  // Reactive data
  const verticalBar = useVerticalBar();
  const location = useLocation();
  const onemapMenu = useOneMapMenus();
  const touch = useTouch();
  const station = useStations();
  const pipelayer = usePipeLayers();
  const users = useUsers();
  const workorders = useWorkOrders();

  // State
  const cleanMark = ref(0);
  const basemap = ref("vec");
  const dmaVisible = ref(false);
  const curLayerIds = ref([]);
  const partitions = ref([]);
  const current = ref(1);
  const bottomMenu = ref([]);
  const pullTimer = ref(undefined);

  // Map popup data
  const mapPop = reactive({
    type: "table",
    title: "",
    data: [],
    columns: [],
    obj: {},
  });

  // Computed
  const curModule = computed(() => {
    return onemapMenu?.current?.split("_")?.[0];
  });

  return {
    // Hooks
    verticalBar,
    location,
    onemapMenu,
    touch,
    station,
    pipelayer,
    users,
    workorders,

    // State
    cleanMark,
    basemap,
    dmaVisible,
    curLayerIds,
    partitions,
    current,
    bottomMenu,
    pullTimer,
    mapPop,

    // Computed
    curModule,
  };
}
