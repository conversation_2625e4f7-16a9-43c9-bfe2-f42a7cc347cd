<template>
  <view class="page-container">
    <u-navbar
      fixed
      placeholder
      safeAreaInsetTop
      bgColor="linear-gradient(135deg, #667eea 0%, #764ba2 100%)"
      title="立户信息"
      :autoBack="true"
      leftIconSize="20"
      titleStyle="color: #ffffff; font-weight: 600; font-size: 36rpx;"
    >
      <template #right>
        <view class="nv-right" @click="showScreen">
          <view class="filter-btn">
            <u-icon name="options" color="#ffffff" size="20"></u-icon>
            <text class="filter-text">筛选</text>
          </view>
        </view>
      </template>
    </u-navbar>

    <!-- 内容区域 -->
    <view class="content-wrapper">
      <!-- 统计信息卡片 -->
      <view class="stats-card" v-if="tableData.length > 0">
        <view class="stats-item">
          <view class="stats-number">{{ tableData.length }}</view>
          <view class="stats-label">总数</view>
        </view>
        <view class="stats-divider"></view>
        <view class="stats-item">
          <view class="stats-number">{{ normalCount }}</view>
          <view class="stats-label">正常</view>
        </view>
        <view class="stats-divider"></view>
        <view class="stats-item">
          <view class="stats-number">{{ pendingCount }}</view>
          <view class="stats-label">待审核</view>
        </view>
      </view>

      <!-- 用户卡片列表 -->
      <view
        class="user-card"
        v-for="(data, index) in tableData"
        :key="index"
        @click="toDetail(data)"
      >
        <!-- 卡片头部 -->
        <view class="card-header">
          <view class="user-info">
            <view class="user-avatar">
              <u-icon name="account" color="#ffffff" size="24"></u-icon>
            </view>
            <view class="user-details">
              <view class="user-name">{{ data.userName || "用户" }}</view>
              <view class="user-id"
                >户号：{{ data.userCode || data.serialNo }}</view
              >
            </view>
          </view>
          <view class="status-badges">
            <view
              class="status-badge user-status"
              :class="getUserStatusClass(data.userStatus)"
            >
              {{ data.userStatus || "正常" }}
            </view>
            <view
              class="status-badge audit-status"
              :class="getAuditStatusClass(data.auditStatus)"
            >
              {{ data.auditStatusName || "已完成" }}
            </view>
          </view>
        </view>

        <!-- 卡片内容 -->
        <view class="card-content">
          <view class="info-grid">
            <view class="info-item">
              <view class="info-label">用水性质</view>
              <view class="info-value">{{
                data.waterNature || "居民用水"
              }}</view>
            </view>
            <view class="info-item">
              <view class="info-label">客户性质</view>
              <view class="info-value">{{
                data.customerNature || "个人"
              }}</view>
            </view>
            <view class="info-item">
              <view class="info-label">抄表手册</view>
              <view class="info-value">{{ data.meterBook || "测试表册" }}</view>
            </view>
            <view class="info-item">
              <view class="info-label">水表编号</view>
              <view class="info-value">{{ data.meterNo || "--" }}</view>
            </view>
          </view>

          <!-- 地址信息 -->
          <view class="address-info" v-if="data.address">
            <u-icon name="map" color="#91949F" size="16"></u-icon>
            <text class="address-text">{{ data.address }}</text>
          </view>
        </view>

        <!-- 卡片底部操作区域 -->
        <view class="card-footer">
          <view class="operation-time">
            <text>{{
              data.createTime || data.updateTime || "2025-08-08"
            }}</text>
          </view>
          <view class="operation-btn">
            <u-icon name="arrow-right" color="#91949F" size="16"></u-icon>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view
        class="empty-state"
        v-if="tableData.length === 0 && state.status !== 'loading'"
      >
        <view class="empty-icon">
          <u-icon name="inbox" color="#c0c4cc" size="80"></u-icon>
        </view>
        <view class="empty-text">暂无数据</view>
        <view class="empty-desc">暂时没有找到相关信息</view>
      </view>

      <!-- 加载更多 -->
      <u-loadmore
        bg-color="transparent"
        :status="state.status"
        loading-text="努力加载中"
        loadmore-text="加载更多"
        nomore-text="没有更多了"
        margin-top="20"
        margin-bottom="20"
      />
    </view>

    <!-- 筛选弹窗 -->
    <u-popup
      mode="right"
      width="600"
      v-model="state.screenShow"
      closeOnClickOverlay
      overlay
      @close="state.screenShow = false"
      borderRadius="20"
    >
      <view class="popup">
        <view class="popup-header">
          <view class="popup-title">筛选条件</view>
          <view class="popup-close" @click="state.screenShow = false">
            <u-icon name="close" color="#91949F" size="20"></u-icon>
          </view>
        </view>
        <view class="screen-list">
          <u-form
            :model="screenForm"
            ref="form1"
            :label-style="{
              color: '#333333',
              fontSize: '28rpx',
              fontWeight: '500',
            }"
            border-bottom
            input-align="right"
            label-width="200"
          >
            <u-form-item label="关键字：" prop="screenForm.keyword">
              <u-input placeholder="请输入" v-model="screenForm.keyword">
              </u-input>
            </u-form-item>
            <u-form-item label="用水性质：" prop="screenForm.waterNature">
              <u-input
                placeholder="请选择"
                v-model="screenForm.waterNatureName"
                @click="chooseWaterNature"
                readonly
              >
              </u-input>
              <template #right>
                <u-icon name="arrow-down" color="#91949F" size="24"></u-icon>
              </template>
            </u-form-item>

            <u-form-item label="客户性质：" prop="screenForm.customerNature">
              <u-input
                placeholder="请选择"
                v-model="screenForm.customerNatureName"
                @click="chooseCustomerNature"
                readonly
              >
              </u-input>
              <template #right>
                <u-icon name="arrow-down" color="#91949F" size="24"></u-icon>
              </template>
            </u-form-item>

            <u-form-item label="审核状态：" prop="screenForm.auditStatus">
              <u-input
                placeholder="请选择"
                v-model="screenForm.auditStatusName"
                @click="chooseAuditStatus"
                readonly
              >
              </u-input>
              <template #right>
                <u-icon name="arrow-down" color="#91949F" size="24"></u-icon>
              </template>
            </u-form-item>
          </u-form>
        </view>
        <view class="popup-buttons">
          <u-button
            type="default"
            color="#f8f9fa"
            width="240rpx"
            height="80rpx"
            @click="resetScreen"
          >
            重置
          </u-button>
          <u-button
            type="primary"
            color="linear-gradient(135deg, #667eea 0%, #764ba2 100%)"
            width="240rpx"
            height="80rpx"
            @click="submitScreen"
          >
            确定
          </u-button>
        </view>
      </view>
    </u-popup>

    <u-select
      v-model="state.typeShow"
      mode="single-column"
      :list="areaGroup"
      @confirm="confirm"
    ></u-select>
  </view>
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref, getCurrentInstance, computed } from "vue";
import { onPullDownRefresh, onReachBottom } from "@dcloudio/uni-app";
import { workOrderSteps } from "@/common/data/publicData";
import { userOrderList } from "@/common/api/userOrder";
const state = reactive<{
  screenShow: boolean;
  calendarShow: boolean;
  showDateType: string;
  status: string;
  searchWorkSteps: any;
  waterNatureList: any[];
  customerNatureList: any[];
  auditStatusList: any[];
  waterNatureShow: boolean;
  customerNatureShow: boolean;
  auditStatusShow: boolean;
  typeShow: boolean;
  currentSelectType: string;
}>({
  screenShow: false,
  calendarShow: false,
  showDateType: "",
  status: "loadmore",
  searchWorkSteps: workOrderSteps,
  waterNatureList: [
    { label: "居民用水", value: "1" },
    { label: "商业用水", value: "2" },
    { label: "工业用水", value: "3" },
    { label: "其他", value: "4" },
  ],
  customerNatureList: [
    { label: "个人", value: "1" },
    { label: "企业", value: "2" },
    { label: "政府机关", value: "3" },
    { label: "其他", value: "4" },
  ],
  auditStatusList: [
    { label: "待审核", value: "1" },
    { label: "审核通过", value: "2" },
    { label: "审核不通过", value: "3" },
  ],
  waterNatureShow: false,
  customerNatureShow: false,
  auditStatusShow: false,
  typeShow: false,
  currentSelectType: "",
});

const { proxy } = getCurrentInstance();
const triggered = ref<boolean>();
const currentStep = ref<String>("");

// 计算属性：根据当前选择类型返回对应的数据列表
const areaGroup = computed(() => {
  console.log("当前选择类型:", state.currentSelectType); // 调试用
  let result: any[] = [];
  switch (state.currentSelectType) {
    case "waterNature":
      result = state.waterNatureList;
      break;
    case "customerNature":
      result = state.customerNatureList;
      break;
    case "auditStatus":
      result = state.auditStatusList;
      break;
    default:
      result = [];
  }
  console.log("返回的数据列表:", result); // 调试用
  return result;
});

// 计算正常状态数量
const normalCount = computed(() => {
  return tableData.value.filter((item) => item.userStatus === "正常").length;
});

// 计算待审核数量
const pendingCount = computed(() => {
  return tableData.value.filter((item) => item.auditStatusName === "待审核")
    .length;
});
const numList = ref<any>([
  {
    name: "上报",
    value: "PENDING",
  },
  {
    name: "分派",
    value: "ASSIGN",
  },
  {
    name: "接收",
    value: "RESOLVING",
  },
  {
    name: "到场",
    value: "ARRIVING",
  },
  {
    name: "处理",
    value: "PROCESSING",
  },
  {
    name: "完成",
    value: "SUBMIT",
  },
  {
    name: "审核通过",
    value: "APPROVED",
  },
]);

const getCurrentStep = (step: String) => {
  let stepNum: number = 0;
  numList.value.map((item: any, index: number) => {
    if (item.value === step) {
      stepNum = index;
    }
  });

  return stepNum;
};

// 选择区域
const screenForm = reactive<any>({
  keyword: "",
  waterNature: "",
  waterNatureName: "",
  customerNature: "",
  customerNatureName: "",
  auditStatus: "",
  auditStatusName: "",
  page: 1,
  size: 10,
});
const tableData = ref<any>([]);

const checkStep = (step: any) => {
  currentStep.value = step.value;
};

// 选择用水性质
const chooseWaterNature = () => {
  state.currentSelectType = "waterNature";
  state.typeShow = true;
};

// 选择客户性质
const chooseCustomerNature = () => {
  state.currentSelectType = "customerNature";
  state.typeShow = true;
};

// 选择审核状态
const chooseAuditStatus = () => {
  state.currentSelectType = "auditStatus";
  state.typeShow = true;
};

// u-select 确认选择
const confirm = (e: any) => {
  console.log("选择结果:", e); // 调试用
  // e 是选中的对象，格式为 {label: '', value: ''}
  const selected = e;
  switch (state.currentSelectType) {
    case "waterNature":
      screenForm.waterNature = selected.value;
      screenForm.waterNatureName = selected.label;
      break;
    case "customerNature":
      screenForm.customerNature = selected.value;
      screenForm.customerNatureName = selected.label;
      break;
    case "auditStatus":
      screenForm.auditStatus = selected.value;
      screenForm.auditStatusName = selected.label;
      break;
  }
  state.typeShow = false;
  console.log("更新后的表单:", screenForm); // 调试用
};

// 加载更多
const showMoreData = async () => {
  state.status = "loading";
  await getWorkOrderList();
}; ///

// 下拉刷新
const onRefresh = async () => {
  triggered.value = true;
  screenForm.page = 1;
  await getWorkOrderList();
};

// 确定搜索
const submitScreen = () => {
  state.screenShow = false;
  tableData.value = [];
  screenForm.page = 1;
  getWorkOrderList();
};

// 重置筛选条件
const resetScreen = () => {
  screenForm.keyword = "";
  screenForm.waterNature = "";
  screenForm.waterNatureName = "";
  screenForm.customerNature = "";
  screenForm.customerNatureName = "";
  screenForm.auditStatus = "";
  screenForm.auditStatusName = "";
};

const confirmDate = (date: any) => {
  // 这个方法暂时保留，如果后续需要日期选择功能
};
// 选择区域
const showScreen = () => {
  state.screenShow = true;
};

// 获取用户状态样式类
const getUserStatusClass = (status: string) => {
  switch (status) {
    case "正常":
      return "status-normal";
    case "异常":
      return "status-abnormal";
    case "停用":
      return "status-disabled";
    default:
      return "status-normal";
  }
};

// 获取审核状态样式类
const getAuditStatusClass = (status: string) => {
  switch (status) {
    case "已完成":
    case "审核通过":
      return "audit-approved";
    case "待审核":
      return "audit-pending";
    case "审核不通过":
      return "audit-rejected";
    default:
      return "audit-approved";
  }
};

const toDetail = (detail: any) => {
  uni.$u.route({
    url: "pages/InstallationApply/User/userDetail/detail",
    params: {
      id: detail.id,
      fromType: "query",
      status: detail.status,
      statusName: detail.statusName,
    },
  });
};

const mockUserList = [
  {
    id: 1,
    userName: "张三",
    userCode: "100001",
    serialNo: "A001",
    userStatus: "正常",
    auditStatus: "2",
    auditStatusName: "审核通过",
    waterNature: "居民用水",
    customerNature: "个人",
    meterBook: "表册一",
    meterNo: "SB1001",
    address: "广州市天河区珠江新城1号",
    createTime: "2024-06-01",
    updateTime: "2024-06-02",
    status: "已完成",
    statusName: "审核通过",
  },
  {
    id: 2,
    userName: "李四",
    userCode: "100002",
    serialNo: "A002",
    userStatus: "异常",
    auditStatus: "1",
    auditStatusName: "待审核",
    waterNature: "商业用水",
    customerNature: "企业",
    meterBook: "表册二",
    meterNo: "SB1002",
    address: "广州市越秀区中山路2号",
    createTime: "2024-06-03",
    updateTime: "2024-06-04",
    status: "待审核",
    statusName: "待审核",
  },
  {
    id: 3,
    userName: "王五",
    userCode: "100003",
    serialNo: "A003",
    userStatus: "停用",
    auditStatus: "3",
    auditStatusName: "审核不通过",
    waterNature: "工业用水",
    customerNature: "政府机关",
    meterBook: "表册三",
    meterNo: "SB1003",
    address: "广州市白云区机场路3号",
    createTime: "2024-06-05",
    updateTime: "2024-06-06",
    status: "审核不通过",
    statusName: "审核不通过",
  },
]

// 用户订单列表
const getWorkOrderList = async () => {
  // mock数据测试用，实际开发请注释掉下面三行
  tableData.value = mockUserList;
  state.status = "nomore";
  triggered.value = false;
  uni.stopPullDownRefresh();
  return;

  // state.status = "loadmore";
  // const params = {
  //   ...screenForm,
  //   keyword: screenForm.keyword,
  //   waterNature: screenForm.waterNature,
  //   customerNature: screenForm.customerNature,
  //   auditStatus: screenForm.auditStatus,
  // };
  // const res = await userOrderList(params);
  // const data = res.data?.data?.data;
  // const total = res.data?.data?.total;

  // if (data.length > 0) {
  //   if (screenForm.page === 1) {
  //     tableData.value = data;
  //   } else {
  //     tableData.value = tableData.value.concat(data);
  //   }
  //   screenForm.page += 1;
  //   state.status = "loadmore";
  //   if (data.length === total) {
  //     state.status = "nomore";
  //   }
  // } else {
  //   state.status = "nomore";
  // }

  // triggered.value = false;
  // uni.stopPullDownRefresh();
};

onReachBottom(async () => {
  showMoreData();
});

onPullDownRefresh(async () => {
  onRefresh();
});
onMounted(async () => {
  getWorkOrderList();
});
</script>

<style lang="scss" scoped>
// 页面容器
.page-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

// 导航栏右侧筛选按钮
.nv-right {
  .filter-btn {
    display: flex;
    align-items: center;
    padding: 8rpx 16rpx;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 20rpx;
    backdrop-filter: blur(10rpx);
    transition: all 0.3s ease;

    &:active {
      transform: scale(0.95);
      background: rgba(255, 255, 255, 0.3);
    }

    .filter-text {
      font-size: 24rpx;
      color: #ffffff;
      margin-left: 8rpx;
      font-weight: 500;
    }
  }
}

// 内容包装器
.content-wrapper {
  padding: 20rpx;
}

// 统计卡片
.stats-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  display: flex;
  justify-content: space-around;
  align-items: center;
  box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.25);

  .stats-item {
    text-align: center;
    flex: 1;

    .stats-number {
      font-size: 48rpx;
      font-weight: 700;
      color: #ffffff;
      line-height: 64rpx;
    }

    .stats-label {
      font-size: 24rpx;
      color: rgba(255, 255, 255, 0.8);
      line-height: 32rpx;
      margin-top: 8rpx;
    }
  }

  .stats-divider {
    width: 1px;
    height: 60rpx;
    background: rgba(255, 255, 255, 0.3);
  }
}

// 新的用户卡片样式
.user-card {
  width: 100%;
  margin-bottom: 24rpx;
  border-radius: 20rpx;
  padding: 32rpx;
  background: #ffffff;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 6rpx;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  &:active {
    transform: translateY(-4rpx);
    box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.12);
  }
}

// 卡片头部
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24rpx;

  .user-info {
    display: flex;
    align-items: center;
    flex: 1;

    .user-avatar {
      width: 64rpx;
      height: 64rpx;
      border-radius: 50%;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 20rpx;
      box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.3);
    }

    .user-details {
      flex: 1;

      .user-name {
        font-size: 36rpx;
        font-weight: 700;
        color: #060f27;
        line-height: 48rpx;
        margin-bottom: 8rpx;
      }

      .user-id {
        font-size: 26rpx;
        color: #91949f;
        line-height: 36rpx;
      }
    }
  }

  .status-badges {
    display: flex;
    flex-direction: column;
    gap: 12rpx;
    align-items: flex-end;
  }
}

// 状态徽章
.status-badge {
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 22rpx;
  line-height: 30rpx;
  font-weight: 600;
  backdrop-filter: blur(10rpx);

  &.user-status {
    &.status-normal {
      background: linear-gradient(135deg, #e8f5e8 0%, #d9f2d9 100%);
      color: #52c41a;
      border: 1px solid rgba(82, 196, 26, 0.3);
    }
    &.status-abnormal {
      background: linear-gradient(135deg, #fff2e8 0%, #ffe8d1 100%);
      color: #fa8c16;
      border: 1px solid rgba(250, 140, 22, 0.3);
    }
    &.status-disabled {
      background: linear-gradient(135deg, #f5f5f5 0%, #eeeeee 100%);
      color: #8c8c8c;
      border: 1px solid rgba(140, 140, 140, 0.3);
    }
  }

  &.audit-status {
    &.audit-approved {
      background: linear-gradient(135deg, #e8f5e8 0%, #d9f2d9 100%);
      color: #52c41a;
      border: 1px solid rgba(82, 196, 26, 0.3);
    }
    &.audit-pending {
      background: linear-gradient(135deg, #fff7e6 0%, #fff0d1 100%);
      color: #fa8c16;
      border: 1px solid rgba(250, 140, 22, 0.3);
    }
    &.audit-rejected {
      background: linear-gradient(135deg, #fff2f0 0%, #ffe1dd 100%);
      color: #ff4d4f;
      border: 1px solid rgba(255, 77, 79, 0.3);
    }
  }
}

// 卡片内容
.card-content {
  .info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20rpx 32rpx;
    margin-bottom: 24rpx;

    .info-item {
      .info-label {
        font-size: 24rpx;
        color: #91949f;
        line-height: 32rpx;
        margin-bottom: 8rpx;
      }

      .info-value {
        font-size: 28rpx;
        color: #060f27;
        line-height: 40rpx;
        font-weight: 600;
      }
    }
  }

  .address-info {
    display: flex;
    align-items: center;
    padding: 16rpx 20rpx;
    background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
    border-radius: 12rpx;
    margin-top: 20rpx;
    border: 1px solid rgba(102, 126, 234, 0.1);

    .address-text {
      font-size: 26rpx;
      color: #333333;
      line-height: 36rpx;
      margin-left: 12rpx;
      flex: 1;
    }
  }
}

// 卡片底部
.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 24rpx;
  padding-top: 20rpx;
  border-top: 1px solid #f0f0f0;

  .operation-time {
    font-size: 24rpx;
    color: #91949f;
    line-height: 32rpx;
  }

  .operation-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40rpx;
    height: 40rpx;
    border-radius: 50%;
    background: rgba(102, 126, 234, 0.1);
    transition: all 0.3s ease;

    &:active {
      background: rgba(102, 126, 234, 0.2);
      transform: scale(0.9);
    }
  }
}

// 空状态
.empty-state {
  text-align: center;
  padding: 120rpx 40rpx;

  .empty-icon {
    margin-bottom: 32rpx;
  }

  .empty-text {
    font-size: 32rpx;
    color: #333333;
    font-weight: 600;
    margin-bottom: 16rpx;
  }

  .empty-desc {
    font-size: 26rpx;
    color: #91949f;
    line-height: 36rpx;
  }
}

// 弹窗样式
.popup {
  width: 100%;
  height: 100%;
  position: relative;
  background: #ffffff;
  display: flex;
  flex-direction: column;

  .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 32rpx;
    border-bottom: 1px solid #f0f0f0;

    .popup-title {
      font-size: 36rpx;
      font-weight: 700;
      color: #060f27;
    }

    .popup-close {
      width: 48rpx;
      height: 48rpx;
      border-radius: 50%;
      background: #f8f9fa;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;

      &:active {
        background: #e9ecef;
        transform: scale(0.9);
      }
    }
  }

  .screen-list {
    flex: 1;
    padding: 32rpx;
    overflow-y: auto;
  }

  .popup-buttons {
    display: flex;
    gap: 20rpx;
    padding: 32rpx;
    border-top: 1px solid #f0f0f0;
    background: #fafbfc;
  }

  ::v-deep .placeholderClass {
    font-size: 28rpx;
    color: #91949f;
  }

  ::v-deep .u-form-item {
    padding: 16rpx 0;
    margin-bottom: 16rpx;
    background: #f8f9fa;
    border-radius: 12rpx;

    .u-form-item__body {
      padding: 0 24rpx;
    }
  }

  ::v-deep .u-input {
    font-size: 28rpx;
    color: #333333;
  }
}

// 深色样式覆盖
::v-deep .u-navbar__content__title {
  color: #ffffff !important;
  font-weight: 600 !important;
}

::v-deep .u-icon--left {
  color: #ffffff !important;
}

::v-deep .u-loadmore {
  background: transparent !important;

  .u-loadmore__text {
    font-size: 26rpx !important;
    color: #91949f !important;
  }
}
</style>
