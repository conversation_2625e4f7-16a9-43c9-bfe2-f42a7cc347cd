<template>
	<view class="">
		<view class="content-card">
			<text class="title-text">巡检图片：</text>
			<view style="padding-top: 20rpx;">
				<u-upload :custom-btn="true" :header="header" width="112rpx" height="112rpx" :source-type="['camera']"
					ref="refUpload" :show-upload-list="!!showUploadList?.length" :action="actionUrl"
					@on-success="onSuccess" @onRemove="deletePic">
					<template #addBtn>
						<view class="file-s">
							<view class="margin-center">
								<text class="custom-icon custom-icon-xiangji icon"></text>
							</view>
							<text>拍照上传</text>
						</view>
					</template>
				</u-upload>
			</view>
		</view>
		<view class="content-card">
			<text class="title-text">添加录音：</text>
			<view style="margin-top: 20rpx;" class="flex-center">
				<view class="file-s" style="margin-right: 20rpx;" v-if="voiceList.length>0"
					v-for="(item,index) in voiceList" :key="index">
					<view class="margin-center" @click="playVoice(item)">
						<image src="/static/sound-recording/play.png" style="width:48rpx;height:48rpx"></image>
					</view>
					<view class="close-icon" @click=removeVoice(index)>
						<u-icon name="close" size="10" color="#FFFFFF"></u-icon>
					</view>
				</view>
				<view class="file-s" @click="soundRecord">
					<view class="margin-center">
						<text class="custom-icon custom-icon-htmal5icon13 icon"></text>
					</view>
					<text>添加录音</text>
				</view>
			</view>
		</view>
		<view class="content-card">
			<text class="title-text">添加视频：</text>
			<view style="margin-top: 20rpx;" class="flex-center">
				<view class="file-s" style="margin-right: 20rpx;" v-if="videoList.length>0"
					v-for="(item,index) in videoList" :key="index">
					<view class="margin-center" @click="palyVideo(item)">
						<image src="/static/sound-recording/play.png" style="width:48rpx;height:48rpx"></image>
					</view>
					<view class="close-icon" @click=removeVideo(index)>
						<u-icon name="close" size="10" color="#FFFFFF"></u-icon>
					</view>
				</view>
				<view class="file-s" @click="addVideo">
					<view class="margin-center">
						<text class="custom-icon custom-icon-shipinvideo icon"></text>
					</view>
					<text>添加视频</text>
				</view>
			</view>
		</view>
		<view class="content-card">
			<text class="title-text">添加文件：</text>
			<view style="margin-top: 20rpx;" class="flex-center">
				<view class="file-s" style="margin-right: 20rpx;" v-if="fileList.length>0"
					v-for="(item,index) in fileList" :key="index">
					<view class="margin-center">
						<u-icon name="custom-icon-wenjianjia" customPrefix="custom-icon" size="48" color="#2979ff">
						</u-icon>
					</view>
					<view class="close-icon" @click="removeFile(index)">
						<u-icon name="close" size="10" color="#FFFFFF"></u-icon>
					</view>
				</view>
				<view class="file-s" @click="addFile">
					<view class="margin-center">
						<text class="custom-icon custom-icon-wenjianjia icon"></text>
					</view>
					<text>添加文件</text>
				</view>
			</view>
		</view>
		<u-popup mode="bottom" v-model="showVoice" height="500rpx">
			<sound-recording :voiceP="voiceP" :finishP="finish" :maximum="60" @confirm="confirmVoice"
				@cancel="showVoice=false"></sound-recording>
		</u-popup>
		<video v-show="isOpenVideo" id="video" :src="videoUrl" @fullscreenchange="screenChange"></video>
		<u-toast ref="refToast"></u-toast>
	</view>
</template>

<script setup lang="ts">
	import {
		ref,
		getCurrentInstance,
		onMounted
	} from 'vue'
	import {
		chooseFile
	} from './fileMethod'
	import soundRecording from '@/components/sound-recording/sound-recording.vue'
	const refToast = ref<any>()
	const showVoice = ref<Boolean>(false)
	const finish = ref<Boolean>(false)
	const isOpenVideo = ref<Boolean>(false)
	const voiceP = ref<String>('')
	const voiceList = ref<any>([])
	const videoList = ref<any>([])
	const fileList = ref<any>([])
	const imageList = ref<any>([])
	const showUploadList = ref<any>([])
	const videoContext = ref<any>()
	const refUpload = ref<any>({})
	const videoUrl = ref<any>()
	const actionUrl = ref<string>('')
	const header = ref<any>({
		'X-Authorization': 'Bearer ' + uni.getStorageSync('token')
	})
	//录音
	const soundRecord = () => {
		voiceP.value = ''
		finish.value = false
		showVoice.value = true
	}

	//提交录音
	const confirmVoice = (file : any) => {
		console.log(file)
		uni.uploadFile({
			url: actionUrl.value, //仅为示例，非真实的接口地址
			filePath: file,
			header: {
				'X-Authorization': 'Bearer ' + uni.getStorageSync('token')
			},
			name: 'file',
			success: (uploadFileRes) => {
				voiceList.value.push(uploadFileRes.data)
				showVoice.value = false
				console.log(voiceList.value);
			}
		});
	}

	const addVideo = () => {
		// carmera.value.start({
		// 	success: (a) => {
		// 		console.log("livePusher.start:" + JSON.stringify(a));
		// 	}
		// });
		videoRecording()
	}
	const palyVideo = (url : string) => {
		videoUrl.value = url
		//打开视频（全屏播放）
		isOpenVideo.value = true
		videoContext.value = uni.createVideoContext('video', this)
		videoContext.value.play()
		videoContext.value.requestFullScreen()
	}
	const addFile = () => {
		chooseFile((url : string) => {
			uni.uploadFile({
				url: actionUrl.value, //仅为示例，非真实的接口地址
				filePath: url,
				header: {
					'X-Authorization': 'Bearer ' + uni.getStorageSync('token')
				},
				name: 'file',
				success: (uploadFileRes) => {
					fileList.value.push(uploadFileRes.data)
					console.log(fileList.value);
				},
				fail: (ee) => {
					console.log(ee);
				},
				complete: (ee) => {
					console.log(ee);
				}
			});
		})
		// console.log(showFile.value)
		// showFile.value = true

		// let REQUESTCODE = 1;
		// let main = plus.android.runtimeMainActivity() as any
		// const Intent = plus.android.importClass('android.content.Intent') as any
		// let intent = new Intent(Intent.ACTION_GET_CONTENT);

		// intent.setType("*/*"); //设置类型，任意类型
		// //intent.setType("image/*");
		// //intent.setType("audio/*"); //选择音频
		// //intent.setType("video/*"); //选择视频 （mp4 3gp 是android支持的视频格式）

		// intent.addCategory(Intent.CATEGORY_OPENABLE);
		// main.startActivityForResult(intent, REQUESTCODE)

		// main.onActivityResult = function(requestCode, resultCode, data) {
		// 	if (REQUESTCODE == requestCode) {
		// 		let context = main;
		// 		plus.android.importClass(data);
		// 		// 获得文件路径
		// 		let fileData = data.getData();
		// 		let path = plus.android.invoke(fileData, "getPath");
		// 		console.log("path:", path);
		// 		console.log("fileData:", fileData);
		// 		uni.uploadFile({
		// 			url: actionUrl.value, //仅为示例，非真实的接口地址
		// 			filePath: path,
		// 			header: {
		// 				'X-Authorization': 'Bearer ' + uni.getStorageSync('token')
		// 			},
		// 			name: 'file',
		// 			success: (uploadFileRes) => {
		// 				console.log(uploadFileRes.data);
		// 			},
		// 			fail: (ee) => {
		// 				console.log(ee);
		// 			},
		// 			complete: (ee) => {
		// 				console.log(ee);
		// 			}
		// 		});
		// 		// 判断文件类型
		// 		let resolver = context.getContentResolver();
		// 		let fileType = plus.android.invoke(resolver, "getType", fileData);
		// 	}
		// }
	}
	const removeFile = (index : Number) => {
		uni.showModal({
			title: '提示',
			content: '确定删除吗',
			success: (res) => {
				if (res.confirm) {
					fileList.value = fileList.value.splice(1, index)
				}

			}
		})
	}
	const removeVideo = (index : Number) => {
		uni.showModal({
			title: '提示',
			content: '确定删除吗',
			success: (res) => {
				if (res.confirm) {
					videoList.value = videoList.value.splice(1, index)
				}

			}
		})
	}
	const removeVoice = (index : Number) => {
		uni.showModal({
			title: '提示',
			content: '确定删除吗',
			success: (res) => {
				if (res.confirm) {
					voiceList.value = voiceList.value.splice(index, 1)
				}

			}
		})
	}
	const videoRecordingPath = ref<any>()
	const videoRecording = () => {
		uni.showLoading({
			title: '正在加载...'
		})
		uni.chooseVideo({
			count: 1,
			sourceType: ['album', 'camera'],
			maxDuration: 30,
			mediaType: ['video'],
			compressed: false,
			success: res => {
				console.log('chooseVideo.size1', res.size / 1048576)
				//视频在60-70-80M之间，压缩后约为6M
				saveFile(res.tempFilePath)
				// uni.compressVideo({
				// 	src: res.tempFilePath,
				// 	quality: 'high', //视频质量高低，基本上高质量时60m视频压缩后为6做左右，中质量60m压缩为3m左右，低质量60m压缩为2m左右（看不清人了）
				// 	bitrate: 2000,
				// 	fps: 30,
				// 	resolution: 1,
				// 	success: (result) => {
				// 		videoRecordingPath.value = result.tempFilePath;
				// 		console.log('compressVideo.size2', result.size / 1048576)
				// 		saveFile(result.tempFilePath);
				// 	},
				// 	fail: () => {
				// 		uni.hideLoading();
				// 		uni.showToast({
				// 			title: '相机调用失败！',
				// 			icon: 'none',
				// 			duration: 1500
				// 		});
				// 	}
				// })
			},
			fail: (err) => {
				refToast.value.show({
					title: '视频录制失败!',
					type: 'error'
				})
			}
		})
	}
	const saveFile = (filePath : string) => {
		console.log(filePath)
		uni.uploadFile({
			url: actionUrl.value, //仅为示例，非真实的接口地址
			filePath: filePath,
			header: {
				'X-Authorization': 'Bearer ' + uni.getStorageSync('token')
			},
			name: 'file',
			success: (uploadFileRes) => {
				console.log(uploadFileRes.data);
				videoList.value.push(uploadFileRes.data)
				console.log(videoList.value);
				uni.hideLoading();
			},
			fail: (ee) => {
				console.log(ee);
				uni.hideLoading();
			},
			complete: (ee) => {
				console.log(ee);
				uni.hideLoading();
			}
		});
	}


	const onSuccess = (data, index, lists, name) => {
		imageList.value = lists.map(img => {
			return img.response
		})
		console.log(imageList.value)
	}
	
	const deletePic = event => {
		imageList.value.splice(event.index, 1)
	}
	//全屏
	const screenChange = (e) => {
		//退出全屏时停止播放
		if (!e.detail.fullScreen) {
			videoContext.value.stop()
			isOpenVideo.value = false
		}
	}

	const playVoice = (voicePath : String) => {
		console.log('播放录音');
		voiceP.value = voicePath
		finish.value = true
		showVoice.value = true
	}

	onMounted(() => {
		const globalProperties = getCurrentInstance()?.appContext.config.globalProperties
		console.log('globalProperties-----', globalProperties)
		const path = uni.getStorageSync('url')
		actionUrl.value = path + 'file/api/upload/file'

	})
	defineExpose({
		videoList,
		fileList,
		imageList,
		voiceList
	});
</script>

<style lang="scss" scoped>
	.content-card {
		width: 686rpx;
		margin: 20rpx auto;
		background-color: #FFFFFF;
		min-height: 224rpx;
		border-radius: 8px;
		padding: 24rpx 0 0 28rpx;

		.title-text {
			color: #91949F;
			font-size: 28rpx;
		}

		.file-s {
			width: 116rpx;
			height: 116rpx;
			display: flex;
			justify-content: center;
			flex-direction: column;
			text-align: center;
			background: #F9F9F9;
			position: relative;

			text {
				color: #91949F;
				font-size: 20rpx;
			}

			.close-icon {
				border-radius: 50%;
				width: 32rpx;
				height: 32rpx;
				background-color: red;
				line-height: 32rpx;
				text-align: center;
				position: absolute;
				right: 0;
				top: 0;
			}
		}

		.margin-center {
			margin: 0 auto;
			width: 48rpx;
			height: 48rpx;

			.icon {
				font-size: 48rpx;
			}
		}
	}

	::v-deep .u-form-item__body {
		padding: 16rpx;
	}

	::v-deep .u-form-item {
		padding: 0;
	}
</style>