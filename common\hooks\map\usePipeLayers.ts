import { ref, computed } from 'vue'
import { gisConfig } from '@/common/data/gisData'
import { useStore } from '@/store'
import { storeToRefs } from 'pinia'

// 定义图层字段接口
interface LayerField {
    name: string;
    type: string;
}

// 定义图层信息接口
interface LayerInfo {
    geometrytype: string
    layerdbname: string
    layerid: number
    layername: string
    icon?: string
}

// 定义图层图标映射接口
interface LayerIconMap {
    [key: string]: string;
}

export const usePipeLayers = () => {
  const { pipeLayerInfoes } = storeToRefs(useStore())
  const selected = ref<number[]>(pipeLayerInfoes.value?.map((item: LayerInfo) => item.layerid) || [])
  const selectedIds = computed(() => selected.value.join(','))
  const pipeServerUrl = ref<string>(gisConfig().gisPipeDynamicService)
  const center = ref<string>(gisConfig().gisDefaultCenter)
  const currToggled = ref<{
      id: string | number
      visible: boolean
  }>({
      id: '-1',
      visible: false
  })
    
  const layerIcons: LayerIconMap = {
    '节点': 'custom-icon-jiedianguanli',
    '管网管线': 'custom-icon-guanxianbiaozhu',
    '水源': 'custom-icon-liuliang1',
    '计量装置': 'custom-icon-shuiliang',
    '消防栓': 'custom-icon-xiaofangshuan',
    '阀门': 'custom-icon-famen',
    '非控制阀': 'custom-icon-gis-fm-paiqifa',
    '排气阀': 'custom-icon-gis-fm-paiqifa',
    '流量计': 'custom-icon-liuliang',
    '压力计': 'custom-icon-yali',
    '水表': 'custom-icon-shuibiao',
    '四通': 'custom-icon-GIS-tulitucengicon_sitong',
    '三通': 'custom-icon-gis-jd-santong',
    '弯头': 'custom-icon-GIS-tulitucengicon_wantou',
    '水泵': 'custom-icon-shuibeng',
    '检查井': 'custom-icon-gis-js-jianchajing'
  }
    
  const layers = ref<LayerInfo[]>(pipeLayerInfoes.value || [])
	const pointLayers = computed(() => {
		return layers.value.filter(item => item.geometrytype === 'esriGeometryPoint')
	})
	const lineLayers = computed(() => {
		return layers.value.filter(item => item.geometrytype === 'esriGeometryPolyline')
	})
  const getLayerInfo = (view: { layerInfos: any[] }) => {
      try {
          const layerInfos: LayerInfo[] = view.layerInfos.map((item, index) => {
              const layerName = item.title || item.name;
              return {
                  layerid: index,
                  layername: layerName,
                  layerdbname: item.name,
                  geometrytype: item.geometryType || 'esriGeometryUnknown',
                  icon: layerIcons[layerName as keyof LayerIconMap]
              };
          });

          // 更新状态
          if (layerInfos.length > 0) {
              layers.value = layerInfos;
              selected.value = layerInfos.map(item => item.layerid);
              pipeLayerInfoes.value = layerInfos;
          }

      } catch (error) {
          console.error('获取图层信息失败:', error);
      }
  }
  const toggle = (id: number) => {
      const index = selected.value.findIndex(item => item === id)
      if (index !== -1) {
          selected.value.splice(index, 1)
      } else {
          selected.value.push(id)
      }
      currToggled.value = {
          id: id,
          visible: index === -1
      }
  }
	return {
		getLayerInfo,
		selected,
		toggle,
		currToggled,
		layers,
		pointLayers,
		lineLayers,
		pipeServerUrl,
		selectedIds,
		center
	}
}