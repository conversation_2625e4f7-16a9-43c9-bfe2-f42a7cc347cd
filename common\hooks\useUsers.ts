import { getLatestUserCoords } from "../api/inspection"
import { ref } from 'vue'
interface IUserCoordsParams {
	userName?: string
	departmentId?: string
	userTypeId?: string
	status?: string
	page?: number
	size?: number
	fromTime?: string
	toTime?: string
}
export const useUsers = () => {
	const latestUserCoords = ref<{ data: any[], total: number }>({ data: [], total: 0 })
	const _getLatestUserCoords = async (query: IUserCoordsParams) => {
		const res = await getLatestUserCoords({
			page: 1,
			size: 999999,
			...query
		})
		const result = {
			data: res.data?.data?.data || [],
			total: res.data?.data?.total || 0
		}
		latestUserCoords.value = result
		return result
	}
	const getLatestXunJianRenYuanCoords = async (
		query: Omit<IUserCoordsParams, 'userTypeId'>
	) => {
		await _getLatestUserCoords({
			...query,
			userTypeId: 'XUNJIANRENYUAN'
		})
	}
	const getLatestCHAOBIAORENYUANCoords = (
		query: Omit<IUserCoordsParams, 'userTypeId'>
	) => {
		return _getLatestUserCoords({
			...query,
			userTypeId: 'CHAOBIAORENYUAN'
		})
	}
	const getLatestQIANGXIURENYUANCoords = (
		query: Omit<IUserCoordsParams, 'userTypeId'>
	) => {
		return _getLatestUserCoords({
			...query,
			userTypeId: 'QIANGXIURENYUAN'
		})
	}
	const reset = () => {
		latestUserCoords.value = {
			data: [],
			total: 0
		}
	}
	return {
		reset,
		latestUserCoords,
		_getLatestUserCoords,
		getLatestCHAOBIAORENYUANCoords,
		getLatestXunJianRenYuanCoords,
		getLatestQIANGXIURENYUANCoords
	}
}