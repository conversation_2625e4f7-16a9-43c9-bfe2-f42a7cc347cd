<template>
	<view v-if="visible" class="map-pipe-layers border-box layer">
		<view class="cover-header">
			<text class="title">选择图层</text>
			<text class="icon" @click="close">x</text>
		</view>
		<scroll-view class="menu-list flex-around" :scroll-y="true">
			<view class="layer-box">
				<text class="layer-title">
					管点类
				</text>
				<view class="layer-menu">
					<view class="menu-item" v-for="(menu,i) in pipelayer.pointLayers.value" :key="i"
						@click="toggle(menu.layerid)">
						<view class="icon-bg"
							:style="{'background-color':pipelayer.selected.value.indexOf(menu.layerid)!==-1?'#3862F8':'#E2E3E5'}">
							<text :class="['layer-menu-icon','custom-icon',menu.icon]"
								:style="{'color':pipelayer.selected.value.indexOf(menu.layerid)!==-1?'#ffffff':'#060F27'}"></text>
						</view>

						<text class="layer-menu-text"
							:style="{'color':pipelayer.selected.value.indexOf(menu.layerid)!==-1?'#3862F8':'#060F27'}">{{menu.layername}}</text>
					</view>
				</view>
			</view>
			<view class="layer-box">
				<text class="layer-title">
					管线类
				</text>
				<view class="layer-menu">
					<view class="menu-item" v-for="(menu,i) in pipelayer.lineLayers.value" :key="i"
						@click="toggle(menu.layerid)">
						<view class="icon-bg"
							:style="{'background-color':pipelayer.selected.value.indexOf(menu.layerid)!==-1?'#3862F8':'#E2E3E5'}">
							<text :class="['layer-menu-icon','custom-icon',menu.icon]"
								:style="{'color':pipelayer.selected.value.indexOf(menu.layerid)!==-1?'#ffffff':'#060F27'}"></text>
						</view>

						<text class="layer-menu-text"
							:style="{'color':pipelayer.selected.value.indexOf(menu.layerid)!==-1?'#3862F8':'#060F27'}">{{menu.layername}}</text>
					</view>
				</view>
			</view>
		</scroll-view>
	</view>
</template>

<script lang="ts" setup>
	import {
		usePipeLayers
	} from '@/common/hooks'
	import { ref } from 'vue';
	const emit = defineEmits(['update:modelValue', 'close', 'toggle'])
	defineProps<{ modelValue : string }>()
	const pipelayer = usePipeLayers()
	const visible = ref<boolean>(true)

	const close = () => {
		visible.value = false
		emit('close')
	}
	const open = () => {
		visible.value = true
	}
	const toggle = (id : number) => {
		pipelayer.toggle(id)
		emit('update:modelValue', pipelayer.selectedIds.value)
		emit('toggle', id)
	}
	defineExpose({
		open,
		close
	})
</script>

<style lang="scss" scoped>
	.map-pipe-layers {
		/* width: 750rpx; */
		position: absolute;
		bottom: 0;
		background-color: #FBFBFB;
		// z-index: 99999;
		height: 350rpx;

		&.pull-up {
			height: 800rpx;
		}

		&.layer {
			width: 100%;
			padding: 0 32rpx;
			border-radius: 16rpx 16rpx 0rpx 0;
			height: 800rpx;
			overflow: hidden;



			.cover-main {
				display: flex;
				justify-content: space-between;
				flex-wrap: nowrap;
				height: 200rpx;

				.item {
					width: calc(50% - 20rpx);
					height: 100%;
					position: relative;

					.item-image {
						width: 100%;
						height: 100%;
						border-radius: 8px;
					}

					.item-text {
						background: rgba(255, 255, 255, 0.8);
						border-radius: 0px 0px 8px 8px;
						width: 100%;
						position: absolute;
						bottom: 0;
						left: 0;
						height: 48rpx;
						line-height: 48rpx;
						padding: 0 20rpx;
						font-size: 24rpx;
					}
				}
			}


			.menu-list {
				height: 700rpx;
			}
		}

		.cover-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			height: 86rpx;

			.title {
				text-align: left;
				word-break: keep-all;
			}

			.icon {
				font-size: 1.2em;
			}
		}

		transition: all 0.5s ease-in-out;
	}

	.border-box {
		width: 100%;
		padding-top: 30rpx;
		// z-index: 99999;
		background-color: #FBFBFB;
		position: absolute;
		bottom: 0;
		align-items: center;
		justify-content: space-around;
		// box-shadow: 0 4rpx 30rpx rgba(178, 183, 199, 0.5);
		border-radius: 16rpx 16rpx 0rpx 0;

		.menu-list {
			flex-direction: column;
			display: flex;
			justify-content: space-around;
			width: 100%;

			.layer-box {
				width: 100%;
				padding: 0 32rpx 25rpx;

				.layer-title {
					color: #91949F;
				}

				.layer-menu {
					display: flex;
					justify-content: flex-start;
					align-items: center;
					flex-wrap: wrap;
					border-radius: 8px;
					background-color: #ffffff;
					margin-top: 20rpx;
					padding-bottom: 20rpx;

					.menu-item {
						width: 20%;
						align-items: center;
						padding: 25rpx 0 0;
						text-align: center;
						display: flex;
						justify-content: center;
						flex-direction: column;

						.icon-bg {
							border-radius: 50%;
							width: 64rpx;
							height: 64rpx;
							display: flex;
							align-items: center;
							justify-content: center;

							&.round-rect {
								border-radius: 16rpx;
							}
						}

						.layer-menu-text {
							word-break: keep-all;

						}
					}


				}
			}
		}
	}
</style>