<template>
	<view style="padding-bottom: 140rpx;">
		<u-navbar fixed placeholder safeAreaInsetTop bgColor="#FFFFFF" title="报警信息" :autoBack="true" leftIconSize="20">
			<template #right>
				<view class="nv-right" @click="showScreen">
					<image src="/static/img/icons/shaixuan-black.png" style="height: 48rpx;width: 48rpx;"></image>
				</view>
			</template>
		</u-navbar>
		<!-- <scroll-view style="height: 90vh;" :lower-threshold="50" :refresher-threshold="50" scroll-y refresher-enabled
			refresher-default-style="white" :refresher-triggered="triggered" @refresherrefresh="onRefresh"
			@scrolltolower="showMoreData"> -->
		<view class="main-content">
			<u-checkbox-group @change="chooseAlarms">
				<view class="card-box" v-for="(alarm,index) in alarms" :key="index">
					<view class="flex-between hand">
						<view class="hand-title flex-center">
							<u-checkbox v-model="alarm.checked" :name="alarm.id" shape="circle"></u-checkbox>
							<text>{{alarm.title}}</text>
						</view>
						<view class="status"
							:style="{color:alarmStatus.find(item => item.value === alarm.alarmStatus)?.color}">
							{{alarmStatus.find(item => item.value === alarm.alarmStatus)?.label}}
						</view>
					</view>
					<u-gap height="1" bgColor="#EBEDF6"></u-gap>
					<view class="table">
						<u-row>
							<u-col span="7">
								<view class="info">
									<text>类型：</text> {{alarmType.find(item => item.value === alarm.alarmType)?.label}}
								</view>
							</u-col>
							<u-col span="5">
								<view class="info">
									<text>级别：</text><text
										:style="{color:alarmLevel.find(item => item.value === alarm.alarmLevel)?.color}">{{alarmLevel.find(item => item.value === alarm.alarmLevel)?.label}}</text>
								</view>
							</u-col>
						</u-row>
						<u-row>
							<u-col span="4">
								<view class="info" style="text-align: top;">
									<text>报警描述：</text>
								</view>
							</u-col>
							<u-col span="8">
								<view class="info">
									{{alarm.alarmInfo}}
								</view>
							</u-col>
						</u-row>
						<u-row>
							<u-col span="4">
								<view class="info" style="text-align: top;">
									<text>处理状态：</text>
								</view>
							</u-col>
							<u-col span="8">
								<view class="info"
									:style="{color:processStatus.find(item => item.value === alarm.processStatus)?.color}">
									{{processStatus.find(item => item.value === alarm.processStatus)?.label}}
								</view>
							</u-col>
						</u-row>
						<u-row>
							<u-col span="4">
								<view class="info" style="text-align: top;">
									<text>首次报警：</text>
								</view>
							</u-col>
							<u-col span="8">
								<view class="info">
									{{proxy.formatTime(alarm.time) || '-'}}
								</view>
							</u-col>
						</u-row>
						<u-row>
							<u-col span="4">
								<view class="info" style="text-align: top;">
									<text>报警结束：</text>
								</view>
							</u-col>
							<u-col span="8">
								<view class="info">
									{{proxy.formatTime(alarm.endTime) || '-'}}
								</view>
							</u-col>
						</u-row>

						<!-- <u-row>
							<u-col span="4">
								<view class="info" style="text-align: top;">
									<text>处理信息：</text>
								</view>
							</u-col>
							<u-col span="8">
								<view class="info">
									-
								</view>
							</u-col>
						</u-row> -->
						<!-- <u-row>
							<u-col span="4">
								<view class="info" style="text-align: top;">
									<text>处理时间：</text>
								</view>
							</u-col>
							<u-col span="8">
								<view class="info">
									{{proxy.formatTime(alarm.endTime) || '-'}}
								</view>
							</u-col>
						</u-row> -->
						<!-- <u-row>
							<u-col span="6">
								<view class="info">
									<text>处理人：</text> 超限报警
								</view>
							</u-col>
							<u-col span="6">
								<view class="info">
									<text>联系电话：</text> 一级
								</view>
							</u-col>
						</u-row> -->
					</view>
					<u-gap height="1" bgColor="#EBEDF6"></u-gap>
					<view class="method flex-end">
						<!-- <u-button @click="showAnalysis(alarm)" type="primary" shape="circle" class="custom-style" plain>
							故障分析
						</u-button> -->
						<u-button type="primary" shape="circle" class="custom-style" @click="showModel(alarm.id)">
							报警处理
						</u-button>
					</view>
				</view>
			</u-checkbox-group>
		</view>
		<u-loadmore bg-color="F9F9F9" :status="state.status" loading-text="努力加载中" loadmore-text="加载更多"
			nomore-text="没有了更多" />
		<!-- </scroll-view> -->

		<view class="button flex-between">
			<u-button type="primary" @click="batchHandle">批量处理</u-button>
			<!-- <u-button type="primary">全部处理(非工单)</u-button> -->
		</view>
			<u-popup mode="right" width="528" v-model="state.screenShow" closeOnClickOverlay overlay
				@close="state.screenShow = false">
				<view class="popup">
				<view class="screen-list">
					<u-form :model="screenForm" ref="form1" :label-style="{'color':'#91949F'}" border-bottom input-align="right"
						label-width="180">
						<!-- <u-form-item label="报警名称：" prop="name">
							<u-input placeholder="请输入报警名称" v-model="screenForm.name">
							</u-input>
						</u-form-item> -->

						<u-form-item label="报警类型：" prop="typeName">
							<input inputmode="none" placeholder-class="placeholderClass" placeholder="点击选择" v-model="screenForm.typeName"
								@click="state.alarmTypeShow=true">
							</input>
							<template #right>
								<u-icon name="arrow-down" color="#91949F" size="34"></u-icon>
							</template>
						</u-form-item>
						<u-form-item label="开始时间：" prop="start">
							<input inputmode="none" placeholder-class="placeholderClass" placeholder="点击选择" v-model="screenForm.start"
								@click="chooseDate('start')">
							</input>
							<template #right>
								<u-icon name="calendar" color="#91949F" size="34"></u-icon>
							</template>
						</u-form-item>
						<u-form-item label="结束时间：" prop="end">
							<input inputmode="none" placeholder-class="placeholderClass" placeholder="点击选择" v-model="screenForm.end" @click="chooseDate('end')">
							</input>
							<template #right>
								<u-icon name="calendar" color="#91949F" size="34"></u-icon>
							</template>
						</u-form-item>

						<!-- <u-form-item label="处理状态：" prop="screenForm.statusName">
							<input inputmode="none" placeholder-class="placeholderClass" placeholder="点击选择" v-model="screenForm.statusName">

							</input>
							<template #right>
								<u-icon name="arrow-down" color="#91949F" size="34"></u-icon>
							</template>
						</u-form-item> -->

						<u-form-item label="报警级别：" prop="level">
							<input inputmode="none" placeholder-class="placeholderClass" placeholder="点击选择" v-model="screenForm.levelName"
								@click="state.alarmLevelShow=true">

							</input>
							<template #right>
								<u-icon name="arrow-down" color="#91949F" size="34"></u-icon>
							</template>
						</u-form-item>

						<!-- <u-form-item label="应急预案：" prop="screenForm.val">
							<input  inputmode="none" placeholder-class="placeholderClass" placeholder="点击选择" v-model="screenForm.val">

							</input>
							<template #right>
								<u-icon name="arrow-down" color="#91949F" size="34"></u-icon>
							</template>
						</u-form-item> -->

						<u-form-item label="报警状态：" prop="statusName">
							<input inputmode="none" placeholder-class="placeholderClass" placeholder="点击选择" v-model="screenForm.statusName"
								@click="state.alarmStatusShow=true">
							</input>
							<template #right>
								<u-icon name="arrow-down" color="#91949F" size="34"></u-icon>
							</template>
						</u-form-item>
						<u-form-item label="处理状态：" prop="processStatusName">
							<input inputmode="none" placeholder-class="placeholderClass" placeholder="点击选择" v-model="screenForm.processStatusName"
								@click="state.processStatusShow=true">

							</input>
							<template #right>
								<u-icon name="arrow-down" color="#91949F" size="34"></u-icon>
							</template>
						</u-form-item>

						<!-- <u-form-item label="上报工单：" prop="screenForm.val">
							<input  inputmode="none" placeholder-class="placeholderClass" placeholder="点击选择" v-model="screenForm.val">

							</input>
							<template #right>
								<u-icon name="arrow-down" color="#91949F" size="34"></u-icon>
							</template>
						</u-form-item> -->
					</u-form>
				</view>
				<view class="popup-button">
					<u-button type="primary" color="#3862F8" @click="submitScreen">确定</u-button>
				</view>
				</view>
			</u-popup>
		<!-- <u-action-sheet @close="statusShow=false" :actions="statusList" :closeOnClickOverlay="true"
			:closeOnClickAction="true" v-model="statusShow"></u-action-sheet> -->
		<!-- 数据列表 -->
		<u-popup overlay border-radius="16" closeable mode="bottom" v-model="analysisShow" safeAreaInsetBottom
			round="16" @close="analysisShow=false">
			<view class="popup-a">
				<data-list :propertyList="propertyList" :currentProperty="state.currentProperty" title="故障分析"
					:headers="headers" :tableData="tableData"></data-list>
			</view>
		</u-popup>
		<!-- 告警类型 -->
		<u-picker v-model="state.alarmTypeShow" mode="selector" :defaultSelector="state.typeIndex"
			:range="workOrderTypes" range-key="label" @confirm="selectType"></u-picker>
		<!-- 告警级别 -->
		<u-picker v-model="state.alarmLevelShow" mode="selector" :defaultSelector="state.levelIndex"
			:range="alarmLevelList" range-key="label" @confirm="selectAlarmLevel"></u-picker>
		<!-- 告警状态 -->
		<u-picker v-model="state.alarmStatusShow" mode="selector" :defaultSelector="state.statusIndex"
			:range="alarmStatusList" range-key="label" @confirm="selectAlarmStatus"></u-picker>
		<!-- 处理状态 -->
		<u-picker v-model="state.processStatusShow" mode="selector" :defaultSelector="state.processStatusIndex"
			:range="processStatusList" range-key="label" @confirm="selectProcessStatus"></u-picker>
		<!-- 日期选择 -->
		<u-calendar v-model="state.calendarShow" ref="calendar" @change="confirmDate">
		</u-calendar>
		<!-- <u-picker v-model="state.typeShow" mode="selector" :defaultSelector="[0]" :range="typeList" range-key="name"
			@confirm="selectClick"></u-picker> -->
		<!-- <u-modal v-model="modalShow" title="提示" maskCloseAble content="是否进行报警工单上报？" showCancelButton
			cancelText="否(非工单报警处理)" confirmText="是(报警工单上报)" @cancel="handleAlarm" @confirm="toOrderWork"></u-modal> -->
			<u-modal v-model="modalShow" title="提示" maskCloseAble content="是否处理此报警？" showCancelButton @cancel="modalShow=false" @confirm="handleAlarm"></u-modal>
		<u-toast ref="refToast" />
	</view>
</template>

<script lang="ts" setup>
	import {
		onShow
	} from '@dcloudio/uni-app';
	import {
		alarmRelieveV2,
		getOwnAlarmListV2
	} from '@/common/api/alarmManage'
	import {
		reactive,
		ref,
		getCurrentInstance
	} from "vue"
	import {
		onPullDownRefresh,
		onReachBottom
	} from '@dcloudio/uni-app'
	import {
		getDeviceVarGroup
	} from '@/common/api/monitoring'
	import dataList from '@/components/dataList/dataList.vue'
	import dayjs from 'dayjs';
	const {
		proxy
	} = getCurrentInstance()
	const alarmLevel = [
		{ label: '提醒报警', value: '1', color: '#004cff' },
		{ label: '重要报警', value: '2', color: '#ffa700' },
		{ label: '紧急报警', value: '3', color: '#ff0015' }
	]

	const alarmType = [
		{ label: '液位异常', value: '1' },
		{ label: '水质异常', value: '2' },
		{ label: '设备故障', value: '3' },
		{ label: '通讯异常', value: '4' },
		{ label: '流量异常', value: '5' },
		{ label: '控制异常', value: '6' },
		{ label: '设备健康', value: '7' },
		{ label: '其他', value: '8' }
	]

	const alarmStatus = [
		{ label: '报警中', value: '1', color: '#ff0015' },
		{ label: '已恢复', value: '2', color: '#004cff' },
		{ label: '已解除', value: '3', color: '#004cff' }
	]

	const processStatus = [
		{ label: '未处理', value: '1', color: '#ff0015' },
		{ label: '处理中', value: '2', color: '#ffa700' },
		{ label: '已处理', value: '3', color: '#004cff' },
	]
	// 选择区域
	const state = reactive<{
		screenShow : boolean,
		currentProperty : any,
		status : string,
		exceptionId : String,
		showDateType : String,
		alarmTypeShow : boolean,
		alarmLevelShow : boolean,
		alarmStatusShow : boolean,
		processStatusShow : boolean,
		calendarShow : boolean,
		typeIndex : any,
		statusIndex : any,
		levelIndex : any,
		processStatusIndex:any
	}>({
		typeIndex: [0],
		statusIndex: [0],
		levelIndex: [0],
		processStatusIndex: [1],
		screenShow: false,
		currentProperty: {},
		status: 'loadmore',
		exceptionId: '',
		alarmTypeShow: false,
		alarmLevelShow: false,
		alarmStatusShow: false,
		processStatusShow: false,
		calendarShow: false,
		showDateType: '',
	})

	const minDate = ref<string>(proxy.$startDate)
	const modalShow = ref<boolean>(false)
	const refToast = ref<any>()
	const screenForm = reactive<any>({
		size: 10,
		page: 1,
		alarmType: '',
		processStatus: '1',
		alarmStatus: '',
		alarmLevel: '',
	})

	const triggered = ref<boolean>()
	const analysisShow = ref<boolean>(false)
	const propertyList = ref<any>([])
	const alarms = ref<any>([])
	const alarmIds = ref<any>([])
	const headers = ref<any>([])
	const tableData = ref<any>([])
	const workOrderTypes = ref<any>(alarmType)
	const alarmLevelList = ref<any>(alarmLevel)
	const alarmStatusList = ref<any>(alarmStatus)
	const processStatusList = ref<any>(processStatus)
	// 显示详情数据
	// const showHistoryData = async (params ? : any) => {
	// 	params = params || {}
	// 	params = {
	// 		start: dayjs(params.start).startOf('day').valueOf(),
	// 		end: dayjs(params.end).endOf('day').valueOf(),
	// 		type: params.type || '15m',
	// 		attributes: [state.currentProperty.deviceId + '.' + state.currentProperty.property],
	// 	}
	// 	const res = await getDeviceData(params)
	// 	const data = res.data
	// 	let newData = []
	// 	for (let key in data) {
	// 		newData.push({
	// 			'date': key,
	// 			'val': data[key][state.currentProperty.deviceId + '.' + state.currentProperty.property]
	// 		})
	// 	}
	// 	tableData.value = newData
	// 	analysisShow.value = true
	// }

	//查询历史数据
	// const onQuery = (query: any) => {
	// 	showHistoryData(query)
	// }
	// 加载更多
	const showMoreData = async () => {
		console.log('dddddd')
		state.status = 'loading'
		await alarmList()
	} ///
	const chooseDate = (type : string) => {
		state.calendarShow = true
		if (type === 'end') {
			minDate.value = screenForm.start
		} else {
			minDate.value = proxy.$startDate
		}
		state.showDateType = type
	}

	const confirmDate = (date : any) => {
		state.calendarShow = false
		if (state.showDateType === 'start') {
			screenForm.start = date.result
			screenForm.end = ''
		} else {
			screenForm.end = date.result
		}
	}

	// 下拉刷新
	const onRefresh = async () => {
		triggered.value = true
		screenForm.page = 1
		await alarmList()
	}
	//分析
	const showAnalysis = async (data : any) => {

		const res = await getDeviceVarGroup(data.deviceId)
		const groups = res.data['全部']
		console.log(groups)
		// if(!data.property){
		// 	data.property =
		// }
		let property = groups.find(g => g.label === data.property)
		if (!property) {
			property = groups[0]
		}
		data = {
			...data,
			propertyName: property.value,
			unit: property.unit
		}
		console.log(data)
		state.currentProperty = data
		propertyList.value = [state.currentProperty]
		// await showHistoryData()
		analysisShow.value = true
	}
	// 类型
	const selectType = (val : any) => {
		state.typeIndex = val
		const type = workOrderTypes.value[state.typeIndex[0]]
		screenForm.alarmType = type.value
		screenForm.typeName = type.label
		state.typeIndex = [0]
	}
	// 级别
	const selectAlarmLevel = (val : any) => {
		state.levelIndex = val
		const status = alarmLevelList.value[state.levelIndex[0]]
		screenForm.levelName = status.label
		screenForm.alarmLevel = status.value
		state.levelIndex = [0]
	}
	// 告警状态
	const selectAlarmStatus = (val : any) => {
		state.statusIndex = val
		const status = alarmStatusList.value[state.statusIndex[0]]
		screenForm.statusName = status.label
		screenForm.alarmStatus = status.value
		state.statusIndex = [0]
	}
	// 处理状态
	const selectProcessStatus = (val : any) => {
		state.processStatusIndex = val
		console.log(val)
		const status = processStatusList.value[state.processStatusIndex[0]]
		screenForm.processStatusName = status.label
		screenForm.processStatus = status.value
		state.processStatusIndex = [0]
	}

	// 提交筛选
	const submitScreen = () => {
		screenForm.page = 1
		console.log('参数',screenForm)
		alarmList()
		state.screenShow = false
	}
	const chooseAlarms = (val : any) => {
		console.log(val)
		alarmIds.value = val
	}
	//批量处理
	const batchHandle = () => {
		if (alarmIds.value && alarmIds.value.length > 0) {
			uni.showModal({
				title: '提示',
				content: '确定批量处理吗',
				success: (res) => {
					if (res.confirm) {
						alarmRelieveV2(alarmIds.value).then(res => {
							console.log(res)
							refToast.value.show({
								title: '处理成功',
								type: 'success',
								callback: () => {
									onRefresh()
								}
							})

						}).catch(() => {
							refToast.value.show({
								title: '处理失败',
								type: 'error '
							})
						})
					}

				}
			})
		} else {
			uni.$u.toast('请选择报警信息')
		}

	}
	// const statusList = reactive < any > ([{
	// 	value: '1',
	// 	name: '合格'
	// }, {
	// 	value: '2',
	// 	name: '不合格'
	// }, {
	// 	value: '3',
	// 	name: '其他'
	// }, ])
	// 选择日期
	const showScreen = () => {
		state.screenShow = true
	}
	// 报警处理
	const showModel = (id : String) => {
		state.exceptionId = id
		// handleAlarm()
		modalShow.value = true
		// uni.$u.route({
		// 	url:'pages/wisdomProduce/exceptionalManage/handleAlarm/index'
		// })
	}
	// 报警处理
	const handleAlarm = () => {
		modalShow.value = false
		// 暂时取消处理内容填写
		// uni.$u.route({
		// 	url: 'pages/wisdomProduce/exceptionalManage/handleAlarm/index',
		// 	params: {
		// 		exceptionId: state.exceptionId
		// 	}
		// })
		alarmRelieveV2([state.exceptionId]).then(res => {
			refToast.value.show({
				title: '处理成功',
				type: 'success',
				callback: () => {
					onRefresh()
				}
			})
		}).catch(() => {
			refToast.value.show({
				title: '处理失败',
				type: 'error '
			})
		})
	}
	//前往工单发起页面
	const toOrderWork = () => {
		modalShow.value = false
		uni.$u.route({
			url: 'pages/wisdomProduce/exceptionalManageV3/workOrderInitiation/index',
			params: {
				exceptionId: state.exceptionId
			}
		})
	}

	//
	const alarmList = async () => {
		alarmIds.value = []
		const params = {
			...screenForm,
			start: screenForm.start ? dayjs(screenForm.start).startOf('day').valueOf() : null,
			end: screenForm.end ? dayjs(screenForm.end).endOf('day').valueOf() : null,
		}
		getOwnAlarmListV2(params).then(res => {
			const data = res.data?.data?.data
			const total = res.data?.data?.total
			if (screenForm.page === 1) {
				alarms.value = data
			} else {
				alarms.value = alarms.value.concat(data)
			}
			if (data?.length > 0 && total > alarms.value.length) {
				screenForm.page += 1
				state.status = 'loadmore'
			} else {
				state.status = 'nomore'
			}
			triggered.value = false
			uni.stopPullDownRefresh()
			// console.log(total, screenForm.page)
			// if (data && data.length > 0 && total > alarms.value.length) {
			// 	screenForm.page += 1
			// 	state.status = 'loadmore'
			// } else {
			// 	state.status = 'nomore'
			// }
			// if (data.length > 0) {
			// 	if (screenForm.page === 1) {
			// 		alarms.value = data
			// 	} else {
			// 		alarms.value = alarms.value.concat(data)
			// 	}
			// 	screenForm.page += 1
			// 	state.status = 'loadmore'
			// 	if (data.length === total) { state.status = 'nomore' }
			// } else {
			// 	state.status = 'nomore'
			// }

			// triggered.value = false
			// uni.stopPullDownRefresh()
		})

	}

	onReachBottom(async () => {
		showMoreData()
	})

	onPullDownRefresh(async () => {
		onRefresh()
	})

	// onBeforeMount(async () => {
	// 	workOrderTypes.value = await getWorkOrderTypeList()
	// })

	onShow(async () => {
		alarmIds.value = 1
		screenForm.page = 1
		onRefresh()
	})
</script>

<style lang="scss" scoped>
	.main {
		padding-bottom: 140rpx;
	}

	.main-content {
		width: 686rpx;
	}

	.card-box {
		width: 686rpx;
		margin: 20rpx auto;
		min-height: 308rpx;
		border-radius: 16rpx;
		padding: 22rpx 28rpx;
		background-color: #FFFFFF;

		.hand {
			height: 62rpx;
			line-height: 62rpx;
			padding: 22rpx 0;

			.hand-title {
				width: 80%;

				text {
					padding-left: 16rpx;
					font-size: 28rpx;
					color: #060F27;
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
					font-weight: 700;
				}
			}

			.status {
				color: #91949F;
			}
		}

		.table {
			margin-top: 24rpx;

			.info {
				font-size: 28rpx;
				padding-bottom: 18rpx;

				text:nth-child(1) {
					color: #91949F;
				}
			}
		}

		.method {
			padding-top: 24rpx;
			width: 80%;
			position: relative;
			left: 60%;
			.custom-style {
				width: 220rpx;
				height: 56rpx;
				font-size: 24rpx;
				margin-left: 24rpx;
				color: #FFFFFF;
			}
		}
	}

	.popup {
		width: 100%;
		height: 100%;
		position: relative;
		padding: 0 34rpx;


		.popup-button {
			width: 94%;
			position: absolute;
			bottom: 40rpx;
			left: 0;
			right: 0;
			margin: 0 auto;
		}

		::v-deep .placeholderClass {
			font-size: 28rpx;
		}

		.screen-list {
			padding-top: 222rpx;
		}
	}

	.button {
		button {
			width: 90%;
		}
	}

	::v-deep .u-form-item__body {
		padding: 8rpx 34rpx;
	}

	::v-deep.u-form-item {
		padding: 0;
	}

	::v-deep.u-checkbox {
		display: block;
	}
</style>
