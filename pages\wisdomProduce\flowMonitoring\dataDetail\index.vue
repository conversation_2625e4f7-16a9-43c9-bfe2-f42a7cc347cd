<template>
	<view class="main">
		<u-navbar :borderBottom="false" :background="{backgroundColor: '#151A2C'}" title="流量监测" titleColor="#FFFFFF"
			backIconColor="#FFFFFF">
		</u-navbar>
		<view class="top">
			<view class="flex-center title">
				<image src="/static/img/icons/flow.png" style="width:36rpx;height: 36rpx;"></image>
				<text>{{state.currentProperty.name}}</text>
			</view>
			<scroll-view scroll-y>
				<view class="property-list flex-between">
					<view :class="['property','flex-around',{'check-p':(item.property+index) === state.attrDetail.key}]"
						v-for="(item,index) in propertyList" :key="index" @click="checkAttr(item,index)">
						<image
							:src="(item.property === state.attrDetail.property?state.flowIcon[0]:((index+1)%2!=0)?state.flowIcon[1]:state.flowIcon[2])"
							style="width:35rpx;height: 35rpx;"></image>
						<view class="info">
							<view class="value">
								{{item.value}}
							</view>
							<view class="name">
								{{item.propertyName}}{{item.unit?'('+item.unit+')':''}}
							</view>
						</view>
					</view>
				</view>
			</scroll-view>
			<view class="time">
				读取时间：{{proxy.formatTime(state.attrDetail.collectionTime)}}
			</view>
		</view>
		<view class="detail">
			<view class="flex-between" style="margin-bottom: 20rpx;">
				<view class="title-bold">
					{{state.attrDetail.label}}
				</view>
				<view class="tabs flex-between">
					<view v-for="(tab,i) in state.tabs" :key="i"
						:class="['tab',{'check-tab':state.activeTab.value===tab.value}]" @click="checkActiveTab(tab)">
						{{tab.title}}
					</view>

				</view>
			</view>
			<view class="bottom">
				<view class="y-title flex-between">
					<view>
						单位:{{state.attrDetail.unit}}
					</view>
					<view class="flex-center">
						<view class="flex-center" style="padding: 0 8rpx;" v-for="(tab,i) in state.tabList" :key="i">
							<view class="color-view" :style="{'background-color':tab.color}"></view>
							<view style="color:#B2B7C7;">{{tab.title}}</view>
						</view>
					</view>
				</view>
				<view class="line-ucharts">
					<view>
						<l-echart ref="lineChart"></l-echart>
					</view>
				</view>
			</view>
		</view>
		<view class="buttons flex-between">
			<u-button class="custom-style-left" @click="showHistoryData()">查看详情</u-button>
			<u-button class="custom-style-right" @click="showAlarmData()">报警详情</u-button>
		</view>
		<u-popup overlay mode="bottom" closeable v-model="showStatus" safeAreaInsetBottom border-radius="16"
			@close="showStatus=false">
			<view v-if="state.popupType === 'data'" class="popup">
				<data-list @onQuery="onQuery" :propertyList="propertyList" :currentProperty="state.attrDetail" title="查看详情"
					:headers="headers" :tableData="tableData"></data-list>
			</view>
			<view v-if="state.popupType === 'alarm'" class="popup">
				<alarm-list :stationId="state.currentProperty.stationId"></alarm-list>
			</view>
		</u-popup>
	</view>
</template>

<script lang="ts" setup>
	import {
		onMounted,
		reactive,
		ref,
		getCurrentInstance
	} from "vue";
	import dataList from '@/components/dataList/dataList.vue'
	import alarmList from '@/components/alarmList.vue'
	import {
		getThreeDaysData,
		getStationRealTimeDetail
	} from '@/common/api/monitoring'
	import {
		lineOption
	} from '@/common/data/echartsData'
	import {
		tabs,
	} from '@/common/data/publicData'
	import {
		removeSlash
	} from '@/common/utils/removeIdSlash'
	import * as echarts from 'echarts'
	const {
		proxy
	} = getCurrentInstance()
	const showStatus = ref < boolean > (false)
	const lineChart = ref < any > ({})
	const state = reactive < {
		activeTab: any,
		tabList: any,
		tabs: any,
		popupType: string,
		flowIcon: string[],
		currentProperty: any,
		attrDetail: any,
		today: any,
		yesterday: any,
		beforeYesterday: any,
		date: any,
	} > ({
		currentProperty: {},
		tabList: [tabs[1]],
		attrDetail: {},
		today: [],
		yesterday: [],
		beforeYesterday: [],
		date: [],
		flowIcon: [
			'/static/img/icons/check-flow.png',
			'/static/img/icons/left-flow.png',
			'/static/img/icons/right-flow.png',
		],
		activeTab: tabs[1],
		tabs: tabs,
		popupType: 'data'
	})

	const propertyList = ref < any > ([])
	const headers = ref < any > ()
	const tableData = ref < any > ([])

	//查询历史数据
	const onQuery = (query: any) => {
		//showHistoryData(query)
	}

	// 显示详情数据
	// const showHistoryData = async (params ? : any) => {
	// 	params = params || {}
	// 	const attributes = propertyList.value.map(property => {
	// 		return removeSlash(property.deviceId) + '.' + property.property
	// 	})
	// 	params = {
	// 		start: dayjs(params.start).startOf('day').valueOf() || dayjs().startOf('day').valueOf(),
	// 		end: dayjs(params.end).endOf('day').valueOf() || dayjs().endOf('day').valueOf(),
	// 		type: params.type || '15m',
	// 		attributes: attributes,
	// 	}
	// 	const res = await getDeviceData(params)
	// 	const data = res.data
	// 	let newData = []
	// 	console.log(data)
	// 	headers.value = [{
	// 		label: '读取时间',
	// 		key: 'date',
	// 		width: '300'
	// 	}].concat(propertyList.value.map(p => {
	// 		return {
	// 			label: p.propertyName + '（' + p.unit + '）',
	// 			key: removeSlash(p.deviceId) + '.' + p.property,
	// 			width: '300'
	// 		}
	// 	}))

	// 	for (let key in data) {
	// 		newData.push({
	// 			'date': key,
	// 			...data[key]
	// 		})
	// 	}
	// 	console.log('newData', newData)
	// 	tableData.value = newData
	// 	state.popupType = 'data'
	// 	showStatus.value = true
	// }

	// 显示详情数据
	const showHistoryData = async () => {
		state.popupType = 'data'
		showStatus.value = true
	}
	// 显示报警详情
	const showAlarmData = async () => {
		state.popupType = 'alarm'
		showStatus.value = true
	}

	// 选择属性
	const checkAttr = (item: any,index:number) => {
		state.activeTab = tabs[1]
		state.attrDetail = {
			...item,
			key: item.property+index,
			label: item.propertyName
		}
		getData()
	}

	//获取压力最近三天的数据
	const getData = async () => {
		const params = {
			attr: state.attrDetail.property,
			deviceId: removeSlash(state.attrDetail.deviceId)
		}
		const res = await getThreeDaysData(params)
		const data = res.data?.data
		state.date = data.todayDataList.map((data: any) => {
			return data.ts
		})
		state.today = data.todayDataList.map((data: any) => {
			return data.value
		})
		state.yesterday = data.yesterdayDataList.map((data: any) => {
			return data.value
		})
		state.beforeYesterday = data.beforeYesterdayDataList.map((data: any) => {
			return data.value
		})
		initLine(state.date, {
			'今天': state.today
		})
	}

	// 切换时间查询曲线
	const checkActiveTab = (tab: any) => {
		state.activeTab = tab
		if (state.activeTab.value === 'today') {
			initLine(state.date, {
				'今天': state.today
			}, state.activeTab.color)
			state.tabList = state.tabs.slice(1,2)
		} else if (state.activeTab.value === 'yesterday') {
			initLine(state.date, {
				'昨天': state.yesterday
			}, state.activeTab.color)
			state.tabList = state.tabs.slice(2,3)
		} else if (state.activeTab.value === 'beforeYesterday') {
			initLine(state.date, {
				'前天': state.beforeYesterday
			}, state.activeTab.color)
			state.tabList = state.tabs.slice(3,4)
		} else {
			initLine(state.date, {
				'今天': state.today,
				'昨天': state.yesterday,
				'前天': state.beforeYesterday
			})
			state.tabList = state.tabs.slice(1,4)
		}
	}

	const initLine = (dataX: string[], data: any, color ? : string) => {
		lineChart.value.init(echarts, (chart: any) => {
			const options = lineOption(dataX, data, color)
			chart.setOption(options);
		});
	}

	const stationAttrList = async () => {
		const res = await getStationRealTimeDetail(state.currentProperty.stationId)
		const attrList = res.data
		propertyList.value = attrList
		state.attrDetail = {
			...attrList[0] || {},
			key: attrList[0].property+0,
			label: attrList[0].propertyName || ''
		}
		getData()
	}

	onMounted(async () => {
		var pages = getCurrentPages();
		var page = pages[pages.length - 1];
		state.currentProperty = page.$page.options || {}
		await stationAttrList()
	})
</script>

<style lang="scss" scoped>
	.top {
		width: 100%;
		height: 560rpx;
		background: #151A2C;
		font-size: 28rpx;
		color: #FFFFFF;

		.title {
			padding: 22rpx 32rpx;

			text {
				padding-left: 12rpx;
			}
		}

		.property-list {
			height: 380rpx;
			margin: 0 auto;
			flex-wrap: wrap;
			padding: 0 32rpx;

			.property {
				margin-bottom: 20rpx;
				height: 112rpx;
				width: 332rpx;
				color: #A3A7B6;
				border-radius: 16rpx;
				background: rgba(255, 255, 255, 0.15);

				.info {
					width: 226rpx;
					text-align: left;

					.value {
						font-size: 32rpx;
						font-weight: 600;
					}
				}

			}

			.check-p {
				background: #3862F8;
				color: #FFFFFF;
			}
		}

		.time {
			text-align: center;
			color: #FFFFFF;
		}
	}

	.detail {
		position: relative;
		top: -40rpx;
		background: #FBFBFB;
		border-radius: 32rpx 32rpx 0px 0px;
		padding: 24rpx 32rpx;

		.tabs {
			width: 50%;
			height: 46rpx;
			line-height: 46rpx;
			text-align: center;

			.tab {
				width: 88rpx;
				color: #91949F;
			}

			.check-tab {
				width: 88rpx;
				border-radius: 8rpx;
				background-color: #3862F8;
				color: #FFFFFF;
			}
		}

		.bottom {
			background-color: #FFFFFF;
			border-radius: 16rpx;
			padding-bottom: 98rpx;

			.y-title {
				color: #B2B7C7;
				padding: 20rpx 40rpx;

				.color-view {
					width: 20rpx;
					height: 20rpx;
					background-color: #3862F8;
					margin-right: 8rpx;
				}
			}

			.line-ucharts {
				height: 38vh;
				margin: 10rpx auto;
			}
		}
	}

	.buttons {
		width: 100%;
		height: 128rpx;
		background: #FFFFFF;
		box-shadow: 0px 4rpx 30rpx rgba(178, 183, 199, 0.5);
		position: fixed;
		bottom: 0;

		.custom-style-left {
			background-color: #3862F8;
			color: #FFFFFF;
			width: 45%;
		}

		.custom-style-right {
			background-color: #F88938;
			color: #FFFFFF;
			width: 45%;
		}
	}

	.popup {
		height: 85vh;
	}
</style>
