.main{
	background: #F9F9F9;
	font-size: 24rpx;
	color: #000000;
	padding-bottom: 120rpx !important;
	height: 100vh;
}

.card-box {
	border-radius: 8px;
	min-height: 80rpx;
	padding: 20rpx 28rpx;
	margin: 0 auto 20rpx auto;
	background-color: #FFFFFF;
}

.div-radius{
	border-radius: 10rpx;
	width: 94%;
	font-size: 24rpx;
	background-color: #FFF;
}

.main-content{
	padding: 0 32rpx;
}
.content{
	height: 100%;
	width: 100%;
}

.button {
	width: 100%;
	z-index: 99;
	background-color: #FFFFFF;
	position: fixed;
	bottom: 0rpx;
	height: 120rpx;
	box-shadow: 0px 4rpx 30rpx rgba(178, 183, 199, 0.5);

	button {
		border-radius: 8rpx;
		width: 686rpx;
		margin: 20rpx auto;
	}
}
.popupButton{
	width: 94%;
	position: absolute;
	bottom: 40rpx;
	left: 0;
	right: 0;
	margin: 0 auto;
}
.uempty{
	margin-top: 30vh;
}
/* #ifdef APP-VUE || H5*/
.card{
	background-color: #FFFFFF;
	margin: 16rpx auto;
	padding-top: 24rpx;
	.title {
		margin-left: 44rpx;
		padding-left: 12rpx;
		font-weight: 700;
		font-size: 30rpx;
		color: #060F27;
		border-left: 4rpx solid #3862F8;
	}
}
.title-bold{
	font-weight: 700;
	font-size: 30rpx;
	color:#060F27;
}

/* #endif */
.flex{
	display: flex;
}
.flex-center{
	display: flex;
	align-items: center;
}
.flex-between{
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.flex-around{
	display: flex;
	justify-content: space-around;
	align-items: center;
}
.nv-right{
	color: #3862F8;
	font-size: 30rpx;
	padding-right: 28rpx;
}

.text-color-b{
	color: #3862F8;
}

.text-color-g{
	color: #91949F;
}

.text-color-o{
	color: #FBB934;
}

.u-td{
	height: 50rpx !important;
}
.line-charts{
	width: 100%;
	height: 600rpx;
}

::v-deep .placeholderClass{
	color: rgb(192, 196, 204);
	font-size: 0.875rem;
}

::v-deep input{
	font-size: 0.875rem !important;
}

.dw {
	display: flex;
	flex-direction: column;
	justify-content: center;
}
