/**
 * 数据库名称
 */
const dbName = 'meterReading'
/**
 * 数据库地址,推荐以下划线为开头   _doc/xxx.db
 */
const dbPath = '_downloads/meter_reading.db'
/**
 * 判断数据库是否打开
 */
const isOpen = () => {
	// 数据库打开了就返回 true,否则返回 false
	var open = plus.sqlite.isOpenDatabase({
		name: dbName,  // 数据库名称
		path: dbPath  // 数据库地址
	})
	return open;
}
/**
 * 创建数据库 或 有该数据库就打开
 */
const openSqlite = () => {
	return new Promise((resolve, reject) => {
		// 打开数据库
		plus.sqlite.openDatabase({
			name: dbName,
			path: dbPath,
			success(e) {
				console.log('ddd', e)
				resolve(e); // 成功回调
			},
			fail(e) {
				console.log('ccc', e)
				reject(e);  // 失败回调
			}
		})
	})
}
/**
 * 关闭数据库
 */
const closeSqlite = () => {
	return new Promise((resolve, reject) => {
		plus.sqlite.closeDatabase({
			name: dbName,
			success(e) {
				console.log('sqlLite数据库已关闭');
				resolve(e);
			},
			fail(e) {
				reject(e);
			}
		})
	})
}
/**
 * 数据库建表 sql:'CREATE TABLE IF NOT EXISTS dbTable("id" varchar(50),"name" TEXT) 
 * 
 * 创建 CREATE TABLE IF NOT EXISTS 、 dbTable 是表名，不能用数字开头、括号里是表格的表头
 */
const createTable = (dbTable : string, data : string) => {
	return new Promise((resolve, reject) => {
		// executeSql: 执行增删改等操作的SQL语句
		plus.sqlite.executeSql({
			name: dbName,
			sql: [`CREATE TABLE IF NOT EXISTS ${dbTable}(${data})`],
			success(e) {
				resolve(e);
			},
			fail(e) {
				reject(e);
			}
		})
	})
}
/**
 * 数据库删表 sql:'DROP TABLE dbTable'
 */
const dropTable = (dbTable : string) => {
	return new Promise((resolve, reject) => {
		plus.sqlite.executeSql({
			name: dbName,
			sql: [`DROP TABLE ${dbTable}`],
			success(e) {
				resolve(e);
				console.log(`已删除${dbTable}表`);
			},
			fail(e) {
				reject(e);
			}
		})
	})
}
/**
 * 往指定表格中添加一列
 * @params dbTable 表名
 * @params colNameAndType 添加的列名和类型 样例： 'Email Text'
 * return Promise<any>
 */
const addColumn = (dbTable : any, colNameAndType : string) => {
	return new Promise((resolve, reject) => {
		plus.sqlite.executeSql({
			name: dbName,
			sql: [`ALTER TABLE ${dbTable} ADD COLUMN ${colNameAndType}`],
			success(e) {
				resolve(e)
			},
			fail(e) {
				reject(e)
			}
		})
	})
}
/**
 * 向表格里添加数据 sql:'INSERT INTO dbTable VALUES('x','x','x')'   对应新增
 * 
 * 或者 sql:'INSERT INTO dbTable ('x','x','x') VALUES('x','x','x')'   具体新增
 * 
 * 插入 INSERT INTO  、 dbTable 是表名、根据表头列名插入列值
 */
const insertTableData = (dbTable : any, data : any, condition ?: any) => {
	// 判断有没有传参
	if (dbTable !== undefined && data !== undefined) {
		// 判断传的参是否有值
		var bol = (JSON.stringify(data) == "{}");
		if (!bol) {
			if (condition == undefined) {
				var sql = `INSERT INTO ${dbTable} VALUES('${data}')`;
			} else {
				var sql = `INSERT INTO ${dbTable} (${condition}) VALUES(${data})`;
			}
			// console.log(sql);
			return new Promise((resolve, reject) => {
				// 表格添加数据
				plus.sqlite.executeSql({
					name: dbName,
					sql: [sql],
					success(e) {
						resolve(e);
					},
					fail(e) {
						reject(e);
					}
				})
			})
		} else {
			return new Promise((resolve, reject) => { reject("错误添加") })
		}
	} else {
		return new Promise((resolve, reject) => { reject("错误添加") })
	}
}
/**
 * 插入数据
 * @params dbTable 表名
 * @params values [`(row1_val1,row1_val2)`,`(row2_val1,row2_val2)`]
 * @params fields [field1,field2]
 */
const insertRows = (dbTable : any, values : string[], fields ?: string[]) => {
	console.log(fields);
	console.log(values);
	return new Promise((resolve, reject) => {
		let sql = `INSERT INTO ${dbTable}`
		if (fields.length) {
			sql += `(${fields.join(',')})`
		}
		sql += ` VALUES${values.join(',')}`

		// 表格添加数据
		plus.sqlite.executeSql({
			name: dbName,
			sql: [sql],
			success(e) {
				resolve(e);
			},
			fail(e) {
				reject(e);
			}
		})
	})
}
/**
 * 根据条件向表格里添加数据  有数据更新、无数据插入
 * 
 * (建表时需要设置主键) 例如 --- "roomid" varchar(50) PRIMARY KEY
 */
const insertOrReplaceData = (dbTable : string, data : string, condition : string) => {
	// 判断有没有传参
	if (dbTable !== undefined && data !== undefined) {
		if (condition == undefined) {
			var sql = `INSERT OR REPLACE INTO ${dbTable} VALUES('${data}')`;
		} else {
			var sql = `INSERT OR REPLACE INTO ${dbTable} (${condition}) VALUES(${data})`;
		}
		console.log('install', sql);
		return new Promise((resolve, reject) => {
			// 表格添加数据
			plus.sqlite.executeSql({
				name: dbName,
				sql: [sql],
				success(e) {
					resolve(e);
				},
				fail(e) {
					reject(e);
				}
			})
		})
	} else {
		return new Promise((resolve, reject) => { reject("错误添加") })
	}
}
const selectTableData = (dbTable : string, queryData ?: any, orderKey ?: string, num ?: number) => {
	var sql = `SELECT * FROM ${dbTable} WHERE 1 = 1 `;
	if (queryData != undefined || queryData !== null)
		for (const key in queryData) {
			sql += `AND ${key} = '${queryData[key]}'`
		}

	if (dbTable !== undefined) {
		// 第一个是表单名称，后两个参数是列表名，用来检索
		if (orderKey !== undefined && orderKey !== null) {
			sql += ` ORDER BY ${orderKey} ASC`
		}
		if (num !== undefined && num !== null) {
			sql += ` LIMIT ${num} , 10`
		}
		return new Promise((resolve, reject) => {
			// 表格查询数据  执行查询的SQL语句
			plus.sqlite.selectSql({
				name: dbName,
				sql: sql,
				success(e) {
					resolve(e);
				},
				fail(e) {
					reject(e);
				}
			})
		})
	} else {
		return new Promise((resolve, reject) => { reject("错误查询") });
	}
}
/**
 * 分页查询
 */
const selectPage = (params : {
	/**
	 * 表名
	 */
	dbTable : string
	/**
	 * 查询条件
	 */
	where ?: string
	/**
	 * 排序： 'user ASC' 或 'user DESC'
	 */
	order ?: string
	/**
	 * 当前页,默认1
	 */
	page ?: number
	/**
	 * 每页条数，不传返回全部
	 */
	size ?: number
}) : Promise<{ data : any[], total : number }> => {
	return new Promise((resolve, reject) => {
		let sql = `SELECT * FROM ${params.dbTable} WHERE ${params.where}`
		if (params.order) {
			sql += ` ORDER BY ${params.order}`
		}
		if (params.size !== undefined) {
			sql += ` LIMIT ${params.size} OFFSET ${((params.page ?? 1) - 1) * params.size}`
		}
		console.log('sql', sql)
		selectCount(params.dbTable, params.where, 'sid').then((res : { num : number }[]) => {
			const count = res[0]?.num
			console.log('getPipePageRes', count);
			if (!res[0]?.num) {
				resolve({
					data: [],
					total: 0
				})
			} else {

				// 表格查询数据  执行查询的SQL语句
				plus.sqlite.selectSql({
					name: dbName,
					sql: sql,
					success(e) {
						console.log(e);
						resolve({
							data: e,
							total: count
						});
					},
					fail(e) {
						reject(e);
					}
				})
			}
		}).catch(e => {
			reject(e)
		})

	})
}
// 删除表里的数据 sql:'DELETE FROM dbTable WHERE lname = 'lvalue''
// 删除 DELETE FROM 、 dbTable 是表名、 WHERE 查找条件 lname,lvalue 是查询条件的列名和列值
const deleteTableData = (dbTable : string, lname ?: string, lvalue ?: string, ww ?: string, ee ?: string) => {
	if (dbTable !== undefined) {
		if (lname == undefined) {
			var sql = `DELETE FROM ${dbTable}`;
		} else {
			if (ww !== undefined) {
				// 两个检索条件
				var sql = `DELETE FROM ${dbTable} WHERE ${lname} = '${lvalue}' AND ${ww} = '${ee}'`;
			} else {
				// 一个检索条件
				var sql = `DELETE FROM ${dbTable} WHERE ${lname} = '${lvalue}'`;
			}
		}
		console.log(sql)
		return new Promise((resolve, reject) => {
			// 删除表数据
			plus.sqlite.executeSql({
				name: dbName,
				sql: [sql],
				success(e) {
					resolve(e);
				},
				fail(e) {
					reject(e);
				}
			})
		})
	} else {
		return new Promise((resolve, reject) => { reject("错误删除") });
	}
}
/**
 * 删除条件行
 * @params dbTable 表名
 * @params where 查询条件
 */
const deleteRows = (dbTable : string, where : string) => {
	return new Promise((resolve, reject) => {
		if (dbTable === undefined) {
			reject(new Error('删除失败'))
		} else {

			const sql = `DELETE FROM ${dbTable} WHERE ${where}`;
			console.log(sql)
			// 删除表数据
			plus.sqlite.executeSql({
				name: dbName,
				sql: [sql],
				success(e) {
					resolve(e);
				},
				fail(e) {
					reject(e);
				}
			})
		}
	})
}
// 修改数据表里的数据 sql:"UPDATE dbTable SET 列名 = '列值',列名 = '列值' WHERE lname = 'lvalue'"
// 修改 UPDATE 、 dbTable 是表名, data: 要修改的列名=修改后列值, lname,lvalue 是查询条件的列名和列值
const updateTableData = (dbTable : string, data : string, lname : string, lvalue : string) => {
	if (lname == undefined) {
		var sql = `UPDATE ${dbTable} SET ${data}`;
	} else {
		var sql = `UPDATE ${dbTable} SET ${data} WHERE ${lname} = '${lvalue}'`;
	}
	console.log(sql)
	// WHERE 前面是要修改的列名、列值，后面是条件的列名、列值
	return new Promise((resolve, reject) => {
		// 修改表数据
		plus.sqlite.executeSql({
			name: dbName,
			sql: [sql],
			success(e) {
				resolve(e);
			},
			fail(e) {
				reject(e);
			}
		})
	})
}
/**
 * 更新表格
 * @params dbTable 表名
 * @params sets 更新字段，多个字段用','分隔：`age = 10, user = '张三'`
 * @params where 查询条件
 * @returns Promise<any>
 */
const updateRows = (dbTable : string, sets : string, where : string) => {

	// WHERE 前面是要修改的列名、列值，后面是条件的列名、列值
	return new Promise((resolve, reject) => {
		const sql = `UPDATE ${dbTable} SET ${sets} WHERE ${where}`;
		console.log(sql);
		// 修改表数据
		plus.sqlite.executeSql({
			name: dbName,
			sql: [sql],
			success(e) {
				resolve(e);
			},
			fail(e) {
				reject(e);
			}
		})
	})
}
const selectCountData = (dbTable : string, lname ?: string, lvalue ?: string, cc ?: string, dd ?: string, ee ?: string, ff ?: string) => {
	var sql = ''
	if (dbTable !== undefined) {
		// 第一个是表单名称，后两个参数是列表名，用来检索
		if (lname !== undefined && cc !== undefined) {
			// 两个检索条件
			sql = `SELECT count(id) as num FROM ${dbTable} WHERE ${lname} = '${lvalue}' AND ${cc} = '${dd}'`;
		}
		if (lname !== undefined && cc !== undefined && ee != undefined) {
			// 两个检索条件
			sql = `SELECT count(id) as num FROM ${dbTable} WHERE ${lname} = '${lvalue}' AND (${cc} = '${dd}' OR ${ee} = '${ff}')`;
		}
		if (lname !== undefined && cc == undefined) {
			// 一个检索条件
			sql = `SELECT count(id) as num FROM ${dbTable} WHERE ${lname} = '${lvalue}'`;
			// console.log(sql);
		}
		if (lname == undefined) {
			sql = `SELECT count(id) as num FROM ${dbTable}`;
		}
		console.log('sql-count', sql)
		return new Promise((resolve, reject) => {
			// 表格查询数据  执行查询的SQL语句
			plus.sqlite.selectSql({
				name: dbName,
				sql: sql,
				success(e) {
					resolve(e);
				},
				fail(e) {
					reject(e);
				}
			})
		})
	} else {
		return new Promise((resolve, reject) => { reject("错误查询") });
	}
}
const selectCount = (dbTable : string, where : string = '1=1', countField = 'id') => {
	return new Promise((resolve, reject) => {
		const sql = `SELECT count(${countField}) as num FROM ${dbTable} WHERE ${where}`
		console.log(sql);
		// 表格查询数据  执行查询的SQL语句
		plus.sqlite.selectSql({
			name: dbName,
			sql: sql,
			success(e) {
				resolve(e);
			},
			fail(e) {
				reject(e);
			}
		})
	})
}
// 获取指定数据条数  sql:"SELECT * FROM dbTable ORDER BY 'id' DESC LIMIT 15 OFFSET 'num'"
// dbTable 表名, ORDER BY 代表排序默认正序, id 是排序的条件 DESC 代表倒序，从最后一条数据开始拿
// LIMIT 15 OFFSET '${num}',这句的意思是跳过 num 条拿 15 条数据, num 为跳过多少条数据是动态值
// 例 初始num设为0，就从最后的数据开始拿15条，下次不拿刚获取的数据，所以可以让num为15，这样就能一步一步的拿完所有的数据
const pullSQL = (dbTable, id, num) => {
	return new Promise((resolve, reject) => {
		plus.sqlite.selectSql({
			name: dbName,
			sql: `SELECT * FROM ${dbTable} ORDER BY '${id}' DESC LIMIT 15 OFFSET '${num}'`,
			success(e) {
				resolve(e);
			},
			fail(e) {
				reject(e);
			}
		})
	})
}
const transaction = (operation) => {
	return new Promise((resolve, reject) => {
		var options = {
			name: dbName,
			operation: operation,
			success(e) {
				resolve(e); // 成功回调
			},
			fail(e) {
				reject(e); // 失败回调
			}
		}
		plus.sqlite.transaction(options);
	})
}
const transactionBegin = () => transaction("begin")
/**
 * 事务提交
 */
const transactionCommit = () => transaction("commit")
/**
 * 事务回滚
 */
const transactionRollback = () => transaction("rollback")
const SQLLITE = {
	dbName, // 数据库名称
	dbPath, // 数据库地址,推荐以下划线为开头   _doc/xxx.db

	// 判断数据库是否打开
	isOpen,

	// 创建数据库 或 有该数据库就打开
	openSqlite,

	// 关闭数据库
	closeSqlite,

	// 数据库建表 sql:'CREATE TABLE IF NOT EXISTS dbTable("id" varchar(50),"name" TEXT) 
	// 创建 CREATE TABLE IF NOT EXISTS 、 dbTable 是表名，不能用数字开头、括号里是表格的表头
	createTable,

	// 数据库删表 sql:'DROP TABLE dbTable'
	dropTable,

	// 向表格里添加数据 sql:'INSERT INTO dbTable VALUES('x','x','x')'   对应新增
	// 或者 sql:'INSERT INTO dbTable ('x','x','x') VALUES('x','x','x')'   具体新增
	// 插入 INSERT INTO  、 dbTable 是表名、根据表头列名插入列值
	insertTableData,
	/**
	 * 插入数据
	 * @params dbTable 表名
	 * @params values [(row1_val1,row1_val2),(row2_val1,row2_val2)]
	 * @params fields [field1,field2]
	 */
	insertRows,
	// 根据条件向表格里添加数据  有数据更新、无数据插入
	// (建表时需要设置主键) 例如 --- "roomid" varchar(50) PRIMARY KEY
	insertOrReplaceData,
	// 查询获取数据库里的数据 sql:'SELECT * FROM dbTable WHERE lname = 'lvalue''
	// 查询 SELECT * FROM 、 dbTable 是表名、 WHERE 查找条件 lname,lvalue 是查询条件的列名和列值
	selectTableData,
	selectPage,
	// 删除表里的数据 sql:'DELETE FROM dbTable WHERE lname = 'lvalue''
	// 删除 DELETE FROM 、 dbTable 是表名、 WHERE 查找条件 lname,lvalue 是查询条件的列名和列值
	deleteTableData,
	/**
	 * 删除条件行
	 * @params dbTable 表名
	 * @params where 查询条件
	 */
	deleteRows,
	/**
	 * 往指定表格中添加一列
	 * @params dbTable 表名
	 * @params colNameAndType 添加的列名和类型 样例： 'Email Text'
	 * return Promise<any>
	 */
	addColumn,
	// 修改数据表里的数据 sql:"UPDATE dbTable SET 列名 = '列值',列名 = '列值' WHERE lname = 'lvalue'"
	// 修改 UPDATE 、 dbTable 是表名, data: 要修改的列名=修改后列值, lname,lvalue 是查询条件的列名和列值
	updateTableData,
	/**
	 * 更新表格
	 * @params dbTable 表名
	 * @params sets 更新字段，多个字段用','分隔：`age = 10, user = '张三'`
	 * @params where 查询条件
	 * @returns Promise<any>
	 */
	updateRows,
	selectCountData,
	selectCount,
	// 获取指定数据条数  sql:"SELECT * FROM dbTable ORDER BY 'id' DESC LIMIT 15 OFFSET 'num'"
	// dbTable 表名, ORDER BY 代表排序默认正序, id 是排序的条件 DESC 代表倒序，从最后一条数据开始拿
	// LIMIT 15 OFFSET '${num}',这句的意思是跳过 num 条拿 15 条数据, num 为跳过多少条数据是动态值
	// 例 初始num设为0，就从最后的数据开始拿15条，下次不拿刚获取的数据，所以可以让num为15，这样就能一步一步的拿完所有的数据
	pullSQL,
	transaction,
	transactionBegin,
	/**
	 * 事务提交
	 */
	transactionCommit,
	/**
	 * 事务回滚
	 */
	transactionRollback,
}
export default SQLLITE;