<template>
  <view class="cover-view-plus">
    <block v-for="(menu, index) in menus" :key="index">
      <view class="plus-btn" @click="$emit('menuClick', menu.value)">
        <text 
          class="custom-icon btn-img" 
          :class="menu.icon"
          :style="{ color: currentBar === menu.value ? '#3862F8' : '#060F27' }"
        />
        <text 
          class="menu-text"
          :style="{ color: currentBar === menu.value ? '#3862F8' : '#060F27' }"
        >
          {{ menu.name }}
        </text>
      </view>
    </block>
    
    <view class="btn-line" />
    
    <view class="plus-btn" @click="$emit('clearMap')">
      <text class="custom-icon btn-img custom-icon-qingkong" />
      <text class="menu-text">清空</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'VerticalMenuBar',
  props: {
    menus: {
      type: Array,
      default: () => []
    },
    currentBar: {
      type: String,
      default: ''
    }
  },
  emits: ['menuClick', 'clearMap']
}
</script>

<style scoped lang="scss">
.cover-view-plus {
  width: 80rpx;
  background-color: #ffffff;
  border-radius: 20rpx;
  position: absolute;
  top: 62rpx;
  right: 32rpx;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  padding: 8rpx 16rpx;

  .plus-btn {
    width: 48rpx;
    height: 96rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .btn-img {
      font-size: 48rpx;
    }

    .menu-text {
      font-size: 24rpx;
    }
  }

  .btn-line {
    width: 80rpx;
    height: 1rpx;
    border-width: 1rpx;
    border-style: solid;
    border-color: #EBEDF6;
  }
}
</style>