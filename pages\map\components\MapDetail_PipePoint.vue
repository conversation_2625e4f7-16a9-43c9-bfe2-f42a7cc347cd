<template>
	<view class="pipe-detail">
		<view class="pipe-detail-item pipe-detail-total">

			<view class="title">
				{{props.menu.name}}统计
			</view>
			<view class="total-blocks">
				<view class="total-block" v-for="(item, i) in state.total" :key="i">
					<view class="value">{{item.value}} {{item.unit}}</view>
					<view class="text">{{item.title}}</view>
				</view>
			</view>
		</view>
		<scroll-view scroll-y style="height: 70vh;">
		<view v-if="props.staticItems?.ratioField" class="pipe-detail-item pipe-detail-chart">
			<view class="title">{{props.menu.name}}{{props.staticItems.ratioFieldName}}占比统计</view>
			<view class="chart-box">
				<l-echart ref="refLEchart_Status_Ratio"></l-echart>
			</view>
		</view>
		<view v-if="props.staticItems?.countByDiameter" class="pipe-detail-item pipe-detail-chart">
			<view class="title">{{props.menu.name}}口径统计</view>
			<view class="chart-box">
				<l-echart ref="refLEchart_Diameter_Count"></l-echart>
			</view>
		</view>
		<view v-if="props.staticItems?.countByType" class="pipe-detail-item pipe-detail-chart">
			<view class="title">{{props.menu.name}}类型统计</view>
			<view class="chart-box">
				<l-echart ref="refLEchart_Type_Count"></l-echart>
			</view>
		</view>
		<view v-if="props.staticItems?.countByManager" class="pipe-detail-item pipe-detail-chart">
			<view class="title">{{props.menu.name}}管理单位统计</view>
			<view class="chart-box">
				<l-echart ref="refLEchart_Manager_Count"></l-echart>
			</view>
		</view>
		<view v-if="props.staticItems?.countByRoad" class="pipe-detail-item pipe-detail-chart">
			<view class="title">{{props.menu.name}}所在道路统计</view>
			<TableList :list="state.roadList"></TableList>
		</view>
		<view v-if="props.staticItems?.countByManufacturer" class="pipe-detail-item pipe-detail-chart">
			<view class="title">{{props.menu.name}}厂家统计</view>
			<TableList :list="state.manufacturerList"></TableList>
		</view>
		</scroll-view>
	</view>

</template>

<script lang="ts" setup>
	import * as echarts from 'echarts'
	import { PipeStatistics } from '@/common/api/map'
	import lEchart from '@/uni_modules/lime-echart/components/l-echart/l-echart.vue'
	import TableList from './TableList.vue'
	import { onMounted, reactive, ref } from 'vue'
	const props = defineProps<{
		layerids ?: number[]
		menu ?: {
			name : string,
			alias ?: string; icon : string; id : string; type : string; isActive ?: boolean; color ?: string
		},
		staticItems ?: {
			countByType ?: boolean
			ratioField ?: string
			ratioFieldName ?: string
			ratioPrefix ?: string
			countByRoad ?: boolean
			countByDiameter ?: boolean
			countByManufacturer ?: boolean
			countByManager ?: boolean
		},
	}>()
	const refLEchart_Status_Ratio = ref<InstanceType<typeof lEchart>>()
	const refLEchart_Diameter_Count = ref<InstanceType<typeof lEchart>>()
	const refLEchart_Type_Count = ref<InstanceType<typeof lEchart>>()
	const refLEchart_Manager_Count = ref<InstanceType<typeof lEchart>>()

	const state = reactive<{
		total : { title : string; value : string; unit : string }[]
		roadList : { name : string; value : string; }[]
		manufacturerList : { name : string; value : string; }[]
		managerList : { name : string; value : string; }[]
	}>({
		total: [{ title: props.menu.name + '总数', value: '0', unit: '个' }],
		roadList: [],
		manufacturerList: [],
		managerList: []
	})
	const refreshTotalCount = async () => {
		try {
			const res = await PipeStatistics({
				layerids: JSON.stringify(props.layerids),
				group_fields: JSON.stringify([]),
				statistic_field: 'OBJECTID',
				statistic_type: '1',
				where: '1=1'
			})
			if (res.data.code === 10000) {
				const data = res.data?.result?.rows[0]?.rows || []
				let totalCount = 0
				data.map((item : any) => {
					totalCount += item.OBJECTID
				})
				state.total[0].value = totalCount.toFixed(0)
			} else {
				console.log('统计失败');
			}
		} catch (e) {
			console.log(e)
		}
	}

	const refreshStatus = async () => {
		try {
			const res = await PipeStatistics({
				layerids: JSON.stringify((props.layerids)),
				group_fields: JSON.stringify([props.staticItems.ratioField]),
				statistic_field: 'OBJECTID',
				statistic_type: '1',
				where: '1=1'
			})

			if (res.data.code === 10000) {
				const data = res.data?.result?.rows[0]?.rows || []
				const optionData : any[] = []
				data.map((item : any) => {
					optionData.push({ name: `${props.staticItems.ratioPrefix ?? ''}${item[props.staticItems.ratioField] ?? '其它'}`, value: item['OBJECTID'].toFixed(0) })
				})
				refLEchart_Status_Ratio.value.init(echarts, (chart : any) => {
					const option = {
						tooltip: {
							trigger: 'item'
						},
						title: {
							text:
								'{name|'
								+ '合计'
								+ '(个)'
								+ '}\n{val|'
								+ (state.total[0].value)
								+ '}',
							top: 'center',
							left: '33%',
							textAlign: 'center',
							textStyle: {
								rich: {
									name: {
										fontSize: 10,
										fontWeight: 'normal',
										padding: [8, 0],
										align: 'center',
										color: '#2A2A2A'
									},
									val: {
										fontSize: 16,
										fontWeight: 'bold',
										color: '#2A2A2A'
									}
								}
							}
						},
						legend: {
							// selectedMode: false, // 取消图例上的点击事件
							type: 'scroll',
							icon: 'circle',
							orient: 'vertical',
							left: 'right',
							top: 'center',
							align: 'left',
							itemGap: 10,
							itemWidth: 10, // 设置宽度
							itemHeight: 10, // 设置高度
							symbolKeepAspect: true,
							textStyle: {
								color: '#fff',
								rich: {
									name: {
										align: 'left',
										width: 50,
										fontSize: 12,
										color: '#2A2A2A'
									},
									value: {
										align: 'left',
										width: 30,
										fontSize: 12,
										color: '#00ff00'
									},
									unit: {
										align: 'left',
										width: 30,
										fontSize: 12,
										color: '#00ff00'
									}
								}
							},
							data: optionData.map(item => item.name),
							formatter(name : any) {
								if (optionData && optionData.length) {
									for (let i = 0; i < optionData.length; i++) {
										if (name === optionData[i].name) {
											return (
												'{name| '
												+ (optionData[i].name || name)
												+ '}'
												+ '{value| '
												+ (optionData[i].valueAlias || optionData[i].value)
												+ ' '
												+ '}'
												+ '{unit| '
												+ (optionData[i].unit || '个')
												+ '}'
											)
										}
									}
								}
							}
						},
						series: [
							{
								name: props.menu.name,
								type: 'pie',
								radius: ['50%', '80%'],
								center: ['33%', '50%'],
								data: optionData,
								// emphasis: {
								// },
								itemStyle: {
									shadowBlur: 10,
									shadowOffsetX: 0,
									shadowColor: 'rgba(0, 0, 0, 0.5)'
								},
								label: {
									show: true,
									position: 'inside',
									textStyle: {
										fontSize: 12
									},
									formatter(param : any) {
										// correct the percentage
										return param.percent + '%';
									}
								},

								labelLine: {
									show: false
								},
							}
						]
					}
					chart.setOption(option)
				})

			} else {
				console.log('统计失败');
			}
		} catch (e) {
			console.log(e)
		}
	}

	const refreshBarChart = async (chartIns ?: any, groupField ?: string, color ?: string, prefix ?: string) => {
		const xData : string[] = []
		const yData : string[] = []
		try {
			if (!groupField || !chartIns) return
			const res = await PipeStatistics({
				layerids: JSON.stringify((props.layerids)),
				group_fields: JSON.stringify([groupField]),
				statistic_field: 'OBJECTID',
				statistic_type: '1',
				where: '1=1'
			})

			if (res.data.code === 10000) {
				const data = res.data?.result?.rows[0]?.rows || []
				data.map((item : any) => {
					xData.push(`${prefix ?? ''}${item[groupField] ?? '其它'}`)
					yData.push(item['OBJECTID'].toFixed(0))
				})

			} else {
				console.log('统计失败');
			}
		} catch (e) {
			console.log(e)
		}

		chartIns?.init(echarts, (chart : any) => {
			const option = {
				tooltip: {
					trigger: 'axis',
					axisPointer: {
						type: 'cross',
						label: {
							backgroundColor: '#283b56'
						}
					}
				},
				xAxis: [
					{
						type: 'category',
						boundaryGap: true,
						data: xData
					},
				],
				yAxis: [
					{
						type: 'value',
						name: 'km',
						min: 0,
						boundaryGap: [0.2, 0.2]
					},
				],
				series: [
					{
						name: props.menu.name,
						type: 'bar',
						data: yData,
						itemStyle: {
							color: color || '#0064df'
						}
					},
				]
			}
			chart.setOption(option)
		})

	}
	const refreshTable = async (oriArr : any[], groupField ?: string) => {
		try {
			if (!groupField || !oriArr) return
			const res = await PipeStatistics({
				layerids: JSON.stringify((props.layerids)),
				group_fields: JSON.stringify([groupField]),
				statistic_field: 'OBJECTID',
				statistic_type: '1',
				where: '1=1'
			})

			if (res.data.code === 10000) {
				oriArr.splice(0, oriArr.length)
				const data = res.data?.result?.rows[0]?.rows || []
				const other = { name: '其它', value: 0 }
				data.map((item : any) => {
					const name = item[groupField] ?? '其它'
					name !== '其它' && oriArr.push({ name, value: item['OBJECTID'] })
					name === '其它' && (other.value += item['OBJECTID'])
				})
				other.value !== 0 && oriArr.push(other)
			} else {
				console.log('统计失败');
			}
		} catch (e) {
			console.log(e)
		}
	}
	const refreshData = async () => {
		refreshTotalCount()
		if (!props.staticItems) return
		props.staticItems.ratioField && refreshStatus()
		props.staticItems.countByType && refreshBarChart(refLEchart_Type_Count.value, 'SUBTYPE')
		props.staticItems.countByDiameter && refreshBarChart(refLEchart_Diameter_Count.value, 'DIAMETER', '#35ac46', 'DN')
		props.staticItems.countByManager && refreshBarChart(refLEchart_Manager_Count.value, 'MAINTAINEDUNIT')

		props.staticItems.countByRoad && refreshTable(state.roadList, 'LANEWAY')
		props.staticItems.countByManufacturer && refreshTable(state.manufacturerList, 'MANUFACTURER')
	}
	onMounted(() => {

		refreshData()
	})
</script>

<style lang="scss" scoped>
	.pipe-detail {
		padding-bottom: 140rpx;

		.pipe-detail-item {
			.title {
				font-size: 32rpx;
				margin: 32rpx 0 0;
			}
		}

		.pipe-detail-total {
			.total-blocks {
				width: 100%;
				display: flex;
				padding: 24rpx;
				justify-content: flex-start;

				.total-block {
					background-color: #0073ff;
					padding: 10rpx;
					color: #fff;
					width: 280rpx;
					height: 120rpx;
					margin-right: 24rpx;
					text-align: center;

					.text,
					.value {
						line-height: 50rpx;
					}
				}
			}
		}

		.pipe-detail-chart {
			.chart-box {
				height: 480rpx;
			}
		}

	}
</style>