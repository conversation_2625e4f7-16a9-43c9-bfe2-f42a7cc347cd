<template>
  <view class="main">
    <u-sticky>
      <u-tabs
        :list="state.tabList"
        font-size="30"
        bold
        bar-width="160"
        :is-scroll="false"
        v-model="state.currentTabNum"
        :offset="[0, 140]"
        @change="chooseTab"
      ></u-tabs>
    </u-sticky>
    <view class="card-list" v-if="state.currentTab.value === 'todo'">
      <view
        class="item flex-between"
        @click="toInspectionTask"
        v-if="state.allMenus.indexOf('巡检任务') !== -1"
      >
        <view class="left flex-center">
          <u-icon
            size="64"
            name="custom-icon-xunjianxinxi-"
            color="rgb(70, 216, 171)"
            customPrefix="custom-icon"
          ></u-icon>
          <text>巡检任务</text>
        </view>
        <view class="right">
          <text>{{ countData.smCircuitTaskCount || "" }}</text>
          <u-icon name="arrow-right"></u-icon>
        </view>
      </view>
      <view
        class="item flex-between"
        @click="toMaintenanceTasks"
        v-if="state.allMenus.indexOf('养护任务') !== -1"
      >
        <view class="left flex-center">
          <u-icon
            size="64"
            name="custom-icon-weixiu"
            color="rgb(84, 32, 242)"
            customPrefix="custom-icon"
          >
          </u-icon>
          <text>养护任务</text>
        </view>
        <view class="right">
          <text>{{ countData.smMaintainTaskCount || "" }}</text>
          <u-icon name="arrow-right"></u-icon>
        </view>
      </view>
      <view
        class="item flex-between"
        @click="toAssetMaintainTask"
        v-if="state.allMenus.indexOf('资产保养') !== -1"
      >
        <view class="left flex-center">
          <u-icon
            size="64"
            name="custom-icon-xunjianxinxi-"
            color="rgb(70, 216, 171)"
            customPrefix="custom-icon"
          ></u-icon>
          <text>资产保养</text>
        </view>
        <view class="right">
          <text>{{ countData.maintainTaskCount || "" }}</text>
          <u-icon name="arrow-right"></u-icon>
        </view>
      </view>
      <view
        class="item flex-between"
        @click="toAssetCircuitInspectionTask"
        v-if="state.allMenus.indexOf('资产巡检') !== -1"
      >
        <view class="left flex-center">
          <u-icon
            size="64"
            name="custom-icon-xunjianxinxi-"
            color="rgb(70, 216, 171)"
            customPrefix="custom-icon"
          ></u-icon>
          <text>资产巡检</text>
        </view>
        <view class="right">
          <text>{{ countData.circuitTaskCount || "" }}</text>
          <u-icon name="arrow-right"></u-icon>
        </view>
      </view>

      <view
        class="item flex-between"
        @click="toWorkOrder"
        v-if="state.allMenus.indexOf('工单处理') !== -1"
      >
        <view class="left flex-center">
          <u-icon
            size="64"
            name="custom-icon-gongdan2"
            color="rgb(131, 155, 251)"
            customPrefix="custom-icon"
          >
          </u-icon>
          <text>工单任务</text>
        </view>
        <view class="right">
          <text>{{ countData.workOrderCount || "" }}</text>
          <u-icon name="arrow-right"></u-icon>
        </view>
      </view>

      <view
        class="item flex-between"
        @click="toSecondInspection"
        v-if="state.allMenus.indexOf('二供巡检') !== -1"
      >
        <view class="left flex-center">
          <u-icon
            size="64"
            name="custom-icon-xunjianguanli1"
            color="rgb(63, 169, 245)"
            customPrefix="custom-icon"
          ></u-icon>
          <text>二供巡检</text>
        </view>
        <view class="right">
          <text>{{ countData.circuitTaskCount || "" }}</text>
          <u-icon name="arrow-right"></u-icon>
        </view>
      </view>
      <!-- <view class="item flex-between" @click="toInstall" v-if="state.allMenus.indexOf('报装处理')!==-1">
				<view class="left flex-center">
					<u-icon size='64' name="custom-icon-gongdan2" color="rgb(63, 169, 245)" customPrefix="custom-icon">
					</u-icon>
					<text>报装任务</text>
				</view>
				<view class="right">
					<text>{{countData.installCount || ''}}</text>
					<u-icon name="arrow-right"></u-icon>
				</view>
			</view> -->
      <view
        class="item flex-between"
        @click="toRepair"
        v-if="state.allMenus.indexOf('报修处理') !== -1"
      >
        <view class="left flex-center">
          <u-icon
            size="64"
            name="custom-icon-weixiu"
            color="rgb(63, 169, 245)"
            customPrefix="custom-icon"
          >
          </u-icon>
          <text>报修任务</text>
        </view>
        <view class="right">
          <text>{{ countData.repairCount || "" }}</text>
          <u-icon name="arrow-right"></u-icon>
        </view>
      </view>
    </view>

    <view class="card-list" v-if="state.currentTab.value === 'warning'">
      <scroll-view
        :lower-threshold="50"
        :refresher-threshold="50"
        scroll-y
        refresher-default-style="white"
        :refresher-triggered="triggered"
        style="height: 95vh"
      >
        <view
          class="warning-item"
          v-for="(alarm, index) in tableData"
          :key="index"
          @click="toExceptionalManage"
        >
          <view class="top flex-center">
            <view class="bg-box">
              <u-icon size="15" name="bell" color="#FFFFFF"></u-icon>
            </view>
            <text>{{ proxy.formatTime(alarm.time) || "-" }}</text>
          </view>
          <view class="content">
            {{ alarm.title }}
          </view>
        </view>
        <u-loadmore
          bg-color="F9F9F9"
          :status="state.status"
          loading-text="努力加载中"
          loadmore-text="加载更多"
          nomore-text="没有了更多"
        />
      </scroll-view>
    </view>
    <block v-if="proxy.$isHideTabBar">
      <u-tabbar v-model="current" :list="bottomMenu"></u-tabbar>
    </block>
  </view>
</template>

<script lang="ts" setup>
import {
  findMenuByTenantApplication,
  getTenantApplication,
} from "@/common/api/system";
import { removeSlash } from "@/common/utils/removeIdSlash";

import { reactive, getCurrentInstance, ref, onBeforeMount } from "vue";
import { onShow } from "@dcloudio/uni-app";
import { getProjectRoot } from "@/common/api/monitoring";
import { getOwnAlarmListV2 } from "@/common/api/alarmManage";
import { getTodoCount } from "@/common/api/workOrder";
const { proxy } = getCurrentInstance();
const bottomMenu = ref<any>([]);
const current = ref<number>(2);
const applictionList = ref<any>([]);
const userInfo = ref<any>({});
const state = reactive<{
  currentProjectId: string;
  tabList: any;
  currentTabNum: number;
  currentTab: any;
  status: string;
  barHeight: number;
  page: number;
  cells: any;
  allMenus: anyp[];
}>({
  currentProjectId: "",
  tabList: [
    {
      name: "待办",
      value: "todo",
      count: 0,
    },
    {
      name: "报警",
      value: "warning",
      count: 0,
    },
  ],
  currentTabNum: 0,
  currentTab: {
    name: "待办",
    value: "todo",
    count: 0,
  },
  status: "loading",
  page: 1,
  barHeight: 60,
  cells: [
    "巡检任务",
    "资产保养",
    "资产巡检",
    "工单处理",
    "养护任务",
    "二供巡检",
  ],
  allMenus: [],
});
const tableData = ref<any>([]);
const countData = ref<any>({});
let info = uni.getSystemInfoSync();
//顶部高度
state.barHeight = info.statusBarHeight + 20;
const triggered = ref<Boolean>(false);
// 选择标签
const chooseTab = (index: number) => {
  state.currentTab = state.tabList[index];
  if (state.currentTab.value === "warning") {
    refreshData();
  }
};

//
const refreshData = async () => {
  const params = {
    size: 10,
    page: state.page || 1,
    processStatus: 1,
    alarmStatus: 1,
  };
  const res = await getOwnAlarmListV2(params);
  const data = res.data?.data?.data || [];
  const total = res.data?.total || 0;
  state.tabList[1].count = res.data?.total || 0;
  if (data.length > 0) {
    if (state.page === 1) {
      tableData.value = data;
    } else {
      tableData.value = tableData.value.concat(data);
    }
    state.page += 1;
    state.status = "loadmore";
    if (data.length === total) {
      state.status = "nomore";
    }
  } else {
    state.status = "nomore";
  }
  triggered.value = false;
};

// const getInstallCount = async () => {
// 	const res = await getIList({
// 		page: 1,
// 		size: 10,
// 		status: '0,1'
// 	})
// 	return res.data.data.total || 0
// }

// const getRepairCount = async () => {
// 	const res = await getRList({
// 		page: 1,
// 		size: 10,
// 		status: '0,1'
// 	})
// 	return res.data.data.total || 0
// }

// 加载更多
const showMoreData = async () => {
  state.status = "loading";
  await refreshData();
};

// 下拉刷新
const onRefresh = async () => {
  triggered.value = true;
  await refreshData();
  triggered.value = false;
};

//前往巡检任务列表
const toInspectionTask = () => {
  uni.$u.route({
    url: "pages/pipeNetwork/inspectionTask/index",
  });
};
//前往养护任务列表
const toMaintenanceTasks = () => {
  uni.$u.route({
    url: "pages/pipeNetwork/maintenanceTasks/index",
  });
};
//前往工单任务列表
const toWorkOrder = () => {
  uni.$u.route({
    url: "pages/smartOperation/workOrderTask/index",
  });
};
//二供巡检列表
const toSecondInspection = () => {
  uni.$u.route({
    url: "pages/wisdomProduce/secondSupplyInspection/index",
  });
};
//资产保养列表
const toAssetMaintainTask = () => {
  uni.$u.route({
    url: "pages/smartOperation/assetMaintainTask/index",
  });
};
//资产巡检列表
const toAssetCircuitInspectionTask = () => {
  uni.$u.route({
    url: "pages/smartOperation/assetCircuitInspectionTask/index",
  });
};
//报警信息列表
const toExceptionalManage = () => {
  uni.$u.route({
    url: "pages/wisdomProduce/exceptionalManage/index",
  });
};

//报修
const toRepair = () => {
  uni.$u.route({
    url: "pages/smartOperation/quannanInstall/repairTask/index",
  });
};
//报装
const toInstall = () => {
  uni.$u.route({
    url: "pages/smartOperation/quannanInstall/installTask/index",
  });
};
const getApp = async () => {
  userInfo.value = uni.getStorageSync("userInfo");
  let tenantId = userInfo.value.tenantId?.id;
  tenantId = removeSlash(tenantId);
  const res = await getTenantApplication({
    tenantId,
    resourceType: "APP",
  });
  return res.data[0]?.id;
};

onBeforeMount(async () => {
  const res = await getProjectRoot();
  const data = res.data;
  state.currentProjectId = data[0]?.id;
  const id = await getApp();
  findMenuByTenantApplication({
    tenantApplicationId: id,
  }).then((res: any) => {
    const data = res.data[0]?.children;
    applictionList.value = data?.map((menu: any) => {
      const menus = menu.children?.map((child: any) => {
        return child.meta.title;
      });
      state.allMenus = state.allMenus.concat(menus);
    });
    console.log(state.allMenus);
    // const hasApplictionList = data?.map((d : any) => {
    // 	const menus = d.children.find(app => app.title === d.meta.title).muens
    // 	const hasMenus = menus.filter((menu : any) => {
    // 		const state = d.children?.find((child : any) => child.meta.title === menu
    // 			.title)
    // 		return state ? true : false
    // 	})
    // 	d.muens = hasMenus
    // 	return {
    // 		title: d.meta.title,
    // 		muens: hasMenus
    // 	}
    // })
    // const userInfo = uni.getStorageSync('userInfo')
    // if(userInfo.authority === 'TENANT_ADMIN'){
    // 	applictionList.value = applictions()
    // }else{
    // 	applictionList.value = hasApplictionList
    // }
    if (proxy.$isHideTabBar) {
      bottomMenu.value = uni.getStorageSync("bottomMenus");
    }
  });
});
onShow(async () => {
  // uni.hideLoading()
  const res = await getTodoCount();
  // const cs = await getCircuitTaskNum()
  // const ms = await getMaintainTaskNum()
  // const csNum = cs.data?.data
  // const msNum = ms.data?.data
  countData.value = res.data?.data;
  // countData.value.circuiteTaskNum = csNum
  // countData.value.maintainTaskNum = msNum
  state.tabList[0].count = parseInt(countData.value.total); // + parseInt(msNum) + parseInt(csNum)
  refreshData();
});
</script>

<style lang="scss" scoped>
.card-list {
  margin-top: 20rpx;

  .item {
    min-height: 160rpx;
    width: 686rpx;
    border-radius: 16rpx;
    margin: 0 auto;
    margin-bottom: 20rpx;
    background-color: #ffffff;
    font-size: 28rpx;
    font-weight: 700;
    padding: 0 40rpx;

    .left {
      text {
        padding-left: 40rpx;
      }
    }

    .right {
      text {
        color: #fbb934;
      }
    }
  }

  .warning-item {
    min-height: 176rpx;
    width: 686rpx;
    border-radius: 16rpx;
    margin: 0 auto;
    margin-bottom: 20rpx;
    background-color: #ffffff;
    font-size: 24rpx;
    padding: 24rpx 28rpx;

    .top {
      color: #91949f;

      .bg-box {
        height: 32rpx;
        width: 32rpx;
        border-radius: 4rpx;
        background: #f87d38;
        text-align: center;
        line-height: 32rpx;
      }

      text {
        padding-left: 20rpx;
      }
    }

    .content {
      padding-top: 20rpx;
      text-overflow: -o-ellipsis-lastline;
      overflow: hidden; //溢出内容隐藏
      text-overflow: ellipsis; //文本溢出部分用省略号表示
      display: -webkit-box; //特别显示模式
      -webkit-line-clamp: 2; //行数
      line-clamp: 2;
      -webkit-box-orient: vertical;
      color: #060f27;
    }
  }
}
</style>
