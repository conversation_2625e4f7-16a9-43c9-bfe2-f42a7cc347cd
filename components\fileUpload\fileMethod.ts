export const chooseFile = (callback : any, acceptType ?: any) => {
	var CODE_REQUEST = 1000
	var main = plus.android.runtimeMainActivity() as any
	if (plus.os.name == 'Android') {
		console.log("111666")
		var Intent = plus.android.importClass('android.content.Intent') as any
		var intent = new Intent(Intent.ACTION_GET_CONTENT)
		intent.addCategory(Intent.CATEGORY_OPENABLE)
		if (acceptType) {
			intent.setType(acceptType)
		} else {
			intent.setType("*/*")
		}
		main.onActivityResult = (requestCode : any, resultCode : any, data : any) => {
			if (requestCode == CODE_REQUEST) {
				const uri = data.getData()
				plus.android.importClass(uri)
				console.log("2226666",uri)
				const Build = plus.android.importClass('android.os.Build') as any
				const isKitKat = Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT
				const DocumentsContract = plus.android.importClass('android.provider.DocumentsContract') as any
				if (isKitKat && DocumentsContract.isDocumentUri(main, uri)) {
					if ("com.android.externalstorage.documents" == uri.getAuthority()) {
						console.log("2226666")
						var docId = DocumentsContract.getDocumentId(uri)
						var split = docId.split(":")
						var type = split[0]

						if ("primary" == type) {
							var Environment = plus.android.importClass('android.os.Environment') as any
							console.log(type, Environment.getExternalStorageDirectory() + "/" + split[1])
							callback(Environment.getExternalStorageDirectory() + "/" + split[1])
						} else {
							var System = plus.android.importClass('java.lang.System') as any
							var sdPath = System.getenv("SECONDARY_STORAGE")
							if (sdPath) {
								console.log('222', sdPath + "/" + split[1])
								callback(sdPath + "/" + split[1])
							}
						}
					}
					else if ("com.android.providers.downloads.documents" == uri.getAuthority()) {
						var id = DocumentsContract.getDocumentId(uri)
						var ContentUris = plus.android.importClass('android.content.ContentUris') as any
						var contentUri = ContentUris.withAppendedId(
							//    Uri.parse("content://downloads/public_downloads"), Long.valueOf(id))
							Uri.parse("content://downloads/public_downloads"), id)
						callback(getDataColumn(main, contentUri, null, null))
					}
					else if ("com.android.providers.media.documents" == uri.getAuthority()) {
						var docId = DocumentsContract.getDocumentId(uri)
						var split = docId.split(":")
						console.log(split)
						var type = split[0]
						console.log(type)
						var MediaStore = plus.android.importClass('android.provider.MediaStore') as any
						if ("image" == type) {
							contentUri = MediaStore.Images.Media.EXTERNAL_CONTENT_URI
						} else if ("video" == type) {
							//contentUri = MediaStore.Video.Media.EXTERNAL_CONTENT_URI
							contentUri = MediaStore.Files.getContentUri("external")
						} else if ("audio" == type) {
							contentUri = MediaStore.Audio.Media.EXTERNAL_CONTENT_URI
						} else {
							contentUri = MediaStore.Files.getContentUri("external")
						}

						console.log('dasdasdas', contentUri)
						var selection = "_id=?"
						var selectionArgs = new Array()
						selectionArgs[0] = split[1]

						callback(getDataColumn(main, contentUri, selection, selectionArgs))
					}
				}

				else if ("content" == uri.getScheme()) {
					callback(getDataColumn(main, uri, null, null))
				}
				else if ("file" == uri.getScheme()) {
					callback(uri.getPath())
				}
			}
		}
		main.startActivityForResult(intent, CODE_REQUEST)
	}
}

export const getDataColumn = (main : any, uri : any, selection : any, selectionArgs : any) => {
	plus.android.importClass(main.getContentResolver())
	let cursor = main.getContentResolver().query(uri, ['_data'], selection, selectionArgs,
		null)
	plus.android.importClass(cursor)
	if (cursor != null && cursor.moveToFirst()) {
		var column_index = cursor.getColumnIndexOrThrow('_data')
		var result = cursor.getString(column_index)
		cursor.close()
		return result
	}
	return null
}

export const chooseFile2 = (callback : any) => {           //使用plus选择文件
	let that = this;
	let filePath = ''
	let main = plus.android.runtimeMainActivity();
	let Intent = plus.android.importClass('android.content.Intent');
	let Activity = plus.android.importClass('android.app.Activity');
	let intent = new Intent(Intent.ACTION_GET_CONTENT);
	intent.setType('*/*');
	intent.putExtra(Intent.EXTRA_ALLOW_MULTIPLE, false); //关键！多选参数
	intent.addCategory(Intent.CATEGORY_OPENABLE);
	main.startActivityForResult(intent, 200);
	// 获取回调
	main.onActivityResult = (requestCode, resultCode, data) => {
		let Activity = plus.android.importClass('android.app.Activity');
		let ContentUris = plus.android.importClass('android.content.ContentUris');
		let Cursor = plus.android.importClass('android.database.Cursor');
		let Uri = plus.android.importClass('android.net.Uri');
		let Build = plus.android.importClass('android.os.Build');
		let Environment = plus.android.importClass('android.os.Environment');
		let DocumentsContract = plus.android.importClass('android.provider.DocumentsContract');
		var MediaStore = plus.android.importClass('android.provider.MediaStore');
		// 给系统导入 contentResolver
		let contentResolver = main.getContentResolver();
		plus.android.importClass(contentResolver);
		if (resultCode == Activity.RESULT_OK) {
			console.log('data', data)
			// 解析路径
			if (data.getData() != null) {
				let uri = data.getData()
				console.log('uri', uri)
				let path = uri.getPath()  // 获取到选择文件的虚拟路径
				console.log('path', path)
				this.filePath = path
				let docId = DocumentsContract.getDocumentId(uri);
				let split = docId.split(":");
				let type = split[0];
				let selection = "_id=?";
				let selectionArgs = new Array();
				selectionArgs[0] = split[1];
				uri = MediaStore.Files.getContentUri("external");
				plus.android.importClass(main.getContentResolver());
				// 通过查询的方式用虚拟路径的id1获取到文件的真实路径
				console.log('uri', uri)
				console.log('docId', docId)
				console.log('selectionArgs', selectionArgs.toString())
				let cursor = main.getContentResolver().query(uri, ['_data'], selection, selectionArgs,
					null);
				plus.android.importClass(cursor);
				console.log('cursor', cursor)
				console.log('cursor.moveToFirst()', cursor.moveToFirst())
				let result

				if (cursor != null) {
					let column_index = cursor.getColumnIndexOrThrow('_data');
					console.log('column_index', column_index)
					result = cursor.getString(column_index)   // result即文件的真实路径
					console.log('resulttt', result)
					cursor.close();
				}
				result = 'file://' + result
				callback(result)
				// this.filePath = result // 此路径为文件的本地真实路径，可使用该路径进行上传文件
			}
		}
	}
}