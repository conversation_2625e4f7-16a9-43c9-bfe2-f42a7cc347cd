<template>
	<view class="main">
		<view class="w-card" style="background-image: url('static/img/icons/waterWorks-bg1.png')"
			@click="toWaterSupply">
			<text>供水报表</text>
		</view>
		<view class="w-card" style="background-image: url('static/img/icons/waterWorks-bg2.png')"
			@click="toWaterQuality">
			<text>水质报表</text>
		</view>
		<view class="w-card" style="background-image: url('static/img/icons/waterWorks-bg3.png')" @click="toProduction">
			<text>生产报表</text>
		</view>
		<!-- <view class="w-card" style="background-image: url('static/img/icons/waterWorks-bg2.png')"
			@click="toEnergyConsumption">
			<text>能耗报表</text>
		</view>
		<view class="w-card" style="background-image: url('static/img/icons/waterWorks-bg3.png')" @click="toUnitConsumption">
			<text>吨水耗电</text>
		</view> -->
	</view>
</template>

<script lang="ts" setup>
	// 供水报表
	const toWaterSupply = () => {
		uni.$u.route({
			url: 'pages/wisdomProduce/waterWorksReport/waterSupplyReport/index'
		})
	}
	// 水质报表 
	const toWaterQuality = () => {
		uni.$u.route({
			url: 'pages/wisdomProduce/waterWorksReport/waterQualityReport/index'
		})
	}
	// 生产报表 
	const toProduction = () => {
		uni.$u.route({
			url: 'pages/wisdomProduce/waterWorksReport/productionReport/index'
		})
	}
	// 能耗报表 
	// const toEnergyConsumption = () => {
	// 	uni.$u.route({
	// 		url: 'pages/wisdomProduce/waterWorksReport/energyConsumptionReport/index'
	// 	})
	// }
	// 单耗报表 
	// const toUnitConsumption = () => {
	// 	uni.$u.route({
	// 		url: 'pages/wisdomProduce/waterWorksReport/unitConsumptionReport/index'
	// 	})
	// }
</script>

<style lang="scss" scoped>
	.w-card {
		height: 240rpx;
		width: 686rpx;
		border-radius: 16rpx;
		background-color: red;
		margin: 10rpx auto;
		background-size: 100% 100%;
		line-height: 240rpx;
		text-align: center;

		text {
			font-size: 34rpx;
			color: #FFFFFF;
		}
	}
</style>
