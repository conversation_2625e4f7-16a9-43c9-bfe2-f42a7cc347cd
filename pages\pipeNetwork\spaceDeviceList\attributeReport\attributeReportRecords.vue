<template>
	<view class="main">
		<scroll-view class="scroll-view" v-if="list.length" :lower-threshold="50" :refresher-threshold="50">
			<view class="card flex-between" v-for="(device,index) in list" :key="index"
				@click="()=>toReportDetail(device)">
				<view class="left flex-center">
					<view class="image">
						<u-icon name="file-text" color="#fff" size="32"></u-icon>
					</view>
					<view class="info">
						<view class="name">
							{{device.layer}}
						</view>
						<view class="status">
							<span class="status" :class="['status'+device.status]">{{reportType[device.status]}}</span>
							<span>{{formatTime(device.uploadTime)}}</span>
						</view>
					</view>
				</view>
				<view class="right flex-center">
					<u-icon name="arrow-right" color="#91949F" size="14"></u-icon>
				</view>
			</view>
		</scroll-view>
		<view v-else class="empty">
			<text>暂无相关信息！</text>
		</view>
		<view class="botton-placeholder">
			<!-- 底部占位 -->
		</view>
		<view class="button">
			<u-button type="primary" color="#3862F8" @click="goBack">返回</u-button>
		</view>
	</view>
</template>

<script lang="ts" setup>
	import {
		onShow
	} from '@dcloudio/uni-app'
	import {
		ref
	} from "vue";
	import {
		GetMapErrorRecord
	} from '../../../../common/api/map';
	import { removeSlash } from '../../../../common/utils/removeIdSlash';
	const reportType = {
		1: '同意',
		2: '驳回',
		0: '待处理'
	}
	const goBack = () => {
		uni.navigateBack()
	}
	const toReportDetail = (device : any) => {
		uni.navigateTo({
			url: './attributeReportRecordDetail?uploadInfo=' + encodeURIComponent(JSON.stringify(device)),
		});
	}

	const list = ref<any[]>([])
	const refreshData = async () => {
		const userId = uni.getStorageSync('userInfo')?.id?.id
		const res = await GetMapErrorRecord({
			page: 1,
			size: 500,
			uploadUser: userId ? removeSlash(userId) : undefined
		})
		list.value = res.data?.data?.data || []
	}
	onShow(() => {
		console.log('show');
		refreshData()
	})
</script>

<style lang="scss" scoped>
	.empty {
		display: flex;
		align-items: center;
		height: 80rpx;
	}

	.card {
		background-color: #FFFFFF;
		border-radius: 16rpx;
		padding: 22rpx 44rpx;

	}

	.left {
		.info {
			padding-left: 16rpx;

			line-height: 1.5;

			.name {
				color: #060F27;
				font-size: 28rpx;
				font-weight: bold;
			}

			.status {
				.status1 {
					color: #28ff03;
				}

				.status2 {
					color: #ff2a2a;
				}

				.status0 {
					color: #ddab35;
				}
			}

			.status,
			.time {
				color: #91949F;
				font-size: 20rpx;

				&>span {
					color: #060F27;
					margin-right: 20rpx;
				}
			}
		}

		.image {
			background: #5991FF;
			border: 2rpx solid #1564FF;
			width: 56rpx;
			height: 56rpx;
			text-align: center;
			border-radius: 50%;
			line-height: 56rpx;
			display: flex;
			justify-content: center;
			align-items: center;

		}
	}

	.right {
		text {
			&:nth-child(1) {
				color: #3862F8;
				font-weight: bold;
			}

			&:nth-child(2) {
				color: #91949F;
				padding-right: 10rpx;
			}
		}
	}

	.botton-placeholder {
		height: 140rpx;
	}

	.button {
		width: 100%;
		background-color: #FFFFFF;
		position: fixed;
		bottom: 0rpx;
		height: 120rpx;
		box-shadow: 0px 4rpx 30rpx rgba(178, 183, 199, 0.5);

		button {
			border-radius: 8rpx;
			width: 686rpx;
			margin: 20rpx auto;
		}
	}
</style>