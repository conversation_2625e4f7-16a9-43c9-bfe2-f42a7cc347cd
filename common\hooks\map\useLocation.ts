import { ref, computed } from 'vue'
export const useLocation = () => {
	const longitude = ref<number>(0)
	const latitude = ref<number>(0)
	const curDate = ref(new Date().valueOf())
//刷新定位
	const refreshNum =  ref<any>(0)
	const lonLat = computed(() => {
		return refreshNum.value
	})
	// const lonLat = computed(() => {
	// 	return [longitude.value, latitude.value, curDate.value]
	// })
	const lonLatStr = computed(() => {
		return [longitude.value, latitude.value, curDate.value].join(',')
	})
	const loading = ref<boolean>(false)
	const getLocation = (callBack ?: (res : UniNamespace.GetLocationSuccess) => void, errorCallback ?: (error : any) => void) => {
		debugger
		if (loading.value === true) return
		loading.value = true
		uni.getLocation?.({
			isHighAccuracy: true,
			success: (res) => {
				curDate.value = new Date().valueOf()
				debugger
				longitude.value = res.longitude
				latitude.value = res.latitude
				console.log(res, '获取当前定位-----')
				callBack?.(res)
			},
			fail: (error : any) => {
				debugger
				errorCallback?.(error)
				console.log(error.errMsg);
			},
			complete: () => {
				loading.value = false
			}
		})

		// plus.geolocation.getCurrentPosition?.((res : any) => {
		// 	longitude.value = res.coords.longitude
		// 	latitude.value = res.coords.latitude
		// 	callBack?.({
		// 		latitude: res.coords.latitude,
		// 		longitude: res.coords.longitude
		// 	} as any)
		// 	// resolve({lat: res.coords.latitude, lng: res.coords.longitude});
		// }, (error) => {
		// 	uni.showToast({
		// 		title: error.message,
		// 		duration: 2000,
		// 		icon: 'none'
		// 	});
		// })
	}
	const getLocationWithoutLoading = async (callBack ?: (res : UniNamespace.GetLocationSuccess) => void, errorCallback ?: (error : any) => void, complete ?: () => void) => {
		return new Promise((resolve : (res : { longitude : number; latitude : number }) => void, reject : any) => {

			uni.getLocation?.({
				isHighAccuracy: true,
				success: (res : any) => {
					curDate.value = new Date().valueOf()
					longitude.value = res.longitude
					latitude.value = res.latitude
					callBack?.(res)
					resolve(res)
				},
				fail: (error : any) => {
					errorCallback?.(error)
					reject(error)
				},
				complete: () => {
					complete?.()
				}
			})
		})

	}

	return {
		getLocation,
		getLocationWithoutLoading,
		longitude,
		latitude,
		lonLat,
		lonLatStr,
		refreshNum,
		loading
	}
}