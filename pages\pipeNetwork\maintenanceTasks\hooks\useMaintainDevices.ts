import { ref } from 'vue'
import { getMaintainTaskItems } from '../../../../common/api/inspection'
export const useMaintainDevices = () => {
	const mainInfo = ref<{ completed?: any[], unCompleted?: any[], taskid?: string,status?: 'APPROVED', layerid?: string, layername?: string, isOK: number }>({ isOK: 0 })
	// 用于控制底部菜单的拉起显示内容和收起
	const showList = ref<boolean>(false)
	const showListType = ref<string>('')
	const setShowListType = (type: string) => {
		showList.value = !!type
		showListType.value = type
	}
	const setShowList = (flag: boolean) => {
		showList.value = flag
	}
	const init = (options: {
		layerid: string
		layername: string
		taskid: string
		status: 'APPROVED'
	}) => {
		mainInfo.value.layerid = options.layerid
		mainInfo.value.layername = options.layername
		mainInfo.value.taskid = options.taskid
		mainInfo.value.status=options.status
	}
	const refreshData = async () => {
		if (!mainInfo.value.taskid) return
		const proS = [
			getMaintainTaskItems({
				taskId: mainInfo.value.taskid,
				isComplete: true
			}),
			getMaintainTaskItems({
				taskId: mainInfo.value.taskid,
				isComplete: false
			})
		]
		const [res1, res2] = await Promise.all(proS)
		mainInfo.value = {
			...mainInfo.value,
			completed: _resolveRes(res1),
			unCompleted: _resolveRes(res2)
		}
		setTimeout(() => {
			mainInfo.value.isOK++
		}, 0)
	}
	const _resolveRes = (res: any) => {
		const data = res.data?.data?.data || []
		return data
	}
	const maintainCompDevices = ref<any[]>([])
	const maintainUnCompDevices = ref<any[]>([])
	return {
		refreshData,
		mainInfo,
		init,
		setShowListType,
		setShowList,
		showList,
		showListType,
		maintainCompDevices,
		maintainUnCompDevices
	}
}
