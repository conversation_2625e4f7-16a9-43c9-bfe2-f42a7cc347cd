<template>
	<view class="flow-detail">
		<!-- 统计卡片区域 -->
		<view class="stats-section">
			<view class="section-title">
				<text class="title-text">{{props.title}}统计</text>
				<view class="title-line"></view>
			</view>
			<view class="stats-cards">
				<view class="stat-card" v-for="(item, i) in state.total" :key="i">
					<view class="card-icon">
						<text class="iconfont" :class="i === 0 ? 'icon-device' : 'icon-alert'"></text>
					</view>
					<view class="card-content">
						<view class="card-value">{{item.value}}<text class="card-unit">{{item.unit}}</text></view>
						<view class="card-title">{{item.title}}</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 状态占比图表区域 -->
		<view class="chart-section">
			<view class="section-title">
				<text class="title-text">{{props.title}}状态占比统计</text>
				<view class="title-line"></view>
			</view>
			<view class="chart-container">
				<l-echart ref="refLEchart_Status_Ratio"></l-echart>
			</view>
		</view>

		<!-- 列表区域 -->
		<view class="list-section">
			<view class="section-title">
				<text class="title-text">{{props.title}}列表</text>
				<view class="title-line"></view>
			</view>
			<view class="list-container">
				<view class="tabs-container">
					<view class="custom-tabs">
						<view 
							v-for="(tab, index) in state.statusList" 
							:key="index"
							class="tab-item"
							:class="{ active: state.currentStatus === index }"
							@click="chooseTab(index)"
						>
							<text class="tab-text">{{tab.name}}</text>
							<view class="tab-badge" v-if="tab.badge.value > 0">{{tab.badge.value}}</view>
						</view>
					</view>
				</view>
				<view class="table-container">
					<view class="table-header">
						<view class="header-cell" v-for="column in state.listColumns" :key="column.prop">
							{{column.label}}
						</view>
					</view>
					<scroll-view class="table-body" scroll-y="true" :style="{height: '400rpx'}">
						<view 
							v-for="(item, index) in state.list" 
							:key="index"
							class="table-row"
							:class="{ 'row-hover': true }"
						>
							<view class="table-cell">
								<text class="cell-text">{{item.name}}</text>
							</view>
							<view class="table-cell">
								<view class="status-badge" :class="getStatusClass(item.statusAlia)">
									{{item.statusAlia}}
								</view>
							</view>
							<view class="table-cell">
								<text class="cell-text time">{{item.lastTime}}</text>
							</view>
						</view>
						<view v-if="state.list.length === 0" class="empty-state">
							<text class="empty-icon">📊</text>
							<text class="empty-text">暂无数据</text>
						</view>
					</scroll-view>
				</view>
			</view>
		</view>
	</view>
</template>

<script lang="ts" setup>
	import * as echarts from 'echarts'
	import lEchart from '@/uni_modules/lime-echart/components/l-echart/l-echart.vue'
	import TableList from './TableList.vue'
	import { onMounted, reactive, ref } from 'vue'
	import { getQualityMonitorStatusList } from '../../../common/api/stations'
	const props = defineProps<{
		title ?: string
	}>()
	const refLEchart_Status_Ratio = ref<InstanceType<typeof lEchart>>()
	const state = reactive<{
		total : { value : number; title : string; unit ?: string }[],
		list : { name : string; status : string; lastTime : string }[]
		listColumns : { label : string; prop : string }[]
		statusList : { name : string; badge : { value : number } }[],
		currentStatus : number
	}>({
		total: [{ value: 0, title: props.title + '总数', unit: '个' }, { value: 0, title: '报警率', unit: '%' }],
		list: [],
		listColumns: [
			{ label: '名称', prop: 'name' },
			{ label: '状态', prop: 'statusAlia' },
			{ label: '更新时间', prop: 'lastTime' }
		],
		statusList: [
			{ name: '全部', badge: { value: 0 } },
			{ name: '离线', badge: { value: 0 } },
			{ name: '报警', badge: { value: 0 } },
			{ name: '正常', badge: { value: 0 } },
		],
		currentStatus: 0
	})
	const refreshStatus = async () => {
		try {
			let total = state.list.length
			const statusList = [...state.statusList].splice(1,4)
			const data = statusList.map((item) => {
				return { name: item.name, value: item.badge.value }
			})
			refLEchart_Status_Ratio.value?.init(echarts, (chart : any) => {
				const option = {
					tooltip: {
						trigger: 'item',
						backgroundColor: 'rgba(0, 0, 0, 0.85)',
						borderColor: 'transparent',
						borderRadius: 12,
						textStyle: {
							color: '#fff',
							fontSize: 14,
							fontWeight: '500'
						},
						formatter: '{a} <br/>{b}: {c} ({d}%)',
						extraCssText: 'box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.3);'
					},
					title: {
						text: '{name|合计(个)}\n{val|' + total + '}',
						top: 'center',
						left: '35%',
						textAlign: 'center',
						textStyle: {
							rich: {
								name: {
									fontSize: 14,
									fontWeight: 'normal',
									padding: [8, 0],
									align: 'center',
									color: '#666',
									width: 100
								},
								val: {
									fontSize: 24,
									fontWeight: 'bold',
									color: '#0073ff',
									textShadow: '0 2rpx 4rpx rgba(0, 115, 255, 0.3)',
									align: 'center',
									width: 100
								}
							}
						}
					},
					color: ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57', '#ff9ff3'],
					legend: {
						type: 'scroll',
						icon: 'circle',
						orient: 'vertical',
						left: 'right',
						top: 'center',
						align: 'left',
						itemGap: 20,
						itemWidth: 16,
						itemHeight: 16,
						symbolKeepAspect: true,
						textStyle: {
							color: '#333',
							fontSize: 14,
							fontWeight: '500',
							rich: {
								name: {
									align: 'left',
									width: 80,
									fontSize: 14,
									color: '#333',
									fontWeight: '500'
								},
								value: {
									align: 'left',
									width: 80,
									fontSize: 14,
									color: '#0073ff',
									fontWeight: 'bold'
								}
							}
						},
						data: data.map(item => item.name),
						formatter(name : any) {
							if (data && data.length) {
								for (let i = 0; i < data.length; i++) {
									if (name === data[i].name) {
										return '{name|' + (data[i].name || name) + '}\n{value|' + data[i].value + '个}'
									}
								}
							}
						}
					},
					series: [
						{
							name: props.title,
							type: 'pie',
							radius: ['40%', '70%'],
							center: ['35%', '50%'],
							data: data,
							itemStyle: {
								borderRadius: 8,
								borderColor: '#fff',
								borderWidth: 3,
								shadowBlur: 15,
								shadowOffsetX: 0,
								shadowOffsetY: 4,
								shadowColor: 'rgba(0, 0, 0, 0.15)'
							},
							label: {
								show: true,
								position: 'inside',
								textStyle: {
									fontSize: 14,
									color: '#fff',
									fontWeight: 'bold',
									textShadow: '0 1rpx 2rpx rgba(0, 0, 0, 0.3)'
								},
								formatter(param : any) {
									return (param.percent||0) + '%';
								}
							},
							labelLine: {
								show: false
							},
							emphasis: {
								itemStyle: {
									shadowBlur: 25,
									shadowOffsetX: 0,
									shadowOffsetY: 8,
									shadowColor: 'rgba(0, 0, 0, 0.25)',
									borderWidth: 4
								},
								label: {
									fontSize: 16,
									fontWeight: 'bold'
								}
							},
							animationType: 'scale',
							animationEasing: 'elasticOut',
							animationDelay: function (idx: any) {
								return Math.random() * 200;
							}
						}
					]
				}
				chart.setOption(option)
			})

		} catch (e) {
			console.log(e)
		}
	}

	const chooseTab = (index : number) => {
		state.currentStatus = index
		refreshTable(index)
	}
	const refreshTable = async (index : number = 0) => {
		try {
			const curStatus = state.statusList[index]?.name
			const status = curStatus === '离线' ? 'offline' : curStatus === '报警' ? 'alarm' : curStatus === '正常' ? 'online' : ''
			const res = await getQualityMonitorStatusList(status)

			if (res.data.code === 200) {
				state.list = res.data?.data?.map((item : any) => {
					item.statusAlia = item.status === 'offline' ? '离线' : item.status === 'alarm' ? '报警' : item.status === 'online' ? '正常' : ''
					return item
				}) || []
			} else {
				console.log('统计失败');
			}
		} catch (e) {
			console.log(e)
		}
	}
	const refreshData = async () => {
		await refreshTable()
		state.statusList[0].badge.value = state.total[0].value = state.list.length
		state.statusList[1].badge.value = state.list.filter(item => item.status === 'offline').length
		state.statusList[2].badge.value = state.list.filter((item) => item.status === 'alarm').length
		state.statusList[3].badge.value = state.list.filter(item => item.status === 'online').length
		state.total[1].value = state.list.length === 0 ? 0 : Number((state.statusList[2].badge.value / state.list.length * 100).toFixed(2))
		refreshStatus()
	}
	onMounted(() => {

		refreshData()
	})
</script>

<style lang="scss" scoped>
	.flow-detail {
		padding: 24rpx;
		background-color: #fff;
		border-radius: 16rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

		.stats-section {
			margin-bottom: 24rpx;

			.section-title {
				display: flex;
				align-items: center;
				margin-bottom: 24rpx;

				.title-text {
					font-size: 32rpx;
					font-weight: bold;
					color: #333;
				}
				.title-line {
					flex: 1;
					height: 1rpx;
					background-color: #eee;
					margin-left: 20rpx;
				}
			}

			.stats-cards {
				display: flex;
				justify-content: space-between;

				.stat-card {
					background-color: #f0f9eb;
					border: 1rpx solid #e1f3d8;
					border-radius: 12rpx;
					padding: 20rpx;
					flex: 1;
					margin-right: 20rpx;
					display: flex;
					align-items: center;
					justify-content: space-around;

					&:last-child {
						margin-right: 0;
					}

					.card-icon {
						font-size: 48rpx;
						color: #67c23a;
					}
					.card-content {
						text-align: center;

						.card-value {
							font-size: 36rpx;
							font-weight: bold;
							color: #67c23a;
							margin-bottom: 8rpx;
						}
						.card-unit {
							font-size: 24rpx;
							color: #909399;
						}
						.card-title {
							font-size: 24rpx;
							color: #909399;
						}
					}
				}
			}
		}

		.chart-section {
			margin-bottom: 24rpx;

			.section-title {
				display: flex;
				align-items: center;
				margin-bottom: 24rpx;

				.title-text {
					font-size: 32rpx;
					font-weight: bold;
					color: #333;
				}
				.title-line {
					flex: 1;
					height: 1rpx;
					background-color: #eee;
					margin-left: 20rpx;
				}
			}

			.chart-container {
				height: 480rpx;
				background-color: #fff;
				border-radius: 16rpx;
				box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
			}
		}

		.list-section {
			.section-title {
				display: flex;
				align-items: center;
				margin-bottom: 24rpx;

				.title-text {
					font-size: 32rpx;
					font-weight: bold;
					color: #333;
				}
				.title-line {
					flex: 1;
					height: 1rpx;
					background-color: #eee;
					margin-left: 20rpx;
				}
			}

			.list-container {
				.tabs-container {
					display: flex;
					justify-content: space-around;
					margin-bottom: 24rpx;
					background-color: #f5f7fa;
					border-radius: 12rpx;
					padding: 10rpx 0;

					.custom-tabs {
						display: flex;
						justify-content: space-around;
						flex: 1;

						.tab-item {
							display: flex;
							align-items: center;
							padding: 10rpx 20rpx;
							border-radius: 20rpx;
							margin: 0 10rpx;
							background-color: #e0e0e0;
							color: #606266;
							font-size: 28rpx;
							font-weight: bold;
							transition: all 0.3s ease;

							&.active {
								background-color: #409eff;
								color: #fff;
							}

							.tab-text {
								margin-right: 10rpx;
							}
							.tab-badge {
								background-color: #f56c6c;
								color: #fff;
								border-radius: 10rpx;
								padding: 4rpx 8rpx;
								font-size: 20rpx;
								font-weight: bold;
							}
						}
					}
				}

				.table-container {
					background-color: #fff;
					border-radius: 16rpx;
					box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

					.table-header {
						display: flex;
						justify-content: space-around;
						padding: 20rpx 0;
						border-bottom: 1rpx solid #eee;

						.header-cell {
							font-size: 28rpx;
							font-weight: bold;
							color: #333;
							text-align: center;
						}
					}

					.table-body {
						.table-row {
							display: flex;
							justify-content: space-around;
							padding: 20rpx 0;
							border-bottom: 1rpx solid #eee;

							&.row-hover {
								background-color: #f5f7fa;
							}

							.table-cell {
								flex: 1;
								text-align: center;
								font-size: 28rpx;
								color: #606266;

								.cell-text {
									display: block;
								}
								.time {
									font-size: 24rpx;
									color: #909399;
								}
								.status-badge {
									display: inline-block;
									padding: 6rpx 12rpx;
									border-radius: 20rpx;
									font-size: 24rpx;
									font-weight: bold;
									color: #fff;

									&.离线 {
										background-color: #f56c6c;
									}
									&.报警 {
										background-color: #e6a23c;
									}
									&.正常 {
										background-color: #67c23a;
									}
								}
							}
						}
						.empty-state {
							display: flex;
							flex-direction: column;
							align-items: center;
							padding: 80rpx 0;
							color: #909399;
							font-size: 32rpx;

							.empty-icon {
								font-size: 60rpx;
								margin-bottom: 20rpx;
							}
						}
					}
				}
			}
		}
	}
</style>