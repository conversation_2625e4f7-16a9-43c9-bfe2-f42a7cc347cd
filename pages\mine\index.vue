<template>
	<view class="main">
		<!-- <view class="bg"></view> -->
		<view class=" flex-between" @click="toInfo">
			<view class="flex-center user-info">
				<u-avatar src="" size="128"></u-avatar>
				<view class="info">
					<view class="name">
						{{userInfo.firstName}}
					</view>
					<view class="role">
						{{userInfo.email}}
					</view>
				</view>
			</view>
			<u-icon name="arrow-right"></u-icon>
		</view>
		<view class="menu-list">
			<view class="menu flex-between" @click="updatePassword">
				<view class="left flex-center">
					<u-icon size='32' name="custom-icon-jiesuo" color="#5e9efc" customPrefix="custom-icon">
					</u-icon>
					<text>修改密码</text>
				</view>
				<u-icon name="arrow-right"></u-icon>
			</view>
			<!-- <view class="menu flex-between" @click="toMeterConfiguration">
				<view class="left flex-center">
					<u-icon size='32' name="custom-icon-shuju" color="#5e9efc" customPrefix="custom-icon">
					</u-icon>
					<text>抄表功能项配置</text>
				</view>
				<u-icon name="arrow-right"></u-icon>
			</view> -->
			<!-- <view class="menu flex-between" @click="toSetting">
				<view class="left flex-center">
					<u-icon size='32' name="custom-icon-shezhi" color="#5e9efc" customPrefix="custom-icon">
					</u-icon>
					<text>后台定位设置 </text>
				</view>
				<u-icon name="arrow-right"></u-icon>
			</view> -->
			<view class="menu flex-between" @click="clearStorage">
				<view class="left flex-center">
					<u-icon size='32' name="custom-icon-shanchu" color="#5e9efc" customPrefix="custom-icon">
					</u-icon>
					<text>清除缓存</text>
				</view>
				<view class="">
					{{storageSize}}
					<u-icon name="arrow-right"></u-icon>
				</view>

			</view>
			<!-- <view class="menu flex-between" @click="toBooks">
				<view class="left flex-center">
					<u-icon size='32' name="custom-icon-yonghu2" color="#5e9efc" customPrefix="custom-icon">
					</u-icon>
					<text>通讯录 </text>
				</view>
				<u-icon name="arrow-right"></u-icon>
			</view> -->
			<!-- <view class="menu flex-between" @click="toAboutUs">
				<view class="left flex-center">
					<u-icon size='32' name="custom-icon-huiyuan" color="#5e9efc" customPrefix="custom-icon">
					</u-icon>
					<text>关于我们 </text>
				</view>
				<u-icon name="arrow-right"></u-icon>
			</view> -->
			<view class="menu flex-between" v-if="currentVersion" @click="versionUpdate">
				<view class="left flex-center">
					<u-icon size='32' name="custom-icon-shezhi" color="#5e9efc" customPrefix="custom-icon">
					</u-icon>
					<text>当前版本 </text>
				</view>
				<view class="">
					{{currentVersion.version}}
					<u-icon name="arrow-right"></u-icon>
				</view>
			</view>
		</view>
		<view class="logout" @click="logout">
			退出登录
		</view>
		<u-toast ref="refToast"></u-toast>
		<!-- {{clientId}} -->
		<block v-if="proxy.$isHideTabBar">
			<u-tabbar v-model="current" :list="bottomMenu"></u-tabbar>
		</block>
	</view>
</template>

<script lang="ts" setup>
	import * as utils from '@/uni_modules/hic-upgrade/utils/index.js';
	import {
		onMounted,
		getCurrentInstance,
		ref
	} from "vue";

	import {
		onShow
	} from '@dcloudio/uni-app';
	const {
		proxy
	} = getCurrentInstance()
	const GoEasy = uni.$GoEasy;
	const GRTC = uni.$GRTC;
	const lastVersion = ref<any>('')
	const refToast = ref<any>()
	const userInfo = ref<any>({})
	const storageSize = ref<string>('0KB')
	const current = ref<number>(3)
	const bottomMenu = ref<any>([])
	const clientId = ref<any>()
	const currentVersion = ref<any>()
	// 推出
	const logout = () => {
		uni.removeStorageSync("userInfo")
		uni.removeStorageSync("authority")
		uni.removeStorageSync("token")
		// gis用户token
		uni.removeStorageSync('gToken')
		if (GoEasy.getConnectionStatus() !== 'disconnected') {
			GoEasy.disconnect({
				onSuccess: function () {
					console.log("GoEasy disconnect successfully.")
				},
				onFailed: function (error) {
					console.log("Failed to disconnect GoEasy, code:" + error.code + ",error:" + error.content);
				}
			});
		}
		const intervalId : any = setInterval(() => { }, 1000)
		for (let i = 0; i < parseInt(intervalId); i++) {
			clearInterval(i)
		}
		uni.$u.route({
			type: 'redirectTo',
			url: '/pages/login/index'
		})
	}


	// 查看通讯录
	const toBooks = () => {
		uni.$u.route({
			url: 'pages/mine/addressBook/index',
		})
	}
	// 关于我们
	const toAboutUs = () => {
		uni.$u.route({
			url: 'pages/mine/aboutUs/index',
			animationType: 'pop-in',
		})
	}
	// 基本信息
	const toInfo = () => {
		uni.$u.route({
			url: 'pages/mine/info/index'
		})
	}
	// 修改密码
	const updatePassword = () => {
		uni.$u.route({
			url: 'pages/mine/updatePassword/index'
		})
	}

	onMounted(async () => {
		if (proxy.$isHideTabBar) {
			bottomMenu.value = uni.getStorageSync('bottomMenus')
		}
	})
	const versionUpdate = async (tip = true) => {
		console.log('top---', tip)
		if (!tip) { return }
		// 1 请求返回服务端版本信息
		const res : any = await getLatestVersion({
			platform: utils.platform    // 运行平台
		});
		if (!res) {
			if (tip) {
				refToast.value.show({
					title: '暂无更新',
					type: 'info',
					position: 'bottom',
					callback: () => {
					}
				})
			}
			return;
		}
		lastVersion.value = res.versionName
		console.log('res', res)
		// 2 存储服务端版本信息
		utils.setServerInfo({
			version: res.versionName,
			versionCode: res.versionCode,
			downloadUrl: res.downloadUrl,
			wgtUrl: res.wgtUrl,
			forceUpdate: res.forceUpdate,
			isSilently: res.isSilently,
			desc: res.versionDesc
		} as any)
		// 3 判断是否更新
		if (!utils.isUpdate()) {
			if (tip) {
				refToast.value.show({
					title: '已是最新版本',
					type: 'info',
					position: 'bottom',
					callback: () => {
					}
				})

			}
			return;
		}
		const updateInfo = utils.getUpdateInfo();
		const info = utils.getServerVersion();
		// 4 wgt 静默更新
		console.log('top---', updateInfo)
		if (updateInfo.type == 'wgt' && info.isSilently) {
			// 下载完成 直接安装 下次启动生效
			const path = await utils.downloadFile(updateInfo.url);
			await utils.installForAndroid(path);
			return;
		}
		// 5 跳转页面 提示更新
		await utils.toUpgradePage('/uni_modules/hic-upgrade/pages/upgrade');
	}

	const getLatestVersion = (data) => {
		return new Promise((resolve, reject) => {
			const systemConfig = uni.getStorageSync('systemConfig')
			const account = systemConfig?.account
			uni.request({
				url: 'http://app.siloon.com:8081/api/app/version/checkVersion/__UNI__7F59064',
				method: 'GET',
				data: {
					tenantKey: account
				},
				success: function (res : any) {
					const data = res.data.data
					const currentVersion = utils.getCurrentVersion()
					console.log('当前版本', currentVersion)
					if (data.versionCode > currentVersion.versionCode) {
						resolve({
							versionName: data.versionName,
							versionCode: data.versionCode,
							downloadUrl: data.url.indexOf('apk') !== -1 ? data.url : '',
							wgtUrl: data.url.indexOf('wgt') !== -1 ? data.url : '',
							forceUpdate: false,//data.url.indexOf('apk')!==-1,
							isSilently: 1,
							versionDesc: data.versionContent,
						});
					} else {
						resolve(null)
					}
				}
			})
		});
	}

	// 清除缓存
	const clearStorage = () => {
		uni.showModal({
			title: '提示',
			content: '确定清除缓存吗?',
			confirmText: '立即清除',
			success(res) {
				if (res.confirm) {
					// uni.clearStorageSync();
					// //重新获取并显示清除后的缓存大小
					// getStorageSize();
					// uni.showToast({
					// 	title: '清除成功',
					// 	icon: 'none'
					// })
					// //清除完后跳到登录页面
					// uni.$u.route({
					// 	url: '/pages/login/index',
					// 	animationType: 'pop-in',
					// 	animationDuration: 200
					// })
					// #ifdef APP-PLUS
					plus.cache.clear(function () {
						refToast.value.show({
							title: '清除成功',
							type: 'success',
							callback: () => {
								getStorageSize()
							}
						})
					});
					// #endif
					// #ifdef H5
					uni.clearStorage()
					uni.reLaunch({
						url: '/pages/login/index',
						success(result) {
							//plus.navigator.closeSplashscreen();
						}
					})
					// #endif
				}
			}
		})
	}

	const getStorageSize = () => {
		// 使用plus.cache.calculate 获取应用的缓存大小，
		plus.cache.calculate((size) => { //size是多少个字节单位是b
			//你可以做下面相应的处理
			if (size < 1024) {
				storageSize.value = size + 'B';
			} else if (size / 1024 >= 1 && size / 1024 / 1024 < 1) {
				storageSize.value = Math.floor(size / 1024 * 100) / 100 + 'KB';
			} else if (size / 1024 / 1024 >= 1) {
				storageSize.value = Math.floor(size / 1024 / 1024 * 100) / 100 + 'M';
			}
		});
	}

	onShow(() => {
		userInfo.value = uni.getStorageSync('userInfo')
		// #ifdef APP-PLUS
		getStorageSize()
		plus.push.getClientInfoAsync((info) => {
			console.log('设备编号', info)
			const cid = info["clientid"]
			clientId.value = info
		});
		currentVersion.value = utils.getCurrentVersion()
		console.log('当前版本111', currentVersion.value)
		// #endif

	})
</script>

<style lang="scss" scoped>
	.main {
		padding: 128rpx 32rpx;
		position: absolute;
		background: linear-gradient(180deg, #CDD8FF 10%, rgba(251, 251, 251, 1) 30%);
		width: 100%;

		.bg {
			height: 560rpx;
			background: linear-gradient(180deg, #CDD8FF 0%, rgba(251, 251, 251, 0) 10%);
		}

		.user-info {
			.info {
				line-height: 50rpx;
				padding-left: 24rpx;

				.name {
					font-size: 38rpx;
					font-weight: 600;
					color: #060F27;
				}

				.role {
					font-size: 28rpx;
					font-weight: 400;
					color: #91949F;
				}
			}
		}

		.menu-list {
			margin-top: 80rpx;

			.menu {
				margin-bottom: 20rpx;
				min-height: 96rpx;
				border-radius: 16rpx;
				background: #FFFFFF;
				padding: 0 28rpx;
				font-size: 24rpx;
				line-height: 96rpx;

				.left {
					text {
						padding-left: 20rpx;
					}
				}
			}
		}

		.logout {
			height: 80rpx;
			width: 686rpx;
			border-radius: 8rpx;
			background: #FFFFFF;
			line-height: 80rpx;
			text-align: center;
			font-size: 28rpx;
			color: #F83838;
			position: relative;
			top: 408rpx;
		}
	}
</style>