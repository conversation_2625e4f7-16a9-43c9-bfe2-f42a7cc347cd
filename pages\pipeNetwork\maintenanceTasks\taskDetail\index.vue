<template>
	<view class="main">
		<u-navbar fixed placeholder safeAreaInsetTop bgColor="#FFFFFF" title="任务详情" :autoBack="true" leftIconSize="20">
			<template #right>
				<view v-if="maintainDevices.mainInfo.status!=='APPROVED'" class="nv-right" @click="complete">
					<text>完成</text>
				</view>
			</template>
		</u-navbar>
		<view class="main-wrapper">
			<view id="arcgisView" class="map-view" :change:locateMark="arcgisView.locate" :locateMark="location.lonLat"
				:change:pipevisible="arcgisView.togglePipeVisible" :pipevisible="pipelayer.currToggled"
				:change:pipeUrl="arcgisView.setPipeUrl" :pipeUrl="pipelayer.pipeServerUrl"
				:change:completed="arcgisView.initCompInfo" :completed="maintainDevices.mainInfo.completed"
				:change:unCompleted="arcgisView.initUnCompInfo" :unCompleted="maintainDevices.mainInfo.unCompleted"
				:change:layername="arcgisView.initTaskLayer" :layername="maintainDevices.mainInfo.layername"
				:change:taskItemsOk="arcgisView.refreshData" :taskItemsOk="maintainDevices.mainInfo.isOK"
				:change:taskType="arcgisView.getDeviceAttributesList" :taskType="maintainDevices.showListType" />
			<view class="cover-view-plus">
				<template v-for="(menu,index) in verticalBar.menus" :key="index">
					<block v-if="index===1">
						<view class="plus-btn" @click="verticalBar.setCurrent(menu.value)">
							<text class="custom-icon btn-img" :class="menu.icon"
								:style="{'color':verticalBar.currentBar === menu.value?'#3862F8':'#060F27'}"></text>
							<text style="font-size: 24rpx;"
								:style="{'color':verticalBar.currentBar === menu.value?'#3862F8':'#060F27'}">{{menu.name}}</text>
						</view>
					</block>
					<!-- <view v-if="index===0" class="btn-line"></view> -->
				</template>

			</view>
			<view class="cover-view-loca" @click="getLocation">
				<button class="cover-loca-image" :loading="location.loading">
					<image class="loca-btn" src="/static/img/icons/location-black.png" mode="widthFix"></image>
				</button>
			</view>
			<view class="cover-view-menu bottom-menu border-box flex-around"
				:class="[maintainDevices.showList&&maintainDevices.showListType!==''?'pull-up':'']"
				@touchstart="touchStart" @touchend="touchEnd">
				<view class="maintain-wrapper">
					<view class="title">
						养护设备
					</view>
					<u-gap height="2" bg-color="#EBEDF6"></u-gap>
					<view class="item" @click="maintainDevices.setShowListType('unCompleted')">
						<text class="item-title">未完成数量</text>
						<text class="warning">{{maintainDevices.mainInfo.unCompleted?.length||0}}</text>
					</view>
					<view class="item" @click="maintainDevices.setShowListType('completed')">
						<text class="item-title">完成数量</text>
						<text class="primary">{{maintainDevices.mainInfo.completed?.length||0}}</text>
					</view>
				</view>
				<scroll-view scroll-y="true"
					v-if="maintainDevices.showListType==='unCompleted'&&maintainDevices.showList" class="maintain-list">

					<view v-for="(item,i) in maintainDevices.maintainUnCompDevices.value" :key="i" class="list-item"
						@click="toDetail(item)">
						<view class="left">
							<view class="info">
								<view class="name">
									{{item.attributes.SID}}
								</view>
							</view>
						</view>
						<view class="right">
							<u-icon name="arrow-right" size="28"></u-icon>
						</view>
					</view>

				</scroll-view>
				<scroll-view scroll-y="true" v-if="maintainDevices.showListType==='completed'&&maintainDevices.showList"
					class="maintain-list">
					<view v-for="(item,i) in maintainDevices.maintainCompDevices.value" :key="i" class="list-item"
						@click="toDetail(item)">
						<view class="left">
							<view class="info">
								<view class="name">
									{{item.attributes.SID}}
								</view>
							</view>
						</view>
						<view class="right">
							<u-icon name="arrow-right" size="28"></u-icon>
						</view>
					</view>
				</scroll-view>
			</view>
			<view v-if="verticalBar.currentBar==='layer'" class="cover-view-menu border-box layer">
				<view class="cover-header">
					<text class="title">选择图层</text>
					<text class="icon" @click="closeCover">x</text>
				</view>
				<scroll-view class="menu-list flex-around" :scroll-y="true">
					<view class="layer-box">
						<text class="layer-title">
							管点类
						</text>
						<view class="layer-menu">
							<view class="menu-item" v-for="(menu,i) in pipelayer.pointLayers" :key="i"
								@click="pipelayer.toggle(menu.layerid)">
								<view class="icon-bg"
									:style="{'background-color':pipelayer.selected.indexOf(menu.layerid)!==-1?'#3862F8':'#E2E3E5'}">
									<text :class="['layer-menu-icon','custom-icon',menu.icon]"
										:style="{'color':pipelayer.selected.indexOf(menu.layerid)!==-1?'#ffffff':'#060F27'}"></text>
								</view>

								<text class="layer-menu-text"
									:style="{'color':pipelayer.selected.indexOf(menu.layerid)!==-1?'#3862F8':'#060F27'}">{{menu.layername}}</text>
							</view>
						</view>
					</view>
					<view class="layer-box">
						<text class="layer-title">
							管线类
						</text>
						<view class="layer-menu">
							<view class="menu-item" v-for="(menu,i) in pipelayer.lineLayers" :key="i"
								@click="pipelayer.toggle(menu.layerid)">
								<view class="icon-bg"
									:style="{'background-color':pipelayer.selected.indexOf(menu.layerid)!==-1?'#3862F8':'#E2E3E5'}">
									<text :class="['layer-menu-icon','custom-icon',menu.icon]"
										:style="{'color':pipelayer.selected.indexOf(menu.layerid)!==-1?'#ffffff':'#060F27'}"></text>
								</view>

								<text class="layer-menu-text"
									:style="{'color':pipelayer.selected.indexOf(menu.layerid)!==-1?'#3862F8':'#060F27'}">{{menu.layername}}</text>
							</view>
						</view>
					</view>
				</scroll-view>
			</view>
		</view>

	</view>
</template>

<script lang="ts">
	import {
		completeMaintenanceTask
	} from '@/common/api/maintenanceTasks'
	import {
		useLocation,
		useVerticalBar,
		usePipeLayers,
		useTouch
	} from '@/common/hooks'
	import {
		useMaintainDevices
	} from '../hooks/useMaintainDevices'

	export default {
		data() {
			return {
				verticalBar: useVerticalBar(),
				location: useLocation(),
				pipelayer: usePipeLayers(),
				maintainDevices: useMaintainDevices(),
				touch: useTouch(),
				mapRendered: false as boolean
			}
		},
		methods: {
			complete() {
				const taskId = this.maintainDevices.mainInfo.taskid
				if (!taskId) return
				if (this.maintainDevices.mainInfo.unCompleted.length) {

					uni.showModal({
						title: '提示',
						content: '当前还有未处理内容，确定完成？',
						cancelText: '完成',
						confirmText: '返回处理',
						success(e) {
							if (e.confirm) {

							} else {
								completeMaintenanceTask(taskId).then((res : any) => {
									uni.showToast({
										title: res.data.code === 200 ? '操作成功' : '操作失败',
										icon: res.data.code === 200 ? 'success' : 'error'
									})
								}).catch(() => {
									uni.showToast({
										title: '操作失败',
										icon: 'error'
									})
								})
							}
						}
					})
				} else {
					uni.showModal({
						title: '提示',
						content: '确定完成？',
						success() {
							completeMaintenanceTask(taskId).then((res : any) => {
								uni.showToast({
									title: res.data.code === 200 ? '操作成功' : '操作失败',
									icon: res.data.code === 200 ? 'success' : 'error'
								})
							}).catch(() => {
								uni.showToast({
									title: '操作失败',
									icon: 'error'
								})
							})
						}
					})
				}
			},
			touchStart(e : any) {
				this.touch.touchStart(e)
			},
			touchEnd(e : any) {
				let that = this
				this.touch.touchEnd(e)
				if (this.touch.directionY === 'up') {
					// 上拉需要等待上拉完成后再
					this.pullTimer = setTimeout(() => {
						that.maintainDevices.setShowList(true)
					}, 500)
				} else if (this.touch.directionY === 'down') {
					clearTimeout(this.pullTimer)
					that.maintainDevices.setShowList(false)
				}
			},
			changeBaseMap(type : string) {
				this.basemap = type
			},
			closeCover() {
				this.verticalBar.setCurrent('')
			},
			getLocation() {
				// this.location.getLocation(() => {
				// 	console.log(this.location.longitude, this.location.latitude);
				// })
				this.location.refreshNum++
			},
			async getLayerInfoFromRenderjs(params : any) {
				//renderjs调用的service层方法  
				this.pipelayer.getLayerInfo(params)
			},
			onMapRendered() {
				this.mapRendered = true
				this.maintainDevices.refreshData()
			},
			showLoading(config : any) {
				uni.showToast({
					title: '加载中...',
					icon: 'loading',
					...(config || {})
				})
			},
			hideLoading() {
				uni.hideToast()
			},
			toDetail(row : any) {
				if (!row?.type) return
				const item = this.maintainDevices.mainInfo[row.type]?.find((item : any) => item.objectId === row
					.attributes?.OBJECTID?.toString())
				if (!item?.id) return
				uni.navigateTo({
					url: './maintainReport?sid=' + row.attributes.SID + '&type=' + this
						.maintainDevices.mainInfo.status + '&item=' +
						encodeURIComponent(JSON.stringify(item))
				})
			},
			setDeviceList(rows : any[]) {
				if (this.maintainDevices.showListType === 'completed') {
					this.maintainDevices.maintainCompDevices.value = rows
				} else if (this.maintainDevices.showListType === 'unCompleted') {
					this.maintainDevices.maintainUnCompDevices.value = rows
				}
			}
		},
		onShow() {
			this.mapRendered && this.maintainDevices.refreshData()
		},
		onLoad(options : any) {
			if (!options?.id || options.layerid === undefined) return
			this.maintainDevices.init({
				layerid: options.layerid,
				taskid: options.id,
				layername: options.layername,
				status: options.status
			})
		},
	}
</script>
<script lang="renderjs" module="arcgisView">
	//renderjs部分
	import {
		initView,
		loadEsriModules,
		getLayerById,
		setSymbol,
		bindViewClick,
		locationIcon,
		userMarker
	} from '../../../../common/utils/arcMapHelper'
	import {
		usePipeQuery
	} from '../../../../common/hooks/map'
	var view = undefined
	var graphicsLayer = undefined
	var textLayer = undefined
	var pipeQuery = usePipeQuery()
	var Point
	var Graphic
	var Symbol
	var SimpleMarkerSymbol
	var Geometry
	var MapImageLayer
	var PictureMarkerSymbol
	export default {
		name: 'arcgisView',
		data() {
			return {
				pipeUrl: '',
				subLayers: [],
				curOper: '',
				taskLayerName: '',
				completed: [],
				unCompleted: []
			};
		},
		methods: {
			initTaskLayer(layer) {
				console.log(layer);
				this.taskLayerName = layer
			},
			initCompInfo(data) {
				this.completed = data || []
			},
			initUnCompInfo(data) {
				this.unCompleted = data || []
			},
			async loadModules() {
				[Point, Graphic, Symbol, SimpleMarkerSymbol, Geometry, MapImageLayer, PictureMarkerSymbol] =
				await loadEsriModules([
					'esri/geometry/Point',
					'esri/Graphic',
					'esri/symbols/Symbol',
					'esri/symbols/SimpleMarkerSymbol',
					'esri/geometry/Geometry',
					'esri/layers/MapImageLayer',
					"esri/symbols/PictureMarkerSymbol"
				])
			},
			// 设置管线查询路径
			setPipeUrl(url) {
				this.pipeUrl = url
			},
			async refreshData() {
				graphicsLayer?.removeAll();
				textLayer?.removeAll();
				this.resolveDevice('completed')
				this.resolveDevice('unCompleted')
			},
			async resolveDevice(type) {
				console.log('resolveDevice');
				if (type === 'completed' && !this.completed.length) return
				if (type === 'unCompleted' && !this.unCompleted.length) return
				if (!this.taskLayerName) return
				const data = type === 'completed' ? this.completed : this.unCompleted
				// const imgUrl = '/static/img/icons/pipe/' + this.taskLayerName + '.png'
				// const imgUrl = '/static/img/icons/dw.png'
				const layerid = this.subLayers.find(item => item.layername === this.taskLayerName)?.layerid
				console.log('layerid:', layerid);
				if (layerid === -1) return
				const objIds = data?.map(item => item.objectId) || []
				try {
					const res = await pipeQuery.excute(this.pipeUrl + '/' + layerid, {
						outSpatialReference: view?.spatialReferencie,
						returnGeometry: true,
						objectIds: objIds,
						outFields: ['*']
					})
					console.log('查询完成');
					console.log('数据查询结果：', res.features);
					const pointSymbol = new PictureMarkerSymbol({
						width: 25,
						height: 25,
						url: locationIcon,
						yoffset: 13,
					})
					const gTexts = []
					res.features = res.features.map(item => {
						item.symbol = item.geometry.type === 'point' ? pointSymbol : setSymbol(item.geometry
							.type)
						const text = (item.attributes?.SID || '') + '(' + (type === 'completed' ?
							'完成' : '未完成') + ')'
						const color = type === 'completed' ? '#67c23a' : '#f56c6c'
						item.attributes = {
							attributes: item.attributes,
							type: type
						}
						const gText = item.clone()

						gText.symbol = setSymbol('text', {
							text: text,
							yoffset: -8,
							color: color
						})
						gTexts.push(gText)
						return item
					})
					/**
					 * toDo: 这里web显示正常，app不能显示，图标文件路径的问题
					 */
					graphicsLayer?.addMany(res.features);
					textLayer?.addMany(gTexts);
					console.log('resolveEnd');
				} catch (e) {
					//TODO handle the exception
					console.log(e);
				}

			},
			getDeviceAttributesList(type) {
				const res = []
				graphicsLayer?.graphics?.map(item => {
					if (item.attributes?.type === type) {
						res.push(item.attributes)
					}
				})
				this.$ownerInstance?.callMethod('setDeviceList', res);

			},
			togglePipeVisible(params) {
				if (!params) return
				const layer = view?.map?.findLayerById('pipe-layer')
				const subLayer = layer?.sublayers.find(item => item.id === params?.id)
				subLayer && (subLayer.visible = params.visible)
			},
			async loadPipeLayer() {
				const pipeLayer = new MapImageLayer({
					id: 'pipe-layer',
					url: this.pipeUrl
				})
				view?.map?.add(pipeLayer)
				await pipeLayer.when()
				this.subLayers = pipeLayer?.sublayers?.items?.map(item => {
					return {
						layername: item.title,
						layerid: item.id
					}
				}) || []
				this.$ownerInstance?.callMethod('getLayerInfoFromRenderjs', this.subLayers.map(item => item.layerid))
				view?.goTo(pipeLayer.fullExtent)
			},
			// locate(newValue) {
			// 	if (!newValue?.[0] && !newValue?.[1]) return
			// 	const g = new Graphic({
			// 		geometry: {
			// 			type: 'point',
			// 			longitude: newValue?.[0],
			// 			latitude: newValue?.[1],
			// 			spatialReference: view?.spatialReference
			// 		},
			// 		symbol: new PictureMarkerSymbol({
			// 			width: 25,
			// 			height: 25,
			// 			url: locationIcon,
			// 			yoffset: 13
			// 		})
			// 	})
			// 	view?.graphics?.removeAll()
			// 	view?.graphics?.add(g)


			// 	view?.goTo({
			// 		zoom: 17,
			// 		target: newValue
			// 	})
			// },
			locate(data) {
				let that = this
				var options = {
					enableHighAccuracy: true,
					timeout: 5000,
					maximumAge: 0,
				};
			
				function success(position) {
					var lng = position.coords.longitude;
					var lat = position.coords.latitude;
					console.log('刷新定位', data);
					// if (data && data.indexOf('auto') != -1) {
					// 	console.log('data', data)
					// 	that.$ownerInstance?.callMethod('handleLocate', [lng, lat])
					// }
					that.$ownerInstance?.callMethod('hideLoading');
			
					if (!lng && !lat) return
					const g = new Graphic({
						geometry: {
							type: 'point',
							longitude: lng,
							latitude: lat,
							spatialReference: view?.spatialReference
						},
						symbol: new PictureMarkerSymbol({
							width: 25,
							height: 25,
							url: userMarker,
							yoffset: 13
						})
					})
					view?.graphics?.removeAll()
					view?.graphics?.add(g)
			
					view?.goTo({
						zoom: 17,
						target: g
					})
				}
			
				function error(err) {
					console.warn("ERROR(" + err.code + "): " + err.message);
				}
			
				navigator.geolocation.getCurrentPosition(success, error, options);
			},
			resolveClick(response) {
				const attributes = response.results?.[0]?.graphic?.attributes
				if (!attributes?.attributes?.OBJECTID) return
				this.$ownerInstance?.callMethod('toDetail', attributes);
			},

		},
		async mounted() {
			let that = this
			try {
				this.$ownerInstance?.callMethod('showLoading');
				const v = await initView('arcgisView');
				view = v;
				await this.loadModules();
				await this.loadPipeLayer();
				graphicsLayer = await getLayerById(view, 'device-layer');
				textLayer = await getLayerById(view, 'text-layer');

				pipeQuery.init().then(() => {
					console.log('pipeQuery inited');
					this.$ownerInstance?.callMethod('onMapRendered');
				})
				bindViewClick(view, this.resolveClick)
			} catch (e) {
				//TODO handle the exception
			}
			this.$ownerInstance?.callMethod('hideLoading');
		}
	}
</script>
<style scoped lang="scss">
	.warning {
		color: #F8A038;
	}

	.primary {
		color: #3862F8;
	}

	.main {
		// width: 750rpx;
		height: 100vh;

		// 主体内容
		.main-wrapper {
			position: relative;
			height: calc(100% - var(--status-bar-height));

			.map-view {
				height: 100%;
			}
		}

		.cover-view-plus {
			width: 80rpx;
			background-color: #ffffff;
			border-radius: 20rpx;
			position: absolute;
			top: 62rpx;
			right: 32rpx;
			display: flex;
			flex-wrap: wrap;
			align-items: center;
			justify-content: center;
			padding: 8rpx 16rpx;

			.plus-btn {
				width: 48rpx;
				height: 96rpx;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;

				.btn-img {
					font-size: 48rpx;
				}
			}

			.btn-line {
				width: 80rpx;
				height: 1rpx;
				border-width: 1rpx;
				border-style: solid;
				border-color: #EBEDF6;
			}

			.btn-img {
				font-size: 48rpx;
			}
		}

		.cover-view-loca {
			width: 80rpx;
			height: 80rpx;
			background-color: #ffffff;
			border-radius: 16rpx;
			position: absolute;
			bottom: 308rpx;
			right: 32rpx;
			display: flex;
			align-items: center;
			justify-content: center;

			.cover-loca-image {
				width: 40rpx;
				height: 40rpx;
				background-color: transparent;
				border: none;
				padding: 0;
				line-height: 40rpx;

				&::after {
					border: none;
					width: 40rpx;
					height: 40rpx;
					transform: scale(1);
				}

				.loca-btn {
					width: 40rpx;
					height: 40rpx;
				}
			}
		}

		.cover-view-menu {
			/* width: 750rpx; */
			position: absolute;
			bottom: 0;
			background-color: #FBFBFB;

			&.pull-up {
				height: 660rpx;
			}

			// z-index: 99999;
			&.bottom-menu {
				padding: 32rpx;
				padding-bottom: 16rpx;
				display: block;

				.maintain-wrapper {
					background: #f5f5f5;
					border-radius: 16rpx;
					width: 686rpx;
					height: 220rpx;
					padding: 0 16rpx 16rpx 16rpx;
					margin-bottom: 20rpx;

					.title {
						font-family: 'Poppins';
						font-style: normal;
						font-weight: 400;
						font-size: 12px;
						display: flex;
						align-items: center;
						text-align: center;
						color: #060F27;
						height: 60rpx;
						justify-content: center;
					}

					.item {
						height: 70rpx;
						display: flex;
						justify-content: space-between;
						align-items: center;
						color: #91949F;
					}
				}

				.maintain-list {
					padding: 16rpx 0;
					height: 350rpx;
					background: #f5f5f5;
					border-radius: 16rpx;

					.list-item {
						padding: 0 16rpx;
						height: 70rpx;
						line-height: 60rpx;
						display: flex;
						justify-content: space-between;
						align-items: center;
						color: #91949F;
					}
				}
			}

			&.baselayer,
			&.layer {
				height: 320rpx;
				width: 100%;
				padding: 0 32rpx;
				border-radius: 16rpx 16rpx 0rpx 0;



				.cover-main {
					display: flex;
					justify-content: space-between;
					flex-wrap: nowrap;
					height: 200rpx;

					.item {
						width: calc(50% - 20rpx);
						height: 100%;
						position: relative;

						.item-image {
							width: 100%;
							height: 100%;
							border-radius: 8px;
						}

						.item-text {
							background: rgba(255, 255, 255, 0.8);
							border-radius: 0px 0px 8px 8px;
							width: 100%;
							position: absolute;
							bottom: 0;
							left: 0;
							height: 48rpx;
							line-height: 48rpx;
							padding: 0 20rpx;
							font-size: 24rpx;
						}
					}
				}
			}

			&.baselayer {
				height: 320rpx;
			}

			&.layer {
				height: 800rpx;
				overflow: hidden;

				.menu-list {
					height: 700rpx;
				}
			}

			.cover-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				height: 86rpx;

				.title {
					text-align: left;
					word-break: keep-all;
				}

				.icon {
					font-size: 1.2em;
				}
			}

			&.layer {}

			&.area {}

			&.distance {}

			transition: all 0.5s ease-in-out;
		}

		.bottom-menu {}

		.border-box {
			width: 100%;
			padding-top: 30rpx;
			// z-index: 99999;
			background-color: #FBFBFB;
			position: absolute;
			bottom: 0;
			align-items: center;
			justify-content: space-around;
			// box-shadow: 0 4rpx 30rpx rgba(178, 183, 199, 0.5);
			border-radius: 16rpx 16rpx 0rpx 0;

			.menu-list {
				flex-direction: column;
				display: flex;
				justify-content: space-around;
				width: 100%;

				.layer-box {
					width: 100%;
					padding: 0 32rpx 25rpx;

					.layer-title {
						color: #91949F;
					}

					.layer-menu {
						display: flex;
						justify-content: flex-start;
						align-items: center;
						flex-wrap: wrap;
						border-radius: 8px;
						background-color: #ffffff;
						margin-top: 20rpx;
						padding-bottom: 20rpx;




					}
				}
			}
		}

	}

	.menu-item {
		width: 20%;
		align-items: center;
		padding: 25rpx 0 0;
		text-align: center;
		display: flex;
		justify-content: center;
		flex-direction: column;

		.icon-bg {
			border-radius: 50%;
			width: 80rpx;
			height: 80rpx;
			display: flex;
			align-items: center;
			justify-content: center;

			&.round-rect {
				border-radius: 16rpx;
			}
		}

		.layer-menu-text {
			word-break: keep-all;

		}
	}

	:deep(.esri-view .esri-view-surface--inset-outline) {
		&:focus::after {
			display: none;
		}
	}
</style>