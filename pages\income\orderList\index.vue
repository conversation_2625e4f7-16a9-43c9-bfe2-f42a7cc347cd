<template>
  <view class="container">
    <!-- 搜索框 -->
    <view class="search-box">
      <u-search
        v-model="searchText"
        :show-action="false"
        placeholder="输入用户编号或用户名"
        height="70"
        :clearabled="true"
      ></u-search>
      <!-- <u-dropdown class="u-dropdown">
        <u-dropdown-item v-model="selectedMonth" :title="selectedMonth" :options="monthOptions"></u-dropdown-item>
      </u-dropdown> -->
      <view class="selected-month" @click="showPicker = true">{{
        selectedMonth
      }}</view>
      <u-icon
        :name="showPicker ? 'arrow-up' : 'arrow-down'"
        size="28"
        color="#666"
      ></u-icon>
    </view>
    <u-picker
      mode="time"
      v-model="showPicker"
      :params="params"
      @confirm="onConfirm"
    ></u-picker>

    <!-- 账单列表 -->
    <scroll-view scroll-y class="order-list">
      <view v-for="(order, index) in orderList" :key="index" class="order-item">
        <view class="order-header">
          <view class="date-box">
            <text class="year-month">{{ order.orderDate }}</text>
          </view>
          <view class="user-info">
            <text class="user-id">{{ order.userInfo.userNo }}</text>
            <text class="separator">|</text>
            <text class="user-name">{{ order.userInfo.userName }}</text>
          </view>
          <view class="global-icon">
            <u-icon name="global" size="40" color="#3862F8"></u-icon>
          </view>
        </view>
        <view class="order-content">
          <view class="amount">
            <text class="amount-value">{{ order.orderAmount }}</text>
            <text class="unit">元</text>
          </view>
          <view class="order-time">{{ order.orderTime }}</view>
          <navigator
            class="detail-link"
            :url="'/pages/income/orderDetail/index?id=' + order.userInfo.userNo"
          >
            <text>查看详情</text>
            <u-icon name="arrow-right" size="28"></u-icon>
          </navigator>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import dayjs from "dayjs";
import "dayjs/locale/zh-cn";

const searchText = ref<string>("");
const selectedMonth = ref<string>("");
// 默认选中最近月份
selectedMonth.value = dayjs().format("YYYY年MM月");
// const monthOptions = [
//   { label: '2024年5月', value: '2024年5月' },
//   { label: '2024年4月', value: '2024年4月' },
//   { label: '2024年3月', value: '2024年3月' },
// ];
const showPicker = ref<boolean>(false);
// 时间选择器参数
const params = ref({
  year: true,
  month: true,
  day: false,
  hour: false,
  minute: false,
  second: false,
});

const orderList = reactive([
  {
    orderDate: "2024年09月",
    userInfo: {
      userNo: "10017166",
      userName: "王雪琪",
    },
    orderAmount: "82.50",
    orderTime: "2024-09-10 10:47:21",
  },
  {
    orderDate: "2024年09月",
    userInfo: {
      userNo: "10017166",
      userName: "王雪琪",
    },
    orderAmount: "82.50",
    orderTime: "2024-09-10 10:47:21",
  },
]);
// 处理月份选择确认事件
const onConfirm = (value: any) => {
  console.log("Selected month:", value);
  // 更新选中的月份
  selectedMonth.value = value.year + "年" + value.month + "月";
  showPicker.value = false;
};
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background-color: #f5f6f7;
  padding: 20rpx;
}

.search-box {
  display: flex;
  background: #ffffff;
  padding: 20rpx;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  align-items: center;

  .selected-month {
    margin-left: 20rpx;
    font-size: 28rpx;
    color: #666;
    cursor: pointer;
  }
}

.order-list {
  height: calc(100vh - 200rpx);
}

.order-item {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.order-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;

  .date-box {
    background: #e5e6eb;
    padding: 10rpx 20rpx;
    border-radius: 8rpx;
  }

  .user-info {
    flex: 1;
    margin: 0 20rpx;

    .separator {
      margin: 0 10rpx;
      color: #999;
    }
  }
}

.order-content {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .amount {
    .amount-value {
      font-size: 40rpx;
      font-weight: bold;
      color: #333;
    }

    .unit {
      font-size: 24rpx;
      color: #666;
      margin-left: 4rpx;
    }
  }

  .order-time {
    color: #999;
    font-size: 24rpx;
  }

  .detail-link {
    display: flex;
    align-items: center;
    color: #3862f8;

    text {
      margin-right: 4rpx;
    }
  }
}
</style>
