/**
 * 查询缓存管理器
 * 提供查询结果缓存和性能优化
 */

interface CacheItem {
  key: string
  data: any
  timestamp: number
  expireTime: number
  hitCount: number
}

interface CacheStats {
  totalQueries: number
  cacheHits: number
  cacheMisses: number
  hitRate: number
  totalCacheSize: number
  itemCount: number
}

export class QueryCacheManager {
  private cache: Map<string, CacheItem> = new Map()
  private maxCacheSize: number = 100 // 最大缓存项数
  private defaultTTL: number = 5 * 60 * 1000 // 5分钟默认过期时间
  private stats: CacheStats = {
    totalQueries: 0,
    cacheHits: 0,
    cacheMisses: 0,
    hitRate: 0,
    totalCacheSize: 0,
    itemCount: 0
  }

  constructor(options?: {
    maxCacheSize?: number
    defaultTTL?: number
  }) {
    if (options?.maxCacheSize) {
      this.maxCacheSize = options.maxCacheSize
    }
    if (options?.defaultTTL) {
      this.defaultTTL = options.defaultTTL
    }
    
    // 定期清理过期缓存
    this.startCleanupTimer()
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(type: string, params: any): string {
    const sortedParams = this.sortObject(params)
    const paramsStr = JSON.stringify(sortedParams)
    return `${type}:${this.hashString(paramsStr)}`
  }

  /**
   * 对象排序（确保相同参数生成相同的键）
   */
  private sortObject(obj: any): any {
    if (obj === null || typeof obj !== 'object') {
      return obj
    }
    
    if (Array.isArray(obj)) {
      return obj.map(item => this.sortObject(item)).sort()
    }
    
    const sorted: any = {}
    Object.keys(obj).sort().forEach(key => {
      sorted[key] = this.sortObject(obj[key])
    })
    
    return sorted
  }

  /**
   * 简单哈希函数
   */
  private hashString(str: string): string {
    let hash = 0
    if (str.length === 0) return hash.toString()
    
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }
    
    return Math.abs(hash).toString(36)
  }

  /**
   * 获取缓存
   */
  get(type: string, params: any): any | null {
    this.stats.totalQueries++
    
    const key = this.generateCacheKey(type, params)
    const item = this.cache.get(key)
    
    if (!item) {
      this.stats.cacheMisses++
      this.updateHitRate()
      return null
    }
    
    // 检查是否过期
    if (Date.now() > item.expireTime) {
      this.cache.delete(key)
      this.stats.cacheMisses++
      this.updateHitRate()
      return null
    }
    
    // 更新命中统计
    item.hitCount++
    this.stats.cacheHits++
    this.updateHitRate()
    
    return item.data
  }

  /**
   * 设置缓存
   */
  set(type: string, params: any, data: any, ttl?: number): void {
    const key = this.generateCacheKey(type, params)
    const expireTime = Date.now() + (ttl || this.defaultTTL)
    
    const item: CacheItem = {
      key,
      data,
      timestamp: Date.now(),
      expireTime,
      hitCount: 0
    }
    
    // 如果缓存已满，删除最少使用的项
    if (this.cache.size >= this.maxCacheSize) {
      this.evictLeastUsed()
    }
    
    this.cache.set(key, item)
    this.updateStats()
  }

  /**
   * 删除缓存
   */
  delete(type: string, params: any): boolean {
    const key = this.generateCacheKey(type, params)
    const deleted = this.cache.delete(key)
    if (deleted) {
      this.updateStats()
    }
    return deleted
  }

  /**
   * 清空缓存
   */
  clear(): void {
    this.cache.clear()
    this.resetStats()
  }

  /**
   * 清空指定类型的缓存
   */
  clearByType(type: string): void {
    const keysToDelete: string[] = []
    
    this.cache.forEach((item, key) => {
      if (key.startsWith(`${type}:`)) {
        keysToDelete.push(key)
      }
    })
    
    keysToDelete.forEach(key => {
      this.cache.delete(key)
    })
    
    this.updateStats()
  }

  /**
   * 淘汰最少使用的缓存项
   */
  private evictLeastUsed(): void {
    let leastUsedKey = ''
    let leastHitCount = Infinity
    let oldestTime = Infinity
    
    this.cache.forEach((item, key) => {
      if (item.hitCount < leastHitCount || 
          (item.hitCount === leastHitCount && item.timestamp < oldestTime)) {
        leastUsedKey = key
        leastHitCount = item.hitCount
        oldestTime = item.timestamp
      }
    })
    
    if (leastUsedKey) {
      this.cache.delete(leastUsedKey)
    }
  }

  /**
   * 清理过期缓存
   */
  private cleanupExpired(): void {
    const now = Date.now()
    const keysToDelete: string[] = []
    
    this.cache.forEach((item, key) => {
      if (now > item.expireTime) {
        keysToDelete.push(key)
      }
    })
    
    keysToDelete.forEach(key => {
      this.cache.delete(key)
    })
    
    if (keysToDelete.length > 0) {
      this.updateStats()
    }
  }

  /**
   * 启动清理定时器
   */
  private startCleanupTimer(): void {
    setInterval(() => {
      this.cleanupExpired()
    }, 60000) // 每分钟清理一次
  }

  /**
   * 更新命中率
   */
  private updateHitRate(): void {
    if (this.stats.totalQueries > 0) {
      this.stats.hitRate = this.stats.cacheHits / this.stats.totalQueries
    }
  }

  /**
   * 更新统计信息
   */
  private updateStats(): void {
    this.stats.itemCount = this.cache.size
    this.stats.totalCacheSize = this.calculateCacheSize()
    this.updateHitRate()
  }

  /**
   * 计算缓存大小（估算）
   */
  private calculateCacheSize(): number {
    let size = 0
    this.cache.forEach(item => {
      try {
        size += JSON.stringify(item.data).length
      } catch (e) {
        size += 1000 // 估算值
      }
    })
    return size
  }

  /**
   * 重置统计信息
   */
  private resetStats(): void {
    this.stats = {
      totalQueries: 0,
      cacheHits: 0,
      cacheMisses: 0,
      hitRate: 0,
      totalCacheSize: 0,
      itemCount: 0
    }
  }

  /**
   * 获取缓存统计信息
   */
  getStats(): CacheStats {
    this.updateStats()
    return { ...this.stats }
  }

  /**
   * 获取缓存详情
   */
  getCacheDetails(): Array<{
    key: string
    size: number
    hitCount: number
    age: number
    ttl: number
  }> {
    const now = Date.now()
    const details: Array<any> = []
    
    this.cache.forEach((item, key) => {
      try {
        const size = JSON.stringify(item.data).length
        const age = now - item.timestamp
        const ttl = item.expireTime - now
        
        details.push({
          key: key.substring(0, 50) + (key.length > 50 ? '...' : ''),
          size,
          hitCount: item.hitCount,
          age,
          ttl
        })
      } catch (e) {
        // 忽略序列化错误
      }
    })
    
    return details.sort((a, b) => b.hitCount - a.hitCount)
  }

  /**
   * 预热缓存
   */
  async warmup(queries: Array<{
    type: string
    params: any
    queryFn: () => Promise<any>
  }>): Promise<void> {
    const promises = queries.map(async query => {
      try {
        const result = await query.queryFn()
        this.set(query.type, query.params, result)
      } catch (error) {
        console.warn(`缓存预热失败 ${query.type}:`, error)
      }
    })
    
    await Promise.allSettled(promises)
  }

  /**
   * 导出缓存数据
   */
  export(): string {
    const exportData = {
      cache: Array.from(this.cache.entries()),
      stats: this.stats,
      timestamp: Date.now()
    }
    
    return JSON.stringify(exportData)
  }

  /**
   * 导入缓存数据
   */
  import(data: string): boolean {
    try {
      const importData = JSON.parse(data)
      
      if (!importData.cache || !Array.isArray(importData.cache)) {
        return false
      }
      
      this.clear()
      
      importData.cache.forEach(([key, item]: [string, CacheItem]) => {
        // 检查数据是否仍然有效
        if (Date.now() < item.expireTime) {
          this.cache.set(key, item)
        }
      })
      
      this.updateStats()
      return true
    } catch (error) {
      console.error('导入缓存数据失败:', error)
      return false
    }
  }
}

// 创建全局缓存管理器实例
export const queryCache = new QueryCacheManager({
  maxCacheSize: 200,
  defaultTTL: 5 * 60 * 1000 // 5分钟
})

/**
 * 缓存装饰器
 * 自动为查询函数添加缓存功能
 */
export function cached(type: string, ttl?: number) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value
    
    descriptor.value = async function (...args: any[]) {
      const params = args[0] || {}
      
      // 尝试从缓存获取
      const cachedResult = queryCache.get(type, params)
      if (cachedResult !== null) {
        return cachedResult
      }
      
      // 执行原方法
      const result = await method.apply(this, args)
      
      // 缓存结果
      if (result && result.success) {
        queryCache.set(type, params, result, ttl)
      }
      
      return result
    }
    
    return descriptor
  }
}

/**
 * 批量缓存操作
 */
export class BatchCacheOperations {
  private operations: Array<{
    type: 'set' | 'delete' | 'clear'
    params: any[]
  }> = []

  /**
   * 添加设置操作
   */
  set(type: string, params: any, data: any, ttl?: number): this {
    this.operations.push({
      type: 'set',
      params: [type, params, data, ttl]
    })
    return this
  }

  /**
   * 添加删除操作
   */
  delete(type: string, params: any): this {
    this.operations.push({
      type: 'delete',
      params: [type, params]
    })
    return this
  }

  /**
   * 添加清空操作
   */
  clear(): this {
    this.operations.push({
      type: 'clear',
      params: []
    })
    return this
  }

  /**
   * 执行所有操作
   */
  execute(): void {
    this.operations.forEach(operation => {
      switch (operation.type) {
        case 'set':
          queryCache.set(...operation.params)
          break
        case 'delete':
          queryCache.delete(...operation.params)
          break
        case 'clear':
          queryCache.clear()
          break
      }
    })
    
    this.operations = []
  }
}

/**
 * 创建批量操作实例
 */
export const createBatchCacheOperations = () => new BatchCacheOperations()