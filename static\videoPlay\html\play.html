<html dir="ltr" mozdisallowselectionprint>
	<head>
		<meta charset="utf-8">
		<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
		<meta name="google" content="notranslate">
		<title>视频监控</title>
		<div id="dplayer" style="width: 100%; height: 40%"></div>
		<script src="./hls.min.js"></script>
		<script src="./DPlayer.min.js"></script>
		<script>
			document.addEventListener('UniAppJSBridgeReady', function() {
				document.querySelector('.btn-list').addEventListener('click', function(evt) {
					var target = evt.target;
					if (target.tagName === 'BUTTON') {
						var action = target.getAttribute('data-action');
						switch (action) {
							case 'switchTab':
								uni.switchTab({
									url: '/pages/index/index'
								});
								break;
							case 'navigateBack':
								uni.navigateBack({
									delta: 1
								});
								break;
							case 'postMessage':
								uni.postMessage({
									data: {
										action: getQuery('data'),
									}
								});
								break;
							case 'getEnv':
								uni.getEnv(function(res) {
									alert('当前环境：' + JSON.stringify(res));
								});
								break;
							default:
								uni[action]({
									url: '/pages/editImg/editImg'
								});
								break;
						}
					}
				});
			});

			console.log('dadada', getQuery('data')); //获取 uni-app 传来的值

			function getQuery(name) {
				// 正则：[找寻'&' + 'url参数名字' = '值' + '&']（'&'可以不存在）
				let reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
				let r = window.location.search.substr(1).match(reg);
				console.log('rrr',r)
				if (r != null) {
					// 对参数值进行解码
					return unescape(r[2]);
				}
				return null;
			}

			var query = window.location.search;
			console.log('dddd', query)
			const dp = new DPlayer({
				container: document.getElementById('dplayer'),
				loop: false,
				autoplay: true, // 自动播放
				preventClickToggle: true,
				live: true, // 直播模式
				mutex: false, // 多个视频播放(false允许/true不允许)
				video: {
					url: getQuery('data'),
					type: 'customHls',
					customType: {
						customHls: function(video, player) {
							const hls = new Hls();
							hls.loadSource(video.src);
							hls.attachMedia(video);
						},
					},
				},
			})
		</script>
		</body>
</html>
