<template>
	<view class="main">
		<u-navbar fixed placeholder safeAreaInsetTop bgColor="#FFFFFF" title="消息中心" :autoBack="true" leftIconSize="20">
			<template #right>
				<view class="nv-right" @click="clearAll">
					<image src="/static/img/icons/clear.png" style="height: 40rpx;width: 40rpx;"></image>
				</view>
			</template>
		</u-navbar>
		<u-sticky :offset-top="state.barHeight" :h5-nav-height="30">
			<u-tabs :list="state.tabList" font-size="30" bold bar-width="160" :is-scroll="false"
				v-model="state.currentTabNum" :offset="[0,140]" @change="chooseTab"></u-tabs>
		</u-sticky>
		<view class="card-list">
			<scroll-view :lower-threshold="50" :refresher-threshold="50" scroll-y refresher-default-style="white"
				:refresher-triggered="triggered" @scrolltolower="showMoreData" style="height: 95vh;">
				<view class="warning-item" v-for="(data,index) in tableData" :key="index">
					<view @click="toList(data.id)">
						<view class="top flex-center">
							<view class="bg-box"
								:style="{background: state.currentTab.value==0?'#2979ff':state.currentTab.value==1? '#ff0000':'#F87D38'}">
								<u-icon size='15' name="bell" color="#FFFFFF"></u-icon>
							</view>
							<text>{{proxy.formatTime(data.time) || '-'}}</text>
						</view>
						<view class="content" :style="{color:data.status==='1'?'#b1b1b1':'#000'}">
							<!-- <view>{{data.fromUser}}</view> -->
							<view>{{data.content}}</view>
						</view>
					</view>
				</view>
				<u-loadmore bg-color="F9F9F9" :status="state.status" loading-text="努力加载中" loadmore-text="加载更多"
					nomore-text="没有了更多" />
			</scroll-view>
		</view>
		<u-toast ref="refToast"></u-toast>
	</view>
</template>

<script lang="ts" setup>
	import {
		removeSlash
	} from '@/common/utils/removeIdSlash'

	import {
		reactive,
		getCurrentInstance,
		ref,
		onBeforeMount
	} from "vue";
	import {
		onPullDownRefresh,
		onShow
	} from '@dcloudio/uni-app'
	import {
		systemNotifyList,
		getSystemNotifyCount,
		readOne,
		readAll
	} from '@/common/api/system'
	import dayjs from "dayjs";
	const {
		proxy
	} = getCurrentInstance()
	const refToast = ref < any > ()
	const current = ref < number > (2)
	const userInfo = ref < any > ({})
	const state = reactive < {
		currentProjectId: string,
		tabList: any,
		currentTabNum: number
		currentTab: any,
		status: string,
		barHeight: number,
		page: number
	} > ({
		currentProjectId: '',
		tabList: [{
			name: '通知',
			value: '0',
			count: 0
		}, {
			name: '报警',
			value: '1',
			count: 0
		}, {
			name: '待办',
			value: '2',
			count: 0
		}],
		currentTabNum: 0,
		currentTab: {
			name: '通知',
			value: '0',
			count: 0
		},
		status: 'loading',
		page: 1,
		barHeight: 60,
	})
	const tableData = ref < any > ([])
	let info = uni.getSystemInfoSync()
	//顶部高度
	state.barHeight = info.statusBarHeight + 20
	const triggered = ref < Boolean > (false)
	// 选择标签
	const chooseTab = (index: number) => {
		state.currentTab = state.tabList[index]
		state.page = 1
		tableData.value = []
		refreshData()
	}

	//清楚消息
	const clearAll = () => {
		readAll({
			type: state.currentTab.value,
			to: removeSlash(userInfo.value.id?.id)
		}).then(res => {
			refToast.value.show({
				title: '清除成功',
				type: 'success',
				position: 'bottom',
				callback: () => {
					refreshData()
				}
			})
		})
	}

	const toList = (id: string) => {
		readOne({
			id: id
		})
		let url = ''
		if (state.currentTab.value === '0') {
			url = '/pages/smartOperation/workOrderTask/index'
		} else if (state.currentTab.value === '1') {
			// url = '/pages/wisdomProduce/exceptionalManageV2/index'
			url = '/pages/wisdomProduce/exceptionalManageV3/index'
		} else if (state.currentTab.value === '2') {
			url = '/pages/smartOperation/workOrderAssignment/index'
		}
		uni.navigateTo({
			url: url
		})
	}

	const refreshData = async () => {
		userInfo.value = uni.getStorageSync('userInfo')
		getSystemNotifyCount({
			to: removeSlash(userInfo.value.id?.id),
			status: 0
		}).then((res: any) => {
			console.log(res.data.data)
			state.tabList[0].count = res.data?.data['0'] || 0
			state.tabList[1].count = res.data.data['1'] || 0
			state.tabList[2].count = res.data.data['2'] || 0
		})

		const params = {
			size: 10,
			page: state.page || 1,
			to: removeSlash(userInfo.value.id?.id),
			type: state.currentTab.value,
			status: 0,
			// beginTime: dayjs().startOf('year').format('YYYY-MM-DD'),
			// endTime: dayjs().format('YYYY-MM-DD'),
		}
		const res = await systemNotifyList(params)
		const data = res.data.data?.data || []
		const total = res.data.data?.total || 0
		if (data.length > 0) {
			if (state.page === 1) {
				tableData.value = data
			} else {
				tableData.value = tableData.value.concat(data)
			}
			state.page += 1
			state.status = 'loadmore'
			if (data.length === total) {
				state.status = 'nomore'
			}
		} else {
			state.status = 'nomore'
		}
		uni.stopPullDownRefresh()
		triggered.value = false
	}

	// 加载更多
	const showMoreData = async () => {
		state.status = 'loading'
		await refreshData()
	}

	// 下拉刷新
	onPullDownRefresh(async () => {
		triggered.value = true
		await refreshData()
		triggered.value = false
	})

	onBeforeMount(async () => {
		userInfo.value = uni.getStorageSync('userInfo')
	})


	onShow(async () => {
		state.page = 1
		refreshData()
	})
</script>

<style lang="scss" scoped>
	.main {
		padding-bottom: 10rpx !important;
	}

	.nv-right {
		border-radius: 50%;
		width: 40rpx;
		height: 40rpx;
		margin-right: 20rpx;
	}

	.card-list {
		.warning-item {
			min-height: 176rpx;
			width: 686rpx;
			border-radius: 16rpx;
			margin: 0 auto;
			margin-top: 20rpx;
			background-color: #FFFFFF;
			font-size: 24rpx;
			padding: 24rpx 28rpx;

			.top {
				color: #91949F;

				.bg-box {
					height: 32rpx;
					width: 32rpx;
					border-radius: 4rpx;
					background: #F87D38;
					text-align: center;
					line-height: 32rpx;
				}

				text {
					padding-left: 20rpx;
				}
			}

			.content {
				padding-top: 20rpx;
				text-overflow: -o-ellipsis-lastline;
				overflow: hidden; //溢出内容隐藏
				text-overflow: ellipsis; //文本溢出部分用省略号表示
				display: -webkit-box; //特别显示模式
				-webkit-line-clamp: 2; //行数
				line-clamp: 2;
				-webkit-box-orient: vertical;
				color: #060F27;
			}
		}
	}
</style>
