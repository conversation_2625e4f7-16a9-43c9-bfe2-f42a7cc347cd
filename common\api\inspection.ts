import { http } from '../vmeitime-http/index'

export const getCircuitTaskList = (params : {
	page : number,
	size : number,
	type : String,
	projectId : String,
	status ?: String,
	executionUserId : String
}) => {
	return http().get('api/sp/circuitTask', params)
}

export const getCircuitTaskItemList = (params : {
	mainId : String,
	page : number,
	size : number,
	type ?: String,
}) => {
	return http().get('api/circuitTaskItem', params)
}

export const receiveCircuitTask = (id : String) => {
	return http().post(`api/sp/circuitTask/${id}/receive`)
}

// 完成子任务
export const completeCircuitTask = (id : String, params : {
	workOderId ?: String,
	result : String,
	file ?: String
}) => {
	return http().post(`api/circuitTaskItem/${id}/complete`, params)
}

// 申请审核
export const sendVerifyTask = (id : String, auditUserId : String) => {
	return http().post(`/api/sp/circuitTask/${id}/sendVerify/${auditUserId}`)
}

// 故障上报
export const faultReport = (params : any) => {
	return http().post('api/fault/report', params)
}


// 处理中和完成统计
export const getFaultType = (params:any) => {
	return http().get('api/fault/type',params)
}

// 处理中和完成统计
export const processingAndCompleteCount = () => {
	return http().get('api/sp/circuitTask/processingAndCompleteCount')
}
/**
 * 查询最新用户坐标列表
 * @param params
 * @returns
 */
export const getLatestUserCoords = (params : {
	userName ?: string
	departmentId ?: string
	userTypeId ?: string
	status ?: string
	page ?: number
	size ?: number
	fromTime ?: string
	toTime ?: string
}) => {
	return http().get('api/userCoordinate/newest', params)
}
/**
 * 上传用户最新定位信息
 */
export const updateUserCoords = (params : any) => {
	return http().post('api/userCoordinate', params)
}
/**
 * 查询养护任务设备、附件
 * @param params
 * @returns
 */
export const getMaintainTaskItems = (params : {
	taskId ?: string
	isComplete ?: boolean
}) => {
	return http().get('api/sm/maintainTaskItem', params)
}
/**
 * 查询区域/路径点JSON
 * @param id
 * @returns
 */
export const GetDistrictPointsJson = (id : string) => {
	return http().get(`api/sm/circuitDistrictArea/${id}/points`)
}
/**
 * 查询区域/路线下的关键点
 * @param params
 * @returns
 */
export const GetKeyPoint = (params : { areaId : string }) => {
	return http().get('api/sm/circuitDistrictPoint', params)
}
/**
 * 查询单个关键点
 * @param id 关键点的id
 */
export const GetSingleKeyPoint = (id : string) => {
	return http().get('api/sm/circuitDistrictPoint/' + id)
}
/**
 * 查询巡检任务的设备
 * @param params: {taskCode: 任务code,type: 设备类型：关键点、设备、专项设备,不传type查所有类型}
 */
export const getInspectionDevices = (params : { taskCode : string, type ?: string }) => {
	return http().get('api/sm/circuitTaskReport', params)
}
/**
 * 上报巡检工单
 */
export const postInspectionWorkOrder = (params : any) => {
	return http().post('api/sm/circuitTask/workOrder', params)
}
/**
 * 查询用户巡检的轨迹坐标数据
 */
export const getInspectionLocus = (params : { page ?: number, size ?: number, formTime ?: number, toTime ?: number, taskCode : string }) => {
	return http().get('/api/sm/circuitTaskCoordinate', {
		page: 1,
		size: -1,
		...params
	})
}
/**
 * 上报用户巡检实时坐标
 */
export const postInspectionCoordinates = (params : {
	taskCode : string
	x : number
	y : number
}) => {
	return http().post('/api/sm/circuitTaskCoordinate', params)
}
/**
 * 完成巡检任务
 */
export const completeInspectionTask = (id : string) => {
	return http().post(`api/sm/circuitTask/${id}/complete`)
}

/**
 * 巡检分派
 */
export const assignmentInspectionTask = (params:{
	receiveUserId:string
	collaborateUserId:string
}) => {
	return http().post(`api/sm/circuitTask/assign`,params)
}
/**
 * 查询巡检设备的上报信息
 */
export const getInspectionReportRecord = (taskCode : string, pointId : string) => {
	return http().get(`api/sm/circuitTask/workOrder/pitfall/${taskCode}/${pointId}`)
}
/**
 * 查询带单位部门信息的巡检人员树
 */
export const getInspectionUserWithDepartmentTree = (type : string) => {
	return http().get('api/sm/circuitTask/user/group/' + type)
}
/**
 * 查询巡检人员统计
 */
export const getInspectionUserStatistic = (type : string) => {
	return http().get('api/sm/circuitTask/user/statistic/' + type)
}
/**
 * 查询管网巡检任务列表
 */
export const getInspectionTaskList = (params ?: any) => {
	return http().get('api/sm/circuitTask', params)
}
/**
 * 查询管网巡检任务完成率
 */
export const getInspectionTaskComplete = () => {
	return http().get('api/sm/circuitTask/completeCount/global')
}
/**
 * 查询抄表员统计
 */
export const getMeterReaderStatistic = () => {
	return http().get('/api/sm/circuitTask/copyUser/statistic')
}
/**
 * 查询巡检工单
 */
export const getInspectionWorkOrder = (params : { processUserId ?: string; statusStage ?: string; type ?: string; keyword ?: string }) => {
	return http().get('api/sm/circuitTask/workOrder/pitfall', params)
}
/**
 * 查询巡检表单项
 */
export const getInspectionWorkTaskList = (id : string) => {
	return http().get(`api/sm/circuitTaskFormRecord/task/${id}`)
}
/**
 * 填写巡检表单项
 */
export const postInspectionWorkTask = (id : string, params : any) => {
	return http().post(`api/sm/circuitTaskFormRecord/${id}/checkResult`, params)
}

