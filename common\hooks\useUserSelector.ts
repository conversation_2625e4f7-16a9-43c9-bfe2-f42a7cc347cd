import { ref } from 'vue'
import {
	onShow
} from '@dcloudio/uni-app';
import {
	useStore
} from '@/store/index'
export const useUserSelector = () => {
	const store = useStore();
	const users = ref<any[]>([])
	/**
	 * 跳转到选择用户界面
	 */
	const toChooseUser = () => {
		uni.$u.route({
			url: 'pages/userTree/organization',
		})
	}
	/**
	 * 添加用户
	 */
	const add = (user: any) => {
		if (users.value.findIndex(item => item.firstName === user.firstName) === -1) {
			users.value.push(user)
		}
	}
	/**
	 * 按索引移除用户
	 */
	const remove = (index: any) => {
		users.value.splice(index, 1)
	}
	onShow(async () => {
		let {
			userData
		} = store;
		if (userData) {
			add(userData)
		}
	})
	return {
		toChooseUser,
		users,
		add,
		remove
	}
}