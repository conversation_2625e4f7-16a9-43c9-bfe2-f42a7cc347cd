
export const gaugeOption = (val: number) => {
	return {
		series: [
			{
				type: 'gauge',
				radius: '75%',
				center: ['50%', '38%'],
				min: 0,
				max: 10,
				pointer: {
					offsetCenter: [0, '10%'],
					length: '50%',
					width: 2,
					itemStyle: {
						color: '#3862F8'
					}
				},
				progress: {
					show: true,
					overlap: true,
					roundCap: true,
					width: 4,
					itemStyle: {
						color: '#3862F8'
					}
				},
				axisTick: {
					distance: 1,
					length: 5,
				},
				splitLine: {
					distance: 1,
					length: 6,
					lineStyle: {
						width: 2
					}
				},
				axisLabel: {
					distance: 10,
				},
				axisLine: {
					roundCap: true,
					lineStyle: {
						width: 4,
						color: [[1, '#91949F']]
					}
				},
				detail: {
					valueAnimation: true,
					precision: 2,
					offsetCenter: [0, '60%'],
					fontSize: 24,
					fontWeight: 'bold',
					color: '#FFFFFF'
				},
				title: {
					offsetCenter: [0, '30%'],
					fontSize: 14,
					fontWeight: 'bold',
					color: '#FFFFFF'
				},
				data: [
					{
						value: val,
						name: '压力值(Mpa)',
					}
				]
			}
		]
	}
}

export const lineOption = (dataX?: String[], data?: any, color?: String) => {
	let dataArray = []
	console.log(data)
	for (let key in data) {
		const nData = {
			name: key,
			data: data[key] || [],
			type: 'line',
			color: color,
			areaStyle: {
				opacity: 0.1
			}
		}
		dataArray.push(nData)
	}

	return {
		color: ['#3862F8', '#ff0000', '#00ff26'],
		animation:false,
		grid: {
			left: 50,
			right: 20,
			top: 20,
		},
		xAxis: {
			boundaryGap: false,
			data: dataX || []
		},
		yAxis: {
			type: 'value',
			show: true
		},
		tooltip: {
			trigger: 'axis'
		},
		dataZoom: {
			type: 'inside',
		},
		series: dataArray
	}
}