<template>
	<view class="main">
		<u-navbar fixed :border-bottom="false" placeholder safeAreaInsetTop bgColor="#FFFFFF" title="资产保养"
			:autoBack="true" leftIconSize="20">
			<template #right>
				<!-- <view class="nv-right" @click="showDate">
					<image src="/static/img/icons/shaixuan-black.png" style="height: 48rpx;width: 48rpx;"></image>
				</view> -->
			</template>
		</u-navbar>
		<u-sticky bg-color="#FFFFFF">
			<u-tabs :list="state.tabs" active-color="#3862F8" name="label" v-model="state.currentTab" bgColor="#FFFFFF"
				:is-scroll="false" :scrollable="false" @change="changeStatus"></u-tabs>
		</u-sticky>
		<!-- <scroll-view style="height: 100vh;" :lower-threshold="50" :refresher-threshold="50" scroll-y refresher-enabled
			refresher-default-style="white" :refresher-triggered="triggered" @refresherrefresh="onRefresh"
			@scrolltolower="showMoreData"> -->
		<view class="card-box" v-for="(data,index) in tableData" :key="index" @click="toDetail(data)">
			<view class="flex-between hand">
				<view class="hand-title">
					<text>{{data.name}}</text>
				</view>
				<view class="status">
					{{status[data.status*1]}}
				</view>
			</view>
			<view class="table">
				<view class="info">
					<text>任务编号：</text> <text>{{data.code}}</text>
				</view>
				<view class="info">
					<text>任务名称：</text> <text>{{data.name}}</text>
				</view>
				<view class="info flex-center">
					<text>任务类型：</text>
					<view class="bg">
						<text>{{data.type}}</text>
					</view>
				</view>
				<view class="info">
					<text>执行班组：</text> <text>{{data.teamName}}</text>
				</view>
				<block v-if="data.status === '0'">
					<view class="info">
						<text>预计开始时间：</text> <text>{{proxy.formatTime(data.startTime) }}</text>
					</view>
					<view class="info">
						<text>预计结束时间：</text> <text>{{proxy.formatTime(data.endTime)}}</text>
					</view>
				</block>
				<block v-if="data.status === '1'">
					<view class="info">
						<text>开始时间：</text> <text>{{proxy.formatTime(data.realStartTime)}}</text>
					</view>
					<view class="info">
						<text>预计结束时间：</text> <text>{{proxy.formatTime(data.endTime)}}</text>
					</view>
				</block>
				<block v-if="data.status === '2'">
					<view class="info">
						<text>开始时间：</text> <text>{{proxy.formatTime(data.realStartTime)}}</text>
					</view>
					<view class="info">
						<text>结束时间：</text> <text>{{proxy.formatTime(data.realStartTime)}}</text>
					</view>
				</block>
				<!-- <view class="info">
							<text>是否需要反馈：</text>
							<text>{{data.isNeedFeedback===true?'是':data.isNeedFeedback===false?'否':'-'}}</text>
						</view> -->
				<view class="info">
					<text>任务描述：</text> <text>{{data.remark}}</text>
				</view>
			</view>
		</view>
		<u-loadmore bg-color="F9F9F9" :status="state.status" loading-text="努力加载中" loadmore-text="加载更多"
			nomore-text="没有了更多" />
		<!-- </scroll-view> -->
		<view class="">
			<u-calendar :max-date="maxDate" v-model="state.dateShow" ref="calendar" @close="state.dateShow=false"
				@change="chooseDate"></u-calendar>
		</view>
	</view>
</template>

<script lang="ts" setup>
	import {
		reactive,
		getCurrentInstance,
		ref
	} from "vue"
	import {
		onShow,
		onPullDownRefresh,
		onReachBottom
	} from '@dcloudio/uni-app'
	import {
		getMaintainTaskList
	} from '@/common/api/asset'
	import {
		maxDate
	} from '@/common/data/publicdata'
	import {
		removeSlash
	} from '@/common/utils/removeIdSlash'
	const {
		proxy
	} = getCurrentInstance()
	// 选择区域
	const state = reactive < {
		tabs: any,
		activceTab: any,
		dateShow: boolean,
		status: string,
		query: any,
		currentTab: number,
	} > ({
		currentTab: 0,
		tabs: [{
				label: '待接收',
				value: '0'
			},
			{
				label: '待处理',
				value: '1'
			},
			{
				label: '已完成',
				value: '2,3'
			},
		],
		activceTab: {
			label: '待接收',
			value: '0'
		},
		dateShow: false,
		status: 'loadmore',
		query: {
			page: 1,
			size: 10
		}
	})
	const status = ref < any > (['待接收', '已接收', '按时完成', '超时完成', '未完成'])
	const triggered = ref < boolean > ()
	const tableData = ref < any > ([])
	const toDetail = (params ? : any) => {
		uni.$u.route({
			url: 'pages/smartOperation/assetMaintainTask/taskDetail',
			params: {
				id:params.id
			}
		})
	}
	//选择日期
	const chooseDate = () => {}

	// 加载更多
	const showMoreData = async () => {
		console.log('dddddd')
		state.status = 'loading'
		await inspectionList()
	} ///

	// 下拉刷新
	const onRefresh = async () => {
		triggered.value = true
		state.query.page = 1
		await inspectionList()
	}
	// 切换保养状态数据
	const changeStatus = async (index: number) => {
		state.activceTab = state.tabs[index]
		state.query.page = 1
		tableData.value = []
		await inspectionList()
	}
	// 选择日期
	const showDate = () => {
		state.dateShow = true
	}

	// 保养列表
	const inspectionList = async () => {
		state.query = {
			...state.query,
			status: state.activceTab.value,
			userId: removeSlash(uni.getStorageSync('userInfo').id?.id),
		}
		const res = await getMaintainTaskList(state.query)
		const data = res.data?.data?.data
		const total = res.data?.data?.total
		if (state.query.page === 1) {
			tableData.value = data
		} else {
			tableData.value = tableData.value.concat(data)
		}
		if (data?.length > 0 && total > tableData.value.length) {
			state.query.page += 1
			state.status = 'loadmore'
		} else {
			state.status = 'nomore'
		}
		triggered.value = false
		uni.stopPullDownRefresh()
	}
	onReachBottom(async () => {
		showMoreData()
	})

	onPullDownRefresh(async () => {
		onRefresh()
	})
	onShow(async () => {
		inspectionList()
		// getCount()
	})
</script>

<style lang="scss" scoped>
	.card-box {
		width: 686rpx;
		margin: 20rpx auto;
		border-radius: 16rpx;
		padding: 22rpx 28rpx;
		background-color: #FFFFFF;

		.hand {
			border-left: 4rpx solid #3862F8;

			.hand-title {
				height: 42rpx;
				line-height: 42rpx;

				text {
					font-size: 28rpx;
					margin-left: 16rpx;
					color: #060F27;
					font-weight: 600;
				}
			}

			.status {
				color: #91949F;
			}
		}

		.table {
			margin-top: 24rpx;

			.info {
				font-size: 28rpx;
				padding-bottom: 18rpx;

				text {
					&:nth-child(1) {
						color: #91949F;
					}

					&:nth-child(2) {
						flex: 1;
					}
				}

				.bg {
					padding: 2rpx 12rpx;
					background: rgba(56, 98, 248, 0.2);
					border-radius: 8rpx;

					text {
						color: #3862F8;
					}
				}
			}
		}
	}
</style>
