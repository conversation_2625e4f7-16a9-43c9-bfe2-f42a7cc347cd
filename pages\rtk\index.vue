<template>

	<view class="rtk-container">
		<u-button class="btn" @click="turnOnBluetooth">打开蓝牙</u-button>
		<u-button class="btn" @click="turnOffBluetooth">关闭蓝牙</u-button>
		<u-button class="btn" @click="getPairedDevices">已配对的设备</u-button>
		<u-button class="btn" @click="discoveryNewDevice">搜索蓝牙设备</u-button>
		<u-button class="btn" @click="cancelDiscovery">取消搜索</u-button>
		<div>
			蓝牙状态：
			<pre>{{bluetoothStatusDesc}}</pre>
		</div>
		<div>
			消息：{{state.msg}}
		</div>
		<div>
			<u-button @click="disConnDevice">断开连接</u-button>
		</div>
		<p>已配对的设备：</p>
		<ul>
			<li v-for="device in state.pairedDevices">
				<p>名称：{{device.name}}</p>
				<p>地址：{{device.address}}</p>
				<u-button @click="connDevice(device.address)">连接</u-button>
			</li>
		</ul>
		发现的设备：<br>
		<ul>
			<li v-for="device in state.newDevices">
				<p>名称：{{device.name}}</p>
				<p>地址：{{device.address}}</p>
				<u-button @click="connDevice(device.address)">连接</u-button>
			</li>
		</ul>
		<div>
			发送数据：
			<textarea cols="20" rows="4" v-model="state.sendData"></textarea>
			<u-button @click="onSendData">发送</u-button>
		</div>
		<br>
		<div style="margin-bottom: 100px;">
			<u-button @click="manualRead">手动接收数据</u-button>
			<view>接收的数据：</view>
			<br>
			<view v-if="state.error">{{state.error}}</view>
			<view v-else style="word-break:break-all;">{{state.receiveStr}}</view>
			<view v-for="item in state.NMEA" :key="item">
				<p>{{item.title}}</p>
				<ul>
					<li v-for="(cItem,j) in item.infos" :key="j">
						<label>{{cItem.label}}:</label>
						<span>{{cItem.formatter?.(cItem.value)??cItem.value}} {{cItem.unit??''}}</span>
					</li>
				</ul>
			</view>
			<!-- <u-button @click="clear">清空</u-button> -->

			<!-- <div style="width: 100%;min-height: 50px;">
				<p>byte数组:</p>
				<span v-for="data in state.receiveDataArr">
					{{data}}&nbsp;
				</span>
			</div> -->
			<!-- <div style="width: 100%;min-height: 50px;word-wrap:break-word;">
				<p>对应的string字符串:</p>
				<p v-for="(item,i) in state.receiveStr" :key="i">{{item}}</p>
			</div> -->
		</div>
	</view>
</template>

<script setup lang="ts">
	import { BluetoothTool } from '@/common/utils/BluetoothTool'
	import { computed, onMounted, reactive } from 'vue';
	import { coordinateConvert } from '@/common/utils/arcMapHelper.js'

	// import { useNativeBluetooth } from '@/common/hooks'
	// const bluetooth = useNativeBluetooth()
	const formatMode = (val : string) => {
		switch (val) {
			case 'A':
				return '自主定位'
			case 'D':
				return '差分'
			case 'E':
				return '估算'

			default:
				return '数据无效'
		}
	}
	const state = reactive<any>({
		bluetoothState: {},
		pairedDevices: [],
		newDevices: [],
		receiveDataArr: [],
		receiveStr: '',
		sendData: "",
		msg: "",
		error: '',
		NMEA: {
			GPGGA: {
				id: 'GPGGA',
				title: 'GPS定位信息(GGA)',
				infos: [
					{ label: 'UTC时间', value: '' },
					{ label: '纬度', value: '' },
					{ label: '纬度半球', value: '' },
					{ label: '经度', value: '' },
					{ label: '经度半球', value: '' },
					{

						label: 'GPS状态', value: '', formatter: (val : string) => {
							switch (val) {
								case '0':
									return '无效定位'
								case '1':
									return '有效定位'
								default:
									return val
							}
						}

					},
					{ label: '卫星数量', value: '' },
					{ label: 'HDOP水平精度因子', value: '' },
					{ label: '海拨', value: '' },
					{ label: '高度单位', value: '' },
					{ label: '大地水准面和WGS84椭球面之间的高度差', value: '' },
					{ label: '高度单位', value: '' },
					{ label: '差分GPS 数据龄期', value: '' },
					{ label: '差分参考基站ID', value: '' },
				]
			},
			// 104.053099365,30.5890338805
			GPGSA: {
				id: 'GPGSA',
				title: '当前卫星信息(GSA)',
				infos: [
					{
						label: '模式', value: '', formatter: (val : string) => {
							switch (val) {
								case 'M':
									return '手动'
								case 'A':
									return '自动'
								default:
									return val
							}
						}

					},
					{

						label: '定位类型', value: '', formatter: (val : string) => {
							switch (val) {
								case '1':
									return '未定位'
								case '2':
									return '2D定位'
								case '3':
									return '3D定位'

								default:
									return val
							}
						}

					},
					{ label: 'PDOP位置精度因子', value: '' },
					{ label: 'HDOP水平精度因子', value: '' },
					{ label: 'VDOP垂直精度因子', value: '' },
				]
			},
			GPGSV: {
				id: 'GPGSV',
				title: '可见卫星信息(GSV)',
				infos: [
					{ label: 'GSV语句的总数', value: '' },
					{ label: '本句GSV的编号', value: '' },
					{ label: '可见卫星的总数', value: '' },
					{ label: '卫星PRN号码', value: '' },
					{ label: '卫星仰角', value: '', unit: '度' },
					{ label: '卫星方位角', value: '', unit: '度' },
					{ label: '信噪比', value: '', unit: 'dB' },
					{ label: '可见卫星的总数', value: '' },

				]
			},
			GPRMC: {
				id: 'GPRMC',
				title: '推荐定位信息(RMC)',
				infos: [

					{ label: 'UTC时间', value: '', unit: '' },
					{

						label: '定位状态', value: '', unit: '', formatter: val => {
							switch (val) {
								case 'A':
									return '有效定位'
								case 'V':
									return '无效定位'
								default:
									return val
							}
						}

					},
					{ label: '纬度', value: '' },
					{ label: '纬度半球', value: '' },
					{ label: '经度', value: '' },
					{ label: '经度半球', value: '' },
					{ label: '地面速率', value: '', unit: '节' },
					{ label: '地面航向', value: '', unit: '度' },
					{ label: 'UTC日期', value: '' },
					{ label: '磁偏角', value: '', unit: '度' },
					{ label: '磁偏角方向', value: '' },
					{

						label: '模式指示', value: '', formatter: formatMode

					},
				]
			},
			GPGLL: {
				id: 'GPGLL',
				title: '定位地理信息(GLL)',
				infos: [

					{ label: '纬度', value: '', unit: '' },
					{ label: '纬度半球', value: '', unit: '' },
					{ label: '经度', value: '', unit: '' },
					{ label: '经度半球', value: '', unit: '' },
					{ label: 'UTC时间', value: '', unit: '' },
					{

						label: '定位状态', value: '', unit: '', formatter: (val : string) => {
							switch (val) {
								case 'A':
									return '有效定位'
								case 'V':
									return '无效定位'
								default:
									return val
							}
						}

					},
					{ label: '模式指示', value: '', unit: '', formatter: formatMode },
				]
			}
		}
	})
	// const receiveDataStr = computed(() => {
	// 	console.log(state.receiveDataArr);
	// 	return decoder.decode(new Uint8Array(state.receiveDataArr))
	// 	// return String.fromCharCode.apply(String, state.receiveDataArr);
	// })
	const bluetoothStatusDesc = computed(() => {
		return state.bluetoothState?.bluetoothEnable ? "已开启" : "已关闭";
	})
	let bluetoothTool : any

	function turnOnBluetooth() {
		bluetoothTool.turnOnBluetooth();
	}
	function turnOffBluetooth() {
		bluetoothTool.turnOffBluetooth();
	}
	function getPairedDevices() {
		state.pairedDevices = bluetoothTool.getPairedDevices();
	}
	function discoveryNewDevice() {
		state.newDevices = [];
		bluetoothTool.discoveryNewDevice();
	}
	function cancelDiscovery() {
		bluetoothTool.cancelDiscovery();
	}
	function connDevice(address : any) {
		bluetoothTool.connDevice(address);
	}
	function disConnDevice() {
		bluetoothTool.disConnDevice();
	}
	function onSendData() {
		bluetoothTool.sendData(state.sendData);
	}
	function clear() {
		state.receiveDataArr = [];
		state.receiveStr = [];
	}
	function manualRead() {
		bluetoothTool.read()
	}
	function D2ms(D : string) {

		const ND = Number(D)
		if (isNaN(ND)) return undefined
		const floorND = Math.floor(ND)
		const du = Math.floor(floorND / 100)
		const ms = (floorND - du * 100) / 60 + ND - floorND
		return du + ms
	}
	let converter: any
	onMounted(async () => {
		try {
			// this.showLoading({ title: '正在加载坐标转换器...' })
			console.log('正在加载坐标转换器...');
			converter = await coordinateConvert()
			console.log('坐标转换器已加载');
		} catch (e) {
			//TODO handle the exception
			console.log(e);
			console.log('坐标转换器加载失败');
			// this.showLoading({ title: '坐标转换器加载失败' })
		}

		bluetoothTool = BluetoothTool();
		state.bluetoothState = bluetoothTool.state;
		bluetoothTool.init({
			listenBTStatusCallback: function (state : any) {
				state.msg = "蓝牙状态: " + state;
			},
			discoveryDeviceCallback: (newDevice : any) => {
				state.newDevices.push(newDevice);
			},
			discoveryFinishedCallback: function () {
				state.msg = "搜索完成";
			},
			readDataCallback: (dataByteArr : any, chars : any) => {
				// if (state.receiveDataArr.length >= 200) {
				// state.receiveDataArr = [];
				// }
				state.receiveDataArr.push.apply(state.receiveDataArr, dataByteArr);
				state.receiveStr = chars
				const charArr = chars.split(',')
				const nmeaItem = state.NMEA[charArr[0]]
				const infos = state.NMEA[charArr[0]]?.infos

				if (!infos) return
				// console.log(nmeaItem.id);
				if (nmeaItem.id === 'GPGSA') {
					infos.length = 2
					const sateliteCount = charArr.length - 6
					if (sateliteCount > 0) {
						for (let i = 0; i < sateliteCount; i++) {
							infos.push({ label: '卫星PRN号码' + (i + 1), value: charArr[3 + i * 2] })
						}
					}

					infos.push({ label: 'PDOP位置精度因子', value: '' })
					infos.push({ label: 'HDOP水平精度因子', value: '' })
					infos.push({ label: 'VDOP垂直精度因子', value: '' })
				} else if (nmeaItem.id === 'GPGSV') {
					infos.length = 2
					const sateliteCount = charArr[1] ?? 0
					infos.push({ label: '可见卫星的总数', value: sateliteCount })
					for (let i = 0; i < sateliteCount; i++) {
						infos.push({ label: '卫星PRN号码', value: charArr[3 + i * 4] })
						infos.push({ label: '卫星仰角', value: charArr[4 + i * 4], unit: '度' })
						infos.push({ label: '卫星方位角', value: charArr[5 + i * 4], unit: '度' })
						infos.push({ label: '信噪比', value: charArr[6 + i * 4], unit: 'dB' })
					}
				} else {
					infos.map((item, i) => {
						if (item.label === '经度') {
							const point = converter?.fromLatitudeLongitude([charArr[i + 1], charArr[i - 1]], { wkid: 4326 })

							item.value = point?.longitude ?? charArr[i + 1]
						}
						if (item.label === '纬度') {
							const point = converter?.fromLatitudeLongitude([charArr[i + 1], charArr[i + 3]], { wkid: 4326 })

							item.value = point?.latitude ?? charArr[i + 1]
						}
						item.value = charArr[i + 1]
					})
				}
				// state.receiveStr.push(...res.split('$'));
				// state.receiveStr = dataChars
				// state.receiveDataArr.push(dataByteArr);

			},
			connExceptionCallback: function (e : any) {
				console.log(e);
				state.msg = "设备连接失败";
			}
		});
	})
</script>

<style lang="scss" scoped>
	.rtk-container {
		padding: 0 20rpx;
	}

	.btn {
		margin-bottom: 12rpx;
	}
</style>