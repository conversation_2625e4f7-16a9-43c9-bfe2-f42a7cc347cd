<template>
	<view class="main">
		<u-navbar :border-bottom="false" title="人员列表"></u-navbar>
		<view class="content">
			<u-cell-group v-if="users.length>0">
				<u-cell-item :arrow="false" :title="item.firstName" v-for="(item,index) in users" :index="index"
					:key="index" @click="chooseUser(item)">
				</u-cell-item>
			</u-cell-group>
			<u-empty v-else></u-empty>
		</view>
	</view>
</template>

<script lang="ts" setup>
	import {
		storeToRefs
	} from 'pinia'
	import {
		useStore
	} from '@/store/index'
	import {
		nextTick,
		onMounted,
		ref
	} from "vue";
	import {
		getAllByPid,
	} from '@/common/api/workOrder'
	const store = useStore()
	const users = ref < any > ([])
	const key = ref < any > ('')
	// 显示部门用户
	const getPersonnel = async (data: any) => {
		console.log(data)
		const res = await getAllByPid({
			pid: data.pid,
			page: 1,
			size: 9999,
			status: 1
		})
		users.value = res.data?.data?.data
	}
	// 
	const chooseUser = (data: any) => {
		// console.log(data)
		// const pages = getCurrentPages();
		// console.log(pages)
		// const page = pages[pages.length - 4]
		// page.$vm.chooseUser(data)
		let { userData, otherUserData }  = storeToRefs(store);
		if(key.value){
			otherUserData.value = data
		}else{
			userData.value = data
		}
		nextTick(()=>{
			uni.$emit("chooseUserData",data)
			uni.$emit("chooseOtherUserData",data)
			uni.navigateBack({
				delta: 3,
				success: () => {}
			})
		})
	}

	onMounted(() => {
		const pages = getCurrentPages();
		const page = pages[pages.length - 1];
		const query = page.$page.options
		key.value = query.key || ''
		getPersonnel(query)
	})
</script>

<style lang="scss" scoped>
	.content {
		height: 90vh;
		padding: 20rpx 32rpx;

	}
</style>
