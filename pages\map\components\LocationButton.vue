<template>
  <view class="cover-view-loca" :class="currentBar" @click="$emit('click')">
    <button class="cover-loca-image" :loading="loading">
      <image 
        class="loca-btn" 
        src="/static/img/icons/location-black.png" 
        mode="widthFix"
      />
    </button>
  </view>
</template>

<script>
export default {
  name: 'LocationButton',
  props: {
    loading: {
      type: Boolean,
      default: false
    },
    currentBar: {
      type: String,
      default: ''
    }
  },
  emits: ['click']
}
</script>

<style scoped lang="scss">
.cover-view-loca {
  width: 80rpx;
  height: 80rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  position: absolute;
  bottom: 450rpx;
  right: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;

  .cover-loca-image {
    width: 40rpx;
    height: 40rpx;
    background-color: transparent;
    border: none;
    padding: 0;
    line-height: 40rpx;

    &::after {
      border: none;
      width: 40rpx;
      height: 40rpx;
      transform: scale(1);
    }

    .loca-btn {
      width: 40rpx;
      height: 40rpx;
    }
  }
}
</style>