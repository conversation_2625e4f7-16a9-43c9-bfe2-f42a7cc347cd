<template>
	<view>
		<u-navbar fixed placeholder safeAreaInsetTop bgColor="#FFFFFF" title="生产报表" :autoBack="true" leftIconSize="20">
			<template #right>
				<view class="nv-right" @click="state.screenShow = true">
					<image src="/static/img/icons/shaixuan-black.png"  style="height: 48rpx;width: 48rpx;"></image>
				</view>
			</template>
		</u-navbar>
		<view class="list">
			<view class="title">
				生产报表
			</view>
			<scroll-view scroll-x scroll-y>
				<!-- <wyb-table borderColor="#EBEDF6" first-col-bg-color="#FFFFFF" :showvertborder="true"
					header-bg-color="#F9F9F9" first-line-fixed ref="table" width="100%" :headers="headers"
					:contents="tableData" height="90vh"/> -->
					<u-table font-size="30" padding="14rpx">
						<u-tr class="u-tr">
							<u-th class="u-th" v-for="(item,index) in headers" :key="index" :width="item.width+'rpx'">
								{{item.label}}{{item.unit}}
							</u-th>
						</u-tr>
						<u-tr class="u-tr" v-for="(item,index) in tableData" :key="index">
							<u-td class="u-td" v-for="(key,index) in headers" :key="index" :width="key.width+'rpx'">
								{{item[key.key] || '0'}}
							</u-td>
						</u-tr>
					</u-table>
			</scroll-view>
		</view>
			<u-popup mode="right" width="528" v-model="state.screenShow" closeOnClickOverlay overlay
				@close="state.screenShow = false">
				<view class="popup">
				<view class="screen-list">
					<u-form :model="screenForm" ref="form1" :label-style="{'color':'#91949F'}" border-bottom input-align="right"
						label-width="180">
						<u-form-item label="水厂：" prop="screenForm.stationName">
							<input fontSize="14"inputmode="none" placeholder-class="placeholderClass" placeholder="点击选择" v-model="screenForm.stationName" @click="state.stationShow=true">
							</input>
							<template #right>
								<u-icon name="arrow-down" color="#91949F" size="34"></u-icon>
							</template>
						</u-form-item>
						<u-form-item label="水厂数据表：" prop="screenForm.groupType">
							<input fontSize="14" inputmode="none" placeholder-class="placeholderClass" placeholder="点击选择" v-model="screenForm.groupType" @click="state.groupShow=true">
							</input>
							<template #right>
								<u-icon name="arrow-down" color="#91949F" size="34"></u-icon>
							</template>
						</u-form-item>
						<u-form-item label="查询类型：" prop="screenForm.queryTypeName">
							<input fontSize="14" inputmode="none" placeholder-class="placeholderClass" placeholder="点击选择" v-model="screenForm.queryTypeName" @click="state.typeShow=true">
							</input>
							<template #right>
								<u-icon name="arrow-down" color="#91949F" size="34"></u-icon>
							</template>
						</u-form-item>
						<u-form-item label="选择日期：" prop="screenForm.date">
							<input fontSize="14" inputmode="none" placeholder-class="placeholderClass" placeholder="点击选择" v-model="screenForm.time" @click="state.dateShow=true">
							</input>
							<template #right>
								<u-icon name="calendar" color="#91949F" size="34"></u-icon>
							</template>
						</u-form-item>
					</u-form>
				</view>
				<view class="popup-button">
					<u-button type="primary" color="#3862F8" @click="submitScreen">确定</u-button>
				</view>
				</view>
			</u-popup>
		<!-- <u-action-sheet :actions="typeList" @select="selectClick" v-model="state.typeShow"></u-action-sheet> -->
		<!-- 报告类型 -->
		<u-picker v-model="state.typeShow" mode="selector" :default-selector="[0]" :range="typeList" range-key="name"
			@confirm="selectClick"></u-picker>
		<!-- <u-calendar v-model="state.dateShow" ref="calendar" @close="state.dateShow=false" @change="chooseDate">
		</u-calendar> -->
		<!-- 水厂站点 -->
		<u-picker v-model="state.stationShow" mode="selector" :default-selector="[0]" :range="stations" range-key="name"
			@confirm="selectStationClick"></u-picker>
		<!-- 分组 -->
		<u-picker v-model="state.groupShow" mode="selector" :default-selector="[0]" :range="groups" range-key="type"
			@confirm="selectGroupClick"></u-picker>
		<!-- 日期 -->
		<u-picker v-model="state.dateShow" mode="time" :params="state.params" @confirm="chooseDate"></u-picker>
	</view>
</template>

<script lang="ts" setup>
	import {
		onMounted,
		reactive,
		ref
	} from "vue"
	import {
		getWaterPlantProductionReport,
	} from '@/common/api/report'
	import {
		getStationList,
		getAllStationGroup
	} from '@/common/api/waterplant'
	import {
		queryTypeList
	} from '@/common/data/publicData'
	import dayjs from 'dayjs'
	// import wybTable from '@/components/wyb-table/wyb-table.vue'
	const state = reactive < {
		screenShow: boolean,
		typeShow: boolean,
		groupShow: boolean,
		stationShow: boolean,
		dateShow: boolean,
		params : any
	} > ({
		screenShow: false,
		stationShow: false,
		groupShow: false,
		typeShow: false,
		dateShow: false,
		params: {
			year: true,
			month: true,
			day: true,
			hour: false,
			minute: false,
			second: false,
			// 选择时间的时间戳
			timestamp: false,
		}
	})

	const stations = ref < any > ([])
	const groups = ref < any > ([])
	const screenForm = reactive < any > ({
		queryType: 'day',
		queryTypeName: '日报表',
		time: dayjs().format('YYYY-MM-DD'),
		stationId: '',
		stationName: '',
		groupType: '',
	})

	const typeList = reactive < any > (queryTypeList)
	const headers = ref < any > ([])
	const tableData = ref < any > ([])

	//选择报表类型
	const selectClick = (index: number) => {
		screenForm.queryTypeName = typeList[index].name
		screenForm.queryType = typeList[index].value
		state.typeShow = false
		screenForm.time = dayjs().format(typeList[index].format)
		state.params = typeList[index].param
	}

	// 选择日期
	const chooseDate = (date: any) => {
		console.log(date)
		state.dateShow = false
		if (screenForm.queryType === 'day') {
			screenForm.time = date.year+ '-' + date.month + '-' + date.day
		} else if (screenForm.queryType === 'month') {
			screenForm.time = date.year + '-' + date.month
		} else {
			screenForm.time = date.year
		}
	}
	// 选择日期
	const chooseDate1 = (date: any) => {
		console.log(date)
		state.dateShow = false
		if (screenForm.queryType === 'day') {
			screenForm.time = date.result
		} else if (screenForm.queryType === 'month') {
			screenForm.time = date.year + '-' + date.month
		} else {
			screenForm.time = date.year
		}
	}
	// 提交筛选
	const submitScreen = async () => {
		state.screenShow = false
		await productionReport()
	}

	//选择报表类型
	const selectStationClick = async (index: number) => {
		screenForm.stationId = stations.value[index].id
		screenForm.stationName = stations.value[index].name
		state.stationShow = false
	}

	//选择报表类型
	const selectGroupClick = async (index: number) => {
		screenForm.groupType = groups.value[index].type
		state.groupShow = false
	}
	// 获取所有站点列表
	const stationList = async () => {
		const systemConfig = uni.getStorageSync('systemConfig')
		const params = {
			page: 1,
			size: 999,
			type: systemConfig.type
		}
		const res = await getStationList(params)
		stations.value = res.data?.data
		await selectStationClick(0)
		await groupList(stations.value[0])
	}


	// 获取站点分组
	const groupList = async (station: any) => {
		const res = await getAllStationGroup({
			stationId: station?.id
		})
		console.log(res.data)
		groups.value = res.data
		await selectGroupClick(0)
	}

	// 生产报告
	const productionReport = async () => {
		const params = {
			...screenForm
		}
		const res = await getWaterPlantProductionReport(params)
		const result = res.data?.data
		headers.value = result.tableInfo.map((info: any) => {
			return {
				label: info.columnName,
				key: info.columnValue,
				unit: info.unit ? '(' + info.unit + ')' : '',
				width: '200',
			}
		})
		tableData.value = result.tableDataList
	}

	onMounted(async () => {
		await stationList()
		await productionReport()
	})
</script>

<style lang="scss" scoped>
	.list {
		margin-top: 20rpx;
		min-height: 90vh;
		padding: 22rpx 32rpx;
		background-color: #FFFFFF;

		.title {
			font-weight: 700;
			font-size: 30rpx;
			color: #060F27;
			padding-bottom: 20rpx;
		}
	}

	.popup {
		width: 100%;
		height: 100%;
		position: relative;

		.screen-list {
			padding: 222rpx 34rpx;
		}

		.popup-button {
			width: 94%;
			position: absolute;
			bottom: 40rpx;
			left: 0;
			right: 0;
			margin: 0 auto;
		}

		::v-deep .placeholderClass {
			font-size: 28rpx;
		}

		::v-deep .u-form-item {
			padding: 0;
		}
	}
</style>
