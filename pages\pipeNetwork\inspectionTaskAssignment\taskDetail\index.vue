<!-- gis巡检详情-地图 -->
<template>
	<view class="inspection-assignment-detail">
		<!-- <u-navbar fixed placeholder safeAreaInsetTop bgColor="#FFFFFF" title="任务详情" :autoBack="true" leftIconSize="20">
		</u-navbar> -->
		<view class="main-wrapper">
			<view id="arcgisView" class="map-view" :change:locateMark="arcgisView.locate"
				:locateMark="location.lonLat" :change:pipevisible="arcgisView.togglePipeVisible"
				:pipevisible="pipelayer.currToggled" :change:pipeUrl="arcgisView.setPipeUrl"
				:pipeUrl="pipelayer.pipeServerUrl" :change:districtData="arcgisView.setDistrictData"
				:districtData="inspection.districtData" :change:devices="arcgisView.setDevices"
				:devices="inspection.devices" :change:basemap="arcgisView.toggleBaseMap" :basemap="basemap"
				:change:keyPoints="arcgisView.setKeyPoints" :keyPoints="inspection.keyPoints"
				:change:spetialDevices="arcgisView.setSpetialDevices" :spetialDevices="inspection.devices"
				:change:isStart="arcgisView.setIsStart" :isStart="inspection.isStart"
				:change:isOk="arcgisView.refreshData" :isOk="inspection.isOk"
				:change:verticalBar="arcgisView.handleVerticalBarChange" :verticalBar="verticalBar.currentBar"
				:change:locusPoints="arcgisView.handleLocusChange" :locusPoints="inspection.historyPoints" />
			<view class="cover-view-plus">
				<block v-for="(menu,index) in verticalBar.menus" :key="index">
					<view class="plus-btn" @click="verticalBar.setCurrent(menu.value)">
						<text class="custom-icon btn-img" :class="menu.icon"
							:style="{'color':verticalBar.currentBar === menu.value?'#3862F8':'#060F27'}"></text>
						<text style="font-size: 24rpx;"
							:style="{'color':verticalBar.currentBar === menu.value?'#3862F8':'#060F27'}">{{menu.name}}</text>
					</view>
				</block>

			</view>
			<view class="cover-view-loca" @click="getLocation">
				<button class="cover-loca-image" :loading="location.loading">
					<image class="loca-btn" src="/static/img/icons/location-black.png" mode="widthFix"></image>
				</button>
			</view>
			<view v-if="verticalBar.currentBar!=='baselayer'"
				class="cover-view-menu bottom-menu border-box flex-around">
				<view class="inspection-wrapper">
					<view class="title">
						巡检时长: {{(inspection.hours||0)+'小时'+(inspection.minites||0)+'分钟'}}
					</view>
					<view class="count-wrapper">

						<view class="content">
							<view class="title">
								关键点
							</view>
							<u-gap height="2" bg-color="#EBEDF6"></u-gap>
							<view class="item">
								<text class="item-title">未完成数量</text>
								<text
									class="warning">{{inspection.keyPoints.length - inspection.keyPointsSettledCount}}</text>
							</view>
							<view class="item">
								<text class="item-title">完成数量</text>
								<text class="primary">{{inspection.keyPointsSettledCount}}</text>
							</view>
						</view>
						<view class="content">
							<view class="title">
								设备
							</view>
							<u-gap height="2" bg-color="#EBEDF6"></u-gap>
							<view class="item">
								<text class="item-title">未完成数量</text>
								<text
									class="warning">{{inspection.devices.length - inspection.devicesSettledCount}}</text>
							</view>
							<view class="item">
								<text class="item-title">完成数量</text>
								<text class="primary">{{inspection.devicesSettledCount}}</text>
							</view>
						</view>
					</view>
					<view class="inspection-wrapper">
						<!-- <u-button type="primary" color="#3862F8"
							@click="()=>inspection.toggleInspect(inspection.status==='APPROVED')">
							{{ inspection.isStart?'结束巡检':'开始巡检'}}
						</u-button> -->
						<u-button type="primary" color="#3862F8" @click="()=>toAssignment()">
							巡检分派
						</u-button>
					</view>
				</view>
			</view>
			<view v-if="verticalBar.currentBar==='baselayer'" class="cover-view-menu baselayer">
				<view class="cover-header">
					<text class="title">选择底图</text>
					<text class="icon" @click="closeCover">x</text>
				</view>
				<view class="cover-main">
					<view class="item">
						<image class="item-image" @click="()=>changeBaseMap('vec')"
							src="http://t4.tianditu.gov.cn/vec_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=vec&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX=13&TILEROW=3457&TILECOL=6548&tk=e4e98a7455967290863f2f1bb245f7b5"
							mode=""></image>
						<text class="item-text">标准</text>
					</view>
					<view class="item">
						<image class="item-image" @click="()=>changeBaseMap('img')"
							src="http://t4.tianditu.gov.cn/img_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=img&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX=14&TILEROW=6916&TILECOL=13100&tk=e4e98a7455967290863f2f1bb245f7b5"
							mode=""></image>
						<text class="item-text">卫星</text>
					</view>
				</view>
			</view>
			<view v-if="verticalBar.currentBar==='layer'" class="cover-view-menu border-box layer">
				<view class="cover-header">
					<text class="title">选择图层</text>
					<text class="icon" @click="closeCover">x</text>
				</view>
				<scroll-view class="menu-list flex-around" :scroll-y="true">
					<view class="layer-box">
						<text class="layer-title">
							管点类
						</text>
						<view class="layer-menu">
							<view class="menu-item" v-for="(menu,i) in pipelayer.pointLayers" :key="i"
								@click="pipelayer.toggle(menu.layerid)">
								<view class="icon-bg"
									:style="{'background-color':pipelayer.selected.indexOf(menu.layerid)!==-1?'#3862F8':'#E2E3E5'}">
									<text :class="['layer-menu-icon','custom-icon',menu.icon]"
										:style="{'color':pipelayer.selected.indexOf(menu.layerid)!==-1?'#ffffff':'#060F27'}"></text>
								</view>

								<text class="layer-menu-text"
									:style="{'color':pipelayer.selected.indexOf(menu.layerid)!==-1?'#3862F8':'#060F27'}">{{menu.layername}}</text>
							</view>
						</view>
					</view>
					<view class="layer-box">
						<text class="layer-title">
							管线类
						</text>
						<view class="layer-menu">
							<view class="menu-item" v-for="(menu,i) in pipelayer.lineLayers" :key="i"
								@click="pipelayer.toggle(menu.layerid)">
								<view class="icon-bg"
									:style="{'background-color':pipelayer.selected.indexOf(menu.layerid)!==-1?'#3862F8':'#E2E3E5'}">
									<text :class="['layer-menu-icon','custom-icon',menu.icon]"
										:style="{'color':pipelayer.selected.indexOf(menu.layerid)!==-1?'#ffffff':'#060F27'}"></text>
								</view>

								<text class="layer-menu-text"
									:style="{'color':pipelayer.selected.indexOf(menu.layerid)!==-1?'#3862F8':'#060F27'}">{{menu.layername}}</text>
							</view>
						</view>
					</view>
				</scroll-view>
			</view>
		</view>

	</view>
</template>

<script lang="ts">
	import {
		useLocation,
		useVerticalBar,
		usePipeLayers
	} from '../../../../common/hooks'
	import {
		useInspection
	} from '../../inspectionTask/hooks/useInspection'
	export default {
		data() {
			return {
				verticalBar: useVerticalBar({
					defaultMenus: [{
						name: '底图',
						value: 'baselayer',
						icon: 'custom-icon-shixindiqiu'
					},
					{
						name: '轨迹',
						value: 'locus',
						icon: 'custom-icon-yundanguiji',
					}
					]
				}),
				location: useLocation(),
				pipelayer: usePipeLayers(),
				mapRendered: false,
				basemap: 'vec',
				inspection: useInspection(),
				taskId: ''
			}
		},
		methods: {
			toAssignment() {
				uni.navigateTo({
					url: './eventDispatch?taskId=' + this.taskId,
				})
			},
			changeBaseMap(type : string) {
				this.basemap = type
			},
			closeCover() {
				this.verticalBar.setCurrent('')
			},
			getLocation() {
				this.location.refreshNum++
			},
			async getLayerInfoFromRenderjs(params : any) {
				//renderjs调用的service层方法  
				this.pipelayer.getLayerInfo(params)
			},
			onMapRendered() {
				this.mapRendered = true
				this.inspection.refreshData()
			},
			showLoading(config : any) {
				uni.showToast({
					title: '加载中...',
					icon: 'loading',
					...(config || {})
				})
			},
			hideLoading() {
				uni.hideToast()
			},
			toDetail(params : any) {
				if (!params.row) return
				uni.navigateTo({
					url: '../inspectionReport2/index?row=' +
						encodeURIComponent(JSON.stringify(params.row)) + '&SID=' + (params.attributes?.SID || '') +
						'&type=' + (this.inspection.status || '')
				})
			},
			needClickStart() {
				const that = this
				uni.showModal({
					title: '提示',
					content: '当前未开始巡检，是否开始？',
					cancelText: '取消',
					confirmText: '现在开始'
				}).then(res => {
					if (res.confirm) {
						that.inspection.startInspect()
					}
				})
			},
			refresh() {

			}
		},
		onShow() {
			this.mapRendered && this.inspection.refreshData()
		},
		onLoad(options : any) {
			if (!options?.areaId) return
			this.inspection.areaId = options.areaId
			this.inspection.taskCode = options.taskCode
			this.taskId = options.taskId
			this.inspection.status = options.type
		},
	}
</script>
<script lang="renderjs" module="arcgisView">
	//renderjs部分
	import {
		initView,
		loadEsriModules,
		getLayerById,
		setSymbol,
		changeBaseMap,
		bindViewClick,
		locationIcon,
		getLayersByIds,
		userMarker
	} from '../../../../common/utils/arcMapHelper'
	import {
		usePipeQuery
	} from '../../../../common/hooks/map'
	var view = undefined
	var districtGraphicsLayer = undefined
	var keyPointGraphicsLayer = undefined
	var deviceGraphicsLayer = undefined
	var locusLayer = undefined
	var locusStartEndMarkLayer = undefined
	var pipeQuery = usePipeQuery()
	var Point
	var Graphic
	var Symbol
	var oldExtent
	var SimpleMarkerSymbol
	var Geometry
	var MapImageLayer
	var PictureMarkerSymbol
	export default {
		name: 'arcgisView',
		data() {
			return {
				pipeUrl: '',
				subLayers: [],
				curOper: '',
				devices: [],
				leyPoints: [],
				spatialDevices: [],
				districtData: '',
				isStart: false
			};
		},
		methods: {
			handleLocusChange(locusPoints) {
				console.log(locusPoints);
				locusLayer?.removeAll()
				if (!locusPoints.length) {
					return
				}
				const pathCoords = locusPoints?.map((item, i) => {
					const point = new Point({
						spatialReference: view?.spatialReference,
						longitude: item.x,
						latitude: item.y
					})
					return [point.x, point.y]
				})
				const g = new Graphic({
					geometry: {
						type: 'polyline',
						paths: [pathCoords],
						spatialReference: view?.spatialReference,
					},
					attributes: {
						type: 'locus'
					},
					symbol: setSymbol('polyline', {
						color: '#ff0000'
					})
				})
				locusLayer?.add(g)
			},
			handleVerticalBarChange(bar) {
				if (!locusLayer) return
				if (bar === 'locus') {
					locusLayer.visible = true
					const g = locusLayer.graphics.find(item => item.attributes?.type === 'locus')
					if (g) {
						oldExtent = view?.extent
						view.goTo(g)

					}
				} else {
					locusLayer.visible = false
					oldExtent && view.goTo({
						zoom: 15,
						target: oldExtent
					})
				}
			},
			async loadModules() {
				[Point, Graphic, Symbol, SimpleMarkerSymbol, Geometry, MapImageLayer, PictureMarkerSymbol] =
				await loadEsriModules([
					'esri/geometry/Point',
					'esri/Graphic',
					'esri/symbols/Symbol',
					'esri/symbols/SimpleMarkerSymbol',
					'esri/geometry/Geometry',
					'esri/layers/MapImageLayer',
					"esri/symbols/PictureMarkerSymbol"
				])
			},
			setDistrictData(data) {
				this.districtData = data
			},
			setDevices(data) {
				this.devices = data
			},
			setKeyPoints(data) {
				this.keyPoints = data
			},
			setSpetialDevices(data) {
				this.spatialDevices = data
			},
			setIsStart(data) {
				console.log('setIsStart', data);
				this.isStart = data
			},
			// 设置管线查询路径
			setPipeUrl(url) {
				this.pipeUrl = url
			},
			/**
			 * 处理区域路径
			 */
			resolveDistrict() {
				districtGraphicsLayer?.removeAll()
				if (!this.districtData) return
				const pointjson = JSON.parse(this.districtData)
				if (!pointjson.geometry) return
				// 只有这两种情况：面和线
				const type = pointjson.geometry.rings ?
					'polygon' :
					pointjson.geometry.paths ?
					'polyline' :
					''
				if (!type) return
				const geo = {
					type: type,
					spatialReference: pointjson.geometry.spatialReference
				}
				if (type === 'polygon') {
					geo.rings = pointjson.geometry.rings
				} else {
					geo.paths = pointjson.geometry.paths
				}
				const graphic = new Graphic({
					geometry: geo,
					symbol: setSymbol(type)
				})
				districtGraphicsLayer?.add(graphic)
				view.goTo({
					target: graphic
				})

				// 如果存在buffer
				if (!pointjson.bufferGeometry?.rings) return
				const bufferGeometry = {
					type: 'polygon',
					rings: pointjson.bufferGeometry.rings,
					spatialReference: pointjson.bufferGeometry.spatialReference
				}
				const buffer = new Graphic({
					geometry: bufferGeometry,
					symbol: setSymbol(bufferGeometry?.type || 'polygon', {
						color: [0, 255, 0, 0.1],
						outlineWidth: 1,
						outlineColor: '#00ff00'
					})
				})
				districtGraphicsLayer?.add(buffer)
			},
			async resolvePipeData(layerId, devices) {
				if (!devices?.length) return
				const oIds = devices.map(item => item.deviceType)
				const res = await pipeQuery.excute(this.pipeUrl + '/' + layerId, {
					outSpatialReference: view?.spatialReference,
					returnGeometry: true,
					objectIds: oIds,
					outFields: ['*']
				})
				const pointSymbol = new PictureMarkerSymbol({
					width: 25,
					height: 25,
					url: locationIcon,
					yoffset: 13,
				})
				const gTexts = []
				res.features = res.features.map(item => {
					const device = devices.find(o => o.deviceType === item.attributes.OBJECTID.toString())
					item.symbol = item.geometry.type === 'point' ? pointSymbol : setSymbol(item.geometry
						.type)
					const text = (device.name || '') + '（' + (item.attributes?.SID || '') + '）'
					const color = device.isComplete === true ? '#67c23a' : '#ff9f05'
					item.attributes = {
						attributes: item.attributes,
						row: device
					}
					const gText = item.clone()

					gText.symbol = setSymbol('text', {
						text: text,
						yoffset: -8,
						color: color
					})
					gTexts.push(gText)
					return item
				})
				/**
				 * toDo: 这里web显示正常，app不能显示，图标文件路径的问题
				 */
				keyPointGraphicsLayer?.addMany([...res.features, ...gTexts]);
			},
			/**
			 * 处理管网设备
			 */
			resolveDevice() {
				deviceGraphicsLayer?.removeAll()
				if (!this.devices?.length) return
				this.devices.map(item => item.name).filter(item => !!item).map(item => {
					const layerid = this.subLayers?.find(s => s.layername === item)?.layerid
					if (layerid !== undefined) {
						const os = this.devices?.filter(o => o.name === item) || []
						this.resolvePipeData(layerid, os)
					}
				})
			},
			/**
			 * 处理关键点
			 */
			async resolveKeyPoints() {
				keyPointGraphicsLayer?.removeAll()
				if (!this.keyPoints.length) return
				const texts = []
				const points = this.keyPoints.filter(item => item.lon && item.lat)?.map(item => {
					// const symbol = setSymbol('point', {
					// 	color: item.isSettle ? '#67c23a' : '#f56c6c'
					// })

					const pointSymbol = new PictureMarkerSymbol({
						width: 25,
						height: 25,
						url: locationIcon,
						yoffset: 13,
					})
					const graphic = new Graphic({
						geometry: {
							type: 'point',
							longitude: item.lon,
							latitude: item.lat,
							spatialReference: view?.spatialReference
						},
						symbol: pointSymbol,
						attributes: {
							row: item
						}
					})
					const text = graphic.clone()
					text.symbol = setSymbol('text', {
						text: '关键点(' + graphic.attributes.row.name + ')',
						color: item.isSettle ? '#67c23a' : '#f53939',
						yoffset: -8
					})
					texts.push(text)
					return graphic
				})
				keyPointGraphicsLayer?.addMany([...points, ...texts])
			},
			async refreshData() {
				this.resolveDistrict()
				this.resolveKeyPoints()
				this.resolveDevice()
			},
			togglePipeVisible(params) {
				if (!params) return
				const layer = view?.map?.findLayerById('pipe-layer')
				const subLayer = layer?.sublayers?.find(item => item.id === params?.id)
				subLayer && (subLayer.visible = params.visible)
			},
			toggleBaseMap(type) {
				if (!view?.map) return
				changeBaseMap(view.map, type)
			},
			async loadPipeLayer() {
				try {
					const pipeLayer = new MapImageLayer({
						id: 'pipe-layer',
						url: this.pipeUrl
					})
					view?.map?.add(pipeLayer)
					await pipeLayer.when()
					this.subLayers = pipeLayer?.sublayers?.items?.map(item => {
						return {
							layername: item.title,
							layerid: item.id
						}
					}) || []
					this.$ownerInstance?.callMethod('getLayerInfoFromRenderjs', this.subLayers.map(item => item
						.layerid))
				} catch (e) {
					//TODO handle the exception
				}
				// view?.goTo(pipeLayer.fullExtent)
			},
			// locate(location) {
			// 	const newValue = location?.split(',')
			// 	if (!newValue?.[0] && !newValue?.[1]) return
			// 	const g = new Graphic({
			// 		geometry: {
			// 			type: 'point',
			// 			longitude: newValue?.[0],
			// 			latitude: newValue?.[1],
			// 			spatialReference: view?.spatialReference
			// 		},
			// 		symbol: new PictureMarkerSymbol({
			// 			width: 25,
			// 			height: 25,
			// 			url: locationIcon,
			// 			yoffset: 13
			// 		})
			// 	})
			// 	view?.graphics?.removeAll()
			// 	view?.graphics?.add(g)


			// 	view?.goTo({
			// 		zoom: 18,
			// 		target: newValue
			// 	})
			// },
			locate(data) {
				let that = this
				var options = {
					enableHighAccuracy: true,
					timeout: 5000,
					maximumAge: 0,
				};
			
				function success(position) {
					var lng = position.coords.longitude;
					var lat = position.coords.latitude;
					console.log('刷新定位', data);
					// if (data && data.indexOf('auto') != -1) {
					// 	console.log('data', data)
					// 	that.$ownerInstance?.callMethod('handleLocate', [lng, lat])
					// }
					that.$ownerInstance?.callMethod('hideLoading');
			
					if (!lng && !lat) return
					const g = new Graphic({
						geometry: {
							type: 'point',
							longitude: lng,
							latitude: lat,
							spatialReference: view?.spatialReference
						},
						symbol: new PictureMarkerSymbol({
							width: 20,
							height: 20,
							url: userMarker,
							yoffset: 13
						})
					})
					view?.graphics?.removeAll()
					view?.graphics?.add(g)
			
					view?.goTo({
						zoom: 18,
						target: g
					})
				}
			
				function error(err) {
					console.warn("ERROR(" + err.code + "): " + err.message);
				}
			
				navigator.geolocation.getCurrentPosition(success, error, options);
			},
			resolveClick(response) {
				const results = response.results || []
				console.log(results);
				let index = 0
				while (index < results.length) {
					const attributes = results[index]?.graphic?.attributes
					if (!attributes?.row) index++
					else {
						index = results.length
						if (this.isStart) {
							this.$ownerInstance?.callMethod('toDetail', attributes);
						} else {
							// this.$ownerInstance?.callMethod('needClickStart')
						}
					}
				}

			}

		},
		async mounted() {
			let that = this
			try {
				this.$ownerInstance?.callMethod('showLoading');
				const container = document.getElementById('arcgisView')
				const v = await initView(container);
				view = v;
				await this.loadModules();
				await this.loadPipeLayer();
				[districtGraphicsLayer, locusLayer, keyPointGraphicsLayer, deviceGraphicsLayer,
					locusStartEndMarkLayer
				] = await getLayersByIds(view, ['district-layer', 'locus-layer', 'device-layer', 'pipedevice-layer',
					'locus-start-end-layer'
				])
				pipeQuery.init().then(() => {
					this.$ownerInstance?.callMethod('onMapRendered');
				})
				bindViewClick(view, this.resolveClick)
			} catch (e) {
				console.log(e);
				//TODO handle the exception
			}
			this.$ownerInstance?.callMethod('hideLoading');
		}
	}
</script>
<style scoped lang="scss">
	.warning {
		color: #F8A038;
	}

	.primary {
		color: #3862F8;
	}

	.inspection-assignment-detail {
		// width: 750rpx;
		height: calc(100vh - 100rpx);

		// 主体内容
		.main-wrapper {
			position: relative;
			height: calc(100% - var(--status-bar-height));

			.map-view {
				height: 100%;
			}
		}

		/* #ifdef APP-PLUS */
		height: 100vh;

		// .main-wrapper {
		// 	position: relative;
		// 	height: calc(100% - var(--status-bar-height) - 88rpx);

		// 	.map-view {
		// 		height: 100%;
		// 	}
		// }

		/* #endif */


		.cover-view-plus {
			width: 80rpx;
			background-color: #ffffff;
			border-radius: 20rpx;
			position: absolute;
			top: 62rpx;
			right: 32rpx;
			display: flex;
			flex-wrap: wrap;
			align-items: center;
			justify-content: center;
			padding: 8rpx 16rpx;

			.plus-btn {
				width: 48rpx;
				height: 96rpx;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;

				.btn-img {
					font-size: 48rpx;
				}
			}

			.btn-line {
				width: 80rpx;
				height: 1rpx;
				border-width: 1rpx;
				border-style: solid;
				border-color: #EBEDF6;
			}

			.btn-img {
				font-size: 48rpx;
			}
		}

		.cover-view-loca {
			width: 80rpx;
			height: 80rpx;
			background-color: #ffffff;
			border-radius: 16rpx;
			position: absolute;
			bottom: 468rpx;
			right: 32rpx;
			display: flex;
			align-items: center;
			justify-content: center;

			.cover-loca-image {
				width: 40rpx;
				height: 40rpx;
				background-color: transparent;
				border: none;
				padding: 0;
				line-height: 40rpx;

				&::after {
					border: none;
					width: 40rpx;
					height: 40rpx;
					transform: scale(1);
				}

				.loca-btn {
					width: 40rpx;
					height: 40rpx;
				}
			}
		}

		.cover-view-menu {
			/* width: 750rpx; */
			position: absolute;
			bottom: 0;
			background-color: #FBFBFB;

			&.pull-up {
				height: 660rpx;
			}

			// z-index: 99999;
			&.bottom-menu {
				padding: 32rpx;
				padding-bottom: 16rpx;
				display: block;

				.inspection-wrapper {
					.title {
						font-family: 'Poppins';
						font-style: normal;
						font-weight: 400;
						font-size: 12px;
						display: flex;
						align-items: center;
						text-align: center;
						color: #060F27;
						height: 60rpx;
						justify-content: center;
					}

					.count-wrapper {
						display: flex;
						justify-content: space-between;

						.content {
							width: 335rpx;
						}
					}
				}

				.content {
					background: #f5f5f5;
					border-radius: 16rpx;
					width: 686rpx;
					height: 220rpx;
					padding: 0 16rpx 16rpx 16rpx;
					margin-bottom: 20rpx;

					.item {
						height: 70rpx;
						display: flex;
						justify-content: space-between;
						align-items: center;
						color: #91949F;
					}
				}

				.inspection-list {
					padding: 16rpx 0;
					height: 350rpx;
					background: #f5f5f5;
					border-radius: 16rpx;

					.list-item {
						padding: 0 16rpx;
						height: 70rpx;
						line-height: 60rpx;
						display: flex;
						justify-content: space-between;
						align-items: center;
						color: #91949F;
					}
				}
			}

			&.baselayer,
			&.layer {
				height: 320rpx;
				width: 100%;
				padding: 0 32rpx;
				border-radius: 16rpx 16rpx 0rpx 0;



				.cover-main {
					display: flex;
					justify-content: space-between;
					flex-wrap: nowrap;
					height: 200rpx;

					.item {
						width: calc(50% - 20rpx);
						height: 100%;
						position: relative;

						.item-image {
							width: 100%;
							height: 100%;
							border-radius: 8px;
						}

						.item-text {
							background: rgba(255, 255, 255, 0.8);
							border-radius: 0px 0px 8px 8px;
							width: 100%;
							position: absolute;
							bottom: 0;
							left: 0;
							height: 48rpx;
							line-height: 48rpx;
							padding: 0 20rpx;
							font-size: 24rpx;
						}
					}
				}
			}

			&.baselayer {
				height: 320rpx;
			}

			&.layer {
				height: 800rpx;
				overflow: hidden;

				.menu-list {
					height: 700rpx;
				}
			}

			.cover-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				height: 86rpx;

				.title {
					text-align: left;
					word-break: keep-all;
				}

				.icon {
					font-size: 1.2em;
				}
			}

			&.layer {}

			&.area {}

			&.distance {}

			transition: all 0.5s ease-in-out;
		}

		.bottom-menu {}

		.border-box {
			width: 100%;
			padding-top: 30rpx;
			// z-index: 99999;
			background-color: #FBFBFB;
			position: absolute;
			bottom: 0;
			align-items: center;
			justify-content: space-around;
			// box-shadow: 0 4rpx 30rpx rgba(178, 183, 199, 0.5);
			border-radius: 16rpx 16rpx 0rpx 0;

			.menu-list {
				flex-direction: column;
				display: flex;
				justify-content: space-around;
				width: 100%;

				.layer-box {
					width: 100%;
					padding: 0 32rpx 25rpx;

					.layer-title {
						color: #91949F;
					}

					.layer-menu {
						display: flex;
						justify-content: flex-start;
						align-items: center;
						flex-wrap: wrap;
						border-radius: 8px;
						background-color: #ffffff;
						margin-top: 20rpx;
						padding-bottom: 20rpx;




					}
				}
			}
		}

	}

	.menu-item {
		width: 20%;
		align-items: center;
		padding: 25rpx 0 0;
		text-align: center;
		display: flex;
		justify-content: center;
		flex-direction: column;

		.icon-bg {
			border-radius: 50%;
			width: 80rpx;
			height: 80rpx;
			display: flex;
			align-items: center;
			justify-content: center;

			&.round-rect {
				border-radius: 16rpx;
			}
		}

		.layer-menu-text {
			word-break: keep-all;

		}
	}

	:deep(.esri-view .esri-view-surface--inset-outline) {
		&:focus::after {
			display: none;
		}
	}
</style>