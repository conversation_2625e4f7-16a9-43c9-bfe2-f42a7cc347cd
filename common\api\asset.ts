import { http } from '../vmeitime-http/index'

// 资产保养任务列表
export const getMaintainTaskList = (params: {
	startStartTime?: number
	startEndTime?:number
	endStartTime?:number
	auditStatus?:number
	userId:string
	page: number
	size: number,
	status?: string
}) => {
	return http().get('api/maintain/task/m', params)
}

// 接收任务
export const receiveMaintainTask = (id:string) => {
	return http().get(`api/maintain/task/m/receive/${id}`)
}
// 保养保存
export const saveMaintainTask = (params: {
	id:string
	mainId:string
	img?:string
	remark?:string
	status:number
}) => {
	return http().post('api/maintain/task/c', params)
}
// 资产保养任务详情
export const getMaintainTaskDetail = (code: string) => {
	return http().get(`api/maintain/task/m/detail/${code}`)
}



// 接收巡检任务
export const receiveCircuitTask = (id: string) => {
	return http().get(`api/circuit/task/m/receive/${id}`)
}




// 资产巡检任务列表
export const getCircuitTaskList = (params: {
	startStartTime?: number
	startEndTime?:number
	endStartTime?:number
	auditStatus?:number
	userId:string
	page: number
	size: number
}) => {
	return http().get('api/circuit/task/m',params)
}

// 资产巡检任务详情
export const getCircuitTaskDetail = (code: string) => {
	return http().get(`api/circuit/task/m/detail/${code}`)
}


// 巡检保存
export const saveCircuitTask = (params: {
	id:string
	mainId:string
	img?:string
	remark?:string
	status:number
}) => {
	return http().post('api/circuit/task/c', params)
}



// 
export const getMaintainTaskNum = () => {
	return http().get(`api/maintain/task/m/notCompleteNum`)
}

// 
export const getCircuitTaskNum = () => {
	return http().get(`api/circuit/task/m/notCompleteNum`)
}

