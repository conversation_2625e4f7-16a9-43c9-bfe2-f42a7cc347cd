<template>
	<view>
		<web-view :src="mapUrl" @message="getH5Message"></web-view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				mapUrl: '',
				viewerUrl: '/static/hybrid/html/tianmap.html', // pdf.js的viewer.html所在路径
			}
		},
		onLoad(options) {
			// const address = encodeURIComponent(options.end)
			this.mapUrl = `${this.viewerUrl}`;
		},
		methods: {
			// uni-app 中的代码
			// #ifdef APP-PLUS
			getH5Message(e) {
				console.log('来自webview的消息', e)
				var item = e.detail.data[0]
				uni.$emit('updateLocation', item)
				uni.navigateBack({
					delta: 1
				})
			},
			// #endif
		}
	}
</script>

<script setup>
	// 	import {
	// 		onMounted,
	// 		onUnmounted
	// 	} from "vue";
	// 	import AMapLoader from "@amap/amap-jsapi-loader";

	// 	let map = null;
	// 	var  route, marker;
	// 	onMounted(() => {
	// 		AMapLoader.load({
	// 				key: "dd085ab740abcfb7fd1be352938f51dd", // 申请好的Web端开发者Key，首次调用 load 时必填
	// 				version: "2.0", // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
	// 				plugins: ["AMap.DragRoute", "AMap.Driving"], // 需要使用的的插件列表，如比例尺'AMap.Scale'等
	// 			})
	// 			.then((AMap) => {
	// 				map = new AMap.Map("container", {
	// 					// 设置地图容器id
	// 					viewMode: "3D", // 是否为3D地图模式
	// 					zoom: 11, // 初始化地图级别
	// 					center: [116.397428, 39.90923], // 初始化地图中心点位置
	// 				});
	// 				var path = [];
	// 				    path.push([116.303843, 39.983412]);
	// 				    path.push([116.321354, 39.896436]);
	// 				    path.push([116.407012, 39.992093]);
	// 				   route = new AMap.DragRoute(map, path, AMap.DrivingPolicy.LEAST_FEE); //构造拖拽导航类
	// 				   route.search(); //查询导航路径并开启拖拽导航
	// 				// setTimeout(()=>search(),2000)
	// 			})
	// 			.catch((e) => {
	// 				console.log(e);
	// 			});

	// 	});

	// 	const search = () => {
	// 		map.plugin("AMap.Driving", function() {

	// 		});
	// 	}
	// 	onUnmounted(() => {
	// 		map?.destroy();
	// 	});
	// 
</script>

<!-- <template>
	<view>
		<view id="container" class="container">
		</view>
	</view>

</template> -->

/* .container {
width: 100%;
height: 800px;
} */