<template>
	<view class="main">
		<view id="arcgisView" class="map-view" :style="{height: systemInfo.windowHeight.value}" />
		<view class="cover-view-plus" :style="{top: systemInfo.statusBarHeight.value+20}">
			<block v-for="(menu,index) in verticalBar.menus.value" :key="index">
				<view class="verticalbar-btn" @click="verticalBar.setCurrent(menu.value)">
					<text class="iconfont btn-img"
						:style="{'color':verticalBar.currentBar.value === menu.value?'#3862F8':'#060F27'}">{{menu.iconUnicode}}</text>
					<text style="font-size: 24rpx;"
						:style="{'color':verticalBar.currentBar.value === menu.value?'#3862F8':'#060F27'}">{{menu.name}}</text>
				</view>
			</block>
			<view class="btn-line"></view>
			<view class="verticalbar-btn" @click="clearMap">
				<text class="custom-icon btn-img custom-icon-qingkong"></text>
				<text style="font-size: 24rpx;">清空</text>
			</view>
		</view>
		<view class="cover-view-loca" :class="verticalBar.currentBar.value" @click="getLocation">
			<button class="cover-loca-image" :loading="location.loading.value">
				<image class="loca-btn" src="/static/img/icons/location-black.png" mode="widthFix"></image>
			</button>
		</view>
		<scroll-view v-if="verticalBar.currentBar.value===''" :scroll-y="true" class="cover-view-menu border-box"
			:class="[touch.directionY.value==='up'?'pull-up':'']" @touchend="touchEnd" @touchstart="touchStart">
			<view class="menu-list flex-around">
				<template v-for="(layer,index) in onemapMenu.menus.value" :key="index">
					<view v-if="index<onemapMenu.showRootMenuCount.value" class="layer-box">
						<text class="layer-title">
							{{layer.name}}
						</text>
						<view class="layer-menu">
							<view class="menu-item" v-for="(menu,i) in layer.list" :key="i"
								@click="onemapMenu.setCurrent(menu.id, layer.id)">
								<view class="icon-bg" :class="[index>0?'round-rect':'']"
									:style="{'background':menu.color || (menu.id===onemapMenu.current.value?'#3862F8':'#E2E3E5')}">
									<text :class="['layer-menu-icon','custom-icon',menu.icon]"
										:style="{'color':menu.color? '#ffffff':menu.id===onemapMenu.current.value ?'#ffffff':'#060F27'}"></text>
								</view>

								<text class="layer-menu-text"
									:style="{'color':menu.id===onemapMenu.current.value ?'#3862F8':'#060F27'}">{{menu.name}}</text>
							</view>
						</view>
					</view>
				</template>
			</view>
		</scroll-view>
		<view v-if="verticalBar.currentBar.value ==='baselayer'" class="cover-view-menu baselayer">
			<view class="cover-header">
				<text class="title">选择底图</text>
				<text class="icon" @click="closeCover">x</text>
			</view>
			<view class="cover-main">
				<view class="item">
					<image class="item-image" @click="()=>toggleMap('vec')"
						src="http://t4.tianditu.gov.cn/vec_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=vec&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX=13&TILEROW=3457&TILECOL=6548&tk=e4e98a7455967290863f2f1bb245f7b5"
						mode=""></image>
					<text class="item-text">标准</text>
				</view>
				<view class="item">
					<image class="item-image" @click="()=>toggleMap('img')"
						src="http://t4.tianditu.gov.cn/img_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=img&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX=14&TILEROW=6916&TILECOL=13100&tk=e4e98a7455967290863f2f1bb245f7b5"
						mode=""></image>
					<text class="item-text">卫星</text>
				</view>
			</view>
		</view>
		<view v-if="verticalBar.currentBar.value==='layer'" class="cover-view-menu border-box layer">
			<view class="cover-header">
				<text class="title">选择图层</text>
				<text class="icon" @click="closeCover">x</text>
			</view>
			<scroll-view class="menu-list flex-around" :scroll-y="true">
				<view class="layer-box">
					<text class="layer-title">
						管点类
					</text>
					<view class="layer-menu">
						<view class="menu-item" v-for="(menu,i) in pipelayer.pointLayers.value" :key="i"
							@click="pipelayer.toggle(menu.layerid)">
							<view class="icon-bg"
								:style="{'background-color':pipelayer.selected.value.indexOf(menu.layerid)!==-1?'#3862F8':'#E2E3E5'}">
								<text :class="['layer-menu-icon','custom-icon',menu.icon]"
									:style="{'color':pipelayer.selected.value.indexOf(menu.layerid)!==-1?'#ffffff':'#060F27'}"></text>
							</view>

							<text class="layer-menu-text"
								:style="{'color':pipelayer.selected.value.indexOf(menu.layerid)!==-1?'#3862F8':'#060F27'}">{{menu.layername}}</text>
						</view>
					</view>
				</view>
				<view class="layer-box">
					<text class="layer-title">
						管线类
					</text>
					<view class="layer-menu">
						<view class="menu-item" v-for="(menu,i) in pipelayer.lineLayers.value" :key="i"
							@click="pipelayer.toggle(menu.layerid)">
							<view class="icon-bg"
								:style="{'background-color':pipelayer.selected.value.indexOf(menu.layerid)!==-1?'#3862F8':'#E2E3E5'}">
								<text :class="['layer-menu-icon','custom-icon',menu.icon]"
									:style="{'color':pipelayer.selected.value.indexOf(menu.layerid)!==-1?'#ffffff':'#060F27'}"></text>
							</view>

							<text class="layer-menu-text"
								:style="{'color':pipelayer.selected.value.indexOf(menu.layerid)!==-1?'#3862F8':'#060F27'}">{{menu.layername}}</text>
						</view>
					</view>
				</view>
			</scroll-view>

		</view>
	</view>
</template>

<script lang="ts" setup>
	import {
		onBeforeMount,
		onMounted
	} from 'vue'
	import {
		useLocation,
		useVerticalBar,
		useOneMapMenus,
		useTouch,
		usePipeLayers,
		useSystemInfo
	} from '../../common/hooks'
	import {
		changeBaseMap,
		initView,
		loadEsriModules
	} from '../../common/utils/arcMapHelper';
	const mapState: {
		view ? : __esri.MapView
		pipeLayer ? : __esri.MapImageLayer
		graphicsLayer ? : __esri.GraphicsLayer
		graphics: __esri.Graphic[]
	} = {
		graphics: []
	}
	const verticalBar = useVerticalBar();
	const location = useLocation();
	const onemapMenu = useOneMapMenus();
	const touch = useTouch();
	const systemInfo = useSystemInfo()
	let pullTimer = undefined;
	const pipelayer = usePipeLayers();
	const toggleMap = (type: string) => {
		if (!mapState.view?.map)
			changeBaseMap(mapState.view.map, type)
	};
	const closeCover = () => {
		verticalBar.setCurrent('')
	};
	const touchStart = (e: any) => {
		touch.touchStart(e)
	};
	const touchEnd = (e: any) => {
		touch.touchEnd(e)
		if (touch.directionY.value === 'up') {
			// 上拉需要等待上拉完成后再
			pullTimer = setTimeout(() => {
				onemapMenu.setRootMenuCount(onemapMenu.menus.value.length)
			}, 500)
		} else {
			clearTimeout(pullTimer)
			onemapMenu.setRootMenuCount(1)
		}
	};
	const clearMap = () => {
		// state.mapType = 'reset'
		// polygons.splice(0, polygons.length)
		onemapMenu.setCurrent('', '')
		closeCover()
	};
	const getLocation = () => {
		location.getLocation(() => {
			console.log(location.longitude, location.latitude);
		})
	};
	const loadPipeLayer = async () => {
		const [MapImageLayer] = await loadEsriModules(['esri/layers/MapImageLayer'])
		const pipeLayer = new MapImageLayer({
			id: 'pipe-layer',
			url: 'http://***********:6080/arcgis/rest/services/ANQING/PIPE_QY_ANQING_DYNAMIC/MapServer'
		})
		mapState.view?.map?.add(pipeLayer)
		await pipeLayer.when()
		const subLayers = pipeLayer.sublayers.items.map(item => {
			return item.id
		})
		mapState.view?.goTo(pipeLayer.fullExtent)
	}
	onBeforeMount(()=>{
		systemInfo.fitNav()
		console.log(systemInfo.statusBarHeight.value, systemInfo.windowHeight.value);
	})
	onMounted(() => {
		initView().then((view: __esri.MapView) => {
			mapState.view = view
			console.log(view);
			loadPipeLayer()
		}).catch(e=>{
			console.log(e);
		})
	})
</script>

<style scoped>
	.iconfont {
		font-family: iconfont;
	}
	.main {
		width: 750rpx;
		flex: 1;
		position: relative;
	}

	.map-view {
		width: 750rpx;
		flex: 1;
	}

	.cover-view-plus {
		position: absolute;
		top: 62rpx;
		right: 32rpx;
		width: 80rpx;
		background-color: #ffffff;
		border-radius: 20rpx;
		display: flex;
		flex-wrap: wrap;
		align-items: center;
		justify-content: center;
		padding: 8rpx 16rpx;
	}

	.verticalbar-btn {
		width: 48rpx;
		height: 96rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;


	}

	.btn-img {
		font-size: 48rpx;
	}

	.btn-line {
		width: 80rpx;
		height: 1rpx;
		border-width: 1rpx;
		border-style: solid;
		border-color: #EBEDF6;
	}

	.btn-img {
		font-size: 48rpx;
	}

	.cover-view-loca {
		width: 80rpx;
		height: 80rpx;
		background-color: #ffffff;
		border-radius: 16rpx;
		position: absolute;
		bottom: 450rpx;
		right: 32rpx;
		display: flex;
		align-items: center;
		justify-content: center;


	}

	.cover-loca-image {
		width: 40rpx;
		height: 40rpx;
		background-color: transparent;
		border: none;
		padding: 0;
		line-height: 40rpx;


	}

	.cover-loca-image::after {
		border: none;
		width: 40rpx;
		height: 40rpx;
		transform: scale(1);
	}

	.cover-loca-image.loca-btn {
		width: 40rpx;
		height: 40rpx;
	}

	.cover-view-menu {
		position: absolute;
		bottom: 0;
		background-color: #FBFBFB;
		height: 350rpx;

		transition: all 0.5s ease-in-out;


	}

	.cover-view-menu.pull-up {
		height: 800rpx;
	}

	.cover-view-menu.baselayer,
	.cover-view-menu.layer {
		height: 320rpx;
		width: 100%;
		padding: 0 32rpx;
		border-radius: 16rpx 16rpx 0rpx 0;

	}

	.cover-main {
		display: flex;
		justify-content: space-between;
		flex-wrap: nowrap;
		height: 200rpx;


	}

	.cover-main .item {
		width: calc(50% - 20rpx);
		height: 100%;
		position: relative;


	}

	.cover-main .item-image {
		width: 100%;
		height: 100%;
		border-radius: 8px;
	}

	.cover-main .item-text {
		background: rgba(255, 255, 255, 0.8);
		border-radius: 0px 0px 8px 8px;
		width: 100%;
		position: absolute;
		bottom: 0;
		left: 0;
		height: 48rpx;
		line-height: 48rpx;
		padding: 0 20rpx;
		font-size: 24rpx;
	}

	.cover-view-menu.baselayer {
		height: 320rpx;
	}

	.cover-view-menu.layer {
		height: 800rpx;
		overflow: hidden;

		.menu-list {
			height: 700rpx;
		}
	}

	.cover-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		height: 86rpx;


	}

	.cover-header .title {
		text-align: left;
		word-break: keep-all;
	}

	.cover-header .icon {
		font-size: 1.2em;
	}

	.cover-view-menu.layer {}

	.cover-view-menu.area {}

	.cover-view-menu.distance {}

	.border-box {
		width: 100%;
		padding-top: 30rpx;
		background-color: #FBFBFB;
		position: absolute;
		bottom: 0;
		align-items: center;
		justify-content: space-around;
		border-radius: 16rpx 16rpx 0rpx 0;


	}

	.menu-list {
		flex-direction: column;
		display: flex;
		justify-content: space-around;
		width: 100%;


	}

	.layer-box {
		width: 100%;
		padding: 0 32rpx 25rpx;


	}

	.layer-title {
		color: #91949F;
	}

	.layer-menu {
		display: flex;
		justify-content: flex-start;
		align-items: center;
		flex-wrap: wrap;
		border-radius: 8px;
		background-color: #ffffff;
		margin-top: 20rpx;
		padding-bottom: 20rpx;



	}

	.menu-item {
		width: 20%;
		align-items: center;
		padding: 25rpx 0 0;
		text-align: center;
		display: flex;
		justify-content: center;
		flex-direction: column;


	}

	.icon-bg {
		border-radius: 50%;
		width: 64rpx;
		height: 64rpx;
		display: flex;
		align-items: center;
		justify-content: center;


	}

	.icon-bg.round-rect {
		border-radius: 16rpx;
	}

	.layer-menu-text {
		word-break: keep-all;

	}
</style>
