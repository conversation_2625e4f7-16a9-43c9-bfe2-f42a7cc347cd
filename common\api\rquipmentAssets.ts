import {
	http
} from '../vmeitime-http'

export const getDeviceStorageJournalList = (params: {
	page: number
	size: number
	deviceTypeId?: string
}) => {
	return http().get('api/deviceStorageJournal', params)
}

export const getDeviceDetail = (id: string) => {
	return http().get(`api/deviceStorageJournal/${id}/detail`)
}

export const getDeviceTypeAttr = (code:string) => {
	return http().get(`api/deviceTypeAttr/list/${code}`,)
}
export const getDeviceTypeTree = () => {
	return http().get('api/deviceType/tree')
}

export const getFaultInfo = (code:string) => {
	return http().get(`api/fault/report/statistics/${code}`)
}

export const getFaultList = (code:string,params:{
	page:number,
	size:number
}) => {
	return http().get(`api/fault/report/list/${code}`,params)
}

export const getMaintainInfo = (code:string) => {
	return http().get(`api/maintain/task/c/statistics/${code}`)
}

export const getMaintainList = (code:string,params:{
	page:number,
	size:number
}) => {
	return http().get(`api/maintain/plan/m/list/${code}`,params)
}


export const getCircuitInfo = (code:string) => {
	return http().get(`api/circuit/task/c/statistics/${code}`)
}

export const getCircuitList = (code:string,params:{
	page:number,
	size:number
}) => {
	return http().get(`api/circuit/plan/m/list/${code}`,params)
}

export const getCircuitRecordList = (params:{
	page:number,
	size:number,
	startStartTime?:number | '',
	endStartTime?:number| '',
}) => {
	return http().get(`api/circuit/task/m`,params)
}

//收藏
export const checkCollect = (params:{
	id:string,
	isCollect:string| '',
}) => {
	return http().post(`api/deviceStorageJournal/collect`,params)
}


