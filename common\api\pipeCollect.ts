import { http } from '../vmeitime-http/index'
/**
 * 任务列表
 */
export type IPipeCollectTaskQueryParams = {
	/**当前页*/
	page ?: number;
	/**每页数量*/
	size ?: number;
	/**1待接收 2处理中  3待审核  4完成 5审核不通过*/
	status ?: string;
	/**创建时间开始时间戳*/
	start ?: string;
	/**创建时间结束时间戳*/
	end ?: string;
	/**创建人id*/
	creator ?: string;
	/**处理人id*/
	processUser ?: string;
	/**审核人id*/
	reviewUser ?: string;
}
export const GetPipeCollectTasks = (params : IPipeCollectTaskQueryParams) => {
	return http().get('/api/spp/collect/list', params)
}
/**
 * 新增工程任务
 */
export const PostPipeCollectTask = (params : {
	/**"任务名称"*/
	"name" : string;
	/**"处理人id"*/
	"processUser" : string;
	/**"处理人部门id"*/
	"processUserDepartmentId" : string;
	/**"说明"*/
	"remark" : string;
	/**"审核人id"*/
	"reviewUser" : string;
	/**"地址*/
	"address" : string;
}) => {
	return http().post('api/spp/collect', params)
}
/**
 * 删除采集任务
 */
export const DeletePipeCollectTask = (ids : string[]) => {
	return http().delete('api/spp/collect', ids)
}
/**
 * 接收采集任务
 */
export const ReceivePipecollectTask = (id : string) => {
	return http().post(`api/spp/collect/receive/${id}`)
}
/**
 * 提交采集任务
 */
export const SubmitPipeCollectTask = (params : { id : string; remark : string }) => {
	return http().post('api/spp/collect/submit', params)
}
/**
 * 审核采集任务
 */
export const AuditPipeCollectTask = (params : {
	id : string; remark : string;
	/**
	 * 审核结果：4：通过，5：不通过
	 */
	status : string
}) => {
	return http().post('api/spp/collect/review', params)
}
/**
 * 获取采集的点信息
 */
export const GetPipeCollectDataList = (params : {
	mainId : string
	layerId : string
	start ?: number
	end ?: number
	creator ?: string
	name ?: string
}) => {
	return http().get('api/spp/collect/data/list', params)
}
/**
 * 上传采集数据
 */
export const PostPipeCollectData = (params : {
	/**""*/
	"sid" ?: any,
	/**""*/
	"start_sid" ?: any,
	/**""*/
	"end_sid" ?: any,
	/**""*/
	"start_depth" ?: any,
	/**""*/
	"end_depth" ?: any,
	/**""*/
	"material" ?: any,
	/**""*/
	"laneway" ?: any,
	/**""*/
	"address" ?: any,
	/**""*/
	"burytype" ?: any,
	/**""*/
	"diameter" ?: any,
	/**""*/
	"subtype" ?: any,
	/**""*/
	"pipelength" ?: any,
	/**""*/
	"depth" ?: any,
	/**""*/
	"x" ?: any,
	/**""*/
	"y" ?: any,
	/**""*/
	"z" ?: any,
	end_x?: any,
	end_y?: any,
	end_z?: any,
	/**"名*/
	"name" ?: any,
	/**"工*/
	"mainId" ?: any,
	/**"图*/
	"layerId" ?: any,
}[]) => {
	return http().post('api/spp/collect/data', params)
}
/**
 * 删除采集的数据
 */
export const DeletePipeCollectData = (ids : string[]) => {
	return http().delete('api/spp/collect/data', ids)
}