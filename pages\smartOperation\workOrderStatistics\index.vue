<!-- 工单统计 -->
<template>
	<view class="main">
		<view class="w-card" style="background-image: url('static/img/icons/waterWorks-bg1.png')"
			@click="toTrendAnalysis">
			<text>事件趋势分析</text>
		</view>
		<view class="w-card" style="background-image: url('static/img/icons/waterWorks-bg2.png')"
			@click="toTypeAnalysis">
			<text>事件类型分析</text>
		</view>
		<view class="w-card" style="background-image: url('static/img/icons/waterWorks-bg3.png')"
			@click="toEscalationAnalysis">
			<text>人员上报分析</text>
		</view>
	</view>
</template>

<script lang="ts" setup>
	// 事件趋势分析
	const toTrendAnalysis = () => {
		uni.$u.route({
			url: 'pages/smartOperation/workOrderStatistics/trendAnalysis/index'
		})
	}
	// 事件类型分析 
	const toTypeAnalysis = () => {
		uni.$u.route({
			url: 'pages/smartOperation/workOrderStatistics/typeAnalysis/index'
		})
	}
	// 人员上报分析 
	const toEscalationAnalysis = () => {
		uni.$u.route({
			url: 'pages/smartOperation/workOrderStatistics/escalationAnalysis/index'
		})
	}
</script>

<style lang="scss" scoped>
	.w-card {
		height: 240rpx;
		width: 686rpx;
		border-radius: 16rpx;
		background-color: red;
		margin: 10rpx auto;
		background-size: 100% 100%;
		line-height: 240rpx;
		text-align: center;

		text {
			font-size: 34rpx;
			color: #FFFFFF;
		}
	}
</style>
