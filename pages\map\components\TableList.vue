<template>
	<view class="table-view">
		<view class="table-header-wrapper flex" v-if="props.listColumns?.length">
			<view class="table-cell table-header-cell" v-for="(item) in props.listColumns" :key="item.prop">
				{{item.label}}
			</view>
		</view>
		<view v-if="props.list.length">
			<scroll-view :style="{height: props.height+'rpx'}" class="table-body-wrapper"
				v-if="props.listColumns?.length" scroll-y="true" scroll-x="true" scroll-with-animation="true">

				<view class="table-row flex" v-for="(item,index) in props.list" :key="index">

					<view class="table-cell" v-for="(column) in props.listColumns" :key="props">
						{{item[column.prop]}}
					</view>
				</view>
			</scroll-view>
			<scroll-view :style="{height: props.height+'rpx'}" class="table-body-wrapper" v-else scroll-y="true"
				scroll-x="true" scroll-with-animation="true">

				<view class="table-row flex" v-for="(item,index) in props.list" :key="index">

					<view class="name table-cell">
						{{item.name}}
					</view>
					<view class="value table-cell">
						{{item.value}}
					</view>
				</view>


			</scroll-view>
		</view>

		<view v-else class="empty">暂无内容</view>
	</view>
</template>

<script lang="ts" setup>
	const props = defineProps<{
		height ?: number;
		list ?: { name : string; value : any }[];
		listColumns ?: { label : string; prop : string }[]
	}>()
</script>

<style lang="scss" scoped>
	.table-view {
		width: 100%;
		box-shadow: 0 0 2rpx #909399 inset;
	}

	.table-header-wrapper {
		background-color: #F9F9F9;
	}

	.table-header-wrapper,
	.table-row {
		display: flex;
		height: 64rpx;
		// border: 2rpx #EBEDF6 solid;
		// border-bottom: none;
		// box-sizing: border-box;
		box-shadow: 0 0 2rpx #909399 inset;
	}

	.table-cell {
		height: 100%;
		width: 33.3%;
		display: grid;
		place-items: center;
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
		text-align: center;
		box-shadow: 0 0 2rpx #909399 inset;
		// border-left: none;
		// border-top: none;
		// border-bottom: none;
		// border-right: 2rpx #EBEDF6 solid;
		// &:last-child{
		// 	border-right: none;
		// }
	}

	.table-body-wrapper {
		background-color: #F9F9F9;
		position: relative;
		// border: 1rpx #EBEDF6 solid;


		.table-row {
			height: 64rpx;
			line-height: 64rpx;
			// border: 1rpx #EBEDF6 solid;
			color: #91949F;

			.name {
				width: 50%;
			}

			.value {
				width: 50%;
			}
		}
	}

	.empty {
		height: 120rpx;
		display: grid;
		place-items: center;
		color: rgba(0, 0, 0, 0.5);
	}
</style>