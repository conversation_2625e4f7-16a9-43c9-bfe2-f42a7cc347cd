<template>
	<view class="main">
		<FileUpload ref="refFileUpload" class="detail"></FileUpload>
		<view class="button">
			<u-button type="primary" color="#3862F8" @click="handleSubmit">上传</u-button>
		</view>
	</view>
</template>

<script lang="ts" setup>
	import {
		onLoad
	} from '@dcloudio/uni-app'
	import {
		reactive,
		ref
	} from "vue";
	import FileUpload from "@/components/fileUpload/fileUpload.vue";
	import {
		postPipeAdditionalInfo
	} from "@/common/api/map";
	const state = reactive<{
		layerid : string
		id : any
	}>({
		layerid: '',
		id: ''
	})
	const refFileUpload = ref<InstanceType<typeof FileUpload>>()
	const handleSubmit = async () => {
		if (state.id === undefined || state.layerid === undefined) return
		try {
			const {
				videoList,
				fileList,
				imageList,
				voiceList
			} = refFileUpload.value
			console.log(videoList);
			console.log(fileList);
			console.log(imageList);
			console.log(voiceList);
			console.log(state.layerid);
			console.log(state.id);
			await postPipeAdditionalInfo({
				objectid: state.id,
				layerid: state.layerid,
				img: imageList?.join(','),
				audio: voiceList?.join(','),
				vidio: videoList?.join(','),
				files: fileList?.join(',')
			})
			uni.showToast({
				title: '操作成功',
				complete: () => {
					uni.navigateBack({ delta: 1 })
				}
			})
		} catch (e) {
			//TODO handle the exception
			console.log(e);
			uni.showToast({
				title: '操作失败',
				icon: 'error'
			})
		}
	}
	onLoad((options : any) => {
		state.layerid = decodeURIComponent(options.layerid)
		state.id = JSON.parse(decodeURIComponent(options.id))
	})
</script>

<style lang="scss" scoped>
	// .detail {
	// 	:deep(.content-card) {
	// 		width: 686rpx;
	// 		margin: 20rpx auto;
	// 		background-color: #FFFFFF;
	// 		min-height: 224rpx;
	// 		border-radius: 8px;
	// 		padding: 24rpx 0 0 28rpx;

	// 		text {
	// 			color: #91949F;
	// 			font-size: 28rpx;
	// 		}

	// 		.camera {
	// 			width: 112rpx;
	// 			height: 112rpx;
	// 			margin-top: 20rpx;
	// 			display: flex;
	// 			justify-content: center;
	// 			flex-direction: column;
	// 			text-align: center;
	// 			background: #F9F9F9;

	// 			text {
	// 				color: #91949F;
	// 				font-size: 20rpx;
	// 			}
	// 		}

	// 		.margin-center {
	// 			margin: 0 auto;

	// 			.icon {
	// 				font-size: 48rpx;
	// 			}
	// 		}
	// 	}
	// }

	// ::v-deep .u-form-item__body {
	// 	padding: 16rpx;
	// }

	// ::v-deep .u-form-item {
	// 	padding: 0;
	// }
</style>