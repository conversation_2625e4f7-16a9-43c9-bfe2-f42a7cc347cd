<template>
	<view class="image-uploader">
		<!-- <view class="card-title flex-center">
			<text class="custom-icon custom-icon-xiangji"></text>
			<view class="label">图片</view>
		</view>
		<u-gap height="2" bg-color="#EBEDF6"></u-gap> -->
		<view v-if="!props.readonly" class="list">
			<u-upload :custom-btn="true" :header="header" width="112rpx" height="112rpx" @onRemove="deletePic"
				:source-type="['album','camera']" :show-upload-list="!!imageList?.length" :action="actionUrl"
				@onSuccess="onSuccess">
				<template #addBtn>
					<view class="file-s">
						<view class="margin-center">
							<text class="custom-icon custom-icon-xiangji icon"></text>
						</view>
						<text>拍照上传</text>
					</view>
				</template>
			</u-upload>
		</view>
		<view v-if="props.readonly" class="list images">
			<template v-if="imageList.length">
				<image class="image" v-for="(item,i) in imageList" :key="i" :src="item" mode="aspectFill"
					@click="previewImage(item)" @error="imageError($event,i)">
				</image>
			</template>
			<template v-else>
				<view class="empty">
					<text>暂无图片信息</text>
				</view>
			</template>
		</view>

	</view>
</template>

<script setup lang="ts">
	import {
		ref,
		reactive,
		onBeforeMount,
		watch
	} from 'vue'
	const emit = defineEmits(['success'])
	const props = defineProps<{
		readonly ?: boolean
		imgs ?: string[]

	}>()

	const imageList = ref<string[]>(props.imgs || [])
	const actionUrl = ref<string>('')
	const header = reactive<any>({
		'X-Authorization': 'Bearer ' + uni.getStorageSync('token')
	})
	const onSuccess = (data, index, lists, name) => {
		// console.log(data,lists);
		// console.log(...args);
		console.log(data, index, lists, name);
		if (data.code === 200)
			imageList.value = lists.map((img : any) => {
				return img.response?.data?.url
			}).filter(item => !!item)
		emit('success', imageList.value)
		console.log(imageList.value)
	}

	const deletePic = event => {
		imageList.value.splice(event.index, 1)
		console.log('imageList.value',imageList.value)
		emit('success', imageList.value)
	}
	const previewImage = (url ?: string) => {
		if (!url) return
		uni.previewImage({
			urls: [url]
		})
	}
	const imageError = (e : any, i : number) => {
		console.log(e);
		imageList.value[i] = '../../static/img/404.png'
	}
	watch(() => props.imgs, () => imageList.value = props.imgs || [])
	onBeforeMount(async () => {
		// console.log('globalProperties-----', globalProperties)
		actionUrl.value = uni.getStorageSync('url') + 'file/api/upload/file' //globalProperties.$uploadUrl
	})
	defineExpose({
		imageList,
	});
</script>
<style lang="scss" scoped>
	.empty {
		display: flex;
		align-items: center;
		height: 80rpx;
	}

	.image-uploader {
		margin: 0 auto;
		// margin-top: 20rpx;
		background-color: #FFFFFF;
		min-height: 80rpx;
		border-radius: 8px;
		// padding: 24rpx 28rpx;

		.title-text {
			color: #91949F;
			font-size: 28rpx;
		}

		.file-s {
			width: 116rpx;
			height: 116rpx;
			display: flex;
			justify-content: center;
			flex-direction: column;
			text-align: center;
			background: #F9F9F9;
			position: relative;

			text {
				color: #91949F;
				font-size: 20rpx;
			}

			.close-icon {
				border-radius: 50%;
				width: 32rpx;
				height: 32rpx;
				background-color: red;
				line-height: 28rpx;
				text-align: center;
				position: absolute;
				right: 0;
				top: 0;
			}
		}

		.margin-center {
			margin: 0 auto;
			width: 48rpx;
			height: 48rpx;

			.icon {
				font-size: 48rpx;
			}
		}
	}

	::v-deep .u-form-item__body {
		padding: 16rpx;
	}

	::v-deep .u-form-item {
		padding: 0;
	}

	.card-box {
		width: 686rpx;
		border-radius: 8px;
		padding: 0;
		margin: 20rpx auto;
		color: #FFFFFF;
		background-color: #FFFFFF;

		.card-title {
			padding: 14rpx 28rpx;

			text {
				font-size: 32rpx;
				color: #3862F8;
			}

			.label {
				padding-left: 10rpx;
				font-style: normal;
				font-weight: 600;
				font-size: 28rpx;
				color: #060F27;
			}
		}

		.list {
			padding: 10rpx 16rpx;
			color: #060F27;

			.l-c {
				padding: 10rpx 0;

				text {
					&:nth-child(1) {
						color: #91949F;
					}

					&:nth-child(3) {
						color: #060F27;
					}
				}
			}

			.l-file {
				padding: 10rpx 0;
			}

			&.images {
				display: flex;
				justify-content: flex-start;
				align-items: center;
				flex-wrap: wrap;

				.image {
					height: 112rpx;
					width: 112rpx;
					margin-right: 12rpx;
				}
			}
		}
	}
</style>