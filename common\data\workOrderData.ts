import {
	// workOrderTypeList,
	urgencyList,
	organizationList,
	departmentList,
	getAllByPid,
	getWorkOrderResourceList,
	getWorkOrderTypeList,
	getWorkOrderEmergencyLevelList,
	getWorkOrderProcessLevelList,
	getFaultKnowledgeSerch
} from '@/common/api/workOrder'
// 获取工单类型树
// export const getWorkOrderTypeList = async () => {
// 	const res = await workOrderTypeList({
// 		isDel: 0
// 	})
// 	return (res.data?.data || [])
// }

// 获取紧急程度
export const getUrgencyList = async () => {
	const res = await urgencyList({
		isDel: 0
	})
	return (res.data?.data || [])
}

// 获取组织列表
export const getOrganization = async () => {
	const res = await organizationList()
	return (res.data?.data || [])
}

// 获取组织部门列表
export const getDepartmentList = async (id : String) => {

	const res = await departmentList({
		parentId: id
	})
	return (res.data?.data || [])
}

// 获取用户列表
export const getStepProcessUser = async (id : string) => {
	const params = {
		pid: id,
		status: 1,
		page: 1,
		size: 9999
	}
	const res = await getAllByPid(params)
	return (res.data?.data?.data || [])
}

export const processLevelList = async () => {
	return await workOrderProcessLevelList()
}

export const alarmStatus = () => {
	return [
		{ name: '未确认', value: 'CONFIRM_UNACK' },
		{ name: '已确认', value: 'CONFIRM_ACK' },
		{ name: '已恢复', value: 'RESTORE_ACK' },
		{ name: '强制解除', value: 'CLEAR_FORCED' },
		{ name: '解除', value: 'CLEARED_ACK' }
	]
}
export const alarmLevels = () => {
	return ['全部', '提示', '重要', '紧急', '次要']
}

export const alarmTypes = () => {
	return ['设备离线', '变动告警', '范围告警']
}

// 事件来源
export const workOrderResourceList = async () => {
	const res = await getWorkOrderResourceList({ status: '1', page: 1, size: 999 })
	return traverse(res.data.data.data || [], 'children', { label: 'name', value: 'name' })
}

// 工单类型
export const workOrderTypeList = async () => {
	const res = await getWorkOrderTypeList('1')
	const data = traverse(res.data.data || [], 'children', { label: 'name', value: 'name' })
	console.log('data', data)
	return data
}


// 紧急程度
export const workOrderEmergencyLevelList = async () => {
	const res = await getWorkOrderEmergencyLevelList('1')
	return traverse(res.data.data || [], 'children', { label: 'name', value: 'name' })
}


// 处理级别
export const workOrderProcessLevelList = async () => {
	const res = await getWorkOrderProcessLevelList('1')
	return traverse(res.data.data || [], 'children', { label: 'name', value: 'name' })
}
export const getFaultKnowledgeValue = async () => {
	const params = { size: 99999, page: 1 }
	const res = await getFaultKnowledgeSerch(params)
	return traverse(res.data.data.data || [], 'children', { label: 'name', value: 'id' })
}

export function traverse(
	val : any,
	children : 'children' | string = 'children',
	keys : { label : 'name'; value : 'id' } | any = { label: 'name', value: 'id' },
	newData : any[] = []
) {
	val.map(obj => {
		if (obj) {
			for (const i in keys) {
				if (Array.isArray(keys[i])) {
					let value = obj
					for (const j of keys[i]) {
						obj[i] = value[j]
						value = obj[i]
					}
				} else {
					obj[i] = obj[keys[i]]
				}
			}
			if (obj[children] && obj[children].length) {
				traverse(obj[children], children, keys, newData)
			} else {
				delete obj.children
				newData.push(obj)
			}
		}
		return obj
	})
	return newData
}