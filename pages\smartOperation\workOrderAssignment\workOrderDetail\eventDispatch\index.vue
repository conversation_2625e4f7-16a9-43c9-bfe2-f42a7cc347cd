<!-- 事件分派 -->
<template>
	<view class="main">
		<view class="uform">
			<u-form :model="form" ref="refForm" :label-style="{'color':'#91949F'}" borderBottom labelWidth="180"
				:errorType="['toast']" input-align="right">
				<view class="card-box">
					<!-- <u-form-item required label="处理人：" prop="form.stepProcessUserName">
						<input placeholder="请选择处理人" inputmode="none" placeholder-class="placeholderClass" v-model="form.stepProcessUserName" inputAlign="right"
							@click="showAssignment">
						</input>
						<template #right>
							<u-icon name="arrow-right" size="28"></u-icon>
						</template>
					</u-form-item> -->
					<u-form-item required label="处理人：" prop="stepProcessUserName" labelWidth="220">
						<input placeholder="请选择处理人"  inputmode="none" placeholder-class="placeholderClass" v-model="form.stepProcessUserName" inputAlign="right"
							@click="toChooseUser">
						</input>
						<template #right>
							<u-icon name="arrow-right" size="28"></u-icon>
						</template>
					</u-form-item>
					<u-form-item required label="处理级别：" prop="processLevelLabel">
						<input v-model="form.processLevelLabel"  inputmode="none" placeholder-class="placeholderClass" placeholder="请选择处理级别" input-align="right" 
							@click="state.processLevelShow=true"></input>
						<template #right>
							<u-icon name="arrow-right" size="28"></u-icon>
						</template>
					</u-form-item>
					<u-form-item required label="预计完工时间：" prop="endTime" labelWidth="220">
						<input placeholder="请选择预计完工时间" inputmode="none" placeholder-class="placeholderClass" v-model="form.endTime" inputAlign="right">
						</input>
						<template #right>
							<u-icon name="calendar" size="34"></u-icon>
						</template>
					</u-form-item>
					<u-form-item label="备注：" :borderBottom="false">
					</u-form-item>
					<u-form-item label="" prop="remark">
						<u-input type="textarea" input-align="left"  placeholder="请输入备注" v-model="form.remark" border height="160">
						</u-input>
					</u-form-item>
				</view>
				<!-- <view class="card-box">
					<u-form-item label="共同处理人：" prop="form.val1" :borderBottom="false">
						<u-input placeholder="请选择共同处理人" v-model="form.name" inputAlign="right">
						</u-input>
						<template #right>
							<u-icon name="arrow-right" size="28"></u-icon>
						</template>
					</u-form-item>
				</view> -->
			</u-form>
		</view>
		<view class="button">
			<u-button type="primary" color="#3862F8" @click="submit">提交</u-button>
		</view>
		<!-- 区域 -->
		<!-- <u-picker v-model="state.assignmentShow" mode="multiSelector" :default-selector="[0]"
			:range="assignmentList" range-key="name" @columnchange="acolumnchange" @confirm="selectClick">
		</u-picker> -->
		<u-select v-model="state.assignmentShow" mode="mutil-column-auto" :list="assignmentList"></u-select>
		<!-- 处理级别 -->
		<u-picker v-model="state.processLevelShow" mode="selector" :default-selector="[0]"
			:range="processLevels" range-key="name" @confirm="selectProcessLevel"></u-picker>
		<u-toast ref="refToast" />
	</view>
</template>

<script lang="ts" setup>
	import {
		onMounted,
		reactive,
		ref
	} from "vue";
	import {
		onShow,
		onReady
	} from '@dcloudio/uni-app';
	import {
		useStore
	} from '@/store/index'
	import dayjs from 'dayjs'
	import {
		removeSlash
	} from '@/common/utils/removeIdSlash'
	import {
		getOrganization,
		getDepartmentList,
		workOrderProcessLevelList,
		getStepProcessUser
	} from '@/common/data/workOrderData'
	import {
		assignWorkOrder
	} from '@/common/api/workOrder'
	import {
		storeToRefs
	} from 'pinia'
	const store = useStore();
	const state = reactive < {
		assignmentShow: Boolean
		processLevelShow: Boolean
		processLevelIndex: any
		assignmentIndex: any
	} > ({
		assignmentShow: false,
		processLevelShow: false,
		processLevelIndex: [0],
		assignmentIndex: [0, 0, 0]
	})

	const rules = reactive < any > ({
		stepProcessUserName: [{
			required: true,
			message: '请选择处理人'
		}],
		processLevel: [{
			required: true,
			message: '请选择处理级别'
		}]
	})

	const processLevels = ref < any > ()
	const refForm = ref < any > ()
	const refToast = ref < any > ()
	const organizations = ref < any > ([])
	const departments = ref < any > ([])
	const processUsers = ref < any > ([])
	const assignmentList = ref < any > ([{}, {}, {}])
	const workorderId = ref < String > ('')
	const form = reactive < any > ({
		stepProcessUserId: '',
		processLevel: '',
	})

	// 获取组织部门列表
	const getDepartment = async (id: String) => {
		departments.value = await getDepartmentList(id)
		assignmentList.value[1] = departments.value
		const dId = departments.value[state.assignmentIndex[1]]?.id as String
		await getProcessUser(dId)

	}
	// 获取组织部门用户列表
	const getProcessUser = async (id: String) => {
		processUsers.value = await getStepProcessUser(id)
		assignmentList.value[2] = processUsers.value.length > 0 ? processUsers.value.map(data => {
			return {
				...data,
				name: data.firstName
			}
		}) : [{}]
		console.log(assignmentList.value)
	}

	// 选择紧急程度
	// const selectProcessLevel = (val: any) => {
	// 	state.processLevelIndex = val
	// 	const processLevel = processLevels.value[val[0]]
	// 	form.processLevelName = processLevel.name
	// 	form.processLevel = processLevel.value
	// 	let addTime = 0
	// 	const hourIndex = processLevel.name.indexOf('小时')
	// 	const dayIndex = processLevel.name.indexOf('天')
	// 	console.log( processLevel.name)
	// 	const num = processLevel.name.match(/\d+/g)
	// 	console.log(num)
	// 	if (hourIndex !== -1) {
	// 		addTime = parseInt(num)
	// 	} else if (dayIndex !== -1) {
	// 		addTime = parseInt(num) * 24
	// 	}
	// 	console.log(addTime)
	// 	form.endTime = dayjs().add(addTime, 'hour').format('YYYY-MM-DD HH:mm:ss')
	// 	state.processLevelIndex = [0]
	// }
	// 选择紧急程度
	const selectProcessLevel = (val: any) => {
		state.processLevelIndex = val
		const processLevel = processLevels.value[val[0]]
		form.processLevelLabel = processLevel.name
		form.processLevel = processLevel.dayTime * 1440 + processLevel.hourTime * 60 + processLevel.minuteTime
		let addTime = 0
		// const hourIndex = processLevel.name.indexOf('小时')
		// const dayIndex = processLevel.name.indexOf('天')
		// if (hourIndex > -1) {
		// 	addTime = parseInt(processLevel.name.substring(0, hourIndex))
		// } else if (dayIndex > -1) {
		// 	addTime = parseInt(processLevel.name.substring(0, dayIndex)) * 24
		// }
		form.endTime = dayjs().add(form.processLevel, 'minute').format('YYYY-MM-DD HH:mm:ss')
	}
	const toChooseUser = () => {
		uni.$u.route({
			url: 'pages/userTree/organization',
		})
	}
	// 切换处理人组织部门获取用户列表
	const acolumnchange = async ({
		column: column,
		index: index
	}) => {
		console.log(column, index)
		if (column === 0) {
			await getDepartment(organizations.value[index]?.id)
		} else if (column === 1) {
			await getProcessUser(departments.value[index]?.id)
		}
	}

	// 选择处理人信息
	const selectClick = (val: any) => {
		const processUser = processUsers.value[val[2]]
		if (processUser) {
			state.assignmentIndex = val
			form.stepProcessUserName = processUser.firstName
			form.stepProcessUserId = removeSlash(processUser.id?.id)
		} else {
			uni.showToast({
				title: '请选择处理人',
				icon: 'none'
			})
		}
	}

	const showAssignment = async () => {
		organizations.value = await getOrganization()
		assignmentList.value[state.assignmentIndex[0]] = organizations.value
		await getDepartment(organizations.value[state.assignmentIndex[0]]?.id)
		state.assignmentShow = true
	}

	const submit = () => {
		const params = {
			stepProcessUserId: form.stepProcessUserId,
			processLevel: form.processLevel,
			processLevelLabel: form.processLevelLabel,
			remark: form.remark,
		}
		refForm.value.validate(async (valid: any) => {
			if (valid) {
				uni.showModal({
					title: '提示',
					content: '确定分派吗',
					success: (res) => {
						if (res.confirm) {
							assignWorkOrder(workorderId.value, params).then(res => {
								if (res.data?.code === 200) {
									let {
										userData
									} = storeToRefs(store);
									userData.value = null
									refToast.value.show({
										title: '分派成功',
										type: 'success',
										callback: () => {
											uni.navigateBack({
												delta: 2
											})
										}
									})
								} else {
									refToast.value.show({
										title: '分派失败',
										type: 'error'
									})
								}
							})
						}
					}
				})
			}
		})
	}

	// 选择处理人
	const chooseUser = (user: any) => {
		if (user) {
			form.stepProcessUserName = user.firstName
			form.stepProcessUserId = removeSlash(user.id?.id)
		}
	}

	onReady(async () => {
		console.log('ready')
		refForm.value.setRules(rules)
	})

	onShow(async () => {
		let {
			userData
		} = store;
		if (userData) {
			chooseUser(userData)
		}
	})
	onMounted(async () => {
		var pages = getCurrentPages();
		var page = pages[pages.length - 1];
		workorderId.value = page.$page.options.id
		processLevels.value = await workOrderProcessLevelList()

	})
</script>

<style lang="scss" scoped>
	.main {
		.uform {
			padding: 20rpx 32rpx;
		}

		.card-box {
			border-radius: 8px;
			min-height: 80rpx;
			padding: 20rpx 28rpx;
			margin: 0 auto 20rpx auto;
			color: #FFFFFF;
			background-color: #FFFFFF;

			.form {
				padding: 0 28rpx 10rpx 28rpx;
				color: #060F27;
			}

			.l-file {
				padding: 10rpx 0;
			}
		}


		::v-deep.u-form-item {
			padding: 0;
		}
	}
</style>
