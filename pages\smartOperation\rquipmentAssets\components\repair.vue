<!-- 维修 -->
<template>
	<view class="">
		<view class="card-box">
			<view class="flex-between hand">
				<view class="hand-title flex-center">
					<image src="@/static/img/icons/devices/title-icon.png" style="width: 20rpx;height: 40rpx;"></image>
					<text>故障统计</text>
				</view>
			</view>
			<u-gap height="1" bg-color="#bbb"></u-gap>
			<view class="table" style="padding: 0rpx;">
				<view class="info flex-center" style="background-color: #eaeaea;padding: 24rpx">
					<image src="@/static/img/icons/devices/failure-num.png" class="icon-img"></image>
					<text >维修次数：</text> <text>{{faultInfo.count}}</text>
				</view>
				<view class="info flex-center" style="background-color: #eaeaea;padding: 24rpx;margin-top: 10rpx;">
					<image src="@/static/img/icons/devices/repair-time.png" class="icon-img"></image>
					<text>最近维修：</text> <text>{{faultInfo.latestRepairTime}}</text>
				</view>
			</view>
		</view>

		<view class="card-box">
			<view class="flex-between hand">
				<view class="hand-title flex-center">
					<image src="@/static/img/icons/devices/title-icon.png" style="width: 20rpx;height: 40rpx;"></image>
					<text>故障等级占比</text>
				</view>
			</view>
			<u-gap height="1" bg-color="#d4d4d4"></u-gap>
			<view class="table">
				<view class="line-echarts">
					<l-echart ref="pieChart"></l-echart>
				</view>
			</view>
		</view>

		<view class="card-box">
			<view class="flex-between hand">
				<view class="hand-title flex-center">
					<image src="@/static/img/icons/devices/title-icon.png" style="width: 20rpx;height: 40rpx;"></image>
					<text>近六月维修情况</text>
				</view>
			</view>
			<u-gap height="1" bg-color="#bbb"></u-gap>
			<view class="table">
				<view class="line-echarts">
					<l-echart ref="lineChart"></l-echart>
				</view>
			</view>
		</view>

		<view class="card-box">
			<view class="flex-between hand">
				<view class="hand-title flex-center">
					<image src="@/static/img/icons/devices/title-icon.png" style="width: 20rpx;height: 40rpx;"></image>
					<text>维修记录</text>
				</view>
			</view>
			<u-gap height="1" bg-color="#bbb"></u-gap>
			<view class="table" style="padding: 0rpx;min-height: 200rpx;">
				<view class="list" v-for="(item,index) in faultList" :key="index">
					<text style="font-weight: 600;">{{item.title || '-'}}</text>
					<view class="info flex-between" style="padding: 10rpx 0rpx;">
						<text>任务类型：</text> <text>{{item.type}}</text>
						<text>开始时间：</text> <text>{{item.startTime || ''}}</text>
					</view>
					<view class="info flex-between" style="padding: 0rpx;" @click="toWorkOrder(item.workOrderId)">
						<text>关联工单：</text> <text style="color:#0055ff">{{item.workOrderName}}</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script lang="ts" setup>
	import {
		nextTick,
		onMounted,
		ref
	} from 'vue'
	import * as echarts from 'echarts'
	import {
		getFaultInfo,
		getFaultList
	} from '@/common/api/rquipmentAssets'
	import {
		pieOption,
		lineOption
	} from './echartsData'
	const pieChart = ref < any > ({})
	const lineChart = ref < any > ({})
	const faultList = ref < any > ([])
	const faultInfo = ref < any > ({})
	const props = defineProps({
		detail: {
			type: Object,
		}
	})

	const refreshData = async (code: string) => {
		const res = await getFaultInfo(code)
		faultInfo.value = res.data.data
		console.log(res.data.data)
		const geadeCount = faultInfo.value.gradeCount.map((data: any) => {
			return {
				value: data.count,
				name: data.level
			}
		})
		const dataX = faultInfo.value.nowYearRepair.map((data: any) => data.month)
		const data = faultInfo.value.nowYearRepair.map((data: any) => data.count)
		initPie(geadeCount)
		initLine(dataX, data)
		const res2 = await getFaultList(code, {
			page: 1,
			size: 9999
		})
		faultList.value = res2.data?.data.data
	}

	onMounted(() => {
		nextTick(() => {
			refreshData(props.detail.deviceLabelCode)
		})

	})

	const toWorkOrder = (workOrderId: string) => {
		//
		console.log(workOrderId)
	}
	//饼图
	const initPie = (data: any) => {
		pieChart.value.init(echarts, (chart: any) => {
			const options = pieOption(data)
			chart.setOption(options);
		});
	}
	//折线图
	const initLine = (dataX: string[], data: any, ) => {
		lineChart.value.init(echarts, (chart: any) => {
			const options = lineOption(dataX, data)
			chart.setOption(options);
		});
	}
</script>

<style lang="scss" scoped>
	.card-box {
		width: 686rpx;
		margin: 20rpx auto;
		border-radius: 16rpx;
		// padding: 22rpx 28rpx;
		padding: 0;
		background-color: #FFFFFF;

		.hand {
			padding: 22rpx 28rpx;

			.hand-title {
				height: 42rpx;
				line-height: 42rpx;

				.icon-box {
					text-align: center;
					line-height: 32rpx;
					height: 32rpx;
					width: 32rpx;
					background-color: #3862F8;
					border-radius: 4rpx;
				}

				text {
					font-size: 28rpx;
					margin-left: 16rpx;
					color: #060F27;
					font-weight: 600;
				}
			}

			.status {
				color: #91949F;
			}
		}

		.line {
			padding-bottom: 20rpx;
		}

		.table {
			margin-top: 24rpx;
			padding: 0rpx 28rpx;

			.info {
				font-size: 24rpx;
				padding: 24rpx 0rpx;
				display: flex;

				text {
					&:nth-child(1) {
						color: #000000;
					}

					&:nth-child(2) {
						flex: 1;
						color: #000000;
					}
				}


				.bg {
					padding: 2rpx 12rpx;
					background: rgba(56, 98, 248, 0.2);
					border-radius: 8rpx;

					text {
						color: #3862F8;
					}
				}
			}

			.files {
				font-size: 36rpx;
				font-weight: 600;

				text {
					color: #626262;
					padding-left: 20rpx;
				}
			}

			.list {
				background-color: #f1f1f1;
				padding: 24rpx;
				margin-top: 10rpx;
			}
		}

		.line-echarts {
			height: 400rpx;
			width: 100%;
		}
		.icon-img{
			width: 40rpx;height: 40rpx;padding-right: 20rpx;
		}
	}
</style>
