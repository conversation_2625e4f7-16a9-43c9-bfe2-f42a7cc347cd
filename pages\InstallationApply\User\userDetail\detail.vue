<template>
  <view class="detail-container">
    <!-- 导航栏 -->
    <u-navbar
      fixed
      placeholder
      safeAreaInsetTop
      bgColor="#FFFFFF"
      title="立护详情"
      :autoBack="true"
      leftIconSize="20"
    >
    </u-navbar>

    <!-- 详情内容 -->
    <view class="detail-content">
      <!-- 用户基本信息卡片 -->
      <view class="detail-card">
        <view class="card-background"></view>
        <view class="card-header">
          <view class="user-avatar">
            <view class="avatar-bg">
              <u-icon name="account" color="#ffffff" size="32"></u-icon>
            </view>
            <view class="avatar-ring"></view>
          </view>
          <view class="user-basic">
            <view class="user-name" @click="handleUserNameClick">{{
              userInfo.userName || "张三"
            }}</view>
            <view class="user-code" @click="handleUserCodeClick"
              >户号：{{ userInfo.userCode || userInfo.serialNo }}</view
            >
            <view class="user-type" @click="handleUserTypeClick">{{
              userInfo.customerNature || "单位用户"
            }}</view>
          </view>
          <view class="status-container">
            <view
              class="user-status"
              :class="getUserStatusClass(userInfo.userStatus)"
            >
              <view class="status-dot"></view>
              <text>{{ userInfo.userStatus || "正常" }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 详细信息列表 -->
      <view class="info-list">
        <!-- 基本信息 -->
        <view class="info-section">
          <view class="section-title">
            <view class="title-icon">
              <u-icon name="info-circle" color="#667eea" size="20"></u-icon>
            </view>
            <text>基本信息</text>
          </view>
          <view class="info-grid">
            <view
              class="info-item"
              v-for="(item, index) in basicInfoList"
              :key="index"
            >
              <view class="info-label">
                <view class="label-dot"></view>
                <text>{{ item.label }}</text>
              </view>
              <view class="info-value" @click="handleLongTextClick(item)">{{
                item.value
              }}</view>
            </view>
          </view>
        </view>

        <!-- 联系信息 -->
        <view class="info-section">
          <view class="section-title">
            <view class="title-icon">
              <u-icon name="phone" color="#52c41a" size="20"></u-icon>
            </view>
            <text>联系信息</text>
          </view>
          <view class="info-grid">
            <view
              class="info-item"
              v-for="(item, index) in contactInfoList"
              :key="index"
            >
              <view class="info-label">
                <view class="label-dot"></view>
                <text>{{ item.label }}</text>
              </view>
              <view class="info-value" @click="handleLongTextClick(item)">{{
                item.value
              }}</view>
            </view>
          </view>
        </view>

        <!-- 地址信息 -->
        <view class="info-section">
          <view class="section-title">
            <view class="title-icon">
              <u-icon name="map" color="#fa8c16" size="20"></u-icon>
            </view>
            <text>地址信息</text>
          </view>
          <view class="info-grid">
            <view
              class="info-item"
              v-for="(item, index) in addressInfoList"
              :key="index"
            >
              <view class="info-label">
                <view class="label-dot"></view>
                <text>{{ item.label }}</text>
              </view>
              <view class="info-value" @click="handleLongTextClick(item)">{{
                item.value
              }}</view>
            </view>
          </view>
        </view>

        <!-- 业务信息 -->
        <view class="info-section">
          <view class="section-title">
            <view class="title-icon">
              <u-icon name="setting" color="#722ed1" size="20"></u-icon>
            </view>
            <text>业务信息</text>
          </view>
          <view class="info-grid">
            <view
              class="info-item"
              v-for="(item, index) in businessInfoList"
              :key="index"
            >
              <view class="info-label">
                <view class="label-dot"></view>
                <text>{{ item.label }}</text>
              </view>
              <view class="info-value" @click="handleLongTextClick(item)">{{
                item.value
              }}</view>
            </view>
          </view>
        </view>
      </view>

      <!-- 操作按钮区域 -->
      <view class="action-buttons" v-if="fromType !== 'query'">
        <view class="button-container reject-btn" @click="handleReject">
          <view class="button-icon">
            <u-icon name="close" color="#ff4757" size="20"></u-icon>
          </view>
          <view class="button-content">
            <text class="button-text">拒绝申请</text>
            <text class="button-desc">审核不通过</text>
          </view>
        </view>
        <view class="button-container approve-btn" @click="handleApprove">
          <view class="button-icon">
            <u-icon name="checkmark" color="#2ed573" size="20"></u-icon>
          </view>
          <view class="button-content">
            <text class="button-text">通过申请</text>
            <text class="button-desc">审核通过</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部安全区域 -->
    <view class="safe-area-bottom"></view>
  </view>
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref, computed } from "vue";
import { onLoad } from "@dcloudio/uni-app";

// 接收页面参数
const userInfo = ref<any>({});
const fromType = ref<string>("");
const userId = ref<string>("");
const status = ref<string>("");
const statusName = ref<string>("");

// 计算属性：信息分组
const basicInfoList = computed(() => [
  {
    label: "户号",
    value: userInfo.value.userCode || "2025080819050580244412948490000200004",
  },
  { label: "用户姓名", value: userInfo.value.userName || "张三" },
  { label: "客户性质", value: userInfo.value.customerNature || "单位" },
  { label: "用水性质", value: userInfo.value.waterNature || "非居民用水" },
  { label: "用水人数", value: userInfo.value.waterUserCount || "1" },
  { label: "用户状态", value: userInfo.value.userStatus || "正常" },
  { label: "证件类型", value: userInfo.value.idType || "身份证" },
  { label: "证件号码", value: userInfo.value.idNumber || "111" },
]);

const contactInfoList = computed(() => [
  { label: "手机号码", value: userInfo.value.phoneNumber || "***********" },
  { label: "电子邮箱", value: userInfo.value.email || "-" },
]);

const addressInfoList = computed(() => [
  {
    label: "管业区域",
    value: userInfo.value.businessArea || "19050580244412948490",
  },
  { label: "小区名称", value: userInfo.value.communityName || "2" },
  { label: "单元房号", value: userInfo.value.roomNumber || "311" },
  { label: "用水地址", value: userInfo.value.address || "111111" },
]);

const businessInfoList = computed(() => [
  { label: "缴费人识别号", value: userInfo.value.payerCode || "-" },
  {
    label: "供水日期",
    value: userInfo.value.waterSupplyDate || "2025-08-07 00:00:00",
  },
  { label: "计费方式", value: userInfo.value.billingMethod || "unknown" },
  { label: "文件上传", value: userInfo.value.fileUpload || "-" },
  { label: "图片上传", value: userInfo.value.imageUpload || "-" },
]);

// 页面加载时接收参数
onLoad((options: any) => {
  if (options.id) {
    userId.value = options.id;
  }
  if (options.fromType) {
    fromType.value = options.fromType;
  }
  if (options.status) {
    status.value = options.status;
  }
  if (options.statusName) {
    statusName.value = options.statusName;
  }

  // 获取用户详情
  getUserDetail();
});

// 获取用户状态样式类
const getUserStatusClass = (status: string) => {
  switch (status) {
    case "正常":
      return "status-normal";
    case "异常":
      return "status-abnormal";
    case "停用":
      return "status-disabled";
    default:
      return "status-normal";
  }
};

// 获取用户详情
const getUserDetail = async () => {
  try {
    // 这里应该调用实际的API接口
    // const res = await getUserDetailApi(userId.value)
    // userInfo.value = res.data

    // 暂时使用模拟数据
    userInfo.value = {
      userCode: "2025080819050580244412948490000200004",
      customerNature: "单位",
      waterNature: "非居民用水",
      waterUserCount: "1",
      userName: "张三",
      phoneNumber: "***********",
      idNumber: "111",
      idType: "身份证",
      userStatus: "正常",
      businessArea: "19050580244412948490",
      communityName: "2",
      roomNumber: "311",
      address: "111111",
      email: "-",
      payerCode: "-",
      waterSupplyDate: "2025-08-07 00:00:00",
      billingMethod: "unknown",
      fileUpload: "-",
      imageUpload: "-",
    };
  } catch (error) {
    console.error("获取用户详情失败:", error);
    uni.showToast({
      title: "获取详情失败",
      icon: "error",
    });
  }
};

// 处理拒绝操作
const handleReject = () => {
  uni.showModal({
    title: "确认操作",
    content: "确定要拒绝此用户申请吗？",
    success: (res) => {
      if (res.confirm) {
        // 调用拒绝接口
        processUserApplication("reject");
      }
    },
  });
};

// 处理通过操作
const handleApprove = () => {
  uni.showModal({
    title: "确认操作",
    content: "确定要通过此用户申请吗？",
    success: (res) => {
      if (res.confirm) {
        // 调用通过接口
        processUserApplication("approve");
      }
    },
  });
};

// 处理长文本点击事件
const handleLongTextClick = (item: any) => {
  // 如果文本长度超过20个字符，显示完整内容
  if (item.value && item.value.length > 20) {
    uni.showModal({
      title: item.label,
      content: item.value,
      showCancel: false,
      confirmText: "知道了",
    });
  }
};

// 处理用户名点击
const handleUserNameClick = () => {
  const userName = userInfo.value.userName || "张三";
  if (userName.length > 10) {
    uni.showModal({
      title: "用户姓名",
      content: userName,
      showCancel: false,
      confirmText: "知道了",
    });
  }
};

// 处理户号点击
const handleUserCodeClick = () => {
  const userCode =
    userInfo.value.userCode ||
    userInfo.value.serialNo ||
    "2025080819050580244412948490000200004";
  uni.showModal({
    title: "户号",
    content: userCode,
    showCancel: false,
    confirmText: "知道了",
  });
};

// 处理用户类型点击
const handleUserTypeClick = () => {
  const userType = userInfo.value.customerNature || "单位用户";
  if (userType.length > 8) {
    uni.showModal({
      title: "客户性质",
      content: userType,
      showCancel: false,
      confirmText: "知道了",
    });
  }
};

// 处理用户申请
const processUserApplication = async (action: "approve" | "reject") => {
  try {
    // 这里应该调用实际的API接口
    // const res = await processApplicationApi(userId.value, action)

    uni.showToast({
      title: action === "approve" ? "审核通过" : "已拒绝",
      icon: "success",
    });

    // 延迟返回上一页
    setTimeout(() => {
      uni.navigateBack();
    }, 1500);
  } catch (error) {
    console.error("处理申请失败:", error);
    uni.showToast({
      title: "操作失败",
      icon: "error",
    });
  }
};

onMounted(() => {
  // 页面挂载后的操作
});
</script>

<style lang="scss" scoped>
.detail-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.detail-content {
  padding: 20rpx;
}

// 用户信息卡片
.detail-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 24rpx;
  padding: 40rpx 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 16rpx 48rpx rgba(102, 126, 234, 0.25);
  position: relative;
  overflow: hidden;

  .card-background {
    position: absolute;
    top: -50%;
    right: -30%;
    width: 200rpx;
    height: 200rpx;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    filter: blur(40rpx);
  }

  .card-header {
    display: flex;
    align-items: center;
    position: relative;
    z-index: 2;

    .user-avatar {
      position: relative;
      margin-right: 24rpx;

      .avatar-bg {
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 8rpx 24rpx rgba(255, 107, 107, 0.3);
      }

      .avatar-ring {
        position: absolute;
        top: -8rpx;
        left: -8rpx;
        width: 96rpx;
        height: 96rpx;
        border: 3rpx solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        animation: pulse 2s infinite;
      }
    }

    .user-basic {
      flex: 1;

      .user-name {
        font-size: 40rpx;
        font-weight: 700;
        color: #ffffff;
        line-height: 56rpx;
        text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        max-width: 280rpx;
        cursor: pointer;
        transition: all 0.3s ease;

        &:active {
          opacity: 0.8;
          transform: scale(0.98);
        }
      }

      .user-code {
        font-size: 26rpx;
        color: rgba(255, 255, 255, 0.8);
        line-height: 36rpx;
        margin-top: 4rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        max-width: 400rpx;
        cursor: pointer;
        transition: all 0.3s ease;

        &:active {
          opacity: 0.8;
          transform: scale(0.98);
        }
      }

      .user-type {
        font-size: 24rpx;
        color: rgba(255, 255, 255, 0.7);
        line-height: 32rpx;
        margin-top: 4rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        max-width: 280rpx;
        cursor: pointer;
        transition: all 0.3s ease;

        &:active {
          opacity: 0.8;
          transform: scale(0.98);
        }
      }
    }

    .status-container {
      .user-status {
        padding: 12rpx 20rpx;
        border-radius: 20rpx;
        font-size: 24rpx;
        font-weight: 600;
        display: flex;
        align-items: center;
        backdrop-filter: blur(10rpx);

        .status-dot {
          width: 12rpx;
          height: 12rpx;
          border-radius: 50%;
          margin-right: 8rpx;
          animation: blink 1.5s infinite;
        }

        &.status-normal {
          background: rgba(82, 196, 26, 0.2);
          color: #52c41a;
          border: 1px solid rgba(82, 196, 26, 0.3);

          .status-dot {
            background-color: #52c41a;
          }
        }

        &.status-abnormal {
          background: rgba(250, 140, 22, 0.2);
          color: #fa8c16;
          border: 1px solid rgba(250, 140, 22, 0.3);

          .status-dot {
            background-color: #fa8c16;
          }
        }

        &.status-disabled {
          background: rgba(140, 140, 140, 0.2);
          color: #8c8c8c;
          border: 1px solid rgba(140, 140, 140, 0.3);

          .status-dot {
            background-color: #8c8c8c;
          }
        }
      }
    }
  }
}

// 信息列表
.info-list {
  .info-section {
    background: #ffffff;
    border-radius: 20rpx;
    margin-bottom: 24rpx;
    overflow: hidden;
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.1);
      transform: translateY(-4rpx);
    }

    .section-title {
      padding: 32rpx;
      background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
      border-bottom: 1px solid #f0f0f0;
      display: flex;
      align-items: center;

      .title-icon {
        width: 48rpx;
        height: 48rpx;
        border-radius: 12rpx;
        background: rgba(102, 126, 234, 0.1);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16rpx;
      }

      text {
        font-size: 32rpx;
        font-weight: 600;
        color: #060f27;
      }
    }

    .info-grid {
      padding: 32rpx;

      .info-item {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        padding: 20rpx 0;
        border-bottom: 1px solid #f8f9fb;
        transition: all 0.3s ease;

        &:last-child {
          border-bottom: none;
        }

        &:hover {
          background-color: #fafbff;
          margin: 0 -16rpx;
          padding-left: 16rpx;
          padding-right: 16rpx;
          border-radius: 12rpx;
        }

        .info-label {
          display: flex;
          align-items: center;
          font-size: 28rpx;
          color: #91949f;
          line-height: 40rpx;
          min-width: 160rpx;
          flex-shrink: 0;

          .label-dot {
            width: 8rpx;
            height: 8rpx;
            border-radius: 50%;
            background-color: #667eea;
            margin-right: 12rpx;
          }
        }

        .info-value {
          font-size: 28rpx;
          color: #060f27;
          line-height: 40rpx;
          text-align: right;
          flex: 1;
          word-break: break-all;
          margin-left: 20rpx;
          font-weight: 500;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          max-width: 300rpx;
          cursor: pointer;
          transition: all 0.3s ease;

          &:active {
            color: #667eea;
            transform: scale(0.98);
          }
        }
      }
    }
  }
}

// 操作按钮
.action-buttons {
  display: flex;
  gap: 24rpx;
  padding: 32rpx;
  background: #ffffff;
  border-radius: 20rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);

  .button-container {
    flex: 1;
    padding: 24rpx;
    border-radius: 16rpx;
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
      );
      transition: left 0.5s;
    }

    &:active::before {
      left: 100%;
    }

    .button-icon {
      width: 48rpx;
      height: 48rpx;
      border-radius: 12rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 16rpx;
    }

    .button-content {
      flex: 1;

      .button-text {
        display: block;
        font-size: 28rpx;
        font-weight: 600;
        line-height: 40rpx;
      }

      .button-desc {
        display: block;
        font-size: 22rpx;
        line-height: 30rpx;
        opacity: 0.7;
        margin-top: 4rpx;
      }
    }

    &.reject-btn {
      background: linear-gradient(135deg, #fff5f5 0%, #ffe8e8 100%);
      border: 1px solid rgba(255, 71, 87, 0.2);

      &:active {
        transform: scale(0.98);
        box-shadow: 0 4rpx 16rpx rgba(255, 71, 87, 0.3);
      }

      .button-icon {
        background: rgba(255, 71, 87, 0.1);
      }

      .button-text {
        color: #ff4757;
      }

      .button-desc {
        color: #ff4757;
      }
    }

    &.approve-btn {
      background: linear-gradient(135deg, #f6ffed 0%, #e8f8e8 100%);
      border: 1px solid rgba(46, 213, 115, 0.2);

      &:active {
        transform: scale(0.98);
        box-shadow: 0 4rpx 16rpx rgba(46, 213, 115, 0.3);
      }

      .button-icon {
        background: rgba(46, 213, 115, 0.1);
      }

      .button-text {
        color: #2ed573;
      }

      .button-desc {
        color: #2ed573;
      }
    }
  }
}

// 底部安全区域
.safe-area-bottom {
  height: env(safe-area-inset-bottom);
}

// 动画效果
@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes blink {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

// 深色样式覆盖
::v-deep .u-navbar__content__title {
  color: #ffffff !important;
  font-weight: 600 !important;
}

::v-deep .u-icon--left {
  color: #ffffff !important;
}
</style>
