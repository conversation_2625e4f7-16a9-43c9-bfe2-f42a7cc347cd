<template>
	<view class="main">
		<view class="w-card" style="background-image: url('static/img/icons/waterWorks-bg1.png')"
			@click="toFlowReport">
			<text>压力简报</text>
		</view>
		<view class="w-card" style="background-image: url('static/img/icons/waterWorks-bg2.png')"
			@click="toPressureReport">
			<text>流量简报</text>
		</view>
		<view class="w-card" style="background-image: url('static/img/icons/waterWorks-bg3.png')" @click="toWaterQuality">
			<text>水质简报</text>
		</view>
	</view>
</template>

<script lang="ts" setup>
	const toFlowReport = () => {
		uni.$u.route({
			url: 'pages/pipeNetwork/statisticalAnalysis/flowReport/index'
		})
	}
	const toPressureReport = () => {
		uni.$u.route({
			url: 'pages/pipeNetwork/statisticalAnalysis/pressureReport/index'
		})
	}
	const toWaterQuality = () => {
		uni.$u.route({
			url: 'pages/pipeNetwork/statisticalAnalysis/waterQualityReport/index'
		})
	}
</script>

<style lang="scss" scoped>
	.w-card {
		height: 240rpx;
		width: 686rpx;
		border-radius: 16rpx;
		background-color: red;
		margin: 10rpx auto;
		background-size: 100% 100%;
		line-height: 240rpx;
		text-align: center;

		text {
			font-size: 34rpx;
			color: #FFFFFF;
		}
	}
</style>
