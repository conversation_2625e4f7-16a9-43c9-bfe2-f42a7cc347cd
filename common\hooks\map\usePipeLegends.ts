import { ref } from 'vue'
import { getPipeLegends } from '../../api/map'
export const usePipeLegends = () => {
	const legends = ref<{
		"layerId" : number
		"layerName" : string
		"layerType" : string
		"minScale" : number
		"maxScale" : number
		"legend" : [
			{
				"label" : string
				"url" : string
				"imageData" : string
				"contentType" : string
				"height" : number
				"width" : number
			}
		]
	}[]>([])
	const getLegends = async () => {
		try {
			const res = await getPipeLegends()
			console.log(res);
			legends.value = res.data.layers || []
			return res
		} catch (e) {
			console.log(e);
			legends.value = []
			//TODO handle the exception
		}
	}
	return {
		getLegends,
		legends
	}
}