<template>
	<view class="main">
		<scroll-view scroll-y="true" scroll-with-animation="true" style="padding-bottom: 120rpx;">
			<u-navbar :backTextStyle="state.backTextStyle" :back-text="userInfo.organizationName || '瓜州供排水中心'"
				title="" :border-bottom="false" back-icon-name="" :background="{backgroundColor: '#FBFBFB'}">
				<template #right>
					<view class="flex-center n-right">
						<view class="weather">
							<view>
								{{weather.daytemp}}℃ ~ {{weather.nighttemp}}℃
							</view>
							<view>
								{{weather.dayweather}}
							</view>
						</view>
						<view>
							<u-icon :name="'custom-icon-'+weather.wea_img" customPrefix="custom-icon" size="48"
								color="#3862F8"></u-icon>
							<!-- <image src="/static/img/icons/weather.png" :width="48" :height="48"></image> -->
						</view>
					</view>
				</template>
			</u-navbar>
			<view class="content">
				<!-- <view class="banner div-radius">
					<u-swiper :list="banners" name="url" :autoplay="false" imgMode="scaleToFill" height="168">
					</u-swiper>
				</view> -->
				<view class="div-radius menus card">
					<view class="title1" style="padding-bottom: 0rpx;">
						<text>应用中心</text>
						<view class="bottom-line">
						</view>
					</view>
					<view class="menu-list">
						<u-grid :border="false" col="4">
							<u-grid-item v-for="(item,listIndex) in list" :key="listIndex" @click="routerTo(item.url)"
								:custom-style="{padding:0}">
								<view class="menu">
									<image class="image" :src="item.icon"></image>
								</view>
								<text class="grid-text">{{item.title}}</text>
							</u-grid-item>
						</u-grid>
					</view>
				</view>
				<view class="info div-radius flex-between" @click="toToDo()">
					<view class="info-1">
						通知
					</view>
					<view class="line"></view>
					<view class="info-2 flex-between">
						<text>{{notifyData?notifyData.text:'暂无通知'}}</text>
						<u-icon name="arrow-right" size="28"></u-icon>
					</view>
				</view>
				<view class="marketing div-radius card" v-if="state.allMenus.indexOf('异常管理')!=-1">
					<view class="title1">
						<text>智慧生产</text>
						<view class="bottom-line">
						</view>
					</view>
					<view v-if="state.allMenus.indexOf('异常管理')!=-1">
						<view class="title small-title" style="">
							<text>告警信息</text>
						</view>
						<view class="card-list">
							<view class="card " @click="toAlarm">
								<image src="/static/img/icons/alarm1.png" style="width: 52rpx;height: 52rpx;"
									class="card-img"></image>
								<view class="">
									<view>{{state.alarmInfo.alarmTotal}}</view>
									<view>报警总数（个）</view>
								</view>
							</view>
							<view class="card " @click="toAlarm">
								<image src="/static/img/icons/devcie.png" style="width: 52rpx;height: 52rpx;"
									class="card-img"></image>
								<view class="">
									<view>{{state.alarmInfo.alarmDeviceTotal}}</view>
									<view>报警设备（台）</view>
								</view>
							</view>
							<view class="card " @click="toAlarm">
								<image src="/static/img/icons/alarm3.png" style="width: 52rpx;height: 52rpx;"
									class="card-img"></image>
								<view class="">
									<view>{{state.alarmInfo.offlineTotal}}</view>
									<view>离线报警（个）</view>
								</view>
							</view>
							<view class="card " @click="toAlarm">
								<image src="/static/img/icons/alarm2.png" style="width: 52rpx;height: 52rpx;"
									class="card-img"></image>
								<view class="">
									<view>{{state.alarmInfo.otherTotal}}</view>
									<view>规则告警（个）</view>
								</view>
							</view>
						</view>
					</view>
				</view>
				<view class="marketing div-radius card" v-if="state.allMenus.indexOf('工单处理')!=-1">
					<view class="title1">
						<text>智慧运营</text>
						<view class="bottom-line">
						</view>
					</view>
					<view v-if="state.allMenus.indexOf('工单处理')!=-1">
						<view class="title small-title" style="">
							<text>统一工单</text>
						</view>
						<view class="card-list">
							<view class="card ">
								<image src="/static/img/icons/workOrder1.png" style="width: 52rpx;height: 52rpx;"
									class="card-img"></image>
								<view class="">
									<view>{{state.workOrderCount.waitingCount}}</view>
									<view>待接收（个）</view>
								</view>
							</view>
							<view class="card ">
								<image src="/static/img/icons/workOrder2.png" style="width: 52rpx;height: 52rpx;"
									class="card-img"></image>
								<view class="">
									<view>{{state.workOrderCount.receiveCount}}</view>
									<view>待处理（个）</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</scroll-view>
		<u-tabbar v-model="current" :list="bottomMenu"></u-tabbar>
		<!-- <view id="leafletView" class="map-view" :change:locateMark="leafletView.locate" :locateMark="refreshLocate" /> -->
	</view>
</template>

<script lang="ts">
	import {
		updateUserCoords
	} from "@/common/api/inspection";
	import {
		findMenuByTenantApplication,
		getTenantApplication,
		getMonthReceivableMoney,
		getSystemNotifyCount,
	} from '@/common/api/system'
	import {
		getCountAlarmV2
	} from '@/common/api/alarmManage'
	import {
		removeSlash
	} from '@/common/utils/removeIdSlash'
	import lEchart from '@/uni_modules/lime-echart/components/l-echart/l-echart.vue'
	import * as echarts from 'echarts'
	import {
		gaugeOption
	} from './echartData'
	import {
		countOfStage
	} from '@/common/api/workOrder'
	import {
		getProjectRoot
	} from '@/common/api/monitoring'
	import {
		applictions,
	} from './data'
	import dayjs from 'dayjs';

	export default {
		components: {
			lEchart
		},
		data() {
			return {
				state: {
					backTextStyle: {
						fontSize: '34rpx',
						color: '#060F27',
						fontWeight: 700,
					},
					allMenus: [],
					alarmInfo: {
						alarmDeviceTotal:'-',
						offlineTotal:'-',
						alarmTotal:'-'
					},
					workOrderCount: {},
					meta: {}
				},
				currentUser: null,
				refreshLocate: 0,
				current: 0,
				countInfo: {
					totalWater: '0.00',
					receivedMoney: 0,
					receivableMoney: 0,
				},
				detailModule: [],
				weather: {},
				userInfo: {},
				notifyData: null,
				applictionList: [],
				list: [],
				bottomMenu: [],
				notify: {},
				banners: [{
					url: 'static/img/banner.png',
					name: ''
				}],
				lastMenu: {
					icon: '/static/img/menus/quanbuyingyong.png',
					title: '全部应用',
					url: 'pages/allApplication/index'
				}
			}
		},
		methods: {
			initEcharts(num : number) {
				this.$refs.gaugeChart?.init(echarts, (chart : any) => {
					const options = gaugeOption(num)
					chart.setOption(options);
				});
			},

			//前往告警列表
			toAlarm() {
				uni.$u.route({
					url: "pages/wisdomProduce/exceptionalManageV2/index",
				})
			},

			// 路由跳转
			routerTo(url : string, params ?: any) {
				if (url) {
					uni.$u.route({
						url: url,
						params: params
					})
				} else {
					uni.$u.toast('升级中，敬请期待')
				}
			},

			// 路由跳转
			toToDo() {
				uni.navigateTo({
					url: '/pages/noticeList/index'
				})
			},
			getWeather() {
				// 获取天气信息
				uni.request({
					url: 'https://restapi.amap.com/v3/weather/weatherInfo?parameters',
					method: 'GET',
					data: {
						key: '2ef4bde4346f3aef3799e28c465711e4',
						city: 510115,
						extensions: 'all',
					},
				}).then((res : any) => {
					this.weather = res.data.forecasts[0]?.casts[0] ?? {}
				})
			},
			async getApp() {
				let tenantId = this.userInfo.tenantId?.id
				tenantId = removeSlash(tenantId)
				const res = await getTenantApplication({
					tenantId,
					resourceType: 'APP'
				})

				return res.data[0]?.id
			},

			// 工单数量统计
			async getCountOfStage() {
				const query = {
					self: true
				}
				const res = await countOfStage(query)
				this.state.workOrderCount = res.data?.data
			},

			async monthReceivableMoney() {
				const that = this
				const params = {
					size: 20,
					page: 1,
					// endYm: dayjs().format("YYYY-MM"),
					// beginYm: dayjs().format("YYYY-MM")
				}
				getMonthReceivableMoney(params).then(res => {
					if (res.data.code == 200) {
						if (res.data?.data?.countInfo) {
							that.countInfo = res.data?.data?.countInfo
							const num = parseFloat((that.countInfo.receivedMoney / that.countInfo.receivableMoney).toFixed(2)) || 1
							that.initEcharts(1 - num)
						}
					}
				})
			},

			async countAlarm() {
				const res = await getProjectRoot()
				const data = res.data ? res.data : []
				const currentProjectId = data[0]?.id
				const alarm = await getCountAlarmV2({
					projectId: currentProjectId,
					start: dayjs().startOf('day').valueOf(),
					end: dayjs().endOf('day').valueOf()
				})
				this.state.alarmInfo = alarm.data
			},

			async getSystemInfo() {
				this.list = []
				this.detailModule = []
				const id = await this.getApp()
				findMenuByTenantApplication({
					tenantApplicationId: id
				}).then((res : any) => {
					const data = res.data[0]?.children
					data?.map((d : any) => {
						const hasMenus = applictions().filter((menu : any) => {
							const state = d.children?.find((child : any) => child.path === menu.url)
							if (state) {
								menu.title = state.meta.title
								menu.url = state.path
								menu.icon = state.url || menu.icon
							}
							return state ? true : false
						})
						this.list = this.list.concat(hasMenus)
						this.detailModule.push(d.meta.title)
					})
					// this.detailModule = this.list.map(item=>item.title)
					this.list = this.list.slice(0, 7)
					const find = this.list.find((mu : any) => mu.url === this.lastMenu.url)
					if (!find) {
						this.list.push(this.lastMenu)
					}
					if (this.detailModule.indexOf('智慧营销') != -1) {
						this.monthReceivableMoney()
					}
					this.applictionList = data?.map((menu : any) => {
						const menus = menu.children?.map((child : any) => { return child.meta.title })
						this.state.allMenus = this.state.allMenus.concat(menus)
					})
					// console.log(this.state.allMenus)
				})
				this.bottomMenu = uni.getStorageSync('bottomMenus')
			},
			addUserCoords(coordinate) {
				const userId = removeSlash(this.userInfo.id?.id)
				updateUserCoords({
					userId: userId,
					coordinate: coordinate,
				}).then(res => {
					// console.log("上传定位", res)
					if (res.data.code != 200) {
						// clearInterval(this.userData)
					}
				}).catch(() => {
					// clearInterval(this.userData)
				})
			},
		},
		async mounted() {
			this.userInfo = uni.getStorageSync('userInfo')
			setInterval(() => {
				this.refreshLocate = ++this.refreshLocate
			}, 5000)
			await this.getSystemInfo()
			this.getWeather()
		},
		async onShow() {
			this.userInfo = uni.getStorageSync('userInfo')
			this.currentUser = {
				id: this.userInfo.id.id,
				name: this.userInfo.firstName,
				password: this.userInfo.password,
				avatar: '/static/images/Avatar-3.png',
				email: this.userInfo.email,
				phone: this.userInfo.phone,
			}
			uni.$currentUser = this.currentUser;
			console.log('uni.$currentUser', uni.$currentUser)
			getSystemNotifyCount({
				status: 0,
				to: removeSlash(this.userInfo.id?.id)
			}).then((res : any) => {
				console.log('消息', res.data.data)
				this.notifyData = null
				this.notify = res.data.data
				if (this.notify)
					if (this.notify['0'] && this.notify['0'] > 0) {
						this.notifyData = {
							text: '您有新的通知！',
							type: 0
						}
					} else if (this.notify['1'] && this.notify['1'] > 0) {
						this.notifyData = {
							text: '您有新报警的通知！',
							type: 1
						}
					} else if (this.notify['2'] && this.notify['2'] > 0) {
						this.notifyData = {
							text: '您有新代办事项！',
							type: 2
						}
					}
			})
			await this.countAlarm()
			await this.getCountOfStage()

		}
	}

</script>
<script lang="renderjs" module="leafletView">
	export default {
		name: 'leafletView',
		methods: {
			locate(data) {
				const that = this
				var options = {
					enableHighAccuracy: true,
					timeout: 5000,
					maximumAge: 0,
				};

				function success(position) {
					var lng = position.coords.longitude;
					var lat = position.coords.latitude;
					console.log('刷新定位', lng + ',' + lat);
					console.log('data', data)
					that.$ownerInstance?.callMethod('addUserCoords', lat + ',' + lng)
				}

				function error(err) {
					console.warn("ERROR(" + err.code + "): " + err.message);
				}

				navigator.geolocation.getCurrentPosition(success, error, options);
				// navigator.geolocation.getCurrentPosition(function(position) {})

			}
		}
	}
</script>
<style lang="scss">
	.main {
		height: 100%;
		background: #FBFBFB;
		// .n-left {
		// 	font-size: 34rpx;
		// 	color: #060F27;
		// 	font-weight: 600;
		// }

		.n-right {
			padding-right: 36rpx;

			.weather {
				text-align: center;
				color: #91949F;
				font-size: 24rpx;
				padding-right: 16rpx;
			}
		}

		.banner {
			height: 168rpx;
			margin: 16rpx auto;
			border-radius: 12px;
		}

		.menus {
			height: 446rpx;

			.menu-list {
				width: 90%;
				margin: 24rpx auto;

				.menu {
					width: 96rpx;
					height: 96rpx;
					background: #F0F0FC;
					border-radius: 16rpx;
					display: flex;
					flex-direction: column;
					justify-content: center;
					align-items: center;
					line-height: 96rpx;
				}

				.grid-text {
					font-size: 24rpx;
					color: #060F27;
					font-weight: 500;
					padding-bottom: 24rpx;
					padding-top: 16rpx;
				}
			}
		}

		.info {
			height: 92rpx;
			margin: 16rpx auto;
			background: #FFFFFF;
			line-height: 92rpx;
			font-size: 24rpx;

			.info-1 {
				background-image: linear-gradient(#3862F8, #819CFF);
				-webkit-background-clip: text;
				color: transparent;
				font-size: 30rpx;
				font-weight: bold;
				padding-left: 28rpx;
			}

			.line {
				width: 2rpx;
				background-color: #EBEDF6;
				height: 46rpx;
			}

			.info-2 {
				width: 75%;
				text-align: left;
			}
		}

		.marketing {
			margin: 16rpx auto;

			.echart {
				width: 534rpx;
				height: 266rpx;
				margin: 20rpx auto;
			}

			.card-list {
				display: flex;
				justify-content: space-around;
				flex-wrap: wrap;
				padding-bottom: 36rpx;

				.card {
					align-items: center;
					display: flex;
					justify-content: center;
					width: 296rpx;
					height: 120rpx;
					background: #F9F9F9;
					border-radius: 16rpx;

					view:nth-child(1) {
						font-weight: 700;
						font-size: 32rpx;
						padding-right: 12rpx;
					}
				}
			}
		}

		.image {
			width: 64rpx;
			height: 64rpx;
			border-radius: 10rpx;
		}

		.card-img {
			width: 52rpx;
			height: 52rpx;
		}

	}

	.bottom-line {
		width: 90rpx;
		height: 8rpx;
		background-image: linear-gradient(to right, #3862F8, #ffffff);
	}

	.small-title {
		font-size: 24rpx;
		font-weight: none;
		color: #5c5c5c;
	}

	.title1 {
		margin-left: 44rpx;
		font-weight: 700;
		font-size: 30rpx;
		color: #060F27;
		padding-bottom: 40rpx;
	}
</style>