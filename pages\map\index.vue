<template>
  <view class="map-main">
    <view class="status-bar"></view>
    <view class="main-wrapper">
      <!-- ArcGIS Map Container -->
      <MapContainer
        ref="mapContainer"
        :center="pipelayer.center"
        :currentMenu="onemapMenu.curMenu"
        :currentVMenu="verticalBar.currentBar"
        :cleanMark="cleanMark"
        :locateMark="location.lonLat"
        :basemap="basemap"
        :pipevisible="pipelayer.currToggled"
        :pipeUrl="pipelayer.pipeServerUrl"
        :stations="station.currentData"
        :persons="users.latestUserCoords.data"
        :workorders="workorders.workorders.data"
        :dmaVisible="dmaVisible"
        :partitions="partitions"
        :measureType="currentMeasureType"
        :queryType="currentQueryType"
        :queryResults="queryResults"
        @mapClick="resolveMapClick"
        @onLayerAdded="onLayerAdded"
        @onLayerRemoved="onLayerRemoved"
        @onLayerVisibilityChanged="onLayerVisibilityChanged"
        @getLayerInfoFromRenderjs="getLayerInfoFromRenderjs"
        @refreshStationData="refreshStationData"
        @onQueryClickFromMap="onQueryClickFromMap"
        @handleRangeQuery="handleRangeQuery"
      />

      <!-- Vertical Menu Bar -->
      <VerticalMenuBar
        :menus="verticalBar.menus"
        :currentBar="verticalBar.currentBar"
        @menuClick="verticalBar.setCurrent"
        @clearMap="clearMap"
      />

      <!-- Location Button -->
      <LocationButton
        :loading="location.loading"
        :currentBar="verticalBar.currentBar"
        @click="getLocation"
      />

      <!-- Bottom Menu -->
      <BottomMenu
        :menus="onemapMenu.menus"
        :showCount="onemapMenu.showRootMenuCount"
        :currentMenu="onemapMenu.current"
        :touch="touch"
        @menuClick="onemapMenu.setCurrent"
        @touchStart="handleTouchStart"
        @touchEnd="handleTouchEnd"
      />

      <!-- Base Layer Selector -->
      <BaseLayerSelector
        v-if="verticalBar.currentBar === 'baselayer'"
        @close="closeCover"
        @changeBaseMap="changeBaseMap"
      />

      <!-- Layer Selector -->
      <LayerSelector
        v-if="verticalBar.currentBar === 'layer'"
        :pointLayers="pipelayer.pointLayers"
        :selected="pipelayer.selected"
        :dmaVisible="dmaVisible"
        @close="closeCover"
        @toggleLayer="pipelayer.toggle"
        @toggleDma="toggleDma"
      />

      <!-- Measure Selector -->
      <MeasureSelector
        v-if="verticalBar.currentBar === 'measure'"
        @close="closeCover"
        @select="handleMeasureSelect"
      />

      <!-- Query Selector -->
      <QuerySelector
        v-if="verticalBar.currentBar === 'query'"
        @close="closeCover"
        @select="handleQuerySelect"
      />

      <!-- 条件查询 Drawer -->
      <BottomDrawer
        :open="showConditionQuery"
        :height="drawerHeight"
        :minHeight="maxDrawerHeight / 2.5"
        :maxHeight="maxDrawerHeight"
        :draggable="true"
        :closeOnMask="true"
        title="条件查询"
        @close="clearMap"
        @update:height="updateDrawerHeight"
      >
        <ConditionQuery :layerArray="layerArray" />
      </BottomDrawer>

      <!-- 增强查询结果 -->
      <BottomDrawer
        :open="showQueryResults"
        :height="resultsDrawerHeight"
        :minHeight="maxDrawerHeight / 2.5"
        :maxHeight="maxDrawerHeight"
        :draggable="true"
        :closeOnMask="true"
        title="查询结果"
        @close="clearMap"
        @update:height="updateResultsDrawerHeight"
      >
        <QueryResultsEnhanced
          :results="queryResults"
          @highlightFeature="highlightQueryFeature"
          @displayResults="displayQueryResults"
          @clearHighlight="clearQueryResultHighlight"
          @showNavigation="handleNavigationRoute"
        />
      </BottomDrawer>

      <!-- Popups -->
      <MapPopups
        ref="mapPopups"
        :currentMenu="onemapMenu.curMenu"
        :layerIds="curLayerIds"
        :popData="mapPop"
        :currentModule="curModule"
      />
    </view>

    <!-- Bottom Tab Bar -->
    <u-tabbar v-model="current" :list="bottomMenu"></u-tabbar>
  </view>
</template>

<script>
import {
  useLocation,
  useVerticalBar,
  useTouch,
  usePipeLayers,
  useStations,
  useUsers,
  useWorkOrders,
} from '../../common/hooks';
import { useOneMapMenus } from './hooks/useOneMapMenus';
import { getAllPartition } from '../../common/api/dma';
import dayjs from 'dayjs';
import { getStationRealTimeProperties } from '../../common/api/stations';

// Components
import MapContainer from './components/MapContainer.vue';
import VerticalMenuBar from './components/VerticalMenuBar.vue';
import LocationButton from './components/LocationButton.vue';
import BottomDrawer from '@/components/common/BottomDrawer.vue';
import BottomMenu from './components/BottomMenu.vue';
import BaseLayerSelector from './components/BaseLayerSelector.vue';
import LayerSelector from './components/LayerSelector.vue';
import MapPopups from './components/MapPopups.vue';
import MeasureSelector from './components/MeasureSelector.vue';
import QuerySelector from './components/QuerySelector.vue';
import ConditionQuery from './components/ConditionQuery.vue';
import QueryResultsEnhanced from './components/QueryResultsEnhanced.vue';

export default {
  name: 'MapIndex',
  components: {
    MapContainer,
    VerticalMenuBar,
    LocationButton,
    BottomMenu,
    BaseLayerSelector,
    LayerSelector,
    MapPopups,
    MeasureSelector,
    QuerySelector,
    ConditionQuery,
    QueryResultsEnhanced,
    BottomDrawer,
  },
  data() {
    return {
      // Hooks
      verticalBar: useVerticalBar(),
      location: useLocation(),
      onemapMenu: useOneMapMenus(),
      touch: useTouch(),
      station: useStations(),
      pipelayer: usePipeLayers(),
      users: useUsers(),
      workorders: useWorkOrders(),

      // State
      cleanMark: 0,
      basemap: 'vec',
      dmaVisible: false,
      curLayerIds: [],
      partitions: [],
      current: 1,
      bottomMenu: [],
      pullTimer: undefined,
      currentMeasureType: '', // 当前量测类型
      currentQueryType: '', // 当前查询类型
      showConditionQuery: false, // 显示条件查询弹窗
      drawerHeight: 40, // 条件查询抽屉高度，初始为最小值（只显示把手）
      resultsDrawerHeight: 40, // 查询结果抽屉高度，初始为最小值（只显示把手）
      maxDrawerHeight: 600, // 最大抽屉高度
      queryResults: [], // 查询结果

      // 图层管理相关状态
      availableLayers: [], // 可用图层列表
      layerArray: [], // 可查询图层列表
      layerVisibility: {}, // 图层可见性状态
      layerOpacity: {}, // 图层透明度状态

      // 查询相关状态
      isDrawingRange: false, // 是否正在绘制范围
      rangeGeometry: null, // 范围几何对象
      showQueryResults: false, // 显示查询结果

      // Map popup data
      mapPop: {
        type: 'table',
        title: '',
        data: [],
        columns: [],
        obj: {},
      },
    };
  },
  computed: {
    curModule() {
      return this.onemapMenu?.current?.split('_')?.[0];
    },
  },
  created() {
    this.bottomMenu = uni.getStorageSync('bottomMenus');
  },

  mounted() {
    // 获取屏幕高度
    const systemInfo = uni.getSystemInfoSync();
    this.maxDrawerHeight = systemInfo.windowHeight * 0.8;

    // 页面加载完成后刷新图层列表
    this.$nextTick(() => {
      setTimeout(() => {
        this.refreshLayerList();
      }, 1000); // 等待地图初始化完成
    });
  },
  methods: {
    // Touch events
    handleTouchStart(e) {
      this.touch.touchStart(e);
    },
    handleTouchEnd(e) {
      this.touch.touchEnd(e);
      if (this.touch.directionY === 'up') {
        this.pullTimer = setTimeout(() => {
          this.onemapMenu.setRootMenuCount(this.onemapMenu.menus.length);
        }, 500);
      } else {
        clearTimeout(this.pullTimer);
        this.onemapMenu.setRootMenuCount(1);
      }
    },

    // Map actions
    clearMap() {
      this.cleanMark++;
      this.currentMeasureType = ''; // 清除量测状态
      this.currentQueryType = ''; // 清除查询状态
      this.queryResults = []; // 清除查询结果
      this.showQueryResults = false;
      this.showConditionQuery = false;
      // 禁用查询监听
      this.$refs.mapContainer?.clean?.();
    },
    getLocation() {
      this.location.refreshNum++;
    },
    changeBaseMap(type) {
      this.basemap = type;
    },
    closeCover() {
      this.verticalBar.setCurrent('');
    },
    handleMeasureSelect(type) {
      this.currentMeasureType = type;
      // 关闭竖工作栏
      this.verticalBar.setCurrent('');
    },
    async handleQuerySelect(type) {
      this.currentQueryType = type;

      // 关闭竖工作栏
      this.verticalBar.setCurrent('');

      switch (type) {
        case 'point':
          this.startPointQuery();
          break;
        case 'range':
          this.startRangeQuery();
          break;
        case 'condition':
          try {
            // 准备条件查询数据
            await this.prepareConditionQuery();
            this.showConditionQuery = true; // 受控打开
          } catch (error) {
            uni.showToast({
              title: '准备查询数据失败',
              icon: 'error',
            });
            // 即使失败也显示界面，让用户看到错误信息
            this.showConditionQuery = true; // 受控打开
          }
          break;
      }
    },

    updateDrawerHeight(height) {
      this.drawerHeight = height;
    },

    updateResultsDrawerHeight(height) {
      this.resultsDrawerHeight = height;
    },

    async toggleDma() {
      this.dmaVisible = !this.dmaVisible;
      if (!this.partitions.length) {
        await this.initPartition();
      }
    },

    // Data methods
    async initPartition() {
      try {
        const res = await getAllPartition();
        this.partitions = res.data || [];
      } catch (e) {
        console.error('Failed to load partitions:', e);
      }
    },

    // Popup methods
    openPop(name = 'refPopRight') {
      this.$refs.mapPopups?.openPop(name);
    },
    closePop(name = 'refPopRight') {
      this.$refs.mapPopups?.closePop(name);
    },

    // Data refresh methods - these will be called from renderjs
    async getLayerInfoFromRenderjs(params) {
      debugger
      this.pipelayer.getLayerInfo(params);
    },
    async refreshStationData(params) {
      switch (this.curModule) {
        case 'sssb':
          await this.handleSSSB();
          if (this.onemapMenu.hasSelectMenu()) {
            this.openPop();
          }
          break;
        case 'sbzc':
          this.curLayerIds = [params];
          await this.handleSBZC();
          if (this.onemapMenu.hasSelectMenu()) {
            this.openPop();
          }
          break;
        case 'rycl':
          await this.handleRYCL();
          break;
        case 'ywlc':
          await this.handleYWLC();
          break;
        case 'sjcj':
          await this.handleSJCJ();
          break;
        default:
          this.station.reset();
          this.users.reset();
          break;
      }
    },

    // Module handlers
    async handleSSSB() {
      await this.station.getStationList(this.onemapMenu.curMenu);
    },
    async handleSBZC() {
      // Equipment handling logic
    },
    async handleRYCL() {
      const type = this.onemapMenu?.current;
      if (type === 'rycl_cljk') {
        uni.showToast({
          title: '升级中,敬请期待',
          icon: 'none',
        });
      } else {
        const personType = this.getPersonType(type);
        await this.users._getLatestUserCoords({
          userTypeId: personType,
        });
        if (this.onemapMenu.hasSelectMenu()) {
          this.openPop();
        }
      }
    },
    async handleYWLC() {
      const type = this.onemapMenu?.current;
      const curMenu = this.onemapMenu.curMenu;
      this.workorders.reset();

      if (['ywlc_gdlc', 'ywlc_xjyh'].indexOf(curMenu.id) === -1) {
        uni.$u.toast('升级中,敬请期待');
        return;
      }

      if (type === 'ywlc_gdlc') {
        const res = await this.workorders.getWorkOrders();
        if (!res.data.length) {
          uni.$u.toast('暂无' + curMenu?.name + '信息');
        }
      }

      if (this.onemapMenu.hasSelectMenu()) {
        this.openPop();
      }
    },
    async handleSJCJ() {
      const type = this.onemapMenu?.current;
      switch (type) {
        case 'sjcj_dmafq':
          break;
        default:
          uni.showToast({
            title: '升级中,敬请期待',
            icon: 'none',
          });
          return;
      }

      if (this.onemapMenu.hasSelectMenu()) {
        this.openPop();
      }
    },

    // Map click resolvers
    resolveMapClick(attributes) {
      const type = this.onemapMenu?.current;
      if (!type) return;

      switch (type) {
        case 'sssb_sc':
        case 'sssb_llj':
        case 'sssb_ylj':
        case 'sssb_sz':
        case 'sssb_dyh':
        case 'sssb_ecgs':
          this.resolveSSSBPopupInfo(attributes);
          attributes.id && this.openPop('refPopCenter');
          return;
        case 'rycl_xjry':
        case 'rycl_cby':
          this.resolveYWCLPopupInfo(attributes);
          break;
        case 'ywlc_gdlc':
          this.resolveYWLCPopupInfo(attributes);
          return;
        default:
          return;
      }
      this.openPop('refPopCenter');
    },

    async resolveSSSBPopupInfo(attributes) {
      this.mapPop.title = attributes.name;
      const res = await getStationRealTimeProperties(attributes.id, { type: '' });
      this.mapPop.columns = [
        { label: '', prop: 'propertyName', bgColor: '#f7f7f8' },
        { label: '', prop: 'value', unit: 'unit' },
        { label: '', prop: 'collectionTime', unit: '' },
      ];
      this.mapPop.data =
        res.data.map(item => ({
          ...item,
          collectionTime: dayjs(item.collectionTime).format('YYYY-MM-DD HH:mm:ss'),
        })) || [];
    },

    async resolveYWCLPopupInfo(attributes) {
      if (!attributes) return;
      this.mapPop.title = attributes.userName;
      this.mapPop.obj = attributes;
    },

    resolveYWLCPopupInfo(attributes) {
      uni.$u.route({
        url: 'pages/smartOperation/workOrderQuery/detail/index',
        params: {
          id: attributes.id,
          fromType: 'query',
          status: attributes.status,
          statusName: attributes.statusName,
        },
      });
    },

    // 显示导航路线
    handleNavigationRoute({ route, start, end }) {
      this.$refs.mapContainer?.showNavigationRoute?.(route, start, end);
    },

    // Helper functions
    getPersonType(type) {
      return type === 'rycl_xjry'
        ? 'XUNJIANRENYUAN'
        : type === 'rycl_cby'
          ? 'CHAOBIAORENYUAN'
          : type === 'rycl_qxry'
            ? 'QIANGXIURENYUAN'
            : '';
    },

    // 查询相关方法
    startPointQuery() {
      uni.showToast({
        title: '请点击地图进行查询',
        icon: 'none',
      });
    },

    startRangeQuery() {
      this.isDrawingRange = true;
      uni.showToast({
        title: '请在地图上绘制查询范围',
        icon: 'none',
      });
    },

    async prepareConditionQuery() {
      try {
        // 获取可查询图层
        await this.refreshLayerList();
        // 检查是否有可查询图层（按名称数组判断）
        if (!this.layerArray || this.layerArray.length === 0) {
          console.warn('⚠️ 没有可查询的图层');
          uni.showToast({
            title: '没有可查询的图层',
            icon: 'none',
          });
          return;
        }
        // 不再预拉取字段，交由子组件根据图层自动获取
      } catch (error) {
        console.error('❌ 准备条件查询数据失败:', error);
        throw error; // 重新抛出错误，让调用者处理
      }
    },

    // 处理来自MapContainer的查询点击事件
    onQueryClickFromMap(coordinates) {
      console.log('🎯 主组件接收到查询点击事件:', coordinates);
      if (this.currentQueryType === 'point') {
        console.log('✅ 执行点击查询');
        this.executePointQuery(coordinates);
      } else {
        console.log('❌ 查询类型不匹配，当前类型:', this.currentQueryType);
      }
    },

    // 处理范围查询
    handleRangeQuery(event) {
      // this.rangeGeometry = geometry;
      this.executeRangeQuery(event);
    },

    async executePointQuery(queryInfo) {
      try {
        uni.showLoading({ title: '查询中...' });
        const { queryPipelineByPoint } = await import('../../common/api/geoserver');
        const result = await queryPipelineByPoint({
          layers: this.layerArray,
          longitude: queryInfo.coordinates[0],
          latitude: queryInfo.coordinates[1],
          srs: queryInfo.srs,
          mapWidth: queryInfo.mapWidth,
          mapHeight: queryInfo.mapHeight,
          mapExtent: queryInfo.mapExtent,
          tolerance: 15,
        });
        const fc = result;
        const count = Array.isArray(fc?.features) ? fc.features.length : 0;
        if (count > 0) {
          this.queryResults = fc;
          this.showQueryResults = true;

          uni.showToast({
            title: `查询到 ${count} 条数据`,
            icon: 'success',
          });
        } else {
          uni.showToast({
            title: '该位置未查询到数据',
            icon: 'none',
          });
        }
      } catch (error) {
        console.error('点击查询失败:', error);
        uni.showToast({
          title: '查询失败',
          icon: 'error',
        });
      } finally {
        uni.hideLoading();
      }
    },

    async executeRangeQuery(event) {
      try {
        uni.showLoading({ title: '范围查询中...' });

        // 直接调用底层查询API
        const { queryPipelineByRange } = await import('../../common/api/geoserver');

        let geometry = event.graphic.geometry.rings;
        let srs = 'EPSG:4326';
        if (event.graphic.geometry.spatialReference.isWebMercator) {
          srs = 'EPSG:3857';
        }
        const result = await queryPipelineByRange({
          geometry,
          layers: this.layerArray,
          srs,
        });
        if (result.data && result.data.features.length > 0) {
          this.queryResults = result.data;
          this.showQueryResults = true;

          uni.showToast({
            title: `查询到 ${result.data.features.length} 条数据`,
            icon: 'success',
          });
        } else {
          uni.showToast({
            title: '该范围内未查询到数据',
            icon: 'none',
          });
        }
      } catch (error) {
        console.error('范围查询失败:', error);
        uni.showToast({
          title: '查询失败',
          icon: 'error',
        });
      } finally {
        uni.hideLoading();
        this.isDrawingRange = false;
      }
    },

    // 图层管理相关方法
    onLayerAdded(layerInfo) {
      console.log('图层已添加:', layerInfo.title);
      this.refreshLayerList();
    },

    onLayerRemoved(layerInfo) {
      console.log('图层已移除:', layerInfo.title);
      this.refreshLayerList();
    },

    onLayerVisibilityChanged(data) {
      this.$set(this.layerVisibility, data.layerId, data.visible);
    },

    async refreshLayerList() {
      try {
        // 检查 MapContainer 引用
        if (!this.$refs.mapContainer) {
          console.error('❌ MapContainer 引用不存在');
        }

        // 获取所有图层
        const layers = (await this.$refs.mapContainer?.getAllLayers?.()) || [];
        this.availableLayers = layers;
        this.layerArray = layers.map(l => l.name);
      } catch (error) {
        console.error('❌ 刷新图层列表失败:', error);
        throw error; // 重新抛出错误
      }
    },

    async toggleLayer(layerId) {
      try {
        const visible = await this.$refs.mapContainer?.toggleLayerVisibility?.(layerId);
        if (visible !== null) {
          this.$set(this.layerVisibility, layerId, visible);
        }
      } catch (error) {
        console.error('切换图层可见性失败:', error);
      }
    },

    async setLayerOpacity(layerId, opacity) {
      try {
        const newOpacity = await this.$refs.mapContainer?.setLayerOpacity?.(layerId, opacity);
        if (newOpacity !== null) {
          this.$set(this.layerOpacity, layerId, newOpacity);
        }
      } catch (error) {
        console.error('设置图层透明度失败:', error);
      }
    },

    async zoomToLayer(layerId) {
      try {
        await this.$refs.mapContainer?.zoomToLayer?.(layerId);
      } catch (error) {
        console.error('缩放到图层失败:', error);
      }
    },

    async getLayerInfo(layerId) {
      try {
        return await this.$refs.mapContainer?.getLayerInfo?.(layerId);
      } catch (error) {
        console.error('获取图层信息失败:', error);
        return null;
      }
    },

    async getLayerFields(layerId) {
      try {
        return (await this.$refs.mapContainer?.getLayerFields?.(layerId)) || [];
      } catch (error) {
        console.error('获取图层字段失败:', error);
        return [];
      }
    },

    displayQueryResults(results) {
      // 将查询结果显示在地图上
      this.$refs.mapContainer?.displayQueryResults?.(results);
    },

    highlightQueryFeature(feature) {
      // 高亮显示具有几何数据的要素
      this.$refs.mapContainer?.highlightFeature?.(feature);
    },

    clearQueryResultHighlight() {
      // 清除地图上的高亮效果
      // this.$refs.mapContainer?.clearHighlight?.();
    },
  },
};
</script>

<style scoped lang="scss">
@import './styles/map.scss';
</style>
