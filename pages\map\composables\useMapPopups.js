import dayjs from 'dayjs';
import { getStationRealTimeProperties } from '../../../common/api/stations';

export function useMapPopups(mapData) {
  const { onemapMenu, station, users, workorders, curLayerIds, mapPop, curModule } = mapData;

  // Popup management
  const openPop = (name = 'refPopRight') => {
    // This will be handled by the MapPopups component
  };

  const closePop = (name = 'refPopRight') => {
    // This will be handled by the MapPopups component
  };

  // Data refresh methods
  const refreshStationData = async params => {
    switch (curModule.value) {
      case 'sssb':
        await handleSSSB();
        if (onemapMenu.hasSelectMenu()) {
          openPop();
        }
        break;
      case 'sbzc':
        curLayerIds.value = [params];
        await handleSBZC();
        if (onemapMenu.hasSelectMenu()) {
          openPop();
        }
        break;
      case 'rycl':
        await handleRYCL();
        break;
      case 'ywlc':
        await handleYWLC();
        break;
      case 'sjcj':
        await handleSJCJ();
        break;
      default:
        station.reset();
        users.reset();
        break;
    }
  };

  // Module handlers
  const handleSSSB = async () => {
    await station.getStationList(onemapMenu.curMenu);
  };

  const handleSBZC = async () => {
    // Equipment handling logic
  };

  const handleRYCL = async () => {
    const type = onemapMenu?.current;
    if (type === 'rycl_cljk') {
      uni.showToast({
        title: '升级中,敬请期待',
        icon: 'none',
      });
    } else {
      const personType = getPersonType(type);
      await users._getLatestUserCoords({
        userTypeId: personType,
      });
      if (onemapMenu.hasSelectMenu()) {
        openPop();
      }
    }
  };

  const handleYWLC = async () => {
    const type = onemapMenu?.current;
    const curMenu = onemapMenu.curMenu;
    workorders.reset();

    if (['ywlc_gdlc', 'ywlc_xjyh'].indexOf(curMenu.id) === -1) {
      uni.$u.toast('升级中,敬请期待');
      return;
    }

    if (type === 'ywlc_gdlc') {
      const res = await workorders.getWorkOrders();
      if (!res.data.length) {
        uni.$u.toast('暂无' + curMenu?.name + '信息');
      }
    }

    if (onemapMenu.hasSelectMenu()) {
      openPop();
    }
  };

  const handleSJCJ = async () => {
    const type = onemapMenu?.current;
    switch (type) {
      case 'sjcj_dmafq':
        break;
      default:
        uni.showToast({
          title: '升级中,敬请期待',
          icon: 'none',
        });
        return;
    }

    if (onemapMenu.hasSelectMenu()) {
      openPop();
    }
  };

  // Map click resolvers
  const resolveMapClick = attributes => {
    const type = onemapMenu?.current;
    if (!type) return;

    switch (type) {
      case 'sssb_sc':
      case 'sssb_llj':
      case 'sssb_ylj':
      case 'sssb_sz':
      case 'sssb_dyh':
      case 'sssb_ecgs':
        resolveSSSBPopupInfo(attributes);
        attributes.id && openPop('refPopCenter');
        return;
      case 'rycl_xjry':
      case 'rycl_cby':
        resolveYWCLPopupInfo(attributes);
        break;
      case 'ywlc_gdlc':
        resolveYWLCPopupInfo(attributes);
        return;
      default:
        return;
    }
    openPop('refPopCenter');
  };

  const resolveSSSBPopupInfo = async attributes => {
    mapPop.title = attributes.name;
    const res = await getStationRealTimeProperties(attributes.id, { type: '' });
    mapPop.columns = [
      { label: '', prop: 'propertyName', bgColor: '#f7f7f8' },
      { label: '', prop: 'value', unit: 'unit' },
      { label: '', prop: 'collectionTime', unit: '' },
    ];
    mapPop.data =
      res.data.map(item => ({
        ...item,
        collectionTime: dayjs(item.collectionTime).format('YYYY-MM-DD HH:mm:ss'),
      })) || [];
  };

  const resolveYWCLPopupInfo = async attributes => {
    if (!attributes) return;
    mapPop.title = attributes.userName;
    mapPop.obj = attributes;
  };

  const resolveYWLCPopupInfo = attributes => {
    uni.$u.route({
      url: 'pages/smartOperation/workOrderQuery/detail/index',
      params: {
        id: attributes.id,
        fromType: 'query',
        status: attributes.status,
        statusName: attributes.statusName,
      },
    });
  };

  // Helper functions
  const getPersonType = type => {
    return type === 'rycl_xjry'
      ? 'XUNJIANRENYUAN'
      : type === 'rycl_cby'
      ? 'CHAOBIAORENYUAN'
      : type === 'rycl_qxry'
      ? 'QIANGXIURENYUAN'
      : '';
  };

  return {
    openPop,
    closePop,
    refreshStationData,
    resolveMapClick,
    resolveSSSBPopupInfo,
    resolveYWCLPopupInfo,
    resolveYWLCPopupInfo,
  };
}
