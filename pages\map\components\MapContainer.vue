<template>
  <view
    id="arcgisView"
    class="map-view"
    :change:center="arcgisView.setCenter"
    :center="center"
    :change:currentMenu="arcgisView.currentMenuChanged"
    :currentMenu="currentMenu"
    :change:currentVMenu="arcgisView.currentVerticalMenuChanged"
    :currentVMenu="currentVMenu"
    :change:cleanMark="arcgisView.clean"
    :cleanMark="cleanMark"
    :change:locateMark="arcgisView.locate"
    :locateMark="locateMark"
    :change:basemap="arcgisView.toggleBaseMap"
    :basemap="basemap"
    :change:pipevisible="arcgisView.togglePipeVisible"
    :pipevisible="pipevisible"
    :change:pipeUrl="arcgisView.setPipeUrl"
    :pipeUrl="pipeUrl"
    :change:stations="arcgisView.refreshStations"
    :stations="stations"
    :change:persons="arcgisView.refreshPersons"
    :persons="persons"
    :change:workorders="arcgisView.refreshWorkorders"
    :workorders="workorders"
    :change:dmaVisible="arcgisView.toggleDMA"
    :dmaVisible="dmaVisible"
    :change:partitions="arcgisView.refreshDMA"
    :partitions="partitions"
    :change:measureType="arcgisView.startMeasure"
    :measureType="measureType"
    :change:queryType="arcgisView.startQuery"
    :queryType="queryType"
    :change:queryResults="arcgisView.displayQueryResults"
    :queryResults="queryResults"
  />
</template>

<!-- Vue组件定义移到renderjs中，统一管理 -->

<script lang="renderjs" module="arcgisView">
import { gisConfig } from '@/common/data/gisData';
import {
  initView,
  loadEsriModules,
  changeBaseMap,
  getLayerById,
  setSymbol,
  locationIcon,
  userMarker,
  loadGeoServerWMSLayer
} from '../../../common/utils/arcMapHelper'
import {
  useMeasure
} from '../../../common/hooks/map'
import { createLayerManager } from '../../../common/utils/layerManager'

var measure = useMeasure()
var view = undefined
var graphicsLayer = undefined
var highlightLayer = undefined
var layerManager = undefined
var Point, Graphic, Polygon, SimpleFillSymbol, PictureMarkerSymbol
var Polyline, SimpleLineSymbol, SimpleMarkerSymbol, GraphicsLayer
var wmUtils
var clickEventHandle = null

export default {
  name: 'MapContainer',
  // Vue组件属性定义
  props: {
    center: String,
    currentMenu: Object,
    currentVMenu: String,
    cleanMark: Number,
    locateMark: Array,
    basemap: String,
    pipevisible: Object,
    pipeUrl: String,
    stations: Array,
    persons: Array,
    workorders: Array,
    dmaVisible: Boolean,
    partitions: Array,
    measureType: String,
    queryType: String,
    queryResults: Array,
  },
  emits: ['mapClick', 'queryClick', 'onLayerAdded', 'onLayerRemoved', 'onLayerVisibilityChanged', 'getLayerInfoFromRenderjs', 'onQueryClickFromMap', 'handleRangeQuery', 'refreshStationData'],
  data() {
    return {
      pipeUrl: '',
      mapCenter: undefined,
      center: '',
      subLayers: [],
      partitions: [],
      queryMode: 'none',
      currentAnimationHandles: [], // 存储当前激活的动画句柄
    }
  },
  async mounted() {
    const container = 'arcgisView'

    view = await initView(container)

    const [p, g, pms, pol, sfs] = await loadEsriModules([
      'esri/geometry/Point',
      'esri/Graphic',
      'esri/symbols/PictureMarkerSymbol',
      'esri/geometry/Polygon',
      'esri/symbols/SimpleFillSymbol'
    ])

    Point = p
    Graphic = g
    PictureMarkerSymbol = pms
    Polygon = pol
    SimpleFillSymbol = sfs

    // 额外模块：线、符号、坐标转换
    const [pl, sls, sms, wm, gl] = await loadEsriModules([
      'esri/geometry/Polyline',
      'esri/symbols/SimpleLineSymbol',
      'esri/symbols/SimpleMarkerSymbol',
      'esri/geometry/support/webMercatorUtils',
      'esri/layers/GraphicsLayer'
    ])
    Polyline = pl
    SimpleLineSymbol = sls
    SimpleMarkerSymbol = sms
    wmUtils = wm
    GraphicsLayer = gl

    await this.loadPipeLayer()

    // 获取图形图层用于显示标记
    graphicsLayer = await getLayerById(view, 'device-layer')
    // 高亮图层（单独管理，避免与普通图形混淆）
    highlightLayer = new GraphicsLayer({ id: 'highlight-layer', title: '高亮图层' })
    view.map.add(highlightLayer)

    measure.init()

    // 初始化图层管理器
    console.log('🔧 开始初始化图层管理器...');
    try {
      layerManager = createLayerManager(view);
      console.log('✅ 图层管理器初始化成功:', layerManager);
    } catch (error) {
      console.error('❌ 图层管理器初始化失败:', error);
    }

    // 监听图层事件
    layerManager.on('layer-added', (data) => {
      console.log('图层已添加:', data.layer.title)
      this.$emit('onLayerAdded', {
        id: data.layer.id,
        title: data.layer.title,
        type: data.layer.type,
        visible: data.layer.visible
      })
    })

    layerManager.on('layer-removed', (data) => {
      console.log('图层已移除:', data.layer.title)
      this.$emit('onLayerRemoved', {
        id: data.layer.id,
        title: data.layer.title
      })
    })

    layerManager.on('layer-visibility-changed', (data) => {
      this.$emit('onLayerVisibilityChanged', {
        layerId: data.layer.id,
        visible: data.visible
      })
    })
  },
  methods: {


    setCenter(center) {
      this.mapCenter = center
    },

    setPipeUrl(url) {
      this.pipeUrl = url
    },

    handleViewClick(response) {
      const attrs = response?.results?.[0]?.graphic?.attributes
      this.$emit('mapClick', attrs)
    },

    async toggleDMA(flag) {
      const dmaLayer = await getLayerById(view, 'dma-layer')
      if (!dmaLayer || !this.partitions?.length) return
      this.refreshDMA(this.partitions)
      dmaLayer.visible = flag
    },

    async refreshDMA(partitions) {
      this.partitions = partitions
      const dmaLayer = await getLayerById(view, 'dma-layer')
      if (!dmaLayer || !this.partitions?.length) return

      const gs = []
      this.partitions.map(item => {
        if (!item.geom) return
        const rings = JSON.parse(item.geom)
        var g = new Graphic({
          geometry: new Polygon({
            rings: rings,
            spatialReference: view.spatialReference
          }),
          symbol: new SimpleFillSymbol({
            color: [0, 0, 0, 0.1],
            style: 'solid',
            outline: {
              width: 2,
              style: 'solid',
              color: item.rangeColor,
              outlineColor: item.borderColor
            }
          }),
          attributes: { ...item }
        })
        var text = g.clone()
        text.symbol = setSymbol('text', {
          text: item.name,
          yoffset: -9,
          color: item.borderColor
        })
        gs.push(g)
        gs.push(text)
      }) || []
      dmaLayer.removeAll()
      dmaLayer.addMany(gs)
    },

    refreshStations(stations) {
      if (!view || !graphicsLayer) return

      var graphics = []
      stations?.map(item => {
        const location = item.location?.split(',') || []
        if (location.length === 2) {
          const isWebMercator = view.spatialReference?.isWebMercator
          let pointGeometry
          if (isWebMercator) {
            pointGeometry = {
              x: parseFloat(location[0]),
              y: parseFloat(location[1]),
              spatialReference: view.spatialReference
            }
          } else {
            pointGeometry = {
              longitude: parseFloat(location[0]),
              latitude: parseFloat(location[1]),
              spatialReference: view.spatialReference
            }
          }
          var graphic = new Graphic({
            geometry: new Point(pointGeometry),
            symbol: new PictureMarkerSymbol({
              width: 25,
              height: 25,
              url: locationIcon,
              yoffset: 13
            }),
            attributes: { ...item }
          })
          const text = graphic.clone()
          text.symbol = setSymbol('text', {
            text: item.name,
            yoffset: -8
          })
          graphics.push(graphic)
          graphics.push(text)
        }
      })
      graphicsLayer.addMany(graphics)
      view?.goTo(graphics).catch(function(error) {
        if (error.name != "AbortError") {
          console.error(error)
        }
      })
    },

    async refreshPersons(persons) {
      if (!persons?.length || !view || !graphicsLayer) return

      const graphics = []
      persons.map(item => {
        const location = item.coordinate?.split(',') || []
        if (location.length === 2) {
          const x = parseFloat(location[0])
          const y = parseFloat(location[1])
          if (isNaN(x) || isNaN(y)) return

          const isWebMercator = view.spatialReference?.isWebMercator
          let pointGeometry
          if (isWebMercator) {
            pointGeometry = {
              x: x,
              y: y,
              spatialReference: view.spatialReference
            }
          } else {
            pointGeometry = {
              longitude: x,
              latitude: y,
              spatialReference: view.spatialReference
            }
          }
          var graphic = new Graphic({
            geometry: new Point(pointGeometry),
            symbol: new PictureMarkerSymbol({
              width: 25,
              height: 25,
              url: locationIcon,
              yoffset: 13
            }),
            attributes: { ...item }
          })
          var text = graphic.clone()
          text.symbol = setSymbol('text', {
            text: item.userName + '(' + item.userDepartmentName + ')',
            yoffset: -9
          })
          graphics.push(graphic)
          graphics.push(text)
        }
      })
      graphicsLayer.addMany(graphics)
      view?.goTo(graphics).catch(function(error) {
        if (error.name != "AbortError") {
          console.error(error)
        }
      })
    },

    refreshWorkorders(workorders) {
      if (!workorders?.length || !view || !graphicsLayer) return

      const graphics = []
      workorders.map(item => {
        const location = item.coordinate?.split(',') || []
        if (location.length === 2) {
          const y = parseFloat(location[0])
          const x = parseFloat(location[1])
          if (isNaN(x) || isNaN(y)) return

          const isWebMercator = view.spatialReference?.isWebMercator
          let pointGeometry
          if (isWebMercator) {
            pointGeometry = {
              x: x,
              y: y,
              spatialReference: view.spatialReference
            }
          } else {
            pointGeometry = {
              longitude: x,
              latitude: y,
              spatialReference: view.spatialReference
            }
          }
          var graphic = new Graphic({
            geometry: new Point(pointGeometry),
            symbol: new PictureMarkerSymbol({
              width: 25,
              height: 25,
              url: locationIcon,
              yoffset: 13
            }),
            attributes: { ...item }
          })
          var text = graphic.clone()
          text.symbol = setSymbol('text', {
            text: item.title + '(' + item.statusName + ')',
            yoffset: -8
          })
          graphics.push(graphic)
          graphics.push(text)
        }
      })
      graphicsLayer.addMany(graphics)
      view?.goTo(graphics).catch(function(error) {
        if (error.name != "AbortError") {
          console.error(error)
        }
      })
    },

    togglePipeVisible(params) {
      const layer = view?.map?.findLayerById('pipe-layer')
      const subLayer = layer?.sublayers.find(item => item.id === params?.id)
      layer && subLayer && (subLayer.visible = !!params?.visible)
    },

    toggleBaseMap(type) {
      if (!view?.map) return
      changeBaseMap(view.map, type)
    },

    async loadPipeLayer() {
      try {
        const pipeLayer = await loadGeoServerWMSLayer(view, 'pipe-layer', {
          center: gisConfig().center,
          zoom: gisConfig().zoom
        })

        this.subLayers = pipeLayer?.sublayers?.items?.map(item => ({
          layername: item.title || item.name,
          layerid: item.name
        })) || []

        this.$emit('getLayerInfoFromRenderjs', this.subLayers.map(item => item.layerid))
      } catch (error) {
        console.error('加载GeoServer图层失败:', error)
        uni.showToast({
          title: '加载图层失败',
          icon: 'none'
        })
      }
    },

    locate() {
      var options = {
        enableHighAccuracy: true,
        timeout: 5000,
        maximumAge: 0,
      }

      function success(position) {
        var lng = position.coords.longitude
        var lat = position.coords.latitude

        if (!lng && !lat) return
        const g = new Graphic({
          geometry: {
            type: 'point',
            longitude: lng,
            latitude: lat,
            spatialReference: view?.spatialReference
          },
          symbol: new PictureMarkerSymbol({
            width: 50,
            height: 50,
            url: userMarker,
            yoffset: 13
          })
        })
        view?.graphics?.removeAll()
        view?.graphics?.add(g)

        view?.goTo({
          zoom: 18,
          target: g
        }, {
          padding: { left: 50, right: 50, top: 50, bottom: 200 }
        })
      }

      function error(err) {
        console.warn("ERROR(" + err.code + "): " + err.message)
      }

      navigator.geolocation.getCurrentPosition(success, error, options)
    },

    async currentMenuChanged(newValue) {
      graphicsLayer?.removeAll()

      if (newValue?.type === 'sbzc') {
        const layerName = newValue?.name === '水表' ? '计量装置' : newValue?.name
        const layerid = this.subLayers.find(item => item.layername === layerName)?.layerid
        this.$emit('refreshStationData', layerid)
      } else {
        this.$emit('refreshStationData')
      }
    },

    // 统一的清理方法
    clean() {
      this.cleanLayers()      // 清理所有图层
      this.cleanEvents()      // 清理所有事件监听
      this.cleanAnimations()  // 清理所有动画效果
      this.cleanStates()      // 清理所有状态
      this.cleanNavigationRoute()
    },

    // 清理所有图层相关内容
    cleanLayers() {
      // 清理图形图层
      graphicsLayer?.removeAll()
      // 清理高亮图层
      highlightLayer?.removeAll()
      // 清理测量图层
      measure?.clear()
      // 清理闪烁图层
      if (view?.map) {
        const layers = view.map.layers.items
        layers.forEach(layer => {
          if (layer.id?.startsWith('flash-layer-')) {
            layer.visible = true
            view.map.remove(layer)
          }
        })
      }
    },

    // 清理所有事件监听
    cleanEvents() {
      // 清理点击查询事件
      if (clickEventHandle && typeof clickEventHandle.remove === 'function') {
        try { clickEventHandle.remove() } catch(e) { console.warn('移除点击监听失败:', e) }
        clickEventHandle = null
      }
      this.queryMode = 'none'
    },

    // 清理所有动画效果
    cleanAnimations() {
      // 清理动画定时器
      if (Array.isArray(this.currentAnimationHandles)) {
        this.currentAnimationHandles.forEach(handle => {
          if (handle && typeof handle === 'number') {
            clearInterval(handle)
          }
        })
        this.currentAnimationHandles = []
      }
    },

    // 清理所有状态
    cleanStates() {
      this.queryMode = 'none'      // 重置查询模式
      measure?.clear()             // 重置测量状态
    },

    currentVerticalMenuChanged(newVal) {
      if (['area', 'distance'].includes(newVal)) {
        measure.start(view, newVal)
      } else {
        measure.clear()
      }
    },

    startMeasure(measureType) {
      if (measureType && ['area', 'distance'].includes(measureType)) {
        measure.start(view, measureType)
      } else {
        measure.clear()
      }
    },

    startQuery(queryType) {
      console.log('🗺️ MapContainer 接收到查询类型变化:', queryType);
      const nextMode = queryType === 'point' ? 'point' : (queryType === 'range' ? 'range' : 'none')
      if (this.queryMode === nextMode) {
        console.log('查询模式未变化，跳过');
        return
      }
      // 切换前清理旧监听
      this.disableQuery()
      if (nextMode === 'point') {
        console.log('🎯 启用点击查询模式');
        this.enablePointQuery()
      } else if (nextMode === 'range') {
        console.log('📐 启用范围查询模式');
        this.enableRangeQuery()
      } else {
        console.log('🧹 清除查询模式');
      }
      this.queryMode = nextMode
    },

    enablePointQuery() {
      console.log('🔧 启用点击查询监听器');
      // 清理旧监听并注册新监听
      if (!view) {
        console.error('❌ view 对象不存在，无法启用点击查询');
        return
      }
      // 移除旧句柄
      if (clickEventHandle && typeof clickEventHandle.remove === 'function') {
        try { clickEventHandle.remove() } catch(e) { console.warn('移除旧点击监听失败:', e) }
        clickEventHandle = null
      }
      // 注册新句柄
      clickEventHandle = view.on('click', (evt) => this.handleQueryClick(evt))
      console.log('✅ 点击查询模式已启用，监听器已添加');
    },

    enableRangeQuery() {
      // 启用范围绘制工具
      this.startRangeDrawing()
      console.log('范围查询模式已启用')
    },

    // 禁用查询
    disableQuery() {
      this.cleanEvents()       // 清理事件监听
      this.cleanStates()       // 清理状态
    },

    handleQueryClick(event) {

      let coordinates;
      let srs;
      let mapExtent;

      // 根据地图的空间参考系统获取正确的坐标
      if(event.mapPoint.spatialReference.isWebMercator){
        coordinates = [event.mapPoint.x, event.mapPoint.y];
        srs = 'EPSG:3857';
      } else {
        coordinates = [event.mapPoint.longitude, event.mapPoint.latitude];
        srs = 'EPSG:4326';
      }

      // 获取当前地图视图范围
      if (view && view.extent) {
        mapExtent = {
          xmin: view.extent.xmin,
          ymin: view.extent.ymin,
          xmax: view.extent.xmax,
          ymax: view.extent.ymax,
          spatialReference: view.extent.spatialReference
        };
      }

      // 传递完整的查询信息
      const queryInfo = {
        coordinates: coordinates,
        srs: srs,
        mapExtent: mapExtent,
        mapWidth: view ? view.width : 512,
        mapHeight: view ? view.height : 512,
        spatialReference: event.mapPoint.spatialReference
      };

      console.log('🎯 查询信息:', queryInfo);
      this.$emit('onQueryClickFromMap', queryInfo);
    },

    startRangeDrawing() {
      // 启动范围绘制工具
      if (view) {
        this.enableRangeDrawing()
      }
    },

    async enableRangeDrawing() {
      try {
        const [SketchViewModel, GraphicsLayer] = await loadEsriModules([
          'esri/widgets/Sketch/SketchViewModel',
          'esri/layers/GraphicsLayer'
        ])

        // 创建绘制图层
        const sketchLayer = new GraphicsLayer({
          id: 'sketch-layer',
          title: '绘制图层'
        })
        view.map.add(sketchLayer)

        // 创建绘制工具
        const sketchViewModel = new SketchViewModel({
          view: view,
          layer: sketchLayer,
          polygonSymbol: {
            type: 'simple-fill',
            color: [65, 105, 225, 0.2], // 皇家蓝，半透明
            style: 'solid',
            outline: {
              color: [30, 144, 255, 1], // 道奇蓝，不透明
              width: 2.5,
              style: 'solid'
            }
          },
          pointSymbol: {
            type: 'simple-marker',
            style: 'circle',
            size: 8,
            color: [30, 144, 255, 0.8],
            outline: {
              color: [255, 255, 255, 1],
              width: 2
            }
          },
          polylineSymbol: {
            type: 'simple-line',
            color: [30, 144, 255, 1],
            width: 2.5,
            style: 'solid'
          }
        })

        // 监听绘制完成事件
        sketchViewModel.on('create', (event) => {
          if (event.state === 'complete') {
            // 通知主组件执行范围查询
            this.$emit('handleRangeQuery', event)

            // 清理绘制工具
            sketchViewModel.cancel()
            view.map.remove(sketchLayer)
          }
        })

        // 开始绘制多边形
        sketchViewModel.create('polygon')

      } catch (error) {
        console.error('启动范围绘制失败:', error)
      }
    },

     displayQueryResults(results, style = {}) {
       if (!results || !results.length || !view) return

       // 默认样式配置
       const defaultStyle = {
         point: {
           color: [0, 120, 215, 0.8],
           size: 10,
           outlineColor: [255, 255, 255, 1],
           outlineWidth: 2
         },
         line: {
           color: [255, 140, 0, 0.8],
           width: 6
         },
         polygon: {
           fillColor: [255, 140, 0, 0.3],
           outlineColor: [255, 140, 0, 0.8],
           outlineWidth: 2
         }
       }

       // 合并样式配置
       const mergedStyle = {
         point: { ...defaultStyle.point, ...style.point },
         line: { ...defaultStyle.line, ...style.line },
         polygon: { ...defaultStyle.polygon, ...style.polygon }
       }

       // 坐标系转换工具函数
       const transformCoordinates = (coords, inSrs) => {
         if (!coords || !coords.length) return coords
         const isWebMercator = view.spatialReference?.isWebMercator
         return coords.map(coord => {
           let [x, y] = coord
           if (isWebMercator) {
             if (inSrs === 'EPSG:4326' && wmUtils?.lngLatToXY) {
               const xy = wmUtils.lngLatToXY(x, y)
               return xy
             }
           } else {
             if (inSrs === 'EPSG:3857' && wmUtils?.xyToLngLat) {
               const ll = wmUtils.xyToLngLat(x, y)
               return ll
             }
           }
           return [x, y]
         })
       }

       const graphics = []
       results.forEach(item => {
         if (!item.geometry) return

         // 推断源坐标系：基于数值范围启发式判断
         const coords = item.geometry.coordinates
         const firstCoord = Array.isArray(coords[0]) ? coords[0] : coords
         const x = Array.isArray(firstCoord) ? firstCoord[0] : firstCoord
         const y = Array.isArray(firstCoord) ? firstCoord[1] : coords[1]
         const sourceSrs = (Math.abs(x) > 180 || Math.abs(y) > 90) ? 'EPSG:3857' : 'EPSG:4326'

         let graphic
         const isWebMercator = view.spatialReference?.isWebMercator

         switch (item.geometry.type) {
           case 'Point': {
             const [x, y] = transformCoordinates([coords], sourceSrs)[0]
             const pointGeometry = isWebMercator
               ? { x, y, spatialReference: view.spatialReference }
               : { longitude: x, latitude: y, spatialReference: view.spatialReference }

             graphic = new Graphic({
               geometry: new Point(pointGeometry),
               symbol: new SimpleMarkerSymbol({
                 style: "circle",
                 size: mergedStyle.point.size,
                 color: mergedStyle.point.color,
                 outline: {
                   color: mergedStyle.point.outlineColor,
                   width: mergedStyle.point.outlineWidth
                 }
               }),
               attributes: item.properties
             })
             break
           }

           case 'LineString': {
             const transformedCoords = transformCoordinates(coords, sourceSrs)
             graphic = new Graphic({
               geometry: new Polyline({
                 paths: [transformedCoords],
                 spatialReference: view.spatialReference
               }),
               symbol: new SimpleLineSymbol({
                 color: mergedStyle.line.color,
                 width: mergedStyle.line.width
               }),
               attributes: item.properties
             })
             break
           }

           case 'Polygon': {
             const transformedCoords = transformCoordinates(coords[0], sourceSrs)
             graphic = new Graphic({
               geometry: new Polygon({
                 rings: [transformedCoords],
                 spatialReference: view.spatialReference
               }),
               symbol: new SimpleFillSymbol({
                 color: mergedStyle.polygon.fillColor,
                 outline: {
                   color: mergedStyle.polygon.outlineColor,
                   width: mergedStyle.polygon.outlineWidth
                 }
               }),
               attributes: item.properties
             })
             break
           }
         }

         if (graphic) {
           graphics.push(graphic)

           // 如果是面或线，添加中心点标记
          //  if (item.geometry.type !== 'Point') {
          //    const center = graphic.geometry.extent.center
          //    const centerGraphic = new Graphic({
          //      geometry: center,
          //      symbol: new SimpleMarkerSymbol({
          //        style: 'circle',
          //        color: [255, 140, 0, 0.8],
          //        size: 8,
          //        outline: {
          //          color: [255, 255, 255, 1],
          //          width: 1
          //        }
          //      })
          //    })
          //    graphics.push(centerGraphic)
          //  }
         }
       })

       if (graphics.length > 0) {
         highlightLayer?.removeAll()
         highlightLayer?.addMany(graphics)
         view?.goTo(graphics, {
           padding: { left: 50, right: 50, top: 50, bottom: 300 }  // 底部增加更多padding以避免抽屉遮挡
         }).catch(function(error) {
           if (error.name != "AbortError") {
             console.error(error)
           }
         })
       }
     },

     highlightFeature(item) {
       try {
         // 先清除之前的高亮和动画
         this.clearHighlight()

         // 推断源坐标系：基于数值范围启发式判断
         const detectSrsFromCoords = (coords) => {
           if (!coords) return null
           const pick = Array.isArray(coords[0]) ? coords[0] : coords
           const x = Array.isArray(pick) ? pick[0] : pick
           const y = Array.isArray(pick) ? pick[1] : undefined
           if (x == null || y == null) return null
           // 绝对值远大于经纬度，判定为 WebMercator 米单位
           return (Math.abs(x) > 180 || Math.abs(y) > 90) ? 'EPSG:3857' : 'EPSG:4326'
         }

         // 适配原始 GeoJSON 结构
         let geometry = item.geometry;//|| item.__originalFeature?.geometry
         let srs = 'EPSG:4326' // 默认坐标系

         if (geometry?.coordinates) {
           const inferred = detectSrsFromCoords(geometry.type === 'Polygon' ? geometry.coordinates[0] : geometry.coordinates)
           if (inferred) srs = inferred
         }

        const toViewPoint = (xy) => {
          if (!xy || xy.length < 2) return null
          let x = xy[0], y = xy[1]
          if (view.spatialReference?.isWebMercator) {
            if (srs === 'EPSG:4326' && wmUtils?.lngLatToXY) {
              const xy2 = wmUtils.lngLatToXY(x, y)
              x = xy2[0]
              y = xy2[1]
            }
            // Web Mercator 使用 x/y 属性
            return new Point({ x, y, spatialReference: view.spatialReference })
          } else {
            // 视图为地理坐标（WGS84）
            if (srs === 'EPSG:3857' && wmUtils?.xyToLngLat) {
              const ll = wmUtils.xyToLngLat(x, y)
              x = ll[0]
              y = ll[1]
            }
            // WGS84 使用 longitude/latitude 属性
            return new Point({ longitude: x, latitude: y, spatialReference: view.spatialReference })
          }
        }

         const toViewPath = (coords) => {
           if (!coords || !coords.length) return []
           return coords.map(c => {
             let x = c[0], y = c[1]
             if (view.spatialReference?.isWebMercator) {
               if (srs === 'EPSG:4326' && wmUtils?.lngLatToXY) {
                 const xy2 = wmUtils.lngLatToXY(x, y)
                 x = xy2[0]; y = xy2[1]
               }
             } else {
               if (srs === 'EPSG:3857' && wmUtils?.xyToLngLat) {
                 const ll = wmUtils.xyToLngLat(x, y)
                 x = ll[0]; y = ll[1]
               }
             }
             return [x, y]
           })
         }

         const addPoint = (pt) => {
           if (!pt) return
           const symbol = new SimpleMarkerSymbol({
             style: "circle",
             size: 16,
             color: [255, 0, 0, 0.8],  // 红色
             outline: {
               color: [255, 255, 255, 1],
               width: 3
             }
           })
           const g = new Graphic({
             geometry: pt,
             symbol: symbol,
             attributes: { ...item, __highlight: true }
           })
           return { graphic: g }
         }

         const addLine = (path) => {
           if (!path || path.length < 2) return null
           const line = new Polyline({ paths: [path], spatialReference: view.spatialReference })
           const symbol = new SimpleLineSymbol({
             color: [255, 0, 0, 0.9],  // 红色
             width: 3
           })
           const g = new Graphic({
             geometry: line,
             symbol: symbol
           })
           return { graphic: g }
         }

         // 清理之前的动画和图层
         this.clearHighlight()

         let result = null
         if (geometry) {
           // 创建统一的闪烁图层
           const flashLayer = new GraphicsLayer({
             id: 'flash-layer-' + Date.now(),
             visible: true
           })
           view.map.add(flashLayer)

           // 添加要素到闪烁图层
           if (geometry.type === 'Point') {
             result = addPoint(toViewPoint(geometry.coordinates))
           } else if (geometry.type === 'LineString') {
             const path = toViewPath(geometry.coordinates)
             result = addLine(path)
           } else if (geometry.type === 'Polygon') {
             const ring = geometry.coordinates[0] || []
             const path = toViewPath(ring)
             result = addLine(path)
           }

           // 如果创建了图形，添加到闪烁图层
           if (result?.graphic) {
             flashLayer.add(result.graphic)
           }

           // 启动统一的闪烁动画，5秒后停止
           let startTime = Date.now()
           const handle = setInterval(() => {
             // 检查是否超过5秒
             if (Date.now() - startTime > 5000) {
               this.clearHighlight() // 使用统一的清理方法
               return
             }
             // 切换图层可见性实现闪烁效果
             flashLayer.visible = !flashLayer.visible
           }, 300)  // 每300ms切换一次可见性

           if (result) {
             // 保存动画句柄和图层引用
             this.currentAnimationHandles = this.currentAnimationHandles || []
             this.currentAnimationHandles.push(handle)

             // 定位到要素
             view?.goTo(result.graphic, {
               padding: { left: 50, right: 50, top: 50, bottom: 800 }
             }).catch(() => {})
           }
         }
       } catch (e) {
         console.error('高亮要素失败:', e)
       }
    },

    // 清除高亮效果
    clearHighlight() {
      try {
        this.cleanAnimations()  // 清理动画
        // 清理闪烁图层
        if (view?.map) {
          const layers = view.map.layers.items
          layers.forEach(layer => {
            if (layer.id?.startsWith('flash-layer-')) {
              layer.visible = true
              view.map.remove(layer)
            }
          })
        }
      } catch(e) {
        console.error('清除高亮失败:', e)
      }
    },

    // 图层管理相关方法
    async getAllLayers() {
      if (!layerManager) {
        console.warn('⚠️ layerManager 未初始化');
        return []
      }
      try {
        let layer = await layerManager.getAllLayers();
        if (layer && layer.length > 0 && layer[0].layer && layer[0].layer.sublayers) {
          const sublayers = layer[0].layer.sublayers.items;
          return sublayers;
        } else {
          return [];
        }
      } catch (error) {
        console.error('❌ getAllLayers 失败:', error);
        return [];
      }
    },

    async getQueryableLayers() {
      if (!layerManager) {
        console.warn('⚠️ layerManager 未初始化');
        return []
      }
      try {
        const result = await layerManager.getQueryableLayers();
        return result;
      } catch (error) {
        return [];
      }
    },

    async getOperationalLayers() {
      if (!layerManager) return []
      return await layerManager.getOperationalLayers()
    },

    toggleLayerVisibility(layerId) {
      if (!layerManager) return null
      return layerManager.toggleLayerVisibility(layerId)
    },

    setLayerOpacity(layerId, opacity) {
      if (!layerManager) return null
      return layerManager.setLayerOpacity(layerId, opacity)
    },

    async zoomToLayer(layerId) {
      if (!layerManager) return false
      return await layerManager.zoomToLayer(layerId)
    },

    async addLayer(layerConfig) {
      if (!layerManager) return null
      return await layerManager.addLayer(layerConfig)
    },

    removeLayer(layerId) {
      if (!layerManager) return false
      return layerManager.removeLayer(layerId)
    },

    async getLayerInfo(layerId) {
      if (!layerManager) return null
      return await layerManager.getLayerInfo(layerId)
    },

    async getLayerFields(layerId) {
      console.log('🔍 MapContainer.getLayerFields 被调用，layerId:', layerId);
      if (!layerManager) {
        console.warn('⚠️ layerManager 未初始化');
        return []
      }
      try {
        const result = await layerManager.getLayerFieldInfo(layerId);
        console.log('📋 layerManager.getLayerFieldInfo 返回:', result);
        return result;
      } catch (error) {
        console.error('❌ getLayerFields 失败:', error);
        return [];
      }
    },

    async getLayerStats(layerId) {
      if (!layerManager) return null
      return await layerManager.getLayerStats(layerId)
    },

    async exportLayerConfig() {
      if (!layerManager) return '{}'
      return await layerManager.exportLayerConfig()
    },

    async importLayerConfig(configJson) {
      if (!layerManager) return []
      return await layerManager.importLayerConfig(configJson)
    },

    // 导航相关方法
    async showNavigationRoute(route, start, end) {
      try {
        // 清除已有的路线图层
        this.cleanNavigationRoute();

        // 创建新的路线图层
        const navigationLayer = new GraphicsLayer({
          id: 'navigation-route-layer',
          title: '导航路线'
        });
        view.map.add(navigationLayer);

        // 不再需要箭头图层

        // 绘制导航路线
        if (route && route.paths && route.paths[0]) {
          // 转换路线点
          const points = route.paths[0].points.map(point => [point.lng, point.lat]);

          // 创建路线
          const polyline = new Polyline({
            paths: [points],
            spatialReference: {
              wkid: 4326
            }
          });

          // 创建醒目的绿色虚线样式
          const routeSymbol = new SimpleLineSymbol({
            color: [0, 255, 179, 1], // 实心绿色
            width: 4, // 加粗线条
            style: "dash",
            cap: "round",
            join: "round",
            dashArray: [8, 4] // 更醒目的虚线样式
          });

          // 添加路线图形
          const routeGraphic = new Graphic({
            geometry: polyline,
            symbol: routeSymbol
          });
          navigationLayer.add(routeGraphic);          // 起终点标记已移除
          // 导航指令点已移除

          // 缩放到路线范围
          view.goTo(
            {
              target: polyline.extent.expand(1.5)
            },
            {
              duration: 1000,
              easing: 'ease-out',
              padding: {
                left: 50,
                right: 50,
                top: 50,
                bottom: 200
              }
            }
          );
        }
      } catch (error) {
        console.error('显示导航路线失败:', error);
        throw error;
      }
    },
    cleanNavigationRoute(){
      const existingLayer = view.map.findLayerById('navigation-route-layer');
      if (existingLayer) {
        view.map.remove(existingLayer);
      }
    }
  }
}
</script>

<style scoped>
.map-view {
  height: 100%;
}
</style>
