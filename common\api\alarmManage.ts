import { http } from '../vmeitime-http/index'

export const getAlarmList = (params: {
	stationType?: String
	projectId?: String
	size: number
	page: number
	start?: number
	end?: number
	status?: String
	level?: String
}) => {
	return http().get('api/alarm/v2/list', params)
}

export const getAlarmListV2 = (params?: {
	processStatus?: String
	alarmStatus?: String
	alarmLevel?: String 
	stationId?: String
	startTime?: String
	endTime?: String
	size: number
	page: number
}) => {
	return http().get('api/alarmV2/alarmCenter/list', params)
}



// 解除报警
export const alarmRelieveV2 = (params: {
	alarmId: any[]
}) => {
	return http().post('api/alarmV2/alarmCenter/clearAlarm', params)
}

export const getAlarmRealTime = (projectId: string, params: {
	start: number,
	end: number,
	size: number,
	page: number
}) => {
	return http().get(`api/alarm/getAlarmRealTime/page${projectId ? `/${projectId}` : ''}`, params)
}


// 解除报警
export const alarmRelieve = (params: {
	alarmId: String[]
}) => {
	return http().post('api/alarm/clear', params)
}

// 报警数量
export const getCountAlarm = (params?: any) => {
	return http().get('api/alarm/countAlarm/overview', params)
}

// 报警数量V2
export const getCountAlarmV2 = (params?: any) => {
	return http().get('api/alarmV2/alarmCenter/countAlarm/overview', params)
}

//个人告警中心
export const getOwnAlarmListV2 = (params?: any) => {
	return http().get('api/alarmV2/alarmCenter/own', params)
}
