<template>
  <view class="cover-view-menu baselayer">
    <view class="cover-header">
      <text class="title">选择底图</text>
      <text class="icon" @click="$emit('close')">×</text>
    </view>
    <view class="cover-main">
      <view class="item" @click="$emit('changeBaseMap', 'vec')">
        <image 
          class="item-image"
          src="http://t4.tianditu.gov.cn/vec_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=vec&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX=13&TILEROW=3457&TILECOL=6548&tk=e4e98a7455967290863f2f1bb245f7b5"
        />
        <text class="item-text">标准</text>
      </view>
      <view class="item" @click="$emit('changeBaseMap', 'img')">
        <image 
          class="item-image"
          src="http://t4.tianditu.gov.cn/img_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=img&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX=14&TILEROW=6916&TILECOL=13100&tk=e4e98a7455967290863f2f1bb245f7b5"
        />
        <text class="item-text">卫星</text>
      </view>
      <view class="item" @click="$emit('changeBaseMap', 'arcgis')">
        <image 
          class="item-image"
          src="https://services.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/13/3457/6548"
        />
        <text class="item-text">ArcGIS卫星</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'BaseLayerSelector',
  emits: ['close', 'changeBaseMap']
}
</script>

<style scoped lang="scss">
.cover-view-menu.baselayer {
  height: 320rpx;
  width: 100%;
  padding: 0 32rpx;
  border-radius: 16rpx 16rpx 0rpx 0;
  position: absolute;
  bottom: 0;
  background-color: #FBFBFB;

  .cover-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 86rpx;

    .title {
      text-align: left;
      word-break: keep-all;
    }

    .icon {
      font-size: 1.2em;
      cursor: pointer;
    }
  }

  .cover-main {
    display: flex;
    justify-content: space-between;
    flex-wrap: nowrap;
    height: 200rpx;

    .item {
      width: calc(33.33% - 10rpx);
      height: 100%;
      position: relative;

      .item-image {
        width: 100%;
        height: 100%;
        border-radius: 8px;
      }

      .item-text {
        background: rgba(255, 255, 255, 0.8);
        border-radius: 0px 0px 8px 8px;
        width: 100%;
        position: absolute;
        bottom: 0;
        left: 0;
        height: 48rpx;
        line-height: 48rpx;
        padding: 0 20rpx;
        font-size: 24rpx;
        text-align: center;
      }
    }
  }
}
</style>