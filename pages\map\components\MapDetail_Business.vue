<template>
	<view>
		<MapDetailBusinessInspect v-if="props.menu.id==='ywlc_xjyh'" :title="menuName"></MapDetailBusinessInspect>
		<MapDetailBusinessWorkOrder v-if="props.menu.id==='ywlc_gdlc'" :title="menuName"></MapDetailBusinessWorkOrder>
	</view>

</template>

<script lang="ts" setup>
	import { computed } from 'vue'
	import MapDetailBusinessInspect from './MapDetail_Business_Inspect.vue'
	import MapDetailBusinessWorkOrder from './MapDetail_Business_WorkOrder.vue'

	const props = defineProps<{
		menu ?: {
			name : string,
			alias ?: string; icon : string; id : string; type : string; isActive ?: boolean; color ?: string
		},
	}>()
	const menuName = computed(() => {
		return props.menu.alias || props.menu.name
	})
</script>

<style lang="scss" scoped>
</style>