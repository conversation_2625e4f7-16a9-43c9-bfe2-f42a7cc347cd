import { ref } from 'vue';
export const useVerticalBar = (initData?: { current?: string; defaultMenus?: any[] }) => {
  const currentBar = ref<string>(initData?.current || '');
  const menus = ref<{ name: string; value: string; icon: string; iconUnicode?: string }[]>(
    initData?.defaultMenus || [
      {
        name: '底图',
        value: 'baselayer',
        icon: 'custom-icon-shixindiqiu',
        iconUnicode: '\ue631',
      },
      {
        name: '图层',
        value: 'layer',
        icon: 'custom-icon-tuceng-',
      },
      {
        name: '量测',
        value: 'measure',
        icon: 'custom-icon-map-ruler-full',
      },
      {
        name: '查询',
        value: 'query',
        icon: 'custom-icon-chaxun',
      },
    ],
  );
  const setCurrent = (data: string) => {
    // 处理重复点击相同按钮效果为toggle
    console.log(data, 'data--------');
    currentBar.value = data === currentBar.value ? '' : data;
  };
  return {
    currentBar,
    menus,
    setCurrent,
  };
};

export const useAreaBar = (initData?: { current?: string; defaultMenus?: any[] }) => {
  const currentBar = ref<string>(initData?.current || '');
  const menus = ref<{ name: string; value: string; icon: string; iconUnicode?: string }[]>(
    initData?.defaultMenus || [
      {
        name: '区域',
        value: 'area',
        icon: 'custom-icon-mianji',
      },
    ],
  );
  const setCurrent = (data: string) => {
    // 处理重复点击相同按钮效果为toggle
    currentBar.value = data === currentBar.value ? '' : data;
  };
  return {
    currentBar,
    menus,
    setCurrent,
  };
};
