<template>
	<view class="main">
		<u-sticky bgColor="#FFFFFF">
			<u-tabs bar-height="8" bar-width="40" active-color="#3862F8" :list="factoryList"
				v-model="state.currentFactory" @change="chooseFactory"></u-tabs>
		</u-sticky>
		<factory-monitor v-if="state.currentFactory === 0"></factory-monitor>
		<view v-else>
			<view class="img-bg" :style="{backgroundImage: 'url('+state.activeFactory.imgs+')'}"
				style="background-size: 100% 100%; background-repeat: no-repeat;">
				<view class="flex-between info">
					<view class="left">
						<view>{{state.activeFactory.name}}</view>
						<!-- <view>刷新倒计时：{{state.countDown}}秒</view> -->
					</view>
					<view class="right">
						最近采集时间：{{proxy.formatTime(state.collectionTime)}}
					</view>
				</view>
			</view>
			<view class="works-content">
				<u-tabs name="type" bar-height="8" bar-width="40" active-color="#060F27" inactive-color="#B2B7C7"
					font-size="28" v-model="state.currentWorks" bold :list="worksList" @change="chooseWorks">
				</u-tabs>
				<view class="list">
					<scroll-view scroll-y="true" style="height: 900rpx;">
						<!-- <view class="table flex" v-for="(item,index) in list" :key="index">
							<view class="name">
								{{item.propertyName}}
							</view>
							<view class="value">
								{{ item.propertyType=='4'? (item.value=='1'?'开':'关'): (parseFloat(item.value).toFixed(2)) || '-'}}{{item.unit}}
							</view>
							<view class="ts">
								{{proxy.formatTime(item.collectionTime)}}
							</view>
						</view> -->
						<!-- <u-table font-size="30" padding="14rpx" style="width: 100%;">
							<u-tr class="u-tr">
								<u-th class="u-th" width="320rpx">
									监测点
								</u-th>
								<u-th class="u-th" width="320rpx">
									检测值
								</u-th>
								<u-th class="u-th" width="320rpx">
									采集时间
								</u-th>
							</u-tr>
							<u-tr class="u-tr" v-for="(item,index) in list" :key="index">
								<u-td class="u-td" width="320rpx" style="word-break:breakall">
									<view class="name">
										{{item.propertyName}}
									</view>
								</u-td>
								<u-td class="u-td" width="320rpx">
									<view class="value">
										{{ item.propertyType=='4'? (item.value=='1'?'开':'关'): (parseFloat(item.value).toFixed(2)) || '-'}}{{item.unit}}
									</view>
								</u-td>
								<u-td class="u-td" width="320rpx">
									<view class="ts">
										{{proxy.formatTime(item.collectionTime)}}
									</view>
								</u-td>
							</u-tr>
						</u-table> -->
						<uni-table border stripe emptyText="暂无更多数据">
							<!-- 表头行 -->
							<uni-tr>
								<uni-th align="left" style="font-weight: bold;color: #000000; width: 40%;">监测点</uni-th>
								<uni-th align="left" style="font-weight: bold;color: #000000; width: 20%;">检测值</uni-th>
								<uni-th align="left" style="font-weight: bold;color: #000000; width: 40%;">采集时间</uni-th>
							</uni-tr>
							<!-- 表格数据行 -->
							<uni-tr v-for="(item,index) in list" :key="index" @click="showHistoryData(item)">
								<uni-td>{{item.propertyName}}</uni-td>
								<uni-td
									:style="{color:('停止、否、全关、异常、NaN、掉线、关闭'.indexOf(formatData(item))!==-1 || item.value==0.00 || item.collectionTime==0)?'red':'#00e649'}">
									{{formatData(item)}}
								</uni-td>
								<uni-td>{{proxy.formatTime(item.collectionTime)}}</uni-td>
							</uni-tr>
						</uni-table>
					</scroll-view>
				</view>
			</view>
		</view>
		<u-popup overlay mode="bottom" closeable v-model="showStatus" safeAreaInsetBottom border-radius="16"
			@close="showStatus=false">
			<view v-if="state.popupType === 'data'" class="popup">
				<data-list @onQuery="onQuery" :propertyList="propertyList" :currentProperty="state.attrDetail"
					title="查看详情" :headers="headers" :tableData="tableData"></data-list>
			</view>
			<view v-if="state.popupType === 'alarm'" class="popup">
				<alarm-list :stationId="state.currentProperty.stationId"></alarm-list>
			</view>
		</u-popup>
	</view>
</template>

<script lang="ts" setup>
	import {
		onMounted,
		onUnmounted,
		reactive,
		getCurrentInstance,
		ref
	} from "vue";
	import {
		getStationList,
		getAllStationGroup,
		getStationData
	} from '@/common/api/waterplant'
	import { onPullDownRefresh } from '@dcloudio/uni-app'
	import { keywordArr } from './data'
	import dataList from '@/components/dataList/dataList.vue'
	import factoryMonitor from './Monitor.vue'

	const { proxy } = getCurrentInstance()
	
	let state = reactive < {
		activeFactory: any,
		attrDetail: any,
		currentFactory: number,
		activeWorks: any,
		currentWorks: number,
		countDown: number,
		collectionTime: number,
		interval: any,
		currentProperty: any,
		popupType: string
	} > ({
		attrDetail: {},
		activeFactory: {},
		activeWorks: {},
		currentFactory: 0,
		currentWorks: 0,
		countDown: 60,
		collectionTime: 0,
		interval: null,
		popupType: 'data',
		currentProperty: {}
	})
	const showStatus = ref < boolean > (false)
	const factoryList = ref < any > ([])
	const worksList = ref < any > ([])
	const list = ref < any > ([])
	const propertyList = ref < any > ()
	const headers = ref < any > ()
	const tableData = ref < any > ([])
	//查询历史数据
	const onQuery = (query: any) => {
		// showHistoryData(query)
	}

	// 显示详情数据
	const showHistoryData = async (item: any) => {
		const val = formatData(item)
		const re = /^[\u4E00-\u9FA5]+/g;
		if (re.test(val)) {
			return false;
		} else {
			state.attrDetail = {
				...item,
				key: item.property,
				label: item.propertyName
			}
			state.popupType = 'data'
			showStatus.value = true
		}
	}

	//获取水厂列表
	const getFactoryList = async () => {
		const systemConfig = uni.getStorageSync('systemConfig')
		const params = {
			page: 1,
			size: 999,
			type: systemConfig.type
		}
		getStationList(params).then(async res => {
			factoryList.value = []
			let data = res.data?.data
			// 添加监控tab
			factoryList.value = [{id: 'monitor', name: '水厂监控'}, ...data]
			chooseFactory(state.currentWorks)
		})
	}

	const formatData = (item: any) => {
		const val = item.collectionTime == 0 ? '离线' :
			item.propertyType == '4' ?
			(item.value == '1' ? '启动' : '停止') :
			keywordArr.includes(getProptyLast(item.property)) ?
			(item.value == '1' ? '是' : '否') :
			// item.propertyName.indexOf('状态') != -1 ?
			// (item.value == '1' ? '运行' : '停止') :
			item.propertyName.indexOf('报警') != -1 ?
			(item.value == '1' ? '异常' : '正常') :
			item.propertyName.indexOf('远程自动') != -1 ?
			(item.value == '1' ? '全开' : '全关') :
			item.propertyName.indexOf('方式') != -1 ?
			(item.value == '1' ? '自动' : '手动') :
			item.propertyName.indexOf('远程') != -1 ?
			(item.value == '1' ? '启动' : '关闭') :
			(item.property.indexOf('Status') != -1) ?
			(parseInt(item.value) % 2 == 0 ? '全开' : '全关') :
			item.property.indexOf('StrokeAct') != -1 ?
			(item.value / 10) :
			(item.value ? parseFloat(item.value).toFixed(2) : '掉线') || '-';
		return val + '' + (item.value ? (item.unit || '') : '')
	}

	// 选择水厂
	const chooseFactory = async (index: number) => {
		// 选择水厂监控tab，显示监控页面
		if(index === 0) {
			state.currentFactory = index
			return
		}
		state.currentFactory = index
		state.activeFactory = factoryList.value[index]
		const res = await getAllStationGroup({
			stationId: state.activeFactory.id
		})
		console.log('chooseFactory', res?.data)
		worksList.value = res?.data
		await chooseWorks(0)
	}
	// 切换工作间
	const chooseWorks = async (index: number) => {
		state.currentWorks = index
		state.activeWorks = worksList.value[index]
		const res = await getStationData(state.activeFactory.id, state.activeWorks.type)
		console.log('list', res)
		list.value = res?.data
		state.collectionTime = res?.data[0]?.collectionTime
		uni.stopPullDownRefresh()
	}
	const getProptyLast = (name: string) => {
		const arr = name.split('_')
		const len = arr.length
		return arr[len - 1]
	}
	// 倒计时刷新
	const countDown = () => {
		state.interval = setInterval(async () => {
			if (state.countDown === 0) {
				state.countDown = 60
				await chooseFactory(state.currentWorks)
			} else {
				state.countDown -= 1
			}
		}, 1000)
	}

	onPullDownRefresh(async () => {
		await chooseWorks(state.currentWorks)
	})
	onMounted(async () => {
		await getFactoryList()
		// countDown()
	})

	onUnmounted(async () => {
		clearInterval(state.interval)
	})
</script>

<style lang="scss" scoped>
	.img-bg {
		width: 100vw;
		height: 480rpx;
		// background: url('../../../static/img/temporary/Rectangle.png') no-repeat;
		// background-size: 100% 100%;
		color: #FFFFFF;
		font-size: 20rpx;

		.info {
			padding: 0 30rpx;
			align-items: flex-end;
			position: relative;
			top: 300rpx;

			.left {
				view:nth-child(1) {
					font-weight: bold;
					font-size: 32rpx;
					margin-bottom: 24rpx;
				}
			}
		}
	}

	.works-content {
		position: relative;
		top: -60rpx;
		width: 100%;
		height: 100%;
		background: #FFFFFF;
		border-radius: 32rpx 32rpx 0px 0px;
	}

	.list {
		background-color: #F9F9F9;
		// width: 94%;
		margin: 20rpx auto;
		position: relative;
		border: 1rpx #EBEDF6 solid;

		.table {
			height: 64rpx;
			line-height: 64rpx;
			border: 1rpx #EBEDF6 solid;
			color: #91949F;

			.name {
				width: 20%;
				height: auto;
				padding-left: 20rpx;
				border-right: 2rpx #EBEDF6 solid;
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
			}

			.value {
				width: 30%;
				padding-left: 20rpx;
				border-right: 2rpx #EBEDF6 solid;
			}

			.ts {
				padding-left: 20rpx;
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
				width: 300rpx;
			}
		}
	}

	:deep(.uni-table) {
		overflow-x: hidden;
		min-width: 650rpx !important;
	}

	:deep(.uni-table-scroll) {
		width: 750rpx // width: 100%; table-layout: fixed; word-break: break-all;
	}

	.u-tr {
		//overflow-x: auto;
	}
</style>
