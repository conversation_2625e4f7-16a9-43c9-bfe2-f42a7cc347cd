
import { ref } from 'vue'
export const useSelect = (initialList : { label : string; value : any }[] = [], selectCallback ?: (e : any) => void) => {
	const show = ref<boolean>(false)
	const list = ref<{ label : string; value : any }[]>(initialList)
	const setList = (data : { label : string; value : any }[] = []) => {
		list.value = data
	}
	const handleSelect = (e : any) => {
		selectCallback?.(e)
	}
	return {
		show,
		list,
		setList,
		handleSelect
	}
}