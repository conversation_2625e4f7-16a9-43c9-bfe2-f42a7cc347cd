<template>
	<view class="main">
		<view class="info">
			<view>您好，</view>
			<view>欢迎使用智慧水务</view>
		</view>
		<view class="login-form">
			<u-form :model="form" ref="refForm" :errorType="['toast']">
				<u-form-item label-position="top" label="账号" prop="username" labelWidth="150" :labelStyle="labelStyle">
					<u-input v-model="form.username" placeholder="请输入邮箱地址" :custom-style="customStyle">
					</u-input>
				</u-form-item>
				<u-form-item label-position="top" prop="password" label="密码" labelWidth="150" :labelStyle="labelStyle">
					<u-input type="password" v-model="form.password" placeholder="请输入密码">
					</u-input>
				</u-form-item>
				<u-form-item label="记住密码" :borderBottom="false" labelWidth="150">
					<template #right>
						<u-switch size="40" v-model="remember" @change="chanegRemember"></u-switch>
					</template>
				</u-form-item> 
			</u-form>
			<view class="login-button">
				<u-button shape="circle" @click="handleLogin" type="primary" :loading="state.loading">登录</u-button>
			</view>
		</view>
	</view>
</template>

<script lang="ts" setup>
	import {
		getCurrentInstance,
		onBeforeMount,
		onMounted,
		reactive,
		ref
	} from "vue";
	import {
		onReady
	} from '@dcloudio/uni-app';
	import {
		getKey,
		getInfo,
		login
	} from '@/common/api/login'
	import {
		// getSystemNotifyCount,
		// readAll,
		saveUserCid,
		getAppBottomMenu
	} from '@/common/api/system'
	import AES from '@/common/utils/AES'
	import {
		removeSlash
	} from "../../common/utils/removeIdSlash";
	import {
		TabbarData
	} from '@/common/data/bottomMenuData'
	const {
		proxy
	} = getCurrentInstance()

	const state = reactive < {
		loading: boolean,
	} > ({
		loading: false,
	})
	const form = ref < any > ({
		username: '',
		password: '',
		requestId: ''
	})
	const refForm = ref < any > ({})
	const remember = ref < boolean > (false)
	const customStyle = ref < any > ({})
	const configure = ref < any > ()
	const labelStyle = ref < any > ({
		fontSize: '40rpx',
		fontWeight: 600,
	})
	const rules = reactive < any > ({
		username: [{
			required: true,
			message: '请输入账号'
		}],
		password: [{
			required: true,
			message: '请输入密码'
		}]
	})

	const handleLogin = async () => {
		refForm.value.validate(async (valid: any) => {
			if (valid) {
				const res = await getKey()
				let loginForm = {
					...form.value
				}
				if (remember.value) {
					uni.setStorage({
						key: 'loginInfo',
						data: JSON.stringify(loginForm)
					})
				} else {
					uni.removeStorageSync('loginInfo')
				}
				loginForm.username = AES.encrypt(loginForm.username, res.data.key)
				loginForm.password = AES.encrypt(loginForm.password, res.data.key)
				toLogin(loginForm)
			}
		})
	}

	const toLogin = async (userInfo: any) => {
			const params = {
				username: userInfo.username,
				password: userInfo.password,
				verifyCode: userInfo.verifyCode,
				requestId: userInfo.requestId
			}
			login(params).then(async res=>{
				uni.setStorageSync("token", res.data?.token)
				uni.setStorageSync("feeToken", res.data?.feeToken)
				// parse user id from token
				const tokenBody = JSON.parse(
					atob(
						res.data.token
						.match(/\.(.*?)\./)[1]
						.replace(/-/g, '+')
						.replace(/_/g, '/')
					)
				)
				// Cookies.set('userId', tokenBody.userId)
				console.log(tokenBody)
				// FIXME: SET_ID is not working
				await GetInfo(tokenBody.userId)
			}).catch(res=>{
				uni.$u.toast(res.data?.message || '登录失败')
			})
	}
	// 获取用户信息
	const GetInfo = async (userId: string) => {
		try {
			const res = await getInfo(userId)
			const data = res.data
			const authority = [data.authority]
			uni.setStorageSync('authority', authority)
			uni.setStorageSync('userInfo', data)
			uni.setStorageSync('currentUser', data);
			uni.setStorageSync('remember', remember.value)

			const tenanetId = removeSlash(data.tenantId.id)
			// #ifdef APP-PLUS
			const clientInfo = plus.push.getClientInfo()
			console.log('设备编号', clientInfo)
			if (clientInfo.clientid) {
				saveUserCid({
					userId: removeSlash(data.id.id),
					cid: clientInfo.clientid
				})
			}
			// #endif

			getAppBottomMenu({
				tenantId: tenanetId
			}).then(res => {
				const data = res.data.data
				const tabbar = data.map((d: any) => {
					return TabbarData.find((tabbar: any) => tabbar.key === d.menuKey)
				})
				uni.setStorageSync('bottomMenus', tabbar)
			})
			if (proxy.$isHideTabBar) {
				getAppBottomMenu({
					tenantId: tenanetId
				}).then(res => {
					const data = res.data.data
					const tabbar = data.map((d: any) => {
						return TabbarData.find((tabbar: any) => tabbar.key === d.menuKey)
					})
					uni.setStorageSync('bottomMenus', tabbar)
					uni.$u.route({
						type: 'switchTab',
						url: tabbar[0].pagePath
					})
				})
			} else {
				uni.switchTab({
					url: '/pages/index/index'
				})
			}


			// if (uni.getStorageSync('token')) {
			// 	getSystemNotifyCount({
			// 		status: 0,
			// 		to: removeSlash(data.id?.id)
			// 	}).then((res:any) => {
			// 		console.log('消息', res.data)
			// 		uni.createPushMessage({
			// 			title: '全南公用水务',
			// 			content: res.data?.data
			// 		})
			// 	})
			// }


		} catch (error) {
			//
		}
	}

	const chanegRemember = (val) => {
		console.log(val)
		remember.value = val
	}

	onReady(async () => {
		refForm.value.setRules(rules)
	})

	onMounted(async () => {
		remember.value = uni.getStorageSync('remember') || false
		console.log('remember', remember.value)
		if (remember.value) {
			form.value = JSON.parse(uni.getStorageSync('loginInfo'))
			// handleLogin()
		}
	})
</script>

<style lang="scss" scoped>
	.main {
		height: 100%;
		padding-top: 200rpx;
		background-image: linear-gradient(#0c69ff, #ffffff);
		overflow-y: hidden;
		.info {
			color: #ffffff;
			font-size: 50rpx;
			font-weight: 600;
			line-height: 70rpx;
			padding-left: 50rpx;
		}

		.login-form {
			padding-top: 40rpx;
			background-color: #ffffff;
			border-radius: 60rpx 60rpx 0 0;
			height: calc(100vh - 400rpx);
			padding: 30rpx 80rpx;
			margin-top: 50rpx;
		}

		.login-button {
			width: 90%;
			margin: 40rpx auto;
		}

		.conifg-login {
			color: #0c69ff;
			text-align: right;
			font-size: 30rpx;
			padding-top: 20rpx;
		}
	}
</style>
