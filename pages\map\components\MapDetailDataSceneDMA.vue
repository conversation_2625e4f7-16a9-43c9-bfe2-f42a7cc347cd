<template>
	<view class="pipe-detail">
		<view class="pipe-detail-item pipe-detail-total">

			<view class="title">
				{{props.title}}统计
			</view>
			<view class="total-blocks">
				<view class="total-block" v-for="(item, i) in state.total" :key="i" :style="{
					backgroundColor: item.color
				}">
					<view class="value">{{item.value}} {{item.unit}}</view>
					<view class="text">{{item.title}}</view>
				</view>
			</view>
		</view>
		<view class="pipe-detail-item pipe-detail-chart">
			<view class="title">当月{{props.title}}类型占比统计</view>
			<view class="chart-box">
				<l-echart ref="refLEchart_Status_Ratio"></l-echart>
			</view>
		</view>
		<view class="pipe-detail-item pipe-detail-list">
			<view class="title">{{props.title}}列表</view>
			<TableList :list="state.list" :list-columns="state.listColumns" :height="700"></TableList>
		</view>
	</view>

</template>

<script lang="ts" setup>
	import * as echarts from 'echarts'
	import lEchart from '@/uni_modules/lime-echart/components/l-echart/l-echart.vue'
	import TableList from './TableList.vue'
	import { onMounted, reactive, ref } from 'vue'
	import { getDMANRW, getDMALoss } from '../../../common/api/dma'
	const props = defineProps<{
		title ?: string
	}>()
	const refLEchart_Status_Ratio = ref<InstanceType<typeof lEchart>>()
	const state = reactive<{
		total : { color ?: string; value : number; title : string; unit ?: string }[],
		list : { name : string; status : string; lastTime : string }[]
		listColumns : { label : string; prop : string }[]
	}>({
		total: [
			{ color: 'rgb(82, 143, 248)', value: 0, title: '分区总数', unit: '个' },
			{ color: 'rgb(115, 203, 113)', value: 0, title: '当前差销差', unit: '%' }
		],
		list: [],
		listColumns: [
			{ label: '分区名称', prop: 'name' },
			{ label: '参考产销差', prop: 'nrw' },
			{ label: '类型', prop: 'type' }
		]
	})
	const refreshStatus = async (statusList ?: { data : { percent : number; value : number; key : string }[]; total : number }) => {
		try {
			let total = statusList.total || 0

			const data = statusList?.data?.map((item) => {
				return { name: item.key, value: item.value }
			}) || []
			refLEchart_Status_Ratio.value?.init(echarts, (chart : any) => {
				const option = {
					tooltip: {
						trigger: 'item'
					},
					title: {
						text:
							'{name|'
							+ '合计'
							+ '(m³)'
							+ '}\n{val|'
							+ total
							+ '}',
						top: 'center',
						left: '35%',
						textAlign: 'center',
						textStyle: {
							rich: {
								name: {
									fontSize: 10,
									fontWeight: 'normal',
									padding: [8, 0],
									align: 'center',
									color: '#2A2A2A'
								},
								val: {
									fontSize: 16,
									fontWeight: 'bold',
									color: '#2A2A2A'
								}
							}
						}
					},
					legend: {
						// selectedMode: false, // 取消图例上的点击事件
						type: 'scroll',
						icon: 'circle',
						orient: 'vertical',
						left: 'right',
						top: 'center',
						align: 'left',
						itemGap: 10,
						itemWidth: 10, // 设置宽度
						itemHeight: 10, // 设置高度
						symbolKeepAspect: true,
						textStyle: {
							color: '#fff',
							rich: {
								name: {
									align: 'left',
									width: 70,
									fontSize: 12,
									color: '#2A2A2A'
								},
								value: {
									align: 'left',
									width: 50,
									fontSize: 12,
									color: '#00ff00'
								},
								// unit: {
								// 	align: 'left',
								// 	width: 30,
								// 	fontSize: 12,
								// 	color: '#00ff00'
								// }
							}
						},
						data: data.map(item => item.name),
						formatter(name : any) {
							if (data && data.length) {
								for (let i = 0; i < data.length; i++) {
									if (name === data[i].name) {
										return (
											'{name| '
											+ (data[i].name || name)
											+ '}'
											+ '{value| '
											+ data[i].value
											+ ' 个'
											+ '}'
											// + '{unit| '
											// + 'm³'
											// + '}'
										)
									}
								}
							}
						}
					},
					series: [
						{
							name: props.title,
							type: 'pie',
							radius: ['50%', '80%'],
							center: ['35%', '50%'],
							data: data,
							// emphasis: {
							// },
							itemStyle: {
								shadowBlur: 10,
								shadowOffsetX: 0,
								shadowColor: 'rgba(0, 0, 0, 0.5)'
							},
							label: {
								show: true,
								position: 'inside',
								textStyle: {
									fontSize: 12
								},
								formatter(param : any) {
									// correct the percentage
									const num = param.percent?param.percent+'%':''
									return num;
								}
							},

							labelLine: {
								show: false
							},
						}
					]
				}
				chart.setOption(option)
			})

		} catch (e) {
			console.log(e)
		}
	}

	const refreshTable = async () => {
		try {
			const systemConfig = uni.getStorageSync('systemConfig')
			const res = await getDMANRW({
				appName: systemConfig.account
			})

			if (res.data.code === 200) {
				const data = res.data?.data || {}
				state.total[0].value = data.partitionNum
				state.total[1].value = data.rootNRW?.toFixed(2)
				state.list = data.dataForApp || []
			} else {
				console.log('统计失败');
			}
		} catch (e) {
			console.log(e)
		}
	}
	const refreshData = async () => {
		refreshTable()
		getDMALoss().then((res : any) => {
			const result = res.data?.data?.lossEvaluate || {}
			let total = 0
			const data : any[] = []
			for (let key in result) {
				const value = result[key]
				total += value
				data.push({ percent: 0, value, key })
			}
			total && data.map(item => {
				item.percent = (item.value / total * 100).toFixed(2)
				return item
			})
			refreshStatus({ data, total })
		}).catch(() => {
			refreshStatus()
		})
	}
	onMounted(() => {

		refreshData()
	})
</script>

<style lang="scss" scoped>
	.pipe-detail {

		.pipe-detail-item {
			.title {
				font-size: 32rpx;
				margin: 32rpx 0 0;

			}

			&:first-child {
				.title {
					margin-top: 0;
				}
			}
		}

		.pipe-detail-total {
			.total-blocks {
				border-radius: 4rpx;
				width: 100%;
				display: flex;
				padding: 24rpx;
				flex-wrap: wrap;
				justify-content: flex-start;

				.total-block {
					background-color: #0073ff;
					padding: 10rpx;
					color: #fff;
					width: calc(50% - 24rpx);
					height: 120rpx;
					margin: 12rpx;
					text-align: center;

					.text,
					.value {
						line-height: 50rpx;
					}
				}
			}
		}

		.pipe-detail-chart {
			.chart-box {
				height: 480rpx;
			}
		}

		.pipe-detail-list {
			padding-bottom: 140rpx;
		}

	}
</style>