<template>
	<view class="detail-person">
		<MapDetailPersonInspector v-if="props.menu.id==='rycl_xjry'" :title="menuName" :type="'XUN_JIAN_REN_YUAN'">
		</MapDetailPersonInspector>
		<MapDetailPersonInspector v-if="props.menu.id==='rycl_qxry'" :title="menuName" :type="'QIANG_XIU_REN_YUAN'">
		</MapDetailPersonInspector>
		<MapDetailPersonInspector v-if="props.menu.id==='rycl_cby'" :title="menuName" :type="'CHAO_BIAO_YUAN'">
		</MapDetailPersonInspector>
		<!-- <MapDetailPersonMeterReader v-if="props.menu.id==='rycl_cby'" :title="menuName"></MapDetailPersonMeterReader> -->
	</view>

</template>

<script lang="ts" setup>
	import { computed } from 'vue'
	import MapDetailPersonInspector from './MapDetail_Person_Inspector.vue'
	// import MapDetailPersonMeterReader from './MapDetail_Person_MeterReader.vue'

	const props = defineProps<{
		menu ?: {
			name : string,
			alias ?: string; icon : string; id : string; type : string; isActive ?: boolean; color ?: string
		},
	}>()
	const menuName = computed(() => {
		return props.menu.alias || props.menu.name
	})
</script>

<style lang="scss" scoped>
	.detail-person {

		padding-bottom: 140rpx;
	}
</style>