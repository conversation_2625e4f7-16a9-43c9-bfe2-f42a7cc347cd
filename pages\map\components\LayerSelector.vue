<template>
  <view class="cover-view-menu border-box layer">
    <view class="cover-header">
      <text class="title">选择图层</text>
      <text class="icon" @click="$emit('close')">×</text>
    </view>
    <scroll-view class="menu-list flex-around" :scroll-y="true">
      <!-- 管点类 -->
      <view class="layer-box">
        <text class="layer-title">管点类</text>
        <view class="layer-menu">
          <view
            class="menu-item"
            v-for="(menu, i) in pointLayers"
            :key="i"
            @click="$emit('toggleLayer', menu.layerid)"
          >
            <view
              class="icon-bg"
              :style="{
                'background-color': selected.indexOf(menu.layerid) !== -1 ? '#3862F8' : '#E2E3E5',
              }"
            >
              <text
                :class="['layer-menu-icon', 'custom-icon', menu.icon]"
                :style="{
                  color: selected.indexOf(menu.layerid) !== -1 ? '#ffffff' : '#060F27',
                }"
              />
            </view>
            <text
              class="layer-menu-text"
              :style="{
                color: selected.indexOf(menu.layerid) !== -1 ? '#3862F8' : '#060F27',
              }"
            >
              {{ menu.layername }}
            </text>
          </view>
        </view>
      </view>

      <!-- 管线类 -->
      <view class="layer-box">
        <text class="layer-title">管线类</text>
        <view class="layer-menu"> </view>
      </view>

      <!-- DMA分区 -->
      <view class="layer-box">
        <text class="layer-title">DMA分区</text>
        <view class="layer-menu">
          <view class="menu-item" @click="$emit('toggleDma')">
            <view
              class="icon-bg"
              :style="{ 'background-color': dmaVisible ? '#3862F8' : '#E2E3E5' }"
            >
              <text
                :class="['layer-menu-icon', 'custom-icon', 'custom-icon-mianji']"
                :style="{ color: dmaVisible ? '#ffffff' : '#060F27' }"
              />
            </view>
            <text class="layer-menu-text" :style="{ color: dmaVisible ? '#3862F8' : '#060F27' }">
              DMA分区
            </text>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
export default {
  name: 'LayerSelector',
  props: {
    pointLayers: {
      type: Array,
      default: () => [],
    },
    selected: {
      type: Array,
      default: () => [],
    },
    dmaVisible: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['close', 'toggleLayer', 'toggleDma'],
};
</script>

<style scoped lang="scss">
.cover-view-menu.layer {
  height: 800rpx;
  overflow: hidden;
  width: 100%;
  padding: 0 32rpx;
  border-radius: 16rpx 16rpx 0rpx 0;
  position: absolute;
  bottom: 0;
  background-color: #fbfbfb;

  .cover-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 86rpx;

    .title {
      text-align: left;
      word-break: keep-all;
    }

    .icon {
      font-size: 1.2em;
      cursor: pointer;
    }
  }

  .menu-list {
    height: 700rpx;
    flex-direction: column;
    display: flex;
    justify-content: flex-start;
    width: 100%;

    .layer-box {
      width: 100%;
      padding: 0 0 25rpx;

      .layer-title {
        color: #91949f;
      }

      .layer-menu {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        flex-wrap: wrap;
        border-radius: 8px;
        background-color: #ffffff;
        margin-top: 20rpx;
        padding-bottom: 20rpx;

        .menu-item {
          width: 20%;
          align-items: center;
          padding: 25rpx 0 0;
          text-align: center;
          display: flex;
          justify-content: center;
          flex-direction: column;

          .icon-bg {
            border-radius: 50%;
            width: 64rpx;
            height: 64rpx;
            display: flex;
            align-items: center;
            justify-content: center;
          }

          .layer-menu-text {
            word-break: keep-all;
          }
        }
      }
    }
  }
}
</style>
