.map-main {
  height: calc(100vh - 100rpx);

  /* #ifdef APP-PLUS */
  height: calc(100vh - 88rpx);
  /* #endif */

  // 状态栏占位高度
  .status-bar {
    height: var(--status-bar-height);
  }

  // 主体内容
  .main-wrapper {
    position: relative;
    height: calc(100% - var(--status-bar-height));

    .map-view {
      height: 100%;
    }
  }
}

// ArcGIS 样式覆盖
:deep(.esri-view .esri-view-surface--inset-outline) {
  &:focus::after {
    display: none;
  }
}

// 通用样式
.flex-around {
  display: flex;
  justify-content: space-around;
}

.border-box {
  box-sizing: border-box;
}

// 动画
.pull-up {
  transition: all 0.5s ease-in-out;
}

// 响应式设计
@media (max-width: 750rpx) {
  .map-main {
    .main-wrapper {
      .cover-view-plus {
        right: 16rpx;
      }
      
      .cover-view-loca {
        right: 16rpx;
      }
    }
  }
}