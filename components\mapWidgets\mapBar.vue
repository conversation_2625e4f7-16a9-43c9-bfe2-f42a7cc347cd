<template>
	<view class="cover-view-plus">
		<block v-for="(menu,index) in verticalBar.menus.value" :key="index">
			<view class="plus-btn" @click="setCurrent(menu.value)">
				<text class="custom-icon btn-img" :class="menu.icon"
					:style="{'color':verticalBar.currentBar === menu.value?'#3862F8':'#060F27'}"></text>
				<text style="font-size: 24rpx;"
					:style="{'color':verticalBar.currentBar === menu.value?'#3862F8':'#060F27'}">{{menu.name}}</text>
			</view>
		</block>

	</view>
</template>

<script lang="ts" setup>
	import {
		useVerticalBar,
	} from '@/common/hooks'
	import { onMounted, watch } from 'vue';
	const emit = defineEmits(['update:modelValue','change'])
	const props = defineProps<{ bars : any[], modelValue : string }>()
	const verticalBar = useVerticalBar({
		current: '',
		defaultMenus: props.bars
	})
	const setCurrent = (current : string) => {
		verticalBar.setCurrent(current)
		emit('update:modelValue', current)
		emit('change',current)
	}
	watch(() => props.bars, () => {
		verticalBar.menus.value = props.bars
	})
</script>

<style lang="scss" scoped>
	.cover-view-plus {
		width: 80rpx;
		background-color: #ffffff;
		border-radius: 20rpx;
		position: absolute;
		display: flex;
		flex-wrap: wrap;
		align-items: center;
		justify-content: center;
		padding: 8rpx 16rpx;
	}

	.plus-btn {
		width: 48rpx;
		height: 96rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;

		.btn-img {
			font-size: 48rpx;
		}
	}

	.btn-line {
		width: 80rpx;
		height: 1rpx;
		border-width: 1rpx;
		border-style: solid;
		border-color: #EBEDF6;
	}

	.btn-img {
		font-size: 48rpx;
	}
</style>