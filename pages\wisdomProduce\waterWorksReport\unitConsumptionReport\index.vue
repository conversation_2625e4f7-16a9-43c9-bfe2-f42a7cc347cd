<template>
	<view>
		<u-navbar fixed placeholder safeAreaInsetTop bgColor="#FFFFFF" title="吨水耗电" :autoBack="true" leftIconSize="20">
			<template #right>
				<view class="nv-right" @click="showScreen">
					<image src="/static/img/icons/shaixuan-black.png" style="height: 48rpx;width: 48rpx;"></image>
				</view>
			</template>
		</u-navbar>
		<view class="list">
			<view class="title">
				吨水耗电
			</view>
			<scroll-view scroll-x scroll-y>
				<!-- <wyb-table borderColor="#EBEDF6" first-col-bg-color="#FFFFFF" :showvertborder="true"
					header-bg-color="#F9F9F9" first-line-fixed ref="table" width="100%" :headers="headers"
					:contents="tableData" height="90vh"/> -->
				<u-table font-size="30" padding="14rpx">
					<u-tr class="u-tr">
						<u-th class="u-th" v-for="(item,index) in headers" :key="index" :width="item.width+'rpx'">
							{{item.label}}{{item.unit}}
						</u-th>
					</u-tr>
					<u-tr class="u-tr" v-for="(item,index) in tableData" :key="index">
						<u-td class="u-td" v-for="(key,index) in headers" :key="index" :width="key.width+'rpx'">
							{{item[key.key] || '0'}}
						</u-td>
					</u-tr>
				</u-table>
			</scroll-view>
		</view>
		<view class="popup">
			<u-popup mode="right" width="528" v-model="state.screenShow" closeOnClickOverlay overlay
				@close="state.screenShow = false">
				<view class="screen-list">
					<u-form :model="screenForm" ref="form1" :label-style="{'color':'#91949F'}" border-bottom
						input-align="right" label-width="180">
						<u-form-item label="查询类型：" prop="screenForm.queryTypeName">
							<input fontSize="14" inputmode="none" placeholder-class="placeholderClass"
								placeholder="点击选择" v-model="screenForm.queryTypeName" @click="state.typeShow=true">
							</input>
							<template #right>
								<u-icon name="arrow-down" color="#91949F" size="34"></u-icon>
							</template>
						</u-form-item>
						<u-form-item label="选择日期：" prop="screenForm.date">
							<input fontSize="14" inputmode="none" placeholder-class="placeholderClass"
								placeholder="点击选择" v-model="screenForm.time" @click="state.dateShow=true">
							</input>
							<template #right>
								<u-icon name="calendar" color="#91949F" size="34"></u-icon>
							</template>
						</u-form-item>
					</u-form>
				</view>
				<view class="popup-button">
					<u-button type="primary" color="#3862F8" @click="submitScreen">确定</u-button>
				</view>
			</u-popup>
		</view>
		<!-- <u-action-sheet safe-area-inset-bottom :list="typeList" @click="selectClick" v-model="state.typeShow">
		</u-action-sheet> -->
		<u-picker v-model="state.typeShow" mode="selector" :default-selector="[0]" :range="typeList" range-key="name"
			@confirm="selectClick"></u-picker>
		<u-calendar v-model="state.dateShow" ref="calendar" @close="state.dateShow=false" @change="chooseDate">
		</u-calendar>
	</view>
</template>

<script lang="ts" setup>
	import {
		onMounted,
		reactive,
		ref
	} from "vue"
	import dayjs from 'dayjs'
	import {
		getWaterPlantFlowReport
	} from '@/common/api/report'
	import {
		getStationList
	} from '@/common/api/waterplant'
	import {
		queryTypeList
	} from '@/common/data/publicData'
	const state = reactive<{
		screenShow : boolean,
		typeShow : boolean,
		stationList : string[],
		dateShow : boolean
	}>({
		screenShow: false,
		typeShow: false,
		stationList: [],
		dateShow: false
	})

	const screenForm = reactive<any>({
		queryType: 'day',
		queryTypeName: '日报表',
		time: dayjs().format('YYYY-MM-DD')
	})

	const typeList = reactive<any>(queryTypeList)

	const headers = ref<any>([])
	const tableData = ref<any>([])

	//选择报表类型
	const selectClick = (index : any) => {
		screenForm.queryType = typeList[index].value
		screenForm.queryTypeName = typeList[index].name
		screenForm.time = dayjs().format(typeList[index].format)
		state.typeShow = false
	}

	// 显示更多筛选条件
	const showScreen = () => {
		state.screenShow = true
	}
	// 选择日期
	const chooseDate = (date : any) => {
		console.log(date)
		state.dateShow = false
		if (screenForm.queryType === 'day') {
			screenForm.time = date.result
		} else if (screenForm.queryType === 'month') {
			screenForm.time = date.year + '-' + date.month
		} else {
			screenForm.time = date.year
		}

	}
	// 提交筛选
	const submitScreen = async () => {
		state.screenShow = false
		await waterSupplyDetailReport()
	}

	const waterSupplyDetailReport = async () => {
		const params = {
			...screenForm,
			stationIds: state.stationList.join(','),
		}
		const res = await getWaterPlantFlowReport(params)
		const result = res.data?.data
		headers.value = result.tableInfo.map((info : any) => {
			return {
				label: info.columnName,
				key: info.columnValue,
				unit: info.unit ? '(' + info.unit + ')' : '',
				width: '300'
			}
		})
		tableData.value = result.tableDataList
		console.log(res.data?.data)
	}

	onMounted(async () => {
		const systemConfig = uni.getStorageSync('systemConfig')
		const params = {
			page: 1,
			size: 999,
			type: systemConfig.type
		}
		const res = await getStationList(params)
		console.log(res.data?.data)
		state.stationList = res.data?.data.map((d : any) => {
			return d.id
		})
		await waterSupplyDetailReport()
	})
</script>

<style lang="scss" scoped>
	.list {
		margin-top: 20rpx;
		min-height: 90vh;
		padding: 22rpx 32rpx;
		background-color: #FFFFFF;

		.title {
			font-weight: 700;
			font-size: 30rpx;
			color: #060F27;
			padding-bottom: 20rpx;
		}
	}

	.popup {
		width: 100%;
		height: 100%;
		position: relative;

		.screen-list {
			padding: 222rpx 34rpx;
		}

		.popup-button {
			width: 94%;
			position: absolute;
			bottom: 40rpx;
			left: 0;
			right: 0;
			margin: 0 auto;
		}

		::v-deep .placeholderClass {
			font-size: 28rpx;
		}

		::v-deep .u-form-item {
			padding: 0;
		}
	}
</style>