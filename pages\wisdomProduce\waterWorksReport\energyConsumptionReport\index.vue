<template>
	<view>
		<u-navbar fixed placeholder safeAreaInsetTop bgColor="#FFFFFF" title="能耗报表" :autoBack="true" leftIconSize="20">
			<template #right>
				<view class="nv-right" @click="showScreen">
					<image src="/static/img/icons/shaixuan-black.png" style="height: 48rpx;width: 48rpx;"></image>
				</view>
			</template>
		</u-navbar>
		<view class="list">
			<view class="title-row">
				<view class="title">
					能耗报表
				</view>
				<view class="tab-view">
					<view :class="['tab-btn', {'active':activeTab==='list'}]" @click="activeTab = 'list'">表</view>
					<view :class="['tab-btn', {'active':activeTab==='chart'}]" @click="activeTab = 'chart'; nextTick(() => initChart())">图</view>
				</view>
			</view>
			<view v-if="activeTab === 'chart'" class="chart-view">
				<view class="station-select" @click="state.stationShow = true">
					<text>{{ selectedStationName }}</text>
					<u-icon name="arrow-down" color="#91949F" size="28"></u-icon>
				</view>
				<view class="chart-content">
					<l-echart ref="lineChart"></l-echart>
				</view>
			</view>
			<view v-if="activeTab === 'list'">
				<scroll-view scroll-x scroll-y>
					<!-- <wyb-table borderColor="#EBEDF6" first-col-bg-color="#FFFFFF" :showvertborder="true"
						header-bg-color="#F9F9F9" first-line-fixed ref="table" width="100%" :headers="headers"
						:contents="tableData" height="90vh"/> -->
					<u-table font-size="30" padding="14rpx">
						<u-tr class="u-tr">
							<u-th class="u-th" v-for="(item,index) in headers" :key="index" :width="item.width+'rpx'">
								{{item.label}}{{item.unit}}
							</u-th>
						</u-tr>
						<u-tr class="u-tr" v-for="(item,index) in tableData" :key="index">
							<u-td class="u-td" v-for="(key,index) in headers" :key="index" :width="key.width+'rpx'">
								{{item[key.key] || '0'}}
							</u-td>
						</u-tr>
					</u-table>
				</scroll-view>
			</view>
		</view>
		<view class="popup">
			<u-popup mode="right" width="528" v-model="state.screenShow" closeOnClickOverlay overlay
				@close="state.screenShow = false">
				<view class="screen-list">
					<u-form :model="screenForm" ref="form1" :label-style="{'color':'#91949F'}" border-bottom
						input-align="right" label-width="180">
						<u-form-item label="查询类型：" prop="screenForm.queryTypeName">
							<input fontSize="14" inputmode="none" placeholder-class="placeholderClass"
								placeholder="点击选择" v-model="screenForm.queryTypeName" @click="state.typeShow=true">
							</input>
							<template #right>
								<u-icon name="arrow-down" color="#91949F" size="34"></u-icon>
							</template>
						</u-form-item>
						<u-form-item label="选择日期：" prop="screenForm.date">
							<input fontSize="14" inputmode="none" placeholder-class="placeholderClass"
								placeholder="点击选择" v-model="screenForm.time" @click="state.dateShow=true">
							</input>
							<template #right>
								<u-icon name="calendar" color="#91949F" size="34"></u-icon>
							</template>
						</u-form-item>
					</u-form>
				</view>
				<view class="popup-button">
					<u-button type="primary" color="#3862F8" @click="submitScreen">确定</u-button>
				</view>
			</u-popup>
		</view>
		<!-- <u-action-sheet safe-area-inset-bottom :list="typeList" @click="selectClick" v-model="state.typeShow">
		</u-action-sheet> -->
		<u-picker v-model="state.typeShow" mode="selector" :default-selector="[0]" :range="typeList" range-key="name"
			@confirm="selectClick"></u-picker>
		<u-calendar v-model="state.dateShow" ref="calendar" @close="state.dateShow=false" @change="chooseDate">
		</u-calendar>
		<u-picker v-model="state.stationShow" mode="selector" :default-selector="[0]" :range="stationOptions" range-key="name"
			@confirm="selectStation"></u-picker>
	</view>
</template>

<script lang="ts" setup>
	import { onMounted, reactive, ref, nextTick } from "vue"
	import dayjs from 'dayjs'
	import { getWaterPlantFlowReport } from '@/common/api/report'
	import { getStationList } from '@/common/api/waterplant'
	import { queryTypeList } from '@/common/data/publicData'
	import lEchart from '@/uni_modules/lime-echart/components/l-echart/l-echart.vue'
	import * as echarts from 'echarts'
	import {lineOption} from './echartConf'

	const activeTab = ref<'list' | 'chart'>('list')
	const lineChart = ref<any>({})
	const state = reactive<{
		screenShow : boolean,
		typeShow : boolean,
		stationList : string[],
		dateShow : boolean,
		stationShow: boolean // 控制水厂选择器显示隐藏
	}>({
		screenShow: false,
		typeShow: false,
		stationList: [],
		dateShow: false,
		stationShow: false
	})

	const screenForm = reactive<any>({
		queryType: 'day',
		queryTypeName: '日报表',
		time: dayjs().format('YYYY-MM-DD')
	})

	const typeList = reactive<any>(queryTypeList)

	const headers = ref<any>([])
	const tableData = ref<any>([])

	// 原始数据，用于图表筛选
	const rawTableData = ref<any[]>([])
	const rawTableInfo = ref<any[]>([])

	// 新增：图表显示的水厂ID和名称
	const selectedStationId = ref<string>('')
	const selectedStationName = ref<string>('请选择水厂')
	const stationOptions = ref<any[]>([]) // 水厂选择列表

	// 图表数据
	const chartData = reactive<{
		categories: string[],
		series: { name: string, data: number[] }[]
	}>({
		categories: [],
		series: []
	})

	// 图表配置
	const opts = reactive({
		color: ["#1890FF", "#91CB74", "#FAC858", "#EE6666", "#73C0DE", "#3CA272", "#FC8452", "#9A60B4", "#ea7ccc"],
		padding: [15, 10, 0, 15],
		dataLabel: false,
		enableScroll: false,
		legend: {},
		xAxis: {
			disableGrid: true
		},
		yAxis: {
			data: [{
				min: 0
			}]
		},
		extra: {
			line: {
				type: "curve",
				width: 2,
				activeType: "hollow"
			}
		}
	})

	// 初始化图表
	const initChart = () => {
		lineChart.value.init(echarts, (chart: any) => {
			const options = lineOption(chartData.categories, chartData.series)
			console.log('+++++++++++',options)
			chart.setOption(options);
		});
	}

	//选择报表类型
	const selectClick = (index : any) => {
		screenForm.queryType = typeList[index].value
		screenForm.queryTypeName = typeList[index].name
		screenForm.time = dayjs().format(typeList[index].format)
		state.typeShow = false
	}

	// 显示更多筛选条件
	const showScreen = () => {
		state.screenShow = true
	}
	// 选择日期
	const chooseDate = (date : any) => {
		console.log(date)
		state.dateShow = false
		if (screenForm.queryType === 'day') {
			screenForm.time = date.result
		} else if (screenForm.queryType === 'month') {
			screenForm.time = date.year + '-' + date.month
		} else {
			screenForm.time = date.year
		}

	}

	// 选择水厂
	const selectStation = (index: any) => {
		selectedStationId.value = stationOptions.value[index].id
		selectedStationName.value = stationOptions.value[index].name
		state.stationShow = false
		updateChartData()
	}

	// 提交筛选
	const submitScreen = async () => {
		state.screenShow = false
		await waterSupplyDetailReport()
	}

	const waterSupplyDetailReport = async () => {
		const params = {
			...screenForm,
			stationIds: state.stationList.join(','),
		}
		const res = await getWaterPlantFlowReport(params)
		const result = res.data?.data
		headers.value = result.tableInfo.map((info : any) => {
			return {
				label: info.columnName,
				key: info.columnValue,
				unit: info.unit ? '(' + info.unit + ')' : '',
				width: '300'
			}
		})
		tableData.value = result.tableDataList

		// 存储原始数据，用于图表筛选
		rawTableData.value = result.tableDataList
		rawTableInfo.value = result.tableInfo

		updateChartData()

		console.log(res.data?.data)
	}

	// 更新图表数据
	const updateChartData = () => {
		if (!rawTableData.value.length || !selectedStationId.value) {
			chartData.categories = []
			chartData.series = []
			return
		}

		const stationId = selectedStationId.value
		const timeKey = rawTableInfo.value[0].columnValue

		chartData.categories = rawTableData.value.filter((item: any) => !['最大值','最大值时间','最小值','最小值时间','平均值','合计'].includes(item[timeKey])).map((item: any) => item[timeKey])
		chartData.series = []

		const inletKey = `${stationId}--inlet`
		const outletKey = `${stationId}--outlet`

		const inletData = rawTableData.value.map((item: any) => parseFloat(item[inletKey] || 0))
		const outletData = rawTableData.value.map((item: any) => parseFloat(item[outletKey] || 0))

		// 计算平均值
		const averageData = inletData.map((val: number, index: number) => (val + outletData[index]) / 2)

		chartData.series.push({
			name: '进厂累计流量',
			data: inletData
		})
		chartData.series.push({
			name: '出厂累计流量',
			data: outletData
		})
		chartData.series.push({
			name: '平均值',
			data: averageData
		})
		if (activeTab.value === 'chart') {
			nextTick(() => initChart())
		}
	}

	onMounted(async () => {
		const systemConfig = (globalThis as any).uni.getStorageSync('systemConfig')
		const params = {
			page: 1,
			size: 999,
			type: systemConfig.type
		}
		const res = await getStationList(params)
		console.log(res.data?.data)
		state.stationList = res.data?.data.map((d : any) => {
			return d.id
		})
		stationOptions.value = res.data?.data.map((d: any) => ({
			id: d.id,
			name: d.name
		}))
		if (stationOptions.value.length > 0) {
			selectedStationId.value = stationOptions.value[0].id
			selectedStationName.value = stationOptions.value[0].name
		}
		await waterSupplyDetailReport()
	})
</script>

<style lang="scss" scoped>
	.list {
		margin-top: 20rpx;
		min-height: 90vh;
		padding: 22rpx 32rpx;
		background-color: #FFFFFF;

		.title-row {
			display: flex;
			align-items: center;
			justify-content: space-between;

			.title {
				font-weight: 700;
				font-size: 30rpx;
				color: #060F27;
				padding-bottom: 20rpx;
			}

			.tab-view {
				display: flex;
				align-items: center;
				border: 1px solid #91949F;

				.tab-btn {
					width: 3em;
					text-align: center;
				}
				.active {
					color: #fff;
					background-color: #3862f8;
				}
			}
		}

		.chart-view {
			.station-select {
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 20rpx 0;
				font-size: 28rpx;
				color: #333;
				border-bottom: 1px solid #eee;
			}
			.chart-content {
				width: 100%;
				height: 35vh;
			}
		}

	}

	.popup {
		width: 100%;
		height: 100%;
		position: relative;

		.screen-list {
			padding: 222rpx 34rpx;
		}

		.popup-button {
			width: 94%;
			position: absolute;
			bottom: 40rpx;
			left: 0;
			right: 0;
			margin: 0 auto;
		}

		::v_deep .placeholderClass {
			font-size: 28rpx;
		}

		::v_deep .u-form-item {
			padding: 0;
		}
	}
</style>