<template>
	<view class="data-mian">
		<view class="title">
			{{props.title || '查看详情'}}
		</view>
		<view class="date">
			<u-form label-position="left" :model="form" ref="uForm" :label-style="{'color':'#91949F'}" border-bottom input-align="right" label-width="180">
				<u-form-item label="开始日期：">
					<input inpu placeholder="选择开始日期" inputmode="none" placeholder-class="placeholderClass" v-model="form.start" @click="chooseDate('start')"></input>
					<template #right>
						<u-icon name="calendar" color="#91949F" size="34"></u-icon>
					</template>
				</u-form-item>
				<u-form-item label="结束日期：">
					<input placeholder="选择结束日期" placeholder-class="placeholderClass" inputmode="none" v-model="form.end" @click="chooseDate('end')"></input>
					<template #right>
						<u-icon name="calendar" color="#91949F" size="34"></u-icon>
					</template>
				</u-form-item>
				<u-form-item label="时间间隔：">
					<input placeholder="选择时间间隔" inputmode="none" placeholder-class="placeholderClass" v-model="form.typeName" @click="typeShow=true"><input>
				</u-form-item>
			</u-form>
			<view style="margin-top: 10rpx;">
				<u-button type="primary" color="#3862F8" @click="submit">查询</u-button>
			</view>
		</view>
		<view class="button-tabs flex-center">
			<view :class="['b-tab',{'b-check-tab':state.activeName==='list'}]" @click="state.activeName='list'">
				列表模式
			</view>
			<view :class="['b-tab',{'b-check-tab':state.activeName==='echart'}]" @click="showLineEcharts">
				图表模式
			</view>
		</view>
		<view class="d-content">
			<view class="" v-if="state.activeName==='list'">
				<view class="title-bold" style="padding-left:32rpx;">
					详情列表
				</view>
				<view class="list">
					<scroll-view style="height:45vh" scroll-y>
						<!-- <wyb-table first-col-bg-color="#FFFFFF" :show-vert-border="false" header-bg-color="#FFFFFF"
							first-line-fixed ref="table" height="45vh" width="100%" :headers="headers"
							:contents="tableData" /> -->
						<u-table font-size="30" padding="14rpx">
							<u-tr class="u-tr">
								<u-th class="u-th" v-for="(item,index) in headers" :key="index"
									:width="item.width+'rpx'">
									{{item.label}}
								</u-th>
							</u-tr>
							<u-tr class="u-tr" v-for="(item,index) in tableData" :key="index">
								<u-td class="u-td" v-for="(key,index) in headers" :key="index" :width="key.width+'rpx'">
									{{key.key==='date'?item[key.key]:item[key.key]?parseFloat(item[key.key]).toFixed(2): '0'}}
								</u-td>
							</u-tr>
						</u-table>
					</scroll-view>
				</view>
			</view>

			<view class="ucharts" v-if="state.activeName==='echart'">
				<!-- 通用折现图表 -->
				<view class="detail">
					<view class="flex-between" style="margin-bottom: 20rpx;">
						<view class="title-bold flex-center label">
							{{currentProperty.propertyName || props.currentProperty.label}}
							<view v-if="propertyList.length>1" @click="showMrore">
								<u-icon name="arrow-down" color="#060F27" bold size="12"></u-icon>
							</view>
						</view>
						<view class="tabs flex-between">
							<view v-for="(tab,i) in chartsConfig.tabs" :key="i"
								:class="['tab',{'check-tab':activeTab.value===tab.value}]" @click="checkActiveTab(tab)">
								{{tab.title}}
							</view>
						</view>
					</view>
					<view class="bottom">
						<view class="y-title flex-between">
							<view>
								{{props.currentProperty.unit?' 单位:'+props.currentProperty.unit:''}}
							</view>
							<view class="flex-center">
								<view class="flex-center" style="padding: 0 8rpx;" v-for="(tab,i) in state.tabList"
									:key="i">
									<view class="color-view" :style="{'background-color':tab.color}"></view>
									<view style="color:#B2B7C7;">{{tab.title}}</view>
								</view>
							</view>
						</view>
						<view class="line-ucharts">
							<l-echart ref="lineChart"></l-echart>
						</view>
					</view>
				</view>
			</view>
		</view>
		<!-- 时间选择器 -->
		<!-- <u-datetime-picker v-model="today" @cancel="show=false" :show="show" @close="show=false" mode="date"
			@confirm="confirmDate">
		</u-datetime-picker> -->
		<u-calendar v-model="show" ref="calendar" @change="confirmDate">
		</u-calendar>
		<!-- 查询类型选择器 -->
		<u-picker mode="selector" range-key="label" v-model="typeShow" @cancel="typeShow=false" :range="queryTypeList"
			@confirm="confirmType"></u-picker>
		<!-- 站点属性选择器 -->
		<u-picker mode="selector" v-model="pShow" @cancel="pShow=false" :range="propertyList" range-key="propertyName"
			@confirm="confirmProperty"></u-picker>
	</view>
</template>

<script lang="ts" setup>
	import lEchart from '@/uni_modules/lime-echart/components/l-echart/l-echart.vue'
	import dayjs from 'dayjs'
	import * as echarts from 'echarts'
	import {
		nextTick,
		onMounted,
		reactive,
		ref
	} from "vue";
	import {
		lineOption
	} from './echartData'
	import {
		getDeviceData
	} from '@/common/api/monitoring'
	import {
		removeSlash
	} from '@/common/utils/removeIdSlash'
	const emit = defineEmits(['onQuery']);
	const props = defineProps({
		headers: Array,
		title: String,
		tableData: Array,
		currentProperty: Object,
		propertyList: Array
	})
	const color = reactive(['#3862F8', '#ff0000', '#00ff26'])
	const lineChart = ref < any > ({})
	const today = ref(dayjs().format('YYYY-MM-DD'))
	const state = reactive({
		activeName: 'list',
		tabList: [],
		showDateType: ''
	})
	const show = ref < boolean > (false)
	const pShow = ref < boolean > (false)
	const typeShow = ref < boolean > (false)
	const currentProperty = ref < any > ()

	const chartsConfig = reactive < any > ({
		tabs: [{
			title: '全部',
			color: '#FBFBFB',
			value: 'all'
		}]
	})

	let propertyList = ref < any > ([])
	let tableData = ref < any > ([])
	let headers = ref < any > ([])
	let activeTab = ref < any > (chartsConfig.tabs[0])

	const checkActiveTab = async (tab: any) => {
		const attribute = removeSlash(currentProperty.value.deviceId) + '.' + currentProperty.value.property
		activeTab.value = tab
		let data = tableData.value
		let newOptions = {}
		let times = []
		data.map((d: any) => {
			const key = d.date.substring(0, 10)
			const time = d.date.substring(10, 99)
			times.push(time)
			if (!newOptions[key]) {
				newOptions[key] = []
				newOptions[key].push(d[attribute])
			} else {
				newOptions[key].push(d[attribute])
			}
		})
		const dataX = Array.from(new Set(times))
		if (tab.value === 'all') {
			initLine(dataX, newOptions)

			state.tabList = chartsConfig.tabs.slice(1, 4)
		} else {
			let data = {}
			state.tabList = [activeTab.value]
			data[activeTab.value.value] = newOptions[activeTab.value.value]
			initLine(dataX, data, activeTab.value.color)
		}
	}

	const form = reactive < any > ({
		start: today.value,
		end: today.value,
		typeName: '15分钟',
		type: '15m'
	})

	// 显示详情数据
	const showHistoryData = async (params ? : any) => {
		const attribute = removeSlash(params.deviceId) + '.' + params.property
		params = params || {}
		params = {
			start: dayjs(form.start).startOf('day').valueOf(),
			end: dayjs(form.end).endOf('day').valueOf(),
			type: form.type || '15m',
			attributes: [attribute],
		}
		const res = await getDeviceData(params)
		const data = res.data
		let newData = []
		for (let key in data) {
			newData.push({
				'date': key,
				'val': data[key][attribute]
			})
		}
		return newData
	}


	// 显示详情数据
	const showHistoryArrData = async (params ? : any) => {
		let query = params || {}
		// const attributes = propertyList.value.map(property => {
		// 	return removeSlash(property.deviceId) + '.' + property.property
		// })
		const attributes = [removeSlash(params.deviceId) + '.' + params.property]
		query = {
			start: dayjs(form.start).startOf('day').valueOf(),
			end: dayjs(form.end).endOf('day').valueOf(),
			type: form.type || '15m',
			attributes: attributes,
		}
		getDeviceData(query).then(res => {
			const data = res.data
			let newData = []
			console.log(data)
			headers.value = [{
				label: '读取时间',
				key: 'date',
				width: '300'
			}]
			console.log('headers.valu11e', headers.value)
			headers.value.push({
				label: params.propertyName + '（' + (params.unit || '') + '）',
				key: removeSlash(params.deviceId) + '.' + params.property,
				width: '400'
			})
			// .concat(propertyList.value.map((p: any, index: number) => {
			// 	return {
			// 		label: p.propertyName + '（' + (p.unit || '') + '）',
			// 		key: removeSlash(p.deviceId) + '.' + p.property,
			// 		width: index === 0 ? '400' : '300'
			// 	}
			// }))
			console.log('headers.value', headers.value)
			for (let key in data) {
				newData.push({
					'date': key,
					...data[key]
				})
			}
			uni.showLoading({
				"title": "加载中",
				"mask": true
			})
			console.log('newData', newData)
			const rData = newData.reverse() as any[]
			tableData.value = rData
			uni.hideLoading()
		})
	}
	//切换图表
	const showLineEcharts = () => {
		if (dayjs(form.end).diff(form.start, 'day') >= 3) {
			uni.$u.toast('图表只支持三天内的数据')
			return
		} else {
			state.activeName = 'echart'
			nextTick(() => {
				checkActiveTab(chartsConfig.tabs[0])
			})
		}
	}

	// 折线图
	const initLine = (dataX: string[], data: any, color ? : string) => {
		lineChart.value.init(echarts, (chart: any) => {
			const options = lineOption(dataX, data, color)
			chart.setOption(options);
		});
	}

	let queryTypeList = ref < any > (
		[{
			label: '1分钟',
			value: '1m'
		}, {
			label: '15分钟',
			value: '15m'
		}, {
			label: '30分钟',
			value: '30m'
		}]
	)
	// 提交
	const confirmDate = (date: any) => {
		show.value = false
		if (state.showDateType === 'start') {
			form.start = date.result
		} else {
			form.end = date.result
		}
	}
	// confirmProperty TODO
	const confirmProperty = async (data: any) => {
		currentProperty.value = propertyList.value[data[0]]
		const resData = await showHistoryArrData(propertyList.value[data[0]])
		pShow.value = false
	}

	//confirmType
	const confirmType = (data: any) => {
		form.typeName = queryTypeList.value[data[0]].label
		form.type = queryTypeList.value[data[0]].value
		typeShow.value = false
	}

	const submit = async () => {
		state.activeName = 'list'
		const day = dayjs(form.end).diff(form.start, 'day')
		chartsConfig.tabs = [{
			title: '全部',
			color: '#FBFBFB',
			value: 'all'
		}]
		for (let i = 0; i <= day; i++) {
			const day = dayjs(form.start).add(i, 'day').format('YYYY-MM-DD')
			chartsConfig.tabs.push({
				title: day.substring(5, 100),
				color: color[i],
				value: day,
			})
		}
		// emit('onQuery', form);
		// if (props.propertyList && props.propertyList.length > 0) {
		// 	tableData.value = await showHistoryArrData(props.currentProperty)
		// } else {
		// 	// tableData.value = props.tableData
		// 	tableData.value = await showHistoryData(props.currentProperty)
		// }
		await showHistoryArrData(props.currentProperty)
	}
	// 选择更多属性
	const showMrore = () => {
		if (props.propertyList.length > 0) {
			pShow.value = true
		}
	}
	const chooseDate = (type: string) => {
		show.value = true
		state.showDateType = type
	}
	onMounted(async () => {
		currentProperty.value = props.currentProperty
		headers.value = props.headers
		chartsConfig.tabs.push({
			title: today.value.substring(5, 100),
			color: color[0],
			value: today,
		})
		// if (props.propertyList && props.propertyList.length > 0) {
		// 	propertyList.value = props.propertyList
		// 	tableData.value = await showHistoryArrData(props.currentProperty)
		// } else {
		// 	tableData.value = await showHistoryData(props.currentProperty)
		// }
		propertyList.value = props.propertyList || [{}]
		await showHistoryArrData(props.currentProperty)
	})
</script>

<style lang="scss" scoped>
	.data-mian {
		.title {
			width: 100%;
			text-align: center;
			line-height: 88rpx;
			font-size: 34rpx;
			color: #060F27;
		}

		.date {
			box-sizing: border-box;
			width: 686rpx;
			background: #F4F7FE;
			border-radius: 16rpx;
			margin: 0 auto;
			padding: 42rpx 32rpx;

			::v-deep .u-form-item__body {
				padding: 0;
			}
		}
	}

	.button-tabs {
		margin: 0 auto;
		width: 686rpx;

		.b-tab {
			margin: 24rpx auto;
			width: 342rpx;
			height: 64rpx;
			/* color-main */
			background: #F9F9F9;
			border-radius: 16rpx;
			line-height: 64rpx;
			text-align: center;
			color: #91949F;
		}

		.b-check-tab {
			background: #3862F8;
			color: #FFFFFF;
		}
	}

	.d-content {
		height: 788rpx;
		background: #FBFBFB;
		margin: 0 auto;
		border-radius: 32rpx 32rpx 0px 0px;
		padding: 24rpx 32rpx;

		.list {
			padding: 20rpx;
		}

		.ucharts {
			height: 40vh;
			width: 100%;

			.detail {
				height: 788rpx;
				background: #FBFBFB;
				margin: 0 auto;
				border-radius: 32rpx 32rpx 0px 0px;
				padding: 0 32rpx;

				.tabs {
					height: 46rpx;
					line-height: 46rpx;
					text-align: center;

					.tab {
						padding: 0 8rpx;
						color: #91949F;
					}

					.check-tab {
						border-radius: 8rpx;
						background-color: #3862F8;
						color: #FFFFFF;
					}
				}

				.bottom {
					background-color: #FFFFFF;
					border-radius: 16rpx;
					padding-bottom: 98rpx;

					.y-title {
						color: #B2B7C7;
						padding: 20rpx 0rpx 20rpx 40rpx;

						.color-view {
							width: 20rpx;
							height: 20rpx;
							background-color: #3862F8;
							margin-right: 8rpx;
						}
					}

					.line-ucharts {
						// overflow: hidden;
						height: 35vh;
						margin: 0rpx auto;
					}
				}
			}
		}
	}

	::v-deep.u-form-item {
		padding: 0;
	}
</style>
