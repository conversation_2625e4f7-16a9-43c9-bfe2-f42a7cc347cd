import { computed, ref } from 'vue'
export const useAddress = () => {
	const mapShow = ref<boolean>(true)
	const longitude = ref<number>(0)
	const latitude = ref<number>(0)
	const address = ref<string>('')
	
	const lonlat = computed(() => {
		if (longitude.value && latitude.value) return [longitude.value, latitude.value].join(',')
		else return undefined
	})
	const controls = ref<any[]>([{
		id: '1',
		iconPath: '../../../../static/img/icons/dw.png',
		position: { //控件在地图的位置
			left: 140,
			top: 30,
			width: 20,
			height: 20,
		},
	}])
	const regionchange = (event, callBack?: (info)=>any) => {
		if (event.type == 'onRegionchange') {
			// console.log('regionchange', event)
			// getCenterLanLat()
		} else if (event.type == 'end') {
			console.log('regionchange end');
			getCenterLanLat(callBack)
		}
	}
	// const locaton = (callBack?: (...args: any[]) => any) => {
	// 	uni.getLocation({
	// 		// type: 'gcj02',
	// 		type: 'wgs84',
	// 		altitude: true,
	// 		geocode: true,
	// 		isHighAccuracy: true,
	// 		success: (info) => {
	// 			getLocationAddress(info,callBack)
	// 			toLocation(info)
	// 		},
	// 		fail: (error) => {
	// 			console.log(error);
	// 			uni.showToast({
	// 				title: '获取定位失败',
	// 				icon: 'none'
	// 			})
	// 		}
	// 	})
	// }
	const toLocation = (obj:UniApp.GetLocationSuccess) => {
		const mapContext = uni.createMapContext("maps", this)
		 // 改变地图中心位置
		mapContext.moveToLocation(obj)
		// 移动标记点并添加动画效果
		mapContext.translateMarker({
		  markerId: 1,
		  autoRotate: true,
		  rotate: 0,
		  duration: 100,
		  destination: {
			latitude:obj.latitude,
			longitude:obj.longitude,
		  },
		  animationEnd() {
			console.log('animation end')
		  }
		})
	}
	const getLocationAddress = (res: UniApp.LocationObject,callBack?: (info)=>any) => {
		var point = new plus.maps.Point(res.longitude, res.latitude)
		plus.maps.Map.reverseGeocode(point, {}, (info: any) => {
			latitude.value = info.coord.latitude
			longitude.value = info.coord.longitude
			address.value = info.address
			callBack?.(info)
		})
	}
	// 获取当前地图中心的经纬度
	const getCenterLanLat = (callBack?: (info: any) => any) => {
		const mapContext = uni.createMapContext("maps", this)
		mapContext.getCenterLocation({
			success: (res) => {
				getLocationAddress(res, callBack)
			},
			fail: (err) => {
				console.log('获取当前地图中心的经纬度失败', err)
			}
		})
	}
	return {
		mapShow,
		longitude,
		latitude,
		regionchange,
		controls,
		// locaton,
		lonlat,
		address,
		getCenterLanLat
	}
}