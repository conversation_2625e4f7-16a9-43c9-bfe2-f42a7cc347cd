<template>
  <view
    style="
      background-color: #f9f9f9;
      border-radius: 16rpx;
      padding-bottom: 40rpx;
    "
  >
    <view class="content-card card-box">
      <view class="card-title flex-center">
        <text class="custom-icon custom-icon-xiangji"></text>
        <view class="label">图片</view>
      </view>
      <u-gap height="2" bg-color="#EBEDF6"></u-gap>
      <view v-if="!props.readonly" class="list">
        <u-upload
          :custom-btn="true"
          :header="header"
          width="112rpx"
          height="112rpx"
          @onRemove="deletePic"
          :source-type="['album', 'camera']"
          ref="refUpload"
          :show-upload-list="!!imageList?.length"
          :action="actionUrl"
          @onSuccess="onSuccess"
        >
          <template #addBtn>
            <view class="file-s">
              <view class="margin-center">
                <text class="custom-icon custom-icon-xiangji icon"></text>
              </view>
              <text>拍照上传</text>
            </view>
          </template>
        </u-upload>
      </view>
      <view v-else class="list images">
        <template v-if="imageList.length">
          <image
            class="image"
            v-for="(item, i) in imageList"
            :key="i"
            :src="item"
            mode="aspectFill"
            @click="previewImage(item.url)"
            @error="imageError($event, i)"
          >
          </image>
        </template>
        <template v-else>
          <view class="empty">
            <text>暂无图片信息</text>
          </view>
        </template>
      </view>
    </view>
    <view class="content-card card-box" v-if="!props.hideAudio">
      <view class="card-title flex-center">
        <text class="custom-icon custom-icon-htmal5icon13"></text>
        <view class="label">录音</view>
      </view>
      <u-gap height="2" bg-color="#EBEDF6"></u-gap>
      <view class="flex-center list">
        <view
          class="file-s"
          style="margin-right: 20rpx"
          v-if="voiceList.length > 0"
          v-for="(item, index) in voiceList"
          :key="index"
        >
          <view class="margin-center" @click="playVoice(item)">
            <image
              src="/static/sound-recording/play.png"
              style="height: 48rpx; width: 48rpx"
            ></image>
          </view>
          <view
            v-if="!props.readonly"
            class="close-icon"
            @click="removeVoice(index)"
          >
            <u-icon name="close" size="14" color="#FFFFFF"></u-icon>
          </view>
        </view>

        <template v-if="props.readonly && !voiceList.length">
          <view class="empty">
            <text>暂无录音信息</text>
          </view>
        </template>
        <view v-if="!props.readonly" class="file-s" @click="soundRecord">
          <view class="margin-center">
            <text class="custom-icon custom-icon-htmal5icon13 icon"></text>
          </view>
          <text>添加录音</text>
        </view>
      </view>
    </view>
    <view class="content-card card-box" v-if="!props.hideVideo">
      <view class="card-title flex-center">
        <text class="custom-icon custom-icon-shipinvideo"></text>
        <view class="label">视频</view>
      </view>
      <u-gap height="2" bg-color="#EBEDF6"></u-gap>
      <view class="flex-center list">
        <view
          class="file-s"
          style="margin-right: 20rpx"
          v-if="videoList.length"
          v-for="(item, index) in videoList"
          :key="index"
        >
          <view class="margin-center" @click="palyVideo(item)">
            <image
              src="/static/sound-recording/play.png"
              style="height: 48rpx; width: 48rpx"
            ></image>
          </view>
          <view
            v-if="!props.readonly"
            class="close-icon"
            @click="removeVideo(index)"
          >
            <u-icon name="close" size="14" color="#FFFFFF"></u-icon>
          </view>
        </view>

        <template v-if="props.readonly && !videoList.length">
          <view class="empty">
            <text>暂无视频信息</text>
          </view>
        </template>
        <view v-if="!props.readonly" class="file-s" @click="addVideo">
          <view class="margin-center">
            <text class="custom-icon custom-icon-shipinvideo icon"></text>
          </view>
          <text>添加视频</text>
        </view>
      </view>
    </view>
    <view class="content-card card-box">
      <view class="card-title flex-center">
        <text class="custom-icon custom-icon-wenjianjia"></text>
        <view class="label">文件</view>
      </view>
      <u-gap height="2" bg-color="#EBEDF6"></u-gap>
      <view class="flex-center list" style="margin-bottom: 40rpx">
        <view
          class="file-s"
          style="margin-right: 20rpx"
          v-if="fileList.length > 0"
          v-for="(item, index) in fileList"
          :key="index"
        >
          <view class="margin-center">
            <u-icon
              name="custom-icon-wenjianjia"
              customPrefix="custom-icon"
              size="48"
              color="#2979ff"
            >
            </u-icon>
          </view>
          <view
            v-if="!props.readonly"
            class="close-icon"
            @click="removeFile(index)"
          >
            <u-icon name="close" size="14" color="#FFFFFF"></u-icon>
          </view>
        </view>
        <template v-if="props.readonly && !fileList.length">
          <view class="empty">
            <text>暂无文件信息</text>
          </view>
        </template>
        <!-- <view v-if="!props.readonly" class="file-s" @click="addFile">
					<view class="margin-center">
						<text class="custom-icon custom-icon-wenjianjia icon"></text>
					</view>
					<text>添加文件</text>
				</view> -->
        <lsj-upload
          ref="lsjUpload0"
          childId="upload1"
          :size="50"
          :count="5"
          v-if="!props.readonly"
          formats=".txt,.png,.jpg,.mp4,.doc,.wps,.docx,.xls,.xlsx,.pdf"
          :debug="false"
          :instantly="true"
          :option="option"
          height="116rpx"
          width="116rpx"
          @uploadEnd="onuploadEnd"
        >
          <view class="file-s">
            <view class="margin-center">
              <text class="custom-icon custom-icon-wenjianjia icon"></text>
            </view>
            <text>添加文件</text>
          </view>
        </lsj-upload>
      </view>
    </view>
    <u-popup mode="bottom" v-model="showVoice" height="500rpx">
      <sound-recording
        :voiceP="voiceP"
        :finishP="finish"
        :maximum="60"
        @confirm="confirmVoice"
        @cancel="showVoice = false"
      ></sound-recording>
      <!-- <all-speech @startRecord="start" @endRecord="end" @cancelRecord="cancel"></all-speech> -->
    </u-popup>
    <video
      v-show="isOpenVideo"
      id="video"
      :src="videoUrl"
      @fullscreenchange="screenChange"
    ></video>
    <video class="" ref="refVideo" v-show="false"></video>
    <canvas class="" ref="refCanvas" v-show="false"></canvas>
  </view>
</template>

<script setup lang="ts">
import { ref, getCurrentInstance, reactive, onBeforeMount, watch } from "vue";
import { chooseFile, chooseFile2 } from "./fileMethod";
import soundRecording from "@/components/sound-recording/sound-recording.vue";
import permision from "@/common/js_sdk/wa-permission/permission";
const props = defineProps<{
  readonly?: boolean;
  hideAudio?: boolean;
  hideVideo?: boolean;
  imgs?: string[];
  files?: string[];
  audios?: string[];
  videos?: string[];
}>();
const showVoice = ref<boolean>(false);
const finish = ref<boolean>(false);
const isOpenVideo = ref<boolean>(false);
const voiceP = ref<string>("");
const voiceList = ref<any[]>(props.audios || []);
const videoList = ref<any[]>(props.videos || []);
const videoImage = ref<any[]>([]);
const fileList = ref<any[]>(props.files || []);
const imageList = ref<any[]>(props.imgs || []);
const refVideo = ref<any>();
const refCanvas = ref<any>();
const permisionState = ref<any>();
const videoContext = ref<any>();
const refUpload = ref<Record<string, any>>({});
const videoUrl = ref<any>();
const actionUrl = ref<string>("");
const header = reactive<Record<string, any>>({
  "X-Authorization": "Bearer " + uni.getStorageSync("token"),
});
//录音
const soundRecord = async () => {
  // #ifdef APP-PLUS
  // 先判断os
  let os = uni.getSystemInfoSync().osName;
  if (os == "ios") {
    permisionState.value = await permision.judgeIosPermission("record");
  } else {
    permisionState.value = await permision.requestAndroidPermission(
      "android.permission.RECORD_AUDIO"
    );
  }
  if (permisionState.value !== true && permisionState.value !== 1) {
    uni.showToast({
      title: "请先授权使用录音",
      icon: "none",
    });
  }
  console.log("授权", permisionState.value);
  // #endif
  voiceP.value = "";
  finish.value = false;
  showVoice.value = true;
};
const option = ref<any>({
  url: uni.getStorageSync("url") + "file/api/upload/file",
  header: {
    "X-Authorization": "Bearer " + uni.getStorageSync("token"),
  },
  // 上传附件的key
  name: "file",
});
const deletePic = (event) => {
  imageList.value.splice(event.index, 1);
};
const onuploadEnd = (item) => {
  console.log("上传结束", item);

  fileList.value.push(item.responseText);
  console.log(fileList.value);
};
const start = () => {
  // 开始录音
};
const end = (event) => {
  // 结束录音并处理得到的录音文件
  // event中，app端仅有tempFilePath字段，微信小程序还有duration和fileSize两个字段
};
const cancel = () => {
  // 用户取消录音
};

//提交录音
const confirmVoice = (file: any) => {
  console.log(file);
  uni.uploadFile({
    url: actionUrl.value, //仅为示例，非真实的接口地址
    filePath: file,
    header: {
      "X-Authorization": "Bearer " + uni.getStorageSync("token"),
    },
    name: "file",
    success: (uploadFileRes) => {
      voiceList.value.push(uploadFileRes.data);
      showVoice.value = false;
      console.log(voiceList.value);
    },
    fail: (error) => {
      console.log(error);
    },
  });
};

const addVideo = () => {
  // carmera.value.start({
  // 	success: (a) => {
  // 		console.log("livePusher.start:" + JSON.stringify(a));
  // 	}
  // });
  videoRecording();
};
const palyVideo = (url: string) => {
  videoUrl.value = url;
  //打开视频（全屏播放）
  isOpenVideo.value = true;
  videoContext.value = uni.createVideoContext("video", this);
  videoContext.value.play();
  videoContext.value.requestFullScreen();
};

const addFile = () => {
  chooseFile((url: string) => {
    console.log("url", url);
    uni.uploadFile({
      url: actionUrl.value, //仅为示例，非真实的接口地址
      filePath: url,
      header: {
        "X-Authorization": "Bearer " + uni.getStorageSync("token"),
      },
      name: "file",
      success: (uploadFileRes) => {
        // const data = JSON.parse(uploadFileRes.data)
        fileList.value.push(uploadFileRes.data);
        console.log(fileList.value);
      },
      fail: (ee) => {
        console.log(ee);
      },
      complete: (ee) => {
        console.log(ee);
      },
    });
  });

  // console.log(showFile.value)
  // showFile.value = true

  // let REQUESTCODE = 1;
  // let main = plus.android.runtimeMainActivity() as any
  // const Intent = plus.android.importClass('android.content.Intent') as any
  // let intent = new Intent(Intent.ACTION_GET_CONTENT);

  // intent.setType("*/*"); //设置类型，任意类型
  // //intent.setType("image/*");
  // //intent.setType("audio/*"); //选择音频
  // //intent.setType("video/*"); //选择视频 （mp4 3gp 是android支持的视频格式）

  // intent.addCategory(Intent.CATEGORY_OPENABLE);
  // main.startActivityForResult(intent, REQUESTCODE)

  // main.onActivityResult = function(requestCode, resultCode, data) {
  // 	if (REQUESTCODE == requestCode) {
  // 		let context = main;
  // 		plus.android.importClass(data);
  // 		// 获得文件路径
  // 		let fileData = data.getData();
  // 		let path = plus.android.invoke(fileData, "getPath");
  // 		console.log("path:", path);
  // 		console.log("fileData:", fileData);
  // 		uni.uploadFile({
  // 			url: actionUrl.value, //仅为示例，非真实的接口地址
  // 			filePath: path,
  // 			header: {
  // 				'X-Authorization': 'Bearer ' + uni.getStorageSync('token')
  // 			},
  // 			name: 'file',
  // 			success: (uploadFileRes) => {
  // 				console.log(uploadFileRes.data);
  // 			},
  // 			fail: (ee) => {
  // 				console.log(ee);
  // 			},
  // 			complete: (ee) => {
  // 				console.log(ee);
  // 			}
  // 		});
  // 		// 判断文件类型
  // 		let resolver = context.getContentResolver();
  // 		let fileType = plus.android.invoke(resolver, "getType", fileData);
  // 	}
  // }
};
const removeFile = (index: number) => {
  uni.showModal({
    title: "提示",
    content: "确定删除吗",
    success: (res) => {
      if (res.confirm) {
        fileList.value = fileList.value.splice(1, index);
      }
    },
  });
};
const removeVideo = (index: number) => {
  uni.showModal({
    title: "提示",
    content: "确定删除吗",
    success: (res) => {
      if (res.confirm) {
        videoList.value = videoList.value.splice(1, index);
      }
    },
  });
};
const removeVoice = (index: number) => {
  uni.showModal({
    title: "提示",
    content: "确定删除吗",
    success: (res) => {
      if (res.confirm) {
        voiceList.value = voiceList.value.splice(index, 1);
      }
    },
  });
};
const videoRecordingPath = ref<any>();
const videoRecording = () => {
  // uni.showLoading({
  // 	title: '正在加载...'
  // })
  uni.chooseVideo({
    sourceType: ["camera", "album"],
    success: (blod) => {
      // 获取视频信息，拿到宽高信息
      uni.getVideoInfo({
        src: blod.tempFilePath,
        success: (info: any) => {
          // 上传视频到网络地址，当然也可以使用本地地址。App、H5平台本人都测试过，都没问题！！！
          uni.uploadFile({
            url: actionUrl.value, //仅为示例，非真实的接口地址
            header: {
              "X-Authorization": "Bearer " + uni.getStorageSync("token"),
            },
            filePath: blod.tempFilePath,
            name: "file",
            success: (src) => {
              // getVideoPic(src.data).then(img =>{
              // 	console.log('img',img)
              // })
              videoList.value.push(src.data);
              console.log(videoList.value);
              uni.hideLoading();
            },
            fail: (ee) => {
              console.log(ee);
              uni.hideLoading();
            },
            complete: (ee) => {
              console.log(ee);
              uni.hideLoading();
            },
          });
        },
      });
    },
  });
  // uni.chooseVideo({
  // 	count: 1,
  // 	sourceType: ['album', 'camera'],
  // 	maxDuration: 30,
  // 	mediaType: ['video'],
  // 	compressed: false,
  // 	success: res => {
  // 		console.log('chooseVideo.size1', res.size / 1048576)
  // 		//视频在60-70-80M之间，压缩后约为6M
  // 		saveFile(res.tempFilePath)
  // 		// uni.compressVideo({
  // 		// 	src: res.tempFilePath,
  // 		// 	quality: 'high', //视频质量高低，基本上高质量时60m视频压缩后为6做左右，中质量60m压缩为3m左右，低质量60m压缩为2m左右（看不清人了）
  // 		// 	bitrate: 2000,
  // 		// 	fps: 30,
  // 		// 	resolution: 1,
  // 		// 	success: (result) => {
  // 		// 		videoRecordingPath.value = result.tempFilePath;
  // 		// 		console.log('compressVideo.size2', result.size / 1048576)
  // 		// 		saveFile(result.tempFilePath);
  // 		// 	},
  // 		// 	fail: () => {
  // 		// 		uni.hideLoading();
  // 		// 		uni.showToast({
  // 		// 			title: '相机调用失败！',
  // 		// 			icon: 'none',
  // 		// 			duration: 1500
  // 		// 		});
  // 		// 	}
  // 		// })
  // 	},
  // 	fail: (err) => {
  // 		console.log('dddd', err)
  // 		uni.showToast({
  // 			title: '视频录制失败！',
  // 			duration: 2000
  // 		});
  // 	}
  // })
};

const getVideoPic = async (url: string) => {
  console.log("dddddd", url);
  return new Promise(async (resolve, reject) => {
    let dataURL = "";
    let video = refVideo.value;
    console.log("dddddd", video);
    refVideo.value.setAttribute("crossOrigin", "anonymous"); //处理跨域
    refVideo.value.setAttribute("src", url);
    refVideo.value.setAttribute("width", 58);
    refVideo.value.setAttribute("height", 58);
    refVideo.value.setAttribute("preload", "auto");
    refVideo.value.addEventListener("loadeddata", function () {
      let width = refVideo.value.width, //canvas的尺寸和图片一样
        height = refVideo.value.height;
      refCanvas.value.width = width;
      refCanvas.value.height = height;
      refCanvas.value
        .getContext("2d")
        .drawImage(refVideo.value, 0, 0, width, height); //绘制canvas
      dataURL = refCanvas.value.toDataURL("image/jpeg"); //转换为base64
      console.log("dataURL", dataURL);
      resolve(dataURL);
    });
  });
};

const saveFile = (filePath: string) => {
  console.log(filePath);
  uni.uploadFile({
    url: actionUrl.value, //仅为示例，非真实的接口地址
    filePath: filePath,
    header: {
      "X-Authorization": "Bearer " + uni.getStorageSync("token"),
    },
    name: "file",
    success: (uploadFileRes) => {
      console.log(uploadFileRes.data);
      videoList.value.push(uploadFileRes.data);
      console.log(videoList.value);
      uni.hideLoading();
    },
    fail: (ee) => {
      console.log(ee);
      uni.hideLoading();
    },
    complete: (ee) => {
      console.log(ee);
      uni.hideLoading();
    },
  });
};

const onSuccess = (data, index, lists, name) => {
  imageList.value = lists.map((img: any) => {
    return img.response;
  });
  console.log(imageList.value);
};

//全屏
const screenChange = (e) => {
  //退出全屏时停止播放
  if (!e.detail.fullScreen) {
    videoContext.value.stop();
    isOpenVideo.value = false;
  }
};

const playVoice = (voicePath: string) => {
  console.log("播放录音");
  voiceP.value = voicePath;
  finish.value = true;
  showVoice.value = true;
};
const previewImage = (url?: string) => {
  if (!url) return;
  uni.previewImage({
    urls: [url],
  });
};
const imageError = (e: any, i: number) => {
  console.log(e);
  imageList.value[i] = "../../../static/img/404.png";
};
watch(
  () => props.audios,
  () => (voiceList.value = props.audios)
);
watch(
  () => props.files,
  () => (fileList.value = props.files)
);
watch(
  () => props.videos,
  () => (videoList.value = props.videos)
);
watch(
  () => props.imgs,
  () => (imageList.value = props.imgs)
);
onBeforeMount(async () => {
  const globalProperties =
    getCurrentInstance()?.appContext.config.globalProperties;
  // console.log('globalProperties-----', globalProperties)
  const path = uni.getStorageSync("url");
  actionUrl.value = path + "file/api/upload/file";
});

// onMounted(() => {
// 	console.log('mounted');
// })
defineExpose({
  videoList,
  fileList,
  imageList,
  voiceList,
});
</script>
<style lang="scss" scoped>
.empty {
  display: flex;
  align-items: center;
  height: 80rpx;
}

.content-card {
  margin: 0 auto;
  margin-top: 20rpx;
  background-color: #ffffff;
  min-height: 224rpx;
  border-radius: 8px;
  padding: 24rpx 28rpx;

  .title-text {
    color: #91949f;
    font-size: 28rpx;
  }

  .file-s {
    width: 116rpx;
    height: 116rpx;
    display: flex;
    justify-content: center;
    flex-direction: column;
    text-align: center;
    background: #f9f9f9;
    position: relative;

    text {
      color: #91949f;
      font-size: 20rpx;
    }

    .close-icon {
      border-radius: 50%;
      width: 32rpx;
      height: 32rpx;
      background-color: red;
      line-height: 28rpx;
      text-align: center;
      position: absolute;
      right: 0;
      top: 0;
    }
  }

  .margin-center {
    margin: 0 auto;
    width: 48rpx;
    height: 48rpx;

    .icon {
      font-size: 48rpx;
    }
  }
}

::v-deep .u-form-item__body {
  padding: 16rpx;
}

::v-deep .u-form-item {
  padding: 0;
}

.card-box {
  width: 686rpx;
  border-radius: 8px;
  padding: 0;
  margin: 20rpx auto;
  color: #ffffff;
  background-color: #ffffff;

  .card-title {
    padding: 14rpx 28rpx;

    text {
      font-size: 32rpx;
      color: #3862f8;
    }

    .label {
      padding-left: 10rpx;
      font-style: normal;
      font-weight: 600;
      font-size: 28rpx;
      color: #060f27;
    }
  }

  .list {
    padding: 10rpx 28rpx 10rpx 28rpx;
    color: #060f27;

    .l-c {
      padding: 10rpx 0;

      text {
        &:nth-child(1) {
          color: #91949f;
        }

        &:nth-child(3) {
          color: #060f27;
        }
      }
    }

    .l-file {
      padding: 10rpx 0;
    }

    &.images {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      flex-wrap: wrap;

      .image {
        height: 112rpx;
        width: 112rpx;
        margin-right: 12rpx;
      }
    }
  }
}
</style>
