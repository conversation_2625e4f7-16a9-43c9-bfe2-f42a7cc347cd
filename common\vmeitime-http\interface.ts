function _reqlog(req) {
	if (process.env.NODE_ENV === 'development') {
		// console.log("【" + req.requestId + "】 地址：" + req.url)
		if (req.data) {
			// console.log("【" + req.requestId + "】 请求参数：" + JSON.Stringify(req.data))
		}
	}
}


function _reslog(res) {
	let _statusCode = res.statusCode;

	switch (_statusCode) {
		case 200:
			break;
		case 201:
			break;
		case 401:
			break;
		case 404:
			break;
		default:
			break;
	}
}
export const createRequest = (config: any) => {
	const newRequest = {
		config: {
			baseUrl: "https://qnsw.sikezdh.com/",
			header: {
				'Content-Type': 'application/json'
			},
			data: {},
			method: "GET",
			dataType: "json",
			responseType: "text",
			timeout: 3500000,
			success() { },
			fail() { },
			complete() { },
			...(config||{})
		},
		interceptor: {
			request: null,
			response: null
		},
		request(options) {
			if (!options) {
				options = {}
			}
			options.baseUrl = options.baseUrl || this.config.baseUrl
			options.dataType = options.dataType || this.config.dataType
			options.url = options.baseUrl + options.url
			options.data = options.data || {}
			options.method = options.method || this.config.method

			// uni.showLoading({ "title": "加载中", "mask": true })
			return new Promise((resolve, reject) => {
				let _config = null
				options.complete = (response) => {
					let statusCode = response.statusCode
					response.config = _config
					if (this.interceptor.response) {
						let newResponse = this.interceptor.response(response)
						if (newResponse) {
							response = newResponse
						}
					}

					if (statusCode === 200 || statusCode === 201) {
						resolve(response);
						// uni.hideLoading()
					} else {
						// uni.showToast({
						// 	title: '系统错误，请联系管理员',
						// 	icon: 'none'
						// })
						reject(response)
						// uni.hideLoading()
					}
				}

				_config = Object.assign({}, this.config, options)
				_config.requestId = new Date().getTime()

				if (this.interceptor.request) {
					this.interceptor.request(_config)
				}
				uni.request(_config);
			});
		},
		get(url: String, data?: any, options?: any) {
			if (!options) {
				options = {}
			}
			options.url = url
			options.data = data
			options.method = 'GET'
			return this.request(options)
		},
		post(url: String, data?: any, options?: any) {
			if (!options) {
				options = {}
			}
			options.url = url
			options.data = data
			options.method = 'POST'
			return this.request(options)
		},
		put(url: String, data?: any, options?: any) {
			if (!options) {
				options = {}
			}
			options.url = url
			options.data = data
			options.method = 'PUT'
			return this.request(options)
		},
		delete(url: String, data?: any, options?: any) {
			if (!options) {
				options = {}
			}
			options.url = url
			options.data = data
			options.method = 'DELETE'
			return this.request(options)
		}
	}
	return newRequest
}
export default createRequest({})
