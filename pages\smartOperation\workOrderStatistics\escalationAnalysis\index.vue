<!-- 人员上报分析 -->
<template>
	<view class="main">
		<u-navbar fixed placeholder safeAreaInsetTop bgColor="#FFFFFF" title="人员上报分析" :autoBack="true"
			leftIconSize="20">
			<template #right>
				<view class="nv-right" @click="showScreen">
					<image src="/static/img/icons/shaixuan-black.png" style="height: 48rpx;width: 48rpx;"></image>
				</view>
			</template>
		</u-navbar>
		<view class="list">
			<view class="title">
				人员上报报表
			</view>
			<scroll-view scroll-x scroll-y>
				<!-- <wyb-table first-col-bg-color="#FFFFFF" :show-vert-border="true" header-bg-color="#EBEDF6"
					first-line-fixed ref="table" width="100%" :headers="headers" :contents="tableData" /> -->
				<u-table font-size="30" padding="14rpx">
					<u-tr class="u-tr">
						<u-th class="u-th" v-for="(item,index) in headers" :key="index"
							:width="item.width || 80 +'rpx'">
							{{item.label}}
						</u-th>
					</u-tr>
					<u-tr class="u-tr" v-for="(item,index) in tableData" :key="index">
						<u-td class="u-td" v-for="(key,index) in headers" :key="index" :width="key.width  || 80 +'rpx'">
							{{item[key.key] || '0'}}
						</u-td>
					</u-tr>
				</u-table>
			</scroll-view>
			<view class="echart">
				<view class="title">
					人员上报图表
				</view>
				<view class="line-charts">
					<l-echart ref="pieChart"></l-echart>
				</view>
				<view class="line-charts">
					<l-echart ref="barChart"></l-echart>
				</view>
			</view>
		</view>

		<u-popup mode="right" width="528" v-model="state.screenShow" closeOnClickOverlay overlay
			@close="state.screenShow = false">
			<view class="popup">
				<view class="screen-list">
					<u-form :model="screenForm" ref="form1" :label-style="{'color':'#91949F'}" border-bottom input-align="right"
						label-width="180">
						<u-form-item label="开始时间：" prop="screenForm.start">
							<input inputmode="none" placeholder-class="placeholderClass" placeholder="点击选择"
								v-model="screenForm.start" @click="chooseDate('start')">
							</input>
							<template #right>
								<u-icon name="calendar" color="#91949F" size="34"></u-icon>
							</template>
						</u-form-item>
						<u-form-item label="结束时间：" prop="screenForm.end">
							<input inputmode="none" placeholder-class="placeholderClass" placeholder="点击选择"
								v-model="screenForm.end" @click="chooseDate('end')">
							</input>
							<template #right>
								<u-icon name="calendar" color="#91949F" size="34"></u-icon>
							</template>
						</u-form-item>
					</u-form>
				</view>
				<view class="popup-button">
					<u-button type="primary" color="#3862F8" @click="submitScreen">确定</u-button>
				</view>
			</view>
		</u-popup>
		<!-- 日期选择 -->
		<u-calendar :minDate="minDate" v-model="state.calendarShow" ref="calendar" @change="confirmDate">
		</u-calendar>
	</view>
</template>

<script lang="ts" setup>
	import {
		onMounted,
		reactive,
		ref,
		getCurrentInstance
	} from 'vue'
	import dayjs from 'dayjs'
	import lEchart from '@/uni_modules/lime-echart/components/l-echart/l-echart.vue'
	import {
		workOrderCount
	} from '@/common/api/workOrder'
	import {
		barOption,
		pieOption
	} from '../data/echartsData'
	import * as echarts from 'echarts'
	const {
		proxy
	} = getCurrentInstance()
	const minDate = ref<string>(proxy.$startDate)
	const tableData = ref<any>([])
	const headers = ref<any>([])
	const pieChart = ref<any>({})
	const barChart = ref<any>({})
	const defaultHeaders = [{
		label: '发起人员',
		key: 'key',
		width: 343
	}, {
		label: '发起工单数',
		key: 'value',
		width: 343
	}]

	const state = reactive<{
		screenShow : boolean,
		calendarShow : boolean,
		showDateType : string,
	}>({
		screenShow: false,
		calendarShow: false,
		showDateType: 'start',
	})
	const screenForm = reactive<any>({
		start: dayjs().startOf('day').format('YYYY-MM-DD'),
		end: dayjs().endOf('day').format('YYYY-MM-DD')
	})


	const chooseDate = (type : string) => {
		state.calendarShow = true
		if (type === 'end') {
			minDate.value = screenForm.start
		} else {
			minDate.value = proxy.$startDate
		}
		state.showDateType = type
	}

	const confirmDate = (date : any) => {
		state.calendarShow = false
		if (state.showDateType === 'start') {
			screenForm.start = date.result
			screenForm.end = ''
		} else {
			screenForm.end = date.result
		}
	}

	const showScreen = () => {
		state.screenShow = true
	}

	// 提交筛选
	const submitScreen = () => {
		getWorkOrderCount()
		state.screenShow = false
	}

	const getWorkOrderCount = async () => {
		const params = {
			fromTime: dayjs(screenForm.start).startOf('day').valueOf(),
			toTime: dayjs(screenForm.end).endOf('day').valueOf(),
			statisticOrganizer: true
		}
		headers.value = defaultHeaders
		let barData = []
		let pieData = []
		let dataX = []
		const res = await workOrderCount(params)
		const data = res.data?.data?.organizers
		tableData.value = data?.data
		data?.data?.map(d => {
			barData.push(d.value)
			pieData.push({
				name: d.key,
				value: d.value
			})
			dataX.push(d.key)
		})
		initPie(pieData)
		initBar(dataX, barData)
	}

	// 树状图
	const initBar = (dataX : string[], data : any, color ?: string) => {
		barChart.value.init(echarts, (chart : any) => {
			const options = barOption(dataX, data, color)
			chart.setOption(options);
		});
	}
	// 树状图
	const initPie = (data : any, color ?: string) => {
		pieChart.value.init(echarts, (chart : any) => {
			const options = pieOption(data, color)
			chart.setOption(options);
		});
	}
	onMounted(async () => {
		getWorkOrderCount()
	})
</script>

<style lang="scss" scoped>
	.list {
		min-height: 90vh;
		padding: 22rpx 32rpx;
		background-color: #FFFFFF;
	}

	.title {
		margin-top: 20rpx;
		font-weight: 700;
		font-size: 30rpx;
		color: #060F27;
		padding-bottom: 20rpx;
	}

	.popup {
		width: 100%;
		height: 100%;
		position: relative;
		padding: 0 34rpx;


		.popup-button {
			width: 94%;
			position: absolute;
			bottom: 40rpx;
			left: 0;
			right: 0;
			margin: 0 auto;
		}

		::v-deep .placeholderClass {
			font-size: 28rpx;
		}

		.screen-list {
			padding-top: 222rpx;
		}
	}

	.button {
		button {
			width: 45%;
		}
	}

	::v-deep .u-form-item__body {
		padding: 8rpx 34rpx;
	}

	::v-deep.u-form-item {
		padding: 0;
	}

	::v-deep.u-checkbox {
		display: block;
	}
</style>