<!-- 事件分派 -->
<template>
	<view class="main">
		<u-form :model="form" ref="form1" :label-style="{'color':'#91949F'}" borderBottom labelWidth="180"  input-align="right" >
			<view class="card-box">
				<u-form-item required label="处理人：" prop="form.val1">
					<input placeholder="请选择处理人"  inputmode="none" placeholder-class="placeholderClass"  v-model="form.name" inputAlign="right">
					</input>
					<template #right>
						<u-icon name="arrow-right" size="28"></u-icon>
					</template>
				</u-form-item>
				<u-form-item required label="处理级别：" prop="form.val2">
					<input placeholder="请选择处理级别"  inputmode="none" placeholder-class="placeholderClass"  v-model="form.name" inputAlign="right">
					</input>
					<template #right>
						<u-icon name="arrow-right" size="28"></u-icon>
					</template>
				</u-form-item>
				<u-form-item required label="预计完工时间：" prop="form.val2" labelWidth="220">
					<input placeholder="请选择预计完工时间" v-model="form.name"  inputmode="none" placeholder-class="placeholderClass"  inputAlign="right">
					</input>
					<template #right>
						<u-icon name="calendar" size="34"></u-icon>
					</template>
				</u-form-item>
				<u-form-item label="备注：" prop="form.val2" :borderBottom="false">
				</u-form-item>
				<u-form-item label="" prop="form.val2">
					<u-input type="textarea" input-align="left"  placeholder="请输入备注" v-model="form.name" border height="160">
					</u-input>
				</u-form-item>
			</view>
			<view class="card-box">
				<u-form-item label="共同处理人：" prop="form.val1" :borderBottom="false">
					<input placeholder="请选择共同处理人" v-model="form.name"  inputmode="none" placeholder-class="placeholderClass"  inputAlign="right">
					</input>
					<template #right>
						<u-icon name="arrow-right" size="28"></u-icon>
					</template>
				</u-form-item>
			</view>
		</u-form>
		<view class="button">
			<u-button type="primary" color="#3862F8" @click="">提交</u-button>
		</view>
	</view>
</template>

<script lang="ts" setup>
	import {
		reactive
	} from "vue";
	const form = reactive < any > ({})
</script>

<style lang="scss" scoped>
	.main {
		padding-top: 20rpx;
		.card-box {
			width: 686rpx;
			border-radius: 8px;
			min-height: 80rpx;
			padding: 20rpx 28rpx;
			margin: 0 auto 20rpx auto;
			color: #FFFFFF;
			background-color: #FFFFFF;

			.form {
				padding: 0 28rpx 10rpx 28rpx;
				color: #060F27;
			}

			.l-file {
				padding: 10rpx 0;
			}
		}


		::v-deep.u-form-item {
			padding: 0;
		}
	}
</style>
