<template>
  <view class="main">
    <u-navbar
      fixed
      placeholder
      safeAreaInsetTop
      bgColor="#FFFFFF"
      title="移动抄表"
      :autoBack="true"
      leftIconSize="20"
    >
      <template #right>
        <view class="nv-right" @click="rightClick"> 账单 </view>
      </template>
    </u-navbar>

    <view class="container">
      <!-- 搜索框 -->
      <u-search
        v-model="searchCode"
        placeholder="输入用户编号"
        :show-action="true"
        :clearabled="true"
        action-text="搜索"
        bg-color="#fff"
        class="search-input"
        @search="onSearch"
        @custom="onSearch"
        @cancel="onCancel"
        @clear="onCancel"
        :action-style="{
          color: '#3862F8',
          fontSize: '28rpx',
          fontWeight: '500',
        }"
      ></u-search>

      <!-- 用户信息 -->
      <view class="info-section">
        <view class="section-title">用户信息</view>
        <view class="card-box">
          <view class="info-row">
            <text class="info-label">营业区域</text>
            <text class="info-value">{{ userInfo.areaName }}</text>
          </view>
          <view class="info-row">
            <text class="info-label">抄表手册</text>
            <text class="info-value">{{ userInfo.meterReadingManual }}</text>
          </view>
          <view class="info-row">
            <text class="info-label">用户编号</text>
            <text class="info-value">{{ userInfo.userNo }}</text>
          </view>
          <view class="info-row">
            <text class="info-label">用户名称</text>
            <text class="info-value">{{ userInfo.userName }}</text>
          </view>
          <view class="info-row">
            <text class="info-label">手机号码</text>
            <text class="info-value">{{ userInfo.phoneNumber }}</text>
          </view>
          <view class="info-row">
            <text class="info-label">用水地址</text>
            <text class="info-value address">{{ userInfo.address }}</text>
          </view>
        </view>
      </view>

      <!-- 抄表信息 -->
      <view class="info-section">
        <view class="section-title">抄表信息</view>
        <view class="card-box">
          <view class="info-row">
            <text class="info-label">水表编号</text>
            <text class="info-value">{{ meterInfo.meterNo }}</text>
          </view>
          <view class="info-row">
            <text class="info-label">上次抄表时间</text>
            <text class="info-value">{{ meterInfo.readingTime }}</text>
          </view>
          <view class="info-row">
            <text class="info-label">上次抄表读数</text>
            <text class="info-value">{{ meterInfo.currentReading }}</text>
          </view>
          <view class="info-row">
            <text class="info-label">本次抄表读数</text>
            <u-input
              v-model="currentReading"
              type="number"
              placeholder="请输入本次抄表读数"
              :border="false"
              :height="62"
              :clearabled="true"
              class="reading-input"
              :custom-style="{ padding: '0 14rpx' }"
            />
          </view>
        </view>
      </view>
    </view>

    <!-- 提交按钮 -->
    <view class="button">
      <u-button type="primary" color="#3862F8" @click="submitReading"
        >提交</u-button
      >
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, reactive } from "vue";
// 引入api
import { getUserInfo, postReading } from "@/common/api/income";

const searchCode = ref("");
const currentReading = ref("");

const userInfo = reactive({});

const meterInfo = reactive({});

// 获取用户信息
const getUserInfoData = async () => {
  try {
    const { data } = await getUserInfo(searchCode.value);
    if (data.code === 200) {
      userInfo.areaName = data.data.areaName;
      userInfo.meterReadingManual = data.data.bookName;
      userInfo.userNo = data.data.userNo;
      userInfo.userName = data.data.userName;
      userInfo.phoneNumber = data.data.phoneNumber;
      userInfo.address = data.data.address;

      meterInfo.meterNo = data.data.meterNo;
      meterInfo.readingTime = data.data.readingTime;
      meterInfo.currentReading = data.data.currentReading;
    } else if (data.code === 1001) {
      uni.showToast({
        title: "用户不存在",
        icon: "none",
      });
    } else {
      uni.showToast({
        title: data.msg,
        icon: "none",
      });
    }
  } catch (error) {
    uni.showToast({
      title: "获取用户信息失败",
      icon: "none",
    });
  }
};

// 提交抄表读数
const submitReading = async () => {
  // 校验用户编号
  if (!userInfo.userNo) {
    uni.showToast({
      title: "用户不存在",
      icon: "none",
    });
    return;
  }
  if (!currentReading.value) {
    uni.showToast({
      title: "请输入本次抄表读数",
      icon: "none",
    });
    return;
  }
  // 校验数字
  if (!/^\d+$/.test(currentReading.value)) {
    uni.showToast({
      title: "请输入正确的数字",
      icon: "none",
    });
    return;
  }

  try {
    const { data } = await postReading({
      meterNo: meterInfo.meterNo,
      currentReading: currentReading.value,
    });

    if (data.code === 200) {
      uni.showToast({
        title: "提交成功",
        icon: "success",
      });
      currentReading.value = "";
      // 刷新上次抄表信息
      getUserInfoData();
      // goBack();
    } else {
      uni.showToast({
        title: data.msg,
        icon: "none",
      });
    }
  } catch (error) {
    uni.showToast({
      title: "提交失败",
      icon: "none",
    });
  }
};

// const goBack = () => {
//   uni.navigateBack();
// }

const rightClick = () => {
  uni.navigateTo({
    url: "/pages/income/orderList/index",
  });
};

const onSearch = () => {
  getUserInfoData();
};

const onCancel = () => {
  searchCode.value = "";
  currentReading.value = "";
};
</script>

<style lang="scss" scoped>
.main {
  min-height: 100vh;
  background-color: #f5f6f7;
  display: flex;
  flex-direction: column;
}
.nav-content {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;

  .center {
    padding: 0 20rpx;
    flex: 1;
    font-size: 16px;
    color: #333333;
    text-align: center;
    font-weight: 500;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .right {
    padding: 0.4375rem 0.75rem 0.4375rem 0.4375rem;
    font-size: 16px;
    color: #3862f8;
  }
}

.container {
  flex: 1;
  padding: 20rpx;
}

.search-input {
  .u-content {
    background-color: #fff;
  }
}

.info-section {
  margin: 20rpx 0;

  .section-title {
    font-size: 32rpx;
    font-weight: 500;
    color: #333333;
    margin: 26rpx 0 16rpx;
    padding-left: 16rpx;
    border-left: 8rpx solid #3862f8;
  }
}

.card-box {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 20rpx;
}

.info-row {
  display: flex;
  align-items: baseline;
  margin-bottom: 20rpx;
  font-size: 28rpx;

  &:last-child {
    margin-bottom: 0;
  }

  .info-label {
    width: 200rpx;
    color: #91949f;
    flex-shrink: 0;
  }

  .info-value {
    flex: 1;
    color: #333333;

    &.address {
      word-break: break-all;
    }
  }
}
.reading-input {
  flex: 1;
  background-color: #f5f6f7;
  border-radius: 8rpx;
}

.submit-btn-wrapper {
  padding: 40rpx;
  background-color: #ffffff;
}
</style>
