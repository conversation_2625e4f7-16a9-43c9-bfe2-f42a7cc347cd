<template>
	<view>
		<u-navbar fixed placeholder safeAreaInsetTop bgColor="#FFFFFF" title="设备详情" :autoBack="true" leftIconSize="20">
			<template #right>
				<view class="nv-right" @click="checkIsCollect">
					<!-- <image src="/static/img/icons/clear.png" style="height: 40rpx;width: 40rpx;"></image> -->
					<!-- <image src="/static/img/icons/clear.png" style="height: 40rpx;width: 40rpx;"></image> -->
					<u-icon name="star-fill" color="#feaf2e" v-if="detail.isCollect=='1'" size="45"></u-icon>
					<u-icon name="star" color="#606266" v-else size="45"></u-icon>
				</view>
			</template>
		</u-navbar>
		<u-swiper :list="[detail.deviceImages]"></u-swiper>
		<view class="flex-between" style="padding: 20rpx;background-color: #fff;">
			<text style="font-size: 34rpx;font-weight: 600;">设备名称</text>
			<u-tag :text="detail.scrapped?'已废弃':'在用'" :bg-color="detail.scrapped?'#ff2e13':'#13ff89'" mode="dark" />
		</view>
		<u-sticky>
			<u-tabs :list="tabs" :is-scroll="false" v-model="current"></u-tabs>
		</u-sticky>
		<scroll-view scroll-y style="height: 90vh;">
			<info :detail="detail" v-if="current === 0"></info>
			<repair :detail="detail" v-if="current === 1"></repair>
			<maintenance :detail="detail" v-if="current === 2"></maintenance>
			<inspection :detail="detail" v-if="current === 3"></inspection>
		</scroll-view>
		<u-toast ref='refToast'></u-toast>
	</view>
</template>

<script lang="ts" setup>
	import {
		onMounted,
		ref
	} from 'vue'
	import {
		getDeviceDetail,
		checkCollect
	} from '@/common/api/rquipmentAssets'
	import info from './components/info.vue'
	import maintenance from './components/maintenance.vue'
	import repair from './components/repair.vue'
	import inspection from './components/inspection.vue'

	const refToast = ref<any>()
	const current = ref<number>(0)
	const detail = ref<any>({
		deviceImages: ''
	})
	const tabs = ref<any>([{
		name: '设备信息'
	}, {
		name: '维修信息'
	}, {
		name: '保养信息',
	}, {
		name: '巡检信息',
	}])

	const deviceDetail = async (id : string) => {
		const res = await getDeviceDetail(id)
		console.log(res.data.data)
		detail.value = res.data.data
	}

	const checkIsCollect = () => {
		const param = {
			id: detail.value.id,
			isCollect: detail.value.isCollect == '1' ? '0' : '1'
		}
		checkCollect(param).then(res => {
			detail.value.isCollect = param.isCollect
			refToast.value?.show({
				title: param.isCollect == '1' ? '收藏成功' : '取消收藏',
				position: 'bottom',
			})
		})
	}

	onMounted(() => {
		var pages = getCurrentPages();
		var page = pages[pages.length - 1];
		const options = page.$page.options
		console.log(options)
		deviceDetail(options.id)
	})
</script>

<style lang="scss" scoped>
	.card-box {
		width: 686rpx;
		margin: 20rpx auto;
		border-radius: 16rpx;
		// padding: 22rpx 28rpx;
		padding: 0;
		background-color: #FFFFFF;

		.hand {
			padding: 22rpx 28rpx;

			.hand-title {
				height: 42rpx;
				line-height: 42rpx;

				.icon-box {
					text-align: center;
					line-height: 32rpx;
					height: 32rpx;
					width: 32rpx;
					background-color: #3862F8;
					border-radius: 4rpx;
				}

				text {
					font-size: 28rpx;
					margin-left: 16rpx;
					color: #060F27;
					font-weight: 600;
				}
			}

			.status {
				color: #91949F;
			}
		}

		.line {
			padding-bottom: 20rpx;
		}

		.table {
			margin-top: 24rpx;
			padding: 0rpx 28rpx;

			.info {
				font-size: 24rpx;
				padding: 24rpx 0rpx;
				display: flex;

				text {
					&:nth-child(1) {
						color: #000000;
					}

					&:nth-child(2) {
						flex: 1;
						color: #91949F;
					}
				}


				.bg {
					padding: 2rpx 12rpx;
					background: rgba(56, 98, 248, 0.2);
					border-radius: 8rpx;

					text {
						color: #3862F8;
					}
				}
			}

			.files {
				font-size: 36rpx;
				font-weight: 600;

				text {
					color: #626262;
					padding-left: 20rpx;
				}
			}
		}

	}
</style>