<!-- 养护上报 -->
<template>
	<view class="main">
		<view class="detail">
			<view class="content-card card-box sid">
				<view class="card-title flex-center">
					<text class="label" style="padding: 0;">当前设备:</text>
					<text class="label">{{state.sid}}</text>
				</view>
			</view>
			<FileUpload ref="refFileUpload" :readonly="state.readonly" :imgs="state.item?.imgs"
				:audios="state.item?.audios" :videos="state.item?.videos" :files="state.item?.files"></FileUpload>
			<view class="content-card card-box">
				<view class="card-title flex-center">
					<text class="custom-icon custom-icon-caiyouduo_hemaixiangqing-dingdanxiangqing"></text>
					<view class="label">备注信息</view>
				</view>
				<u-gap height="2" bg-color="#EBEDF6"></u-gap>
				<view class="flex-center list">
					<u-input type="textarea" input-align="left"  v-model="state.remark" :placeholder="state.readonly?' ':'请输入详细信息'">
					</u-input>
				</view>
			</view>
		</view>
		<view class="button">
			<u-button v-if="state.readonly" type="primary" color="#3862F8" @click="handleBack">返回</u-button>
			<u-button v-else type="primary" color="#3862F8" @click="handleComplete">完成</u-button>
		</view>
	</view>
</template>

<script lang="ts" setup>
	import {
		onLoad,
	} from '@dcloudio/uni-app'
	import {
		reactive,
		ref,
	} from "vue";
	import FileUpload from "@/components/fileUpload/fileUpload.vue";
	import {
		maintainItemReport
	} from '../../../../common/api/maintenanceTasks';
	const state = reactive < {
		sid: string,
		item: {
			id ? : string
			imgs ? : string[]
			files ? : string[]
			audios ? : string[]
			videos ? : string[]
		},
		remark: string,
		readonly: boolean
	} > ({
		sid: '',
		item: {

		},
		remark: '',
		readonly: false
	})
	const refFileUpload = ref < InstanceType < typeof FileUpload >> ()
	const handleComplete = async () => {
		try {
			if (!refFileUpload.value || !state.item) return
			const {
				videoList,
				fileList,
				imageList,
				voiceList
			} = refFileUpload.value
			const res = await maintainItemReport(state.item.id, {
				// taskItemId: state.itemId,
				img: imageList?.join(','),
				audio: voiceList?.join(','),
				file: fileList?.join(','),
				video: videoList?.join(','),
				remark: state.remark
			})
			const data = res.data
			if (data.code !== 200) {
				uni.showToast({
					title: '提交失败',
					icon: 'error'
				})
			} else {
				uni.showToast({
					title: '提交成功',
					icon: 'success'
				})
				setTimeout(() => {
					uni.navigateBack()
				}, 1000)
			}

		} catch (e) {
			//TODO handle the exception
			uni.showToast({
				title: '提交失败',
				icon: 'error'
			})
		}

	}
	const handleBack = () => {
		uni.navigateBack()
	}
	onLoad((options: any) => {
		// console.log(options);
		state.sid = options.sid
		state.readonly = options.type === 'APPROVED'
		const item = options.item ? JSON.parse(decodeURIComponent(options.item)) : undefined
		if (item) {
			state.item = {
				...item,
				audios: item.audio?.split(',').filter((item: any) => !!item),
				files: item.file?.split(',').filter((item: any) => !!item),
				videos: item.video?.split(',').filter((item: any) => !!item),
				imgs: item.img?.split(',').filter((item: any) => !!item),
			}
		} else {
			state.item = undefined
		}
	})
</script>

<style lang="scss" scoped>
	.main {
		background-color: #FBFBFB;
		padding: 20rpx 0 120rpx 0;
	}

	.detail {
		.card-box {
			min-height: 376rpx;
			width: 686rpx;
			margin: 0 auto;
			border-radius: 16rpx;
			padding: 12rpx 28rpx;
			background-color: #FFFFFF;

			&.sid {
				min-height: 60rpx;
			}

			.icon {
				color: #3862F8;
			}

		}

		.content-card {
			width: 686rpx;
			margin: 20rpx auto;
			background-color: #FFFFFF;
			min-height: 224rpx;
			border-radius: 8px;
			padding: 24rpx 28rpx;

			text {
				color: #91949F;
				font-size: 28rpx;
			}

			.file-s {
				width: 112rpx;
				height: 112rpx;
				margin-top: 20rpx;
				display: flex;
				justify-content: center;
				flex-direction: column;
				text-align: center;
				background: #F9F9F9;

				text {
					color: #91949F;
					font-size: 20rpx;
				}
			}

			.margin-center {
				margin: 0 auto;

				.icon {
					font-size: 48rpx;
				}
			}
		}

		.card-box {
			width: 686rpx;
			border-radius: 8px;
			padding: 0;
			margin: 20rpx auto;
			color: #FFFFFF;
			background-color: #FFFFFF;

			.card-title {
				padding: 14rpx 28rpx;

				text {
					font-size: 32rpx;
					color: #3862F8;
				}

				.label {
					padding-left: 10rpx;
					font-style: normal;
					font-weight: 600;
					font-size: 28rpx;
					color: #060F27;
				}
			}

			.list {
				padding: 10rpx 28rpx 10rpx 28rpx;
				color: #060F27;
			}
		}
	}

	::v-deep .u-form-item__body {
		padding: 16rpx;
	}

	::v-deep .u-form-item {
		padding: 0;
	}
</style>
