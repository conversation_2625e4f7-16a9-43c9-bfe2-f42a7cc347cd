import { gisConfig } from '@/common/data/gisData'
// @ts-ignore
declare const uni: any

// 天地图API返回的数据格式
interface TiandituRouteItem {
  strguide: string;          // 线路文字描述
  signage: string;           // 路牌引导提示
  streetName: string;        // 当前路段名称
  nextStreetName: string;    // 下一段道路名称
  tollStatus: '0' | '1' | '2';  // 道路收费信息
  turnlatlon: string;        // 转折点经纬度
}

interface TiandituSimpleItem {
  strguide: string;          // 线路文字描述
  streetNames: string;       // 当前行驶路段名称
  lastStreetName: string;    // 最后一段道路名称
  linkStreetName: string;    // 合并段之间衔接的道路名称
  signage: string;           // 路牌引导提示
  tollStatus: '0' | '1' | '2';  // 道路收费信息
  turnlatlon: string;        // 转折点经纬度
  streetLatLon: string;      // 线路经纬度
  streetDistance: string;    // 行驶总距离(米)
  segmentNumber: string;     // 合并后的号段
}

interface TiandituMapInfo {
  center: string;            // 适宜中心经纬度
  scale: string;            // 适宜缩放比例
}

interface TiandituResponse {
  result: {
    routes: {
      count: string;        // 分段总数
      time: string;         // 查询时间
      item: TiandituRouteItem[];
    };
    simple: {
      item: TiandituSimpleItem[];
    };
    distance: string;       // 全长(公里)
    duration: string;       // 行驶总时间(秒)
    routelatlon: string;    // 线路经纬度字符串
    mapinfo: TiandituMapInfo;
  };
}

interface NavigationResponse {
  /** 导航路线 */
  paths: {
    /** 路径点 */
    points: {
      /** 经度 */
      lng: number;
      /** 纬度 */
      lat: number;
    }[];
    /** 距离(米) */
    distance: number;
    /** 预计时间(秒) */
    duration: number;
    /** 导航指令 */
    instructions: {
      /** 指令文本 */
      text: string;
      /** 指令点位置 */
      point: {
        lng: number;
        lat: number;
      };
    }[];
  }[];
  /** 响应状态 */
  status: number;
  /** 消息 */
  message: string;
}

/**
 * 获取导航路线
 * @param from 起点坐标 [经度,纬度]
 * @param to 终点坐标 [经度,纬度]
 * @param type 导航类型 drive=驾车 walk=步行 ride=骑行
 * @returns 导航结果
 */
export async function getNavigationRoute(
  from: [number, number],
  to: [number, number],
  type: 'drive' | 'walk' | 'ride' = 'drive'
): Promise<NavigationResponse> {
  const baseUrl = 'http://api.tianditu.gov.cn';
  const [fromLng, fromLat] = from;
  const [toLng, toLat] = to;

  // 根据导航类型设置style
  const styleMap = {
    drive: '0',   // 驾车导航
    walk: '2',    // 步行导航
    ride: '1'     // 骑行导航
  };

  // 构建postStr参数
  const postStr = JSON.stringify({
    orig: `${fromLng},${fromLat}`,
    dest: `${toLng},${toLat}`,
    style: styleMap[type]
  });

  try {
    const response = await uni.request({
      url: `${baseUrl}/${type}`,
      method: 'GET',
      data: {
        postStr,
        type: 'search',
        tk: gisConfig().gisTdtToken
      }
    });

    if (response.statusCode !== 200) {
      throw new Error('导航服务请求失败');
    }

    console.log('导航返回数据:', response.data);
    
    // 处理返回的XML数据
    const { data } = response;
    const parser = new DOMParser();
    const xmlDoc = parser.parseFromString(data, 'text/xml');
    
    // 获取关键信息
    let routelatlon = xmlDoc.querySelector('routelatlon')?.textContent || '';
    const distance = xmlDoc.querySelector('distance')?.textContent || '0';
    const duration = xmlDoc.querySelector('duration')?.textContent || '0';

    // 获取导航指令
    const simpleItems = Array.from(xmlDoc.querySelectorAll('simple item')).map(item => ({
      strguide: item.querySelector('strguide')?.textContent || '',
      turnlatlon: item.querySelector('turnlatlon')?.textContent || ''
    }));
    debugger;
    // 将路线经纬度字符串转换为坐标点数组
    const points = routelatlon.split(';')
      .filter(point => point.trim() !== '') // 过滤掉空值
      .map(point => {
        const [lng, lat] = point.split(',').map(Number);
        return { lng, lat };
      });

    // 转换天地图返回的数据格式为我们需要的格式
    const result: NavigationResponse = {
      status: 0,
      message: '',
      paths: [{
        points,
        distance: parseFloat(distance) * 1000, // 转换为米
        duration: parseInt(duration),
        instructions: simpleItems.map(item => ({
          text: item.strguide,
          point: {
            lng: parseFloat(item.turnlatlon.split(',')[0]),
            lat: parseFloat(item.turnlatlon.split(',')[1])
          }
        }))
      }]
    };
    return result;
  } catch (error) {
    console.error('导航服务错误:', error);
    throw error;
  }
}

/**
 * 驾车导航
 */
export function getDriveRoute(from: [number, number], to: [number, number]) {
  return getNavigationRoute(from, to, 'drive');
}

/**
 * 步行导航
 */
export function getWalkRoute(from: [number, number], to: [number, number]) {
  return getNavigationRoute(from, to, 'walk');
}

/**
 * 骑行导航
 */
export function getRideRoute(from: [number, number], to: [number, number]) {
  return getNavigationRoute(from, to, 'ride');
}
