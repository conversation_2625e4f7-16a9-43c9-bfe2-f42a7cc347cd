import { http } from '../vmeitime-http/index'

// 获取APP权限菜单
export const findMenuByTenantApplication = (params : {
	tenantApplicationId : any
}) => {
	return http().get('api/tenantMenus/findMenuByTenantApplication', params)
}
// 获取APP应用
export const getTenantApplication = (params : {
	tenantId : string,
	resourceType : string
}) => {
	return http().get('api/tenantApplication/all', params)
}



// 获取应用
export const getMonthReceivableMoney = (params : {
	size : number,
	page : number,
	endYm ?: string,
	orgId ?: string,
	beginYm ?: string,
}) => {
	return http().get('api/revenue/receivableMoney/recoveryRateReportByMeterBook', params)
}

// 获取应用底部菜单
export const getAppBottomMenu = (params : {
	tenantId : string,
}) => {
	return http().get('api/appBottomMenu/list', params)
}

export const getSystemNotifyCount = (params : {
	status : number
	to : string
}) => {
	return http().get('api/systemNotify/notifyCount', params)
}

export const readAll = (params : {
	to : string
	type : string
}) => {
	return http().post('api/systemNotify/readAll', params)
}

export const systemNotifyList = (params? : {
	page : number,
	size : number,
	to : string,
	type : string
}) => {
	return http().get('api/systemNotify/list', params)
}

export const readOne = (params : {
	id : string
}) => {
	return http().post('api/systemNotify/read', params)
}




export const saveUserCid = (params : {
	userId : string
	cid : string
}) => {
	return http().post('api/user/saveUserCid', params)
}