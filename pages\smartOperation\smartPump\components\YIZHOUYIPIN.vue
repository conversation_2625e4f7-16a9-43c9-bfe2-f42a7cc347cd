<template>
	<div>
		<AttrPop v-for="(item, i) in state.pumps" :key="i" :style="{ left: item.left, top: item.top }"
			:status="item.status" :text="item.name"></AttrPop>
		<image v-for="(item, i) in state.pans" :key="i" :style="item.styles" class="img" :class="item.status"
			:src="item.status === 'online' ? onlinePng : offlinePng" alt="1" />
	</div>
</template>
<script lang="ts" setup>
	import {
		reactive,
		onMounted,
		ref
	} from 'vue'
	import {
		useStationRealTimeData
	} from '@/common/hooks/useStations'
	import AttrPop from './AttrPop.vue'
	
	const onlinePng = ref < string > ('/static/pump/imgs/fsyx.png')
	const offlinePng = ref < string > ('/static/pump/imgs/fstz.png')
	import {
		useStationAttrGroup
	} from '@/common/hooks'

	const props = defineProps < {
		stationId ? : string
	} > ()
	const state = reactive < {
		pumps: {
			name: string;status: 'online' | 'offline';left: string;top: string
		} []
		pans: {
			styles: any;status: 'online' | 'offline'
		} []
	} > ({
		pumps: [{
				name: '1#PLC-1#泵',
				status: 'offline',
				left: '451px',
				top: '400px'
			},
			{
				name: '1#PLC-1#泵',
				status: 'offline',
				left: '500px',
				top: '370px'
			},
			{
				name: '1#PLC-1#泵',
				status: 'offline',
				left: '620px',
				top: '310px'
			},
			{
				name: '1#PLC-1#泵',
				status: 'offline',
				left: '750px',
				top: '300px'
			}
		],
		pans: [{
				styles: {
					left: '542px',
					top: '465px',
					width: '22px',
					height: '22px'
				},
				status: 'offline'
			},
			{
				styles: {
					left: '592px',
					top: '440px',
					width: '20px',
					height: '20px'
				},
				status: 'offline'
			},
			{
				styles: {
					left: '726px',
					top: '373px',
					width: '18px',
					height: '18px'
				},
				status: 'offline'
			},
			{
				styles: {
					left: '758px',
					top: '356px',
					width: '16px',
					height: '16px'
				},
				status: 'offline'
			}
		]
	})
	const realtime = useStationRealTimeData()
	const group = useStationAttrGroup()
	const refreshData = async () => {
		if (!props.stationId) return
		await group.initAttrGroupData(props.stationId)
		await realtime.getRealTimeData(props.stationId)
		group.group.value
			.filter(item => item.type?.indexOf('泵') !== -1)
			.map((item, i) => {
				if (i >= state.pumps.length) return
				state.pumps[i].name = item.type
				const attrs = item.attrList ?? []
				const deviceId = attrs.find(item => item.attr === 'status')?.deviceId
				const status = realtime.realtimeList.value.find(
					attr => attr.property === 'status' && attr.deviceId === deviceId
				)?.value
				state.pumps[i].status = status === '1.0' ? 'online' : 'offline'
				state.pans[i].status = status === '1.0' ? 'online' : 'offline'
			})
	}
	onMounted(() => {
		refreshData()
	})
</script>
<style lang="scss" scoped>
	.img {
		position: absolute;
		width: 15px;
		height: 15px;

		&.online {
			transform: rotate(360deg);
			animation: rotation 3s linear infinite;
		}
	}

	@keyframes rotation {
		from {
			-webkit-transform: rotate(0deg);
		}

		to {
			-webkit-transform: rotate(360deg);
		}
	}
</style>
