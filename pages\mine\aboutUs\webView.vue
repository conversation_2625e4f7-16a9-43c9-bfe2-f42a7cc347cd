<template>
	<view>
		<u-navbar fixed placeholder safeAreaInsetTop bgColor="#FFFFFF" :title="info.title" :autoBack="true"
			leftIconSize="20">
		</u-navbar>
		<web-view :src="info.url"></web-view>
	</view>
</template>

<script lang="ts" setup>
	import {
		onMounted,
		ref
	} from 'vue'

	const info = ref < any > ({})
	onMounted(async () => {
		var pages = getCurrentPages();
		var page = pages[pages.length - 1];
		info.value = page.$page.options
	})
</script>

<style>
</style>
