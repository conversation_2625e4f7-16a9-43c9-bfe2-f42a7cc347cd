<template>
  <view class="condition-query">
    <!-- 查询结果列表 -->
    <view v-if="showResults" class="results-content">
      <!-- 返回按钮 -->
      <view class="results-header">
        <view class="back-btn" @click="backToQuery">
          <text class="back-icon">←</text>
        </view>
        <text class="results-title">查询结果</text>
      </view>

      <QueryResultsEnhanced 
        :results="queryResults"
        :layer-name="queryParams.selectedLayer"
        @select="handleResultClick"
      />
    </view>

    <!-- 原有的查询条件界面 -->
    <view v-show="!showResults" class="query-content">
      <!-- 图层选择（单选） -->
      <view class="form-group">
        <text class="form-label">查询图层</text>
        <view class="checkbox-group">
          <view
            v-for="name in layerArray"
            :key="name"
            class="checkbox-item"
            :class="{ active: queryParams && queryParams.selectedLayer === name }"
            @click="selectLayer(name)"
          >
            <text class="checkbox-text">{{ name }}</text>
          </view>
        </view>
      </view>

      <!-- 动态查询条件 -->
      <view class="form-group">
        <text class="form-label">查询条件</text>
        
        <view 
          v-for="(condition, index) in (queryParams && queryParams.conditions ? queryParams.conditions : [])" 
          :key="index"
          class="condition-item"
        >
          <view class="condition-row">
            <!-- 第一行：字段和操作符 -->
            <view class="condition-row-top">
              <picker 
                :value="getFieldIndex(condition.field)"
                :range="getAvailableFields()"
                range-key="alias"
                @change="onFieldChange(index, $event)"
              >
                <view class="picker-item field-picker">
                  <text>{{ getFieldAlias(condition.field) || '选择字段' }}</text>
                  <text class="picker-arrow">▼</text>
                </view>
              </picker>
              
              <picker 
                :value="getOperatorIndex(condition.operator)"
                :range="operators"
                range-key="label"
                @change="onOperatorChange(index, $event)"
              >
                <view class="picker-item operator-picker">
                  <text>{{ getOperatorLabel(condition.operator) || '操作符' }}</text>
                  <text class="picker-arrow">▼</text>
                </view>
              </picker>
              
              <input 
                v-model="condition.value"
                :type="getInputType(condition.field)"
                :placeholder="getValuePlaceholder(condition.field)"
                class="condition-value"
              />
              <!-- 删除按钮 -->
              <view class="delete-condition" @click="removeCondition(index)">
                <text class="delete-icon">×</text>
              </view>
            </view>
          </view>
          
          <!-- 逻辑连接符 -->
          <view v-if="queryParams && queryParams.conditions && index < queryParams.conditions.length - 1" class="logic-connector">
            <picker 
              :value="condition.logic === 'OR' ? 1 : 0"
              :range="['AND', 'OR']"
              @change="onLogicChange(index, $event)"
            >
              <view class="logic-picker">
                <text>{{ condition.logic || 'AND' }}</text>
              </view>
            </picker>
          </view>
        </view>
        
        <view v-if="!queryParams || !queryParams.conditions || queryParams.conditions.length === 0" class="empty-conditions">
          <text class="empty-text">暂无查询条件</text>
        </view>

        <!-- 添加条件按钮 -->
        <view class="add-condition-btn" @click="addCondition">
          <text class="add-icon">+</text>
          <text class="add-text">添加条件</text>
        </view>
      </view>

      <!-- 空间定位 -->
      <view class="form-group">
        <text class="form-label">空间定位</text>
        <view class="location-options">
          <view 
            class="location-btn"
            :class="{ active: queryParams && queryParams.useCurrentLocation }"
            @click="toggleCurrentLocation"
          >
            <text class="custom-icon custom-icon-location"></text>
            <text class="location-text">当前位置</text>
          </view>
          <view 
            class="location-btn"
            :class="{ active: queryParams && queryParams.useDrawArea }"
            @click="toggleDrawArea"
          >
            <text class="custom-icon custom-icon-draw"></text>
            <text class="location-text">绘制区域</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部操作按钮 -->
    <view class="query-actions">
      <view class="action-btn reset-btn" @click="resetQuery">
        <text class="btn-text">重置</text>
      </view>
      <view class="action-btn search-btn" @click="executeQuery">
        <text class="btn-text">查询</text>
      </view>
    </view>
    
  </view>
</template>

<script>
import QueryResultsEnhanced from './QueryResultsEnhanced.vue'

export default {
  name: 'ConditionQuery',
  components: {
    QueryResultsEnhanced
  },
  props: {
    layerArray: {
      type: Array,
      default: () => []
    }
  },
  emits: ['query'],
  computed: {
    // 安全地处理 fields（内部自动拉取）
    safeFields() {
      if (!this.fields || !Array.isArray(this.fields)) return []
      return this.fields.map((field, index) => ({
        name: field.name || `field_${index}`,
        alias: field.alias || field.name || `字段 ${index + 1}`,
        type: field.type || 'string',
        layerName: field.layerName || null,
        length: field.length || 0,
        nullable: field.nullable !== undefined ? field.nullable : true,
        editable: field.editable !== undefined ? field.editable : true,
        domain: field.domain || null,
        defaultValue: field.defaultValue || null
      }))
    }
  },
  watch: {
    // 监听选中图层变化（单选），自动拉取字段
    'queryParams.selectedLayer': {
      async handler() {
        await this.refreshFields()
      },
      deep: true
    }
  },
  mounted() {

  },
  data() {
    return {
      operators: [
        { label: '=', value: 'eq', types: ['string', 'number', 'date'] },
        { label: '≠', value: 'ne', types: ['string', 'number', 'date'] },
        { label: '>', value: 'gt', types: ['number', 'date'] },
        { label: '≥', value: 'gte', types: ['number', 'date'] },
        { label: '<', value: 'lt', types: ['number', 'date'] },
        { label: '≤', value: 'lte', types: ['number', 'date'] },
        { label: 'LIKE', value: 'like', types: ['string'] },
        { label: 'NOT LIKE', value: 'not_like', types: ['string'] },
        { label: 'IS NULL', value: 'is_null', types: ['string', 'number', 'date'], noValue: true },
        { label: 'IS NOT NULL', value: 'is_not_null', types: ['string', 'number', 'date'], noValue: true },
        { label: 'IN', value: 'in', types: ['string', 'number'], multiValue: true },
        { label: 'NOT IN', value: 'not_in', types: ['string', 'number'], multiValue: true },
        { label: 'BETWEEN', value: 'between', types: ['number', 'date'], rangeValue: true }
      ],

      queryParams: {
        selectedLayer: '',
        conditions: [
          {
            field: '',
            operator: 'eq',
            value: '',
            multiValue: '',
            minValue: '',
            maxValue: '',
            logic: 'AND'
          }
        ],
        useCurrentLocation: false,
        useDrawArea: false
      },
      // 内部字段数据
      fields: [],
      loadingFields: false,
      fieldError: '',
      // 控制查询结果显示
      showResults: false,
      // 查询结果数据
      queryResults: [],
    }
  },
  methods: {
    // 检查值是否为数字
    isNumber(value) {
      return !isNaN(value) && value.trim() !== '';
    },
    
    selectLayer(layerName) {
      if (!this.queryParams) return
      this.queryParams.selectedLayer = layerName
      // 切换图层时，清空已选字段和值，避免跨图层混淆
      if (this.queryParams.conditions && this.queryParams.conditions.length > 0) {
        this.queryParams.conditions = this.queryParams.conditions.map(c => ({
          ...c,
          field: '',
          value: '',
          multiValue: '',
          minValue: '',
          maxValue: ''
        }))
      }
    },
    
    addCondition() {
      if (!this.queryParams || !this.queryParams.conditions) return
      
      this.queryParams.conditions.push({
        field: '',
        operator: 'eq',
        value: '',
        multiValue: '',
        minValue: '',
        maxValue: '',
        logic: 'AND'
      })
    },
    
    removeCondition(index) {
      if (!this.queryParams || !this.queryParams.conditions) return
      this.queryParams.conditions.splice(index, 1)
    },
    
    onFieldChange(index, event) {
      if (!this.queryParams || !this.queryParams.conditions) return
      
      const fieldIndex = event.detail.value
      const field = this.safeFields[fieldIndex]
      if (field) {
        this.queryParams.conditions[index].field = field.name
        // 根据字段类型重置值
        this.queryParams.conditions[index].value = ''
      }
    },
    
    onOperatorChange(index, event) {
      if (!this.queryParams || !this.queryParams.conditions) return
      
      const operatorIndex = event.detail.value
      const operator = this.operators[operatorIndex]
      if (operator) {
        this.queryParams.conditions[index].operator = operator.value
        // 某些操作符不需要值
        if (['is_null', 'is_not_null'].includes(operator.value)) {
          this.queryParams.conditions[index].value = ''
        }
      }
    },
    
    onLogicChange(index, event) {
      if (!this.queryParams || !this.queryParams.conditions) return
      
      const logic = event.detail.value === 1 ? 'OR' : 'AND'
      this.queryParams.conditions[index].logic = logic
    },
    
    getFieldIndex(fieldName) {
      return this.safeFields.findIndex(field => field.name === fieldName)
    },
    
    getFieldAlias(fieldName) {
      const field = this.safeFields.find(field => field.name === fieldName)
      return field ? field.alias : fieldName
    },
    
    getOperatorIndex(operatorValue) {
      return this.operators.findIndex(op => op.value === operatorValue)
    },
    
    getOperatorLabel(operatorValue) {
      const operator = this.operators.find(op => op.value === operatorValue)
      return operator ? operator.label : operatorValue
    },
    
    getInputType(fieldName) {
      const field = this.safeFields.find(field => field.name === fieldName)
      if (!field) return 'text'
      
      switch (field.type) {
        case 'number':
        case 'integer':
          return 'number'
        case 'date':
          return 'date'
        default:
          return 'text'
      }
    },
    
    getValuePlaceholder(fieldName) {
      const field = this.safeFields.find(field => field.name === fieldName)
      if (!field) return '请输入值'
      
      switch (field.type) {
        case 'number':
        case 'integer':
          return '请输入数字'
        case 'date':
          return '请选择日期'
        default:
          return `请输入${field.alias || field.name}`
      }
    },
    
    toggleCurrentLocation() {
      if (!this.queryParams) return
      
      this.queryParams.useCurrentLocation = !this.queryParams.useCurrentLocation
      if (this.queryParams.useCurrentLocation) {
        this.queryParams.useDrawArea = false
      }
    },
    
    toggleDrawArea() {
      if (!this.queryParams) return
      
      this.queryParams.useDrawArea = !this.queryParams.useDrawArea
      if (this.queryParams.useDrawArea) {
        this.queryParams.useCurrentLocation = false
      }
    },
    
    resetQuery() {
      this.queryParams = {
        selectedLayer: [],
        conditions: [],
        useCurrentLocation: false,
        useDrawArea: false
      }
    },
    
    async executeQuery() {
      // 安全检查
      if (!this.queryParams || !this.queryParams.selectedLayer) {
        uni.showToast({
          title: '请选择查询图层',
          icon: 'none'
        });
        return;
      }

      try {
        // 构建条件字符串数组
        // 首先过滤出有效的条件
        const validConditions = this.queryParams.conditions.filter(condition => {
          // 过滤无效条件
          if (!condition.field || !condition.operator) return false;
          const operator = this.operators.find(op => op.value === condition.operator);
          if (!operator) return false;
          
          // 检查需要值的操作符
          if (!operator.noValue && !condition.value) return false;
          
          return true;
        });

        // 构建条件字符串
        const conditionStrings = validConditions.map((condition, index, array) => {
          const { field, operator, value, logic } = condition;
          let conditionStr = '';

            // 检查字段名是否包含中文
            const quotedField = /[\u4e00-\u9fa5]/.test(field) ? `"${field}"` : field;
            
            switch (operator) {
              case 'eq':
                conditionStr = `${quotedField} = ${this.isNumber(value) ? value : `'${value}'`}`;
                break;
              case 'ne':
                conditionStr = `${quotedField} <> ${this.isNumber(value) ? value : `'${value}'`}`;
                break;
              case 'gt':
                conditionStr = `${quotedField} > ${value}`;
                break;
              case 'gte':
                conditionStr = `${quotedField} >= ${value}`;
                break;
              case 'lt':
                conditionStr = `${quotedField} < ${value}`;
                break;
              case 'lte':
                conditionStr = `${quotedField} <= ${value}`;
                break;
              case 'like':
                conditionStr = `${quotedField} LIKE '%${value}%'`;
                break;
              case 'not_like':
                conditionStr = `${quotedField} NOT LIKE '%${value}%'`;
                break;
              case 'in':
                const inValues = condition.multiValue 
                  ? condition.multiValue.split(',').map(v => `'${v.trim()}'`).join(',')
                  : value.split(',').map(v => `'${v.trim()}'`).join(',');
                conditionStr = `${quotedField} IN (${inValues})`;
                break;
              case 'not_in':
                const notInValues = condition.multiValue 
                  ? condition.multiValue.split(',').map(v => `'${v.trim()}'`).join(',')
                  : value.split(',').map(v => `'${v.trim()}'`).join(',');
                conditionStr = `${quotedField} NOT IN (${notInValues})`;
                break;
              case 'is_null':
                conditionStr = `${quotedField} IS NULL`;
                break;
              case 'is_not_null':
                conditionStr = `${quotedField} IS NOT NULL`;
                break;
              case 'between':
                conditionStr = `${quotedField} BETWEEN ${condition.minValue} AND ${condition.maxValue}`;
                break;
            }

            // 条件字符串已构建完成
            // 对于非第一个条件，添加对应的逻辑运算符
            const logicPrefix = index > 0 ? `${validConditions[index - 1].logic || 'AND'} ` : '';
            return `${logicPrefix}${conditionStr}`;
          });

        // 构建空间过滤条件字符串
        let spatialFilterStr = '';
        if (this.queryParams.useCurrentLocation) {
          try {
            const location = await this.getCurrentLocation();
            if (location) {
              spatialFilterStr = `DWITHIN(geom, POINT(${location.longitude} ${location.latitude}), 500, meters)`;
            }
          } catch (error) {
            console.warn('获取当前位置失败:', error);
          }
        }

        // 组合所有过滤条件
        const allFilters = [];
        if (conditionStrings.length > 0) {
          allFilters.push(`(${conditionStrings.join(' ')})`);
        }
        if (spatialFilterStr) {
          allFilters.push(spatialFilterStr);
        }

        // 构造完整的查询参数
        const queryParams = {
          layers: [this.queryParams.selectedLayer],
          cqlFilter: allFilters.length > 0 ? allFilters.join(' AND ') : undefined,
        };

        this.executeConditionQuery(queryParams);
      } catch (error) {
        console.error('查询失败:', error);
        uni.showToast({
          title: error?.message || '查询失败',
          icon: 'error'
        });
      }
    },
    async executeConditionQuery(queryParams) {
      debugger
      this.showConditionQuery = false;

      try {
        uni.showLoading({
          title: '查询中...',
        });

        // 直接使用传入的查询参数调用API
        const { queryPipelineByCondition } = await import('../../../common/api/geoserver');
        const result = await queryPipelineByCondition(queryParams);

        if (result.data) {
          this.queryResults = result.data;
          if (result.data.features.length > 0) {
            this.showResults = true;
            uni.showToast({
              title: `查询到 ${result.data.features.length} 条数据`,
              icon: 'success',
            });

          } else {
            uni.showToast({
              title: '未查询到符合条件的数据',
              icon: 'none',
            });
          }
        } else {
          throw new Error(result.error || '查询失败');
        }
      } catch (error) {
        console.error('条件查询失败:', error);
        uni.showToast({
          title: '查询失败，请重试',
          icon: 'error',
        });
      } finally {
        uni.hideLoading();
      }
    },
    
    // 获取可用字段（根据选中图层过滤，单选）
    getAvailableFields() {
      if (!this.queryParams || !this.queryParams.selectedLayer) {
        return this.safeFields
      }
      // 返回选中图层的所有字段（通过 layerName 过滤）
      return this.safeFields.filter(field => field.layerName === this.queryParams.selectedLayer || !field.layerName)
    },

    // 返回查询条件界面
    backToQuery() {
      this.showResults = false
    },

    // 处理结果项点击
    handleResultClick(item) {
      // 触发结果选择事件
      this.$emit('result-select', item)
    },



    // 刷新字段（根据选中图层从接口获取）
    async refreshFields() {
      try {
        this.loadingFields = true
        this.fieldError = ''

        const selected = (this.queryParams && this.queryParams.selectedLayer) || ''
        if (!selected) {
          this.fields = []
          return
        }

        // 直接使用选中的图层名称作为 typeName
        const typeNames = [selected].filter(Boolean)

        const { getLayerFields } = await import('@/common/api/geoserver')

        const all = []
        for (let i = 0; i < typeNames.length; i++) {
          const typeName = typeNames[i]
          try {
            const fields = await getLayerFields(typeName)
            fields.forEach(f => all.push({ ...f, layerName: typeName }))
          } catch (e) {
            console.warn('获取字段失败:', typeName, e)
          }
        }

        this.fields = all
      } catch (e) {
        this.fieldError = e?.message || '获取字段失败'
      } finally {
        this.loadingFields = false
      }
    }
  }
}
</script>

<style scoped lang="scss">

.condition-query {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.results-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;

  .results-header {
    display: flex;
    align-items: center;
    padding: 20rpx 32rpx;
    background: #FFFFFF;
    border-bottom: 2rpx solid #E2E3E5;
    
    .back-btn {
      display: flex;
      align-items: center;
      padding: 12rpx;
      cursor: pointer;
      
      .back-icon {
        font-size: 36rpx;
        color: #3862F8;
        margin-right: 8rpx;
      }
      
      .back-text {
        font-size: 28rpx;
        color: #3862F8;
      }
    }
    
    .results-title {
      flex: 1;
      text-align: center;
      font-size: 32rpx;
      font-weight: 600;
      color: #060F27;
      margin-right: 76rpx; // 为了保持标题居中，抵消返回按钮的宽度
    }
  }
}

.query-content {
  flex: 1;
  padding: 32rpx;
  overflow-y: auto;
}



.form-group {
  margin-bottom: 40rpx;
  
  .form-label {
    display: block;
    font-size: 28rpx;
    font-weight: 600;
    color: #060F27;
    margin-bottom: 16rpx;
  }
}

.checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.checkbox-item {
  padding: 16rpx 24rpx;
  border-radius: 8rpx;
  background-color: #F7F8FA;
  border: 2rpx solid transparent;
  
  &.active {
    background-color: #E8F0FF;
    border-color: #3862F8;
  }
  
  .checkbox-text {
    font-size: 24rpx;
    color: #060F27;
  }
}

.range-input {
  display: flex;
  align-items: center;
  gap: 16rpx;
  
  .range-input-item {
    flex: 1;
    padding: 20rpx 16rpx;
    border-radius: 8rpx;
    background-color: #F7F8FA;
    border: 2rpx solid #E2E3E5;
    font-size: 24rpx;
    
    &:focus {
      border-color: #3862F8;
    }
  }
  
  .range-separator {
    font-size: 24rpx;
    color: #91949F;
  }
}

.location-options {
  display: flex;
  gap: 16rpx;
}

.location-btn {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 16rpx 12rpx;
  border-radius: 6rpx;
  background-color: #F7F8FA;
  border: 1rpx solid transparent;
  
  &.active {
    background-color: #E8F0FF;
    border-color: #3862F8;
  }
  
  .custom-icon {
    font-size: 24rpx;
    color: #3862F8;
    margin-right: 8rpx;
  }
  
  .location-text {
    font-size: 22rpx;
    color: #060F27;
  }
}

.add-condition-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16rpx;
  margin-top: 16rpx;
  background-color: #F7F8FA;
  border: 1rpx dashed #3862F8;
  border-radius: 6rpx;
  cursor: pointer;
  
  .add-icon {
    font-size: 24rpx;
    color: #3862F8;
    margin-right: 4rpx;
  }
  
  .add-text {
    font-size: 24rpx;
    color: #3862F8;
  }

  &:active {
    background-color: #E8F0FF;
  }
}

.condition-item {
  margin-bottom: 24rpx;
  padding: 20rpx;
  background-color: #F7F8FA;
  border-radius: 8rpx;
}

.condition-row {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.condition-row-top {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.condition-row-bottom {
  width: 100%;
}

.picker-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 12rpx;
  background-color: #ffffff;
  border-radius: 6rpx;
  border: 2rpx solid #E2E3E5;
  font-size: 24rpx;
  
  &.field-picker {
    flex: 2;
  }
  
  &.operator-picker {
    flex: 1;
  }
  
  .picker-arrow {
    color: #91949F;
    font-size: 20rpx;
  }
}

.condition-value {
  flex: 1;
  padding: 16rpx 12rpx;
  background-color: #ffffff;
  border-radius: 6rpx;
  border: 2rpx solid #E2E3E5;
  font-size: 24rpx;
  
  &:focus {
    border-color: #3862F8;
  }
}

.delete-condition {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #FF4757;
  border-radius: 6rpx;
  
  .delete-icon {
    color: #ffffff;
    font-size: 28rpx;
    font-weight: bold;
  }
}

.logic-connector {
  display: flex;
  justify-content: center;
  margin-top: 16rpx;
}

.logic-picker {
  padding: 8rpx 24rpx;
  background-color: #E8F0FF;
  border-radius: 16rpx;
  font-size: 24rpx;
  color: #3862F8;
  font-weight: 600;
}

.empty-conditions {
  padding: 40rpx 20rpx;
  text-align: center;
  
  .empty-text {
    font-size: 24rpx;
    color: #91949F;
  }
}

.query-actions {
  display: flex;
  width: 100%;
  gap: 16rpx;
  padding: 16rpx 24rpx calc(16rpx + env(safe-area-inset-bottom));
  padding-bottom: calc(16rpx + constant(safe-area-inset-bottom));
  border-top: 1rpx solid #E2E3E5;
  background: #fff;
}

.action-btn {
  flex: 1;
  height: 72rpx;
  border-radius: 6rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1rpx solid transparent;
  
  .btn-text {
    font-size: 26rpx;
    font-weight: 500;
    line-height: 1;
  }
  
  &.reset-btn {
    background-color: #F7F8FA;
    border-color: #E2E3E5;
    
    .btn-text {
      color: #91949F;
    }
  }
  
  &.search-btn {
    background-color: #3862F8;
    border-color: #3862F8;
    
    .btn-text {
      color: #ffffff;
    }
  }
}
</style>