<template>
	<view class="map-browsing">
		<view class="main-wrapper">
			<view id="leafletMap" class="map-view"></view>
			<view class="cover-view-plus">
				<template v-for="(menu,index) in verticalBar.menus" :key="index">
					<block v-if="index<2">
						<view class="plus-btn" @click="verticalBar.setCurrent(menu.value)">
							<text class="custom-icon btn-img" :class="menu.icon"
								:style="{'color':verticalBar.currentBar === menu.value?'#3862F8':'#060F27'}"></text>
							<text style="font-size: 24rpx;"
								:style="{'color':verticalBar.currentBar === menu.value?'#3862F8':'#060F27'}">{{menu.name}}</text>
						</view>
					</block>
					<view v-if="index===0" class="btn-line"></view>
				</template>
			</view>
			<view class="cover-view-loca" @click="getLocation">
				<button class="cover-loca-image" :loading="location.loading">
					<image class="loca-btn" src="/static/img/icons/location-black.png" mode="widthFix"></image>
				</button>
			</view>
			<view class="cover-view-menu bottom-menu border-box flex-around">
				<view class="menu-item" v-for="(menu,i) in bottomBars.menus" :key="i"
					@click="bottomBars.setCurrent(menu.id)">
					<view class="icon-bg round-rect" :style="{'background':menu.color}">
						<text :class="['layer-menu-icon','custom-icon',menu.icon]" :style="{'color':'#ffffff'}"></text>
					</view>
					<text class="layer-menu-text"
						:style="{'color':menu.id===bottomBars.current?'#3862F8':'#060F27'}">{{menu.alias||menu.name}}</text>
				</view>
			</view>
			<view v-if="verticalBar.currentBar==='baselayer'" class="cover-view-menu baselayer">
				<view class="cover-header">
					<text class="title">选择底图</text>
					<text class="icon" @click="closeCover">x</text>
				</view>
				<view class="cover-main">
					<view class="item">
						<image class="item-image" @click="()=>changeBaseMap('vec')"
							src="http://t4.tianditu.gov.cn/vec_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=vec&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX=13&TILEROW=3457&TILECOL=6548&tk=e4e98a7455967290863f2f1bb245f7b5"
							mode=""></image>
						<text class="item-text">标准</text>
					</view>
					<view class="item">
						<image class="item-image" @click="()=>changeBaseMap('img')"
							src="http://t4.tianditu.gov.cn/img_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=img&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX=14&TILEROW=6916&TILECOL=13100&tk=e4e98a7455967290863f2f1bb245f7b5"
							mode=""></image>
						<text class="item-text">卫星</text>
					</view>
				</view>
			</view>
			<view v-if="verticalBar.currentBar==='layer'" class="cover-view-menu border-box layer">
				<view class="cover-header">
					<text class="title">选择图层</text>
					<text class="icon" @click="closeCover">x</text>
				</view>
				<scroll-view class="menu-list flex-around" :scroll-y="true">
					<view class="layer-box">
						<text class="layer-title">管点类</text>
						<view class="layer-menu">
							<view class="menu-item" v-for="(menu,i) in pipelayer.pointLayers" :key="i"
								@click="toggleLayer(menu)">
								<view class="icon-bg"
									:style="{'background-color':isLayerVisible(menu.layerid)?'#3862F8':'#E2E3E5'}">
									<text :class="['layer-menu-icon','custom-icon',menu.icon]"
										:style="{'color':isLayerVisible(menu.layerid)?'#ffffff':'#060F27'}"></text>
								</view>
								<text class="layer-menu-text"
									:style="{'color':isLayerVisible(menu.layerid)?'#3862F8':'#060F27'}">{{menu.layername}}</text>
							</view>
						</view>
					</view>
					<view class="layer-box">
						<text class="layer-title">管线类</text>
						<view class="layer-menu">
							<view class="menu-item" v-for="(menu,i) in pipelayer.lineLayers" :key="i"
								@click="toggleLayer(menu)">
								<view class="icon-bg"
									:style="{'background-color':isLayerVisible(menu.layerid)?'#3862F8':'#E2E3E5'}">
									<text :class="['layer-menu-icon','custom-icon',menu.icon]"
										:style="{'color':isLayerVisible(menu.layerid)?'#ffffff':'#060F27'}"></text>
								</view>
								<text class="layer-menu-text"
									:style="{'color':isLayerVisible(menu.layerid)?'#3862F8':'#060F27'}">{{menu.layername}}</text>
							</view>
						</view>
					</view>
				</scroll-view>
			</view>
		</view>
	</view>
</template>

<script lang="ts">
import { onMounted, ref } from 'vue';
import { useLocation, useVerticalBar, usePipeLayers } from '../../../common/hooks';
import { useBottomMenus } from './hooks/useBottomMenus';
import L from 'leaflet';
import '@geoman-io/leaflet-geoman-free';
import 'leaflet/dist/leaflet.css';
import '@geoman-io/leaflet-geoman-free/dist/leaflet-geoman.css';
// import { Uni } from '@dcloudio/types';
// declare const uni: Uni;

interface Feature {
	geometry: {
		type: string;
		coordinates: number[];
	};
	properties: {
		type: string;
		name: string;
	};
}

interface LineFeature {
	geometry: {
		type: string;
		coordinates: number[][];
	};
	properties: {
		type: string;
	};
}

export default {
	data() {
		return {
			verticalBar: useVerticalBar(),
			location: useLocation(),
			bottomBars: useBottomMenus(),
			pipelayer: usePipeLayers(),
			map: null as L.Map | null,
			basemap: 'vec',
			drawLayer: null as L.FeatureGroup | null,
			layerGroups: {} as Record<string, L.LayerGroup>,
			visibleLayers: [] as string[],
		}
	},
	methods: {
		initMap() {
			// 初始化地图
			this.map = L.map('leafletMap', {
				center: [39.915, 116.404], // 默认中心点
				zoom: 13,
				zoomControl: false
			});

			// 添加天地图底图
			L.tileLayer('http://t{s}.tianditu.gov.cn/vec_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=vec&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=e4e98a7455967290863f2f1bb245f7b5', {
				subdomains: ['0', '1', '2', '3', '4', '5', '6', '7'],
				maxZoom: 18
			}).addTo(this.map);

			// 初始化绘图图层
			this.drawLayer = new L.FeatureGroup().addTo(this.map);

			// 初始化绘图工具
			this.map.pm.addControls({
				position: 'topleft',
				drawMarker: true,
				drawCircle: true,
				drawPolyline: true,
				drawRectangle: true,
				drawPolygon: true,
				editMode: true,
				dragMode: true,
				cutPolygon: true,
				removalMode: true,
			});

			// 监听绘图完成事件
			this.map.on('pm:create', (e) => {
				this.handleDrawComplete(e);
			});
		},

		changeBaseMap(type: string) {
			if (!this.map) return;
			this.basemap = type;
			this.map.eachLayer((layer) => {
				if (layer instanceof L.TileLayer) {
					this.map?.removeLayer(layer);
				}
			});

			if (type === 'vec') {
				L.tileLayer('http://t{s}.tianditu.gov.cn/vec_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=vec&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=e4e98a7455967290863f2f1bb245f7b5', {
					subdomains: ['0', '1', '2', '3', '4', '5', '6', '7'],
					maxZoom: 18
				}).addTo(this.map);
			} else {
				L.tileLayer('http://t{s}.tianditu.gov.cn/img_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=img&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=e4e98a7455967290863f2f1bb245f7b5', {
					subdomains: ['0', '1', '2', '3', '4', '5', '6', '7'],
					maxZoom: 18
				}).addTo(this.map);
			}
		},

		closeCover() {
			this.verticalBar.setCurrent('');
		},

		getLocation() {
			if (!this.map) return;
			this.location.loading = true;
			navigator.geolocation.getCurrentPosition(
				(position) => {
					const { latitude, longitude } = position.coords;
					this.map?.setView([latitude, longitude], 15);
					L.marker([latitude, longitude]).addTo(this.map as L.Map);
					this.location.loading = false;
				},
				(error) => {
					console.error('定位失败:', error);
					this.location.loading = false;
					uni.showToast({
						title: '定位失败',
						icon: 'none'
					});
				}
			);
		},

		handleDrawComplete(e: any) {
			const layer = e.layer;
			this.drawLayer?.addLayer(layer);
			
			// 获取绘制的图形数据
			const geoJSON = layer.toGeoJSON();
			console.log('绘制完成:', geoJSON);
			
			// 这里可以处理绘制完成后的业务逻辑
			if (this.bottomBars.current === 'space') {
				// 空间查询逻辑
			} else if (this.bottomBars.current === 'buffer') {
				// 缓冲区分析逻辑
			}
		},

		async initLayers() {
			// 初始化图层组
			this.layerGroups = {
				points: L.layerGroup().addTo(this.map as L.Map),
				lines: L.layerGroup().addTo(this.map as L.Map)
			};

			// 加载图层数据
			await this.loadLayerData();
		},

		async loadLayerData() {
			try {
				// 这里应该调用你的管网数据API
				// 示例数据结构
				const pointFeatures: Feature[] = [
					// { type: 'Feature', geometry: { type: 'Point', coordinates: [116.404, 39.915] }, properties: { type: '水厂', name: '测试水厂' } }
				];
				const lineFeatures: LineFeature[] = [
					// { type: 'Feature', geometry: { type: 'LineString', coordinates: [[116.404, 39.915], [116.405, 39.916]] }, properties: { type: '供水管线' } }
				];

				// 清除现有图层
				this.layerGroups.points.clearLayers();
				this.layerGroups.lines.clearLayers();

				// 添加点要素
				pointFeatures.forEach(feature => {
					const marker = L.marker([feature.geometry.coordinates[1], feature.geometry.coordinates[0]], {
						icon: L.divIcon({
							className: 'custom-div-icon',
							html: `<div class="marker-pin" style="background-color: ${this.getLayerColor(feature.properties.type)}"></div>`,
							iconSize: [30, 30],
							iconAnchor: [15, 30]
						})
					});
					marker.bindPopup(feature.properties.name);
					this.layerGroups.points.addLayer(marker);
				});

				// 添加线要素
				lineFeatures.forEach(feature => {
					const line = L.polyline(feature.geometry.coordinates.map(coord => [coord[1], coord[0]]), {
						color: this.getLayerColor(feature.properties.type),
						weight: 3
					});
					this.layerGroups.lines.addLayer(line);
				});
			} catch (error) {
				console.error('加载图层数据失败:', error);
			}
		},

		getLayerColor(type: string) {
			const colors: Record<string, string> = {
				'水厂': '#3c8da5',
				'测流压站': '#9b4b62',
				'泵站': '#8f5c3e',
				'压力监测站': '#909c36',
				'流量监测站': '#327c53',
				'水质监测站': '#5f4894',
				'水源地': '#43548f',
				'大用户': '#489785',
				'污水处理厂': '#9e561d',
				'供水管线': '#3862F8'
			};
			return colors[type] || '#999999';
		},

		toggleLayer(layer: any) {
			const layerId = layer.layerid;
			const index = this.visibleLayers.indexOf(layerId);
			
			if (index === -1) {
				// 显示图层
				this.visibleLayers.push(layerId);
				if (layer.type === 'point') {
					this.layerGroups.points.addTo(this.map as L.Map);
				} else {
					this.layerGroups.lines.addTo(this.map as L.Map);
				}
			} else {
				// 隐藏图层
				this.visibleLayers.splice(index, 1);
				if (layer.type === 'point') {
					this.map?.removeLayer(this.layerGroups.points);
				} else {
					this.map?.removeLayer(this.layerGroups.lines);
				}
			}
		},

		isLayerVisible(layerId: string) {
			return this.visibleLayers.includes(layerId);
		},
	},
	async mounted() {
		this.initMap();
		await this.initLayers();
	},
	beforeUnmount() {
		if (this.map) {
			this.map.remove();
		}
	}
}
</script>

<style lang="scss">
@import 'leaflet/dist/leaflet.css';
@import '@geoman-io/leaflet-geoman-free/dist/leaflet-geoman.css';

.map-browsing {
	height: calc(100vh - 100rpx);
	
	/* #ifdef APP-PLUS */
	height: 100vh;
	/* #endif */

	.main-wrapper {
		position: relative;
		height: calc(100% - var(--status-bar-height));

		.map-view {
			height: 100%;
			position: relative;
			z-index: 1;
		}
	}

	.cover-view-plus {
		width: 80rpx;
		background-color: #ffffff;
		border-radius: 20rpx;
		position: absolute;
		z-index: 10;
		top: 62rpx;
		right: 32rpx;
		display: flex;
		flex-wrap: wrap;
		align-items: center;
		justify-content: center;
		padding: 8rpx 16rpx;

		.plus-btn {
			width: 48rpx;
			height: 96rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;

			.btn-img {
				font-size: 48rpx;
			}
		}

		.btn-line {
			width: 80rpx;
			height: 1rpx;
			border-width: 1rpx;
			border-style: solid;
			border-color: #EBEDF6;
		}

		.btn-img {
			font-size: 48rpx;
		}
	}

	.cover-view-loca {
		width: 80rpx;
		height: 80rpx;
		background-color: #ffffff;
		border-radius: 16rpx;
		position: absolute;
		z-index: 10;
		bottom: 208rpx;
		right: 32rpx;
		display: flex;
		align-items: center;
		justify-content: center;

		.cover-loca-image {
			width: 40rpx;
			height: 40rpx;
			background-color: transparent;
			border: none;
			padding: 0;
			line-height: 40rpx;

			&::after {
				border: none;
				width: 40rpx;
				height: 40rpx;
				transform: scale(1);
			}

			.loca-btn {
				width: 40rpx;
				height: 40rpx;
			}
		}
	}

	.cover-view-menu {
		position: absolute;
		bottom: 0;
		background-color: #FBFBFB;
		height: 350rpx;

		z-index: 10;
		&.bottom-menu {
			height: 176rpx;
		}

		&.baselayer,
		&.layer {
			height: 320rpx;
			width: 100%;
			padding: 0 32rpx;
			border-radius: 16rpx 16rpx 0rpx 0;

			.cover-main {
				display: flex;
				justify-content: space-between;
				flex-wrap: nowrap;
				height: 200rpx;

				.item {
					width: calc(50% - 20rpx);
					height: 100%;
					position: relative;

					.item-image {
						width: 100%;
						height: 100%;
						border-radius: 8px;
					}

					.item-text {
						background: rgba(255, 255, 255, 0.8);
						border-radius: 0px 0px 8px 8px;
						width: 100%;
						position: absolute;
						bottom: 0;
						left: 0;
						height: 48rpx;
						line-height: 48rpx;
						padding: 0 20rpx;
						font-size: 24rpx;
					}
				}
			}
		}

		&.baselayer {
			height: 320rpx;
		}

		&.layer {
			height: 800rpx;
			overflow: hidden;

			.menu-list {
				height: 700rpx;
			}
		}

		.cover-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			height: 86rpx;

			.title {
				text-align: left;
				word-break: keep-all;
			}

			.icon {
				font-size: 1.2em;
			}
		}

		&.layer {}

		&.area {}

		&.distance {}

		transition: all 0.5s ease-in-out;
	}

	.bottom-menu {}

	.border-box {
		width: 100%;
		padding-top: 30rpx;
		background-color: #FBFBFB;
		position: absolute;
		bottom: 0;
		align-items: center;
		justify-content: space-around;
		border-radius: 16rpx 16rpx 0rpx 0;

		.menu-list {
			flex-direction: column;
			display: flex;
			justify-content: space-around;
			width: 100%;

			.layer-box {
				width: 100%;
				padding: 0 32rpx 25rpx;

				.layer-title {
					color: #91949F;
				}

				.layer-menu {
					display: flex;
					justify-content: flex-start;
					align-items: center;
					flex-wrap: wrap;
					border-radius: 8px;
					background-color: #ffffff;
					margin-top: 20rpx;
					padding-bottom: 20rpx;
				}
			}
		}
	}
}

.menu-item {
	width: 20%;
	align-items: center;
	padding: 0;
	text-align: center;
	display: flex;
	justify-content: center;
	flex-direction: column;

	.icon-bg {
		border-radius: 50%;
		width: 80rpx;
		height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: center;

		&.round-rect {
			border-radius: 16rpx;
		}
	}

	.layer-menu-text {
		word-break: keep-all;
	}
}

:deep(.esri-view .esri-view-surface--inset-outline) {
	&:focus::after {
		display: none;
	}
}

// 添加图层相关样式
.custom-div-icon {
	background: transparent;
	border: none;

	.marker-pin {
		width: 12px;
		height: 12px;
		border-radius: 50%;
		border: 2px solid #fff;
		box-shadow: 0 0 4px rgba(0,0,0,0.3);
	}
}
</style>