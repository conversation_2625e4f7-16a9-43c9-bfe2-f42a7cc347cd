import { http } from '../vmeitime-http/index'


export const getStationDynamicRealtimeData = (params: {
	stationType?: String
	projectId: String
}) => {
	return http().get('istar/api/station/data/detailList', params)
}

// 近三天的数据
export const getThreeDaysData = (params: {
	attr: String
	deviceId: String
}) => {
	return http().get('istar/api/station/data/getThreeDaysData', params)
}

// 设备监测历史数据
export const getDeviceData = (params: {
	attributes: String[],
	start: number,
	end: number,
	type: String
}) => {
	return http().post('istar/api/device/data', params)
}

// 查询当前账号所有根项目
export const getProjectRoot = (devices?: boolean) => {
	let params = false
	if (devices) {
		params = true
	}
	return http().get(`api/project/root?devices=${params}`, params)
}

// 查询报警列表
export const getStationAlarm = (stationId: String, params: {
	start: number,
	end: number
}) => {
	return http().get(`api/alarm/station/${stationId}`, params)
}

// 查询报警列表
export const getStationRealTimeDetail = (stationId: String, params?: {
	type?: String
}) => {
	return http().get(`istar/api/station/data/detail/${stationId}`, params)
}


// 监测站属性列表
export const getStationAttrList = (params: {
	stationId: String
}) => {
	return http().get('api/station/stationAttrList', params)
}

// 查询指定类型的站点实时数据列表
export const getStationDetailList = (params: {
	stationType?: String
	projectId: String
}) => {
	return http().get('istar/api/station/data/detailList/view', params)
}

// 获取设备属性列表
export const getDeviceVarGroup = (templateId:String) => {
	return http().get(`api/deviceTemplate/protocol/group/${templateId}`,)
}


// 获取水质
export const getDataCompare = (params: {
	queryType?: String
	attributes:String,
	start: number,
	end:number
}) => {
	return http().get(`istar/api/station/data/getDataCompare`,params)
}
