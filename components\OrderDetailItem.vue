<template>
  <view class="info-item">
    <text class="label">{{ label }}：</text>
    <text class="value">{{ value }}</text>
  </view>
</template>

<script setup>
defineProps({
  label: {
    type: String,
    default: "",
  },
  value: {
    type: String,
    default: "",
  },
});
</script>

<style lang="scss" scoped>
.info-item {
  display: flex;
  margin-bottom: 20rpx;
}

.info-item .label {
  width: 200rpx;
  color: #91949f;
}
</style>
