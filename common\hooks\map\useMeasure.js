import {
	loadEsriModules
} from "../../utils/arcMapHelper"

export const useMeasure = () => {
	let DistanceMeasurement2DViewModel = undefined
	let AreaMeasurement2DViewModel = undefined
	let measure = undefined
	let watcher = undefined
	const init = async () => {
		[DistanceMeasurement2DViewModel, AreaMeasurement2DViewModel] = await loadEsriModules([
			"esri/widgets/DistanceMeasurement2D/DistanceMeasurement2DViewModel",
			"esri/widgets/AreaMeasurement2D/AreaMeasurement2DViewModel"
		])
	}
	const start = (view, type = 'area', autoRestart = true) => {
		measure?.clear()
		measure = type === 'area' ? new AreaMeasurement2DViewModel({
			view: view
		}) : new DistanceMeasurement2DViewModel({
			view: view
		})
		watcher = measure.watch("viewModel.state", function(state) {
			console.log("Current state: ", state);
		});
		measure.start()
	}
	const clear = () => {
		watcher?.remove()
		measure?.clear()
	}
	return {
		start,
		init,
		clear
	}
}
