import { computed, ref } from 'vue'
export const useOneMapMenus = () => {
	const menus = ref<{
		name : string
		id : string
		isActive ?: boolean
		list : { name : string, alias ?: string, icon : string, id : string, type : string, isActive ?: boolean, color ?: string }[]
	}[]>([
		{
			name: '设备监控',
			id: 'sssb',
			list: [
				{
					id: 'sssb_sc',
					name: '水厂',
					icon: 'custom-icon-GIS-tulitucengicon_shuichang',
					type: 'sssb'
				}, 
        {
					id: 'sssb_llj',
					name: '测流压站,流量监测站',
					alias: '流量计',
					icon: 'custom-icon-liuliang1',
					type: 'sssb'
				}, 
        {
					id: 'sssb_ylj',
					name: '测流压站,压力监测站',
					alias: '压力计',
					icon: 'custom-icon-kongxishuiyaliji',
					type: 'sssb'
				}, 
				{
					id: 'sssb_sz',
					name: '水质监测站',
					alias: '水质',
					icon: 'custom-icon-shuizhijiance',
					type: 'sssb'
				}, 
				// {
				// 	id: 'sssb_dyh',
				// 	name: '大用户',
				// 	icon: 'custom-icon-yonghu2',
				// 	type: 'sssb'
				// }, 
				// {
				// 	id: 'sssb_ecgs',
				// 	name: '泵站',
				// 	alias: '泵站',
				// 	icon: 'custom-icon-shuibengfang',
				// 	type: 'sssb'
				// }
			]
		},
		{
			id: 'sbzc',
			name: '设备资产',
			list: [
				{
					color: 'linear-gradient(180deg, #478FF8 0%, #6CA5FE 100%)', id: 'sbzc_gw', name: '管网管线', icon: 'custom-icon-guanxianbiaozhu', type: 'sbzc'
				},
				{ color: 'linear-gradient(180deg, #478FF8 0%, #6CA5FE 100%)', id: 'sbzc_fm', name: '阀门', icon: 'custom-icon-famen', type: 'sbzc' },
				{ color: 'linear-gradient(180deg, #00D4A3 0%, #00E1AC 100%)', id: 'sbzc_sb', name: '水表', icon: 'custom-icon-shuibiao', type: 'sbzc' },
				{ color: 'linear-gradient(180deg, #FE6670 0%, #FE7F86 100%)', id: 'sbzc_xfs', name: '消防栓', icon: 'custom-icon-xiaofangshuan', type: 'sbzc' }
			]
		},
		{
			id: 'rycl',
			name: '人员车辆',
			list: [
				{ color: 'linear-gradient(180deg, #F89C47 0%, #FFBF84 100%)', id: 'rycl_xjry', name: '巡检人员', icon: 'custom-icon-xunjianchulirenyuan', type: 'rycl' },
				{ color: 'linear-gradient(180deg, #F89C47 0%, #FFBF84 100%)', id: 'rycl_qxry', name: '抢修人员', icon: 'custom-icon-weixiugongguanli', type: 'rycl' },
				{ color: 'linear-gradient(180deg, #F89C47 0%, #FFBF84 100%)', id: 'rycl_cby', name: '抄表员', icon: 'custom-icon-zhuanyerenyuan', type: 'rycl' },
				{ color: 'linear-gradient(180deg, #478FF8 0%, #6CA5FE 100%)', id: 'rycl_cljk', name: '车辆监控', icon: 'custom-icon--cheliang', type: 'rycl' }
			]
		},
		{
			id: 'ywlc',
			name: '业务流程',
			list: [
				{ color: 'linear-gradient(180deg, #953CFF 0%, #B271FF 100%)', id: 'ywlc_gdlc', name: '工单流程', icon: 'custom-icon-gongdan', type: 'ywlc' },
				{ color: 'linear-gradient(180deg, #FE6670 0%, #FE7F86 100%)', id: 'ywlc_tsrx', name: '投诉热线', icon: 'custom-icon-yuanqufuwurexian', type: 'ywlc' },
				{ color: 'linear-gradient(180deg, #953CFF 0%, #B271FF 100%)', id: 'ywlc_xjyh', name: '巡检养护', icon: 'custom-icon-shouye', type: 'ywlc' },
				{ color: 'linear-gradient(180deg, #00D4A3 0%, #00E1AC 100%)', id: 'ywlc_yhbz', name: '用户报装', icon: 'custom-icon-baozhuanglihu', type: 'ywlc' }
			]
		},
		{
			id: 'sjcj',
			name: '数据场景',
			list: [
				{ color: 'linear-gradient(180deg, #478FF8 0%, #6CA5FE 100%)', id: 'sjcj_slrd', name: '水量热点', icon: 'custom-icon-shuiliang', type: '' },
				{ color: 'linear-gradient(180deg, #F89C47 0%, #FFBF84 100%)', id: 'sjcj_sjrd', name: '事件热点', icon: 'custom-icon-shijian1', type: '' },
				{ color: 'linear-gradient(180deg, #00D4A3 0%, #00E1AC 100%)', id: 'sjcj_dmafq', name: 'DMA分区', icon: 'custom-icon-LPARfenqu', type: '' },
				{ color: 'linear-gradient(180deg, #478FF8 0%, #6CA5FE 100%)', id: 'sjcj_ylrd', name: '压力热点', icon: 'custom-icon-yali', type: '' }
			]
		}
	])
	const current = ref<string>('')
	const curParent = ref<string>('')
	const showRootMenuCount = ref<number>(1)
	const setCurrent = (value : string, pId : string) => {
		if (current.value === value) {
			current.value = ''
			curParent.value = ''
		} else {
			current.value = value
			curParent.value = pId
		}
	}
	const hasSelectMenu = () => {
		return current.value !== ''
	}
	const curMenu = computed(() => {
		return menus.value.find(item => item.id === curParent.value)?.list.find(item => item.id === current.value)
	})
	/**
	 * 过滤显示的菜单数量，默认只显示第一个菜单列表，上滑显示全部
	 */
	const setRootMenuCount = (count : number) => {
		showRootMenuCount.value = count
	}
	/**
	 * 处理多选时的选中,配合各元素的isActive属性使用
	 */
	const toggleMenu = (pId : string, id : string, open ?: boolean) => {
		const menu = menus.value.find(item => item.id === pId)?.list.find(item => item.id === id)
		menu && (menu.isActive = open === undefined ? !menu.isActive : !!open)
	}
	return {
		menus,
		curMenu,
		current,
		setCurrent,
		toggleMenu,
		showRootMenuCount,
		setRootMenuCount,
		hasSelectMenu
	}
}