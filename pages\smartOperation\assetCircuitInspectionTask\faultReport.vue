<template>
	<view class="i-main">
		<view style="position: absolute;">
			<u-navbar :border-bottom="false" :background="{backgroundColor: state.navBgColor}" title="故障上报" is-back
				:titleColor="state.navColor" :back-iconColor="state.navColor">
			</u-navbar>
		</view>
		<view class="bg">
		</view>
		<view class="detail">
			<u-form :model="form" ref="refForm" :label-style="{'color':'#91949F'}" borderBottom labelWidth="180"
				input-align="right" :errorType="['toast']">
				<view class="card-box">
					<u-form-item label="事件标题：" required prop="title" borderBottom>
						<u-input placeholder="请输入标题" v-model="form.title">
						</u-input>
					</u-form-item>

					<u-form-item label="事件来源：" required prop="source" borderBottom>
						<template #right>
							<u-icon name="arrow-right" size="28"></u-icon>
						</template>
						<input v-model="form.source" inputmode="none" placeholder-class="placeholderClass"
							placeholder="请选择事件来源" @click="clickShow('sourceIndex','sourceShow')">
						</input>
					</u-form-item>

					<u-form-item label="事件类型：" required prop="type" borderBottom>
						<template #right>
							<u-icon name="arrow-right" size="28"></u-icon>
						</template>
						<input v-model="form.type" inputmode="none" placeholder-class="placeholderClass"
							placeholder="请选择事件类型" @click="clickShow('typeIndex','typesShow')">
						</input>
					</u-form-item>


					<!-- <u-form-item label="事件内容：" prop="form.val2" borderBottom @click="statusShow=true">
						<template #right>
							<u-icon name="arrow-right" size="28"></u-icon>
						</template>
						<input v-model="form.val2" inputmode="none" placeholder-class="placeholderClass"  placeholder="请选择事件内容">
						</input>
					</u-form-item> -->

					<!-- <u-form-item label="事件地址：" required prop="address" borderBottom>
						<u-input v-model="form.address" placeholder="请输入事件地址" input-align="right">
						</u-input>
						<template #right>
							<image src="/static/img/icons/location.png" @click="locaton"
								style="margin-bottom:20rpx;height:36rpx;width:36rpx">
							</image>
						</template>
					</u-form-item>
					<u-form-item label="" borderBottom>
						<view style="width: 100%; height: 80px;">
							<map v-show="!mapShow" style="width: 100%; height: 80px;" id="maps" :controls="controls"
								:latitude="state.latitude" :longitude="state.longitude" @regionchange="regionchange">
							</map>
						</view>
					</u-form-item> -->
					<u-form-item label="事件地址：" required prop="address" borderBottom>
						<u-input v-model="form.address" placeholder="请输入事件地址" input-align="right" @blur="changeAddress">
						</u-input>
						<template #right>
							<view @click="chooseAddress" class="dw">
								<image src="/static/img/icons/dw.png" style="height:40rpx;width:40rpx">
								</image>
							</view>
						</template>
					</u-form-item>

					<u-form-item label="详细描述：" required borderBottom>
					</u-form-item>
					<u-form-item label="" prop="remark" borderBottom>
						<u-input type="textarea" input-align="left"  v-model="form.remark" placeholder="请输入详细描述"></u-input>
					</u-form-item>


					<!-- 
					<u-form-item label="处理级别：" required prop="processLevelLabel" borderBottom>
						<template #right>
							<u-icon name="arrow-right" size="28"></u-icon>
						</template>
						<input v-model="form.processLevelLabel" inputmode="none" placeholder-class="placeholderClass"  placeholder="请选择处理级别"
							@click="clickShow('processLevelIndex','processLevelShow')">
						</input>
					</u-form-item>


					<u-form-item label="处理时长：" required prop="processLevelLabel" borderBottom @click="statusShow=true">
						<u-input v-model="form.processLevelLabel" disabled placeholder="处理时长">
						</u-input>
					</u-form-item> -->

					<u-form-item label="故障类别：" prop="faultTypeName" borderBottom>
						<template #right>
							<u-icon name="arrow-right" size="28"></u-icon>
						</template>
						<input v-model="form.faultTypeName" inputmode="none" placeholder-class="placeholderClass"
							placeholder="请选择事件类型" @click="clickShow('faultTypeIndex','faultTypeShow')">
						</input>
					</u-form-item>
					<u-form-item label="故障项目：" prop="faultProject" borderBottom>
						<u-input placeholder="请输入故障项目" v-model="form.faultProject">
						</u-input>
					</u-form-item>
					<u-form-item label="故障描述：" borderBottom>
					</u-form-item>
					<u-form-item label="" prop="faultRemark" borderBottom>
						<u-input type="textarea" input-align="left"  v-model="form.faultRemark" placeholder="请输入故障描述"></u-input>
					</u-form-item>
				</view>

				<view class="card-box">
					<u-form-item label="直接分派：" prop="isDirectDispatch" borderBottom>
						<template #right>
							<u-switch v-model="form.isDirectDispatch" activeColor="#3862F8"></u-switch>
						</template>
					</u-form-item>

					<u-form-item label="处理人员：" prop="processUserId" borderBottom v-if="form.isDirectDispatch">
						<template #right>
							<u-icon name="arrow-right" size="28"></u-icon>
						</template>
						<input v-model="form.processUserName" inputmode="none" placeholder-class="placeholderClass"
							placeholder="请选择" @click="toChooseUser">
						</input>
					</u-form-item>

					<!-- <u-form-item label="备注：" prop="form.val2" borderBottom>
						<u-input v-model="form.val2"  inputmode="none" placeholder-class="placeholderClass" placeholder="请输入备注">
						</u-input>
					</u-form-item> -->

					<!-- <u-form-item label="完成时间：" prop="form.val4" borderBottom>
						<template #right>
							<u-icon name="calendar" size="34"></u-icon>
						</template>
						<input v-model="form.time" readonly inputmode="none" placeholder-class="placeholderClass"  placeholder="完成时间">
						</input>
					</u-form-item> -->
				</view>
			</u-form>

			<view class="card-box" style="padding: 0">
				<file-upload ref="refFileUpload"></file-upload>
			</view>
		</view>
		<view class="button">
			<u-button type="primary" color="#3862F8" @click="submit">提交</u-button>
		</view>
		<u-action-sheet @close="statusShow=false" @select="chooseStatus" closeOnClickOverlay closeOnClickAction
			title="处理结果" v-model="statusShow" keyname :actions="statusList">
		</u-action-sheet>
		<!-- 来源 -->
		<u-picker v-model="state.sourceShow" mode="selector" :default-selector="[0]" :range="sourceTypes"
			range-key="name" @confirm="selectSource"></u-picker>
		<!-- 类型 -->
		<u-picker v-model="state.typesShow" mode="selector" :default-selector="[0]" :range="workOrderTypes"
			range-key="name" @confirm="selectType"></u-picker>
		<!-- 故障类别 -->
		<u-picker v-model="state.faultTypeShow" mode="selector" :default-selector="[0]" :range="faultTypes"
			range-key="name" @confirm="selectFaultType"></u-picker>
		<!-- 处理级别 -->
		<u-picker v-model="state.processLevelShow" mode="selector" :default-selector="[0]" :range="processLevels"
			range-key="name" @confirm="selectProcessLevel"></u-picker>
		<u-toast ref="refToast"></u-toast>
	</view>
</template>

<script lang="ts" setup>
	import {
		onMounted,
		reactive,
		computed,
		ref,
		onBeforeMount
	} from "vue";
	import {
		useStore
	} from '@/store/index'
	import {
		onReady,
		onShow
	} from '@dcloudio/uni-app';
	import {
		onPageScroll
	} from '@dcloudio/uni-app'
	import fileUpload from '@/components/fileUpload/fileUpload.vue'
	import {
		faultReport
	} from '@/common/api/inspection'
	import {
		workOrderTypeList,
		workOrderResourceList,
		workOrderProcessLevelList,
		getFaultKnowledgeValue
	} from '@/common/data/workOrderData'
	import {
		queryGeocoder
	} from '@/common/api/map'
	import {
		removeSlash
	} from "@/common/utils/removeIdSlash";
	import {
		storeToRefs
	} from 'pinia'
	const store = useStore();
	// 监听页面滑动距离顶部
	onPageScroll(e => {
		state.scrollTop = e.scrollTop
		if (e.scrollTop < 65) {
			state.navColor = '#FFFFFF'
			state.navBgColor = 'transparent'
		} else {
			state.navColor = '#000000'
			state.navBgColor = '#FFFFFF'
		}
	})
	const state = reactive<{
		status : any,
		scrollTop : Number,
		navColor : string,
		navBgColor : string,
		latitude : Number,
		longitude : Number,
		sourceShow : boolean,
		sourceIndex : any,
		typesShow : boolean,
		typeIndex : any,
		faultTypeIndex : any,
		processLevelShow : boolean,
		faultTypeShow : boolean,
		processLevelIndex : any,
		deviceLabelCode : string
	}>({
		deviceLabelCode: '',
		typesShow: false,
		processLevelShow: false,
		faultTypeShow: false,
		sourceShow: false,
		sourceIndex: [0],
		processLevelIndex: [0],
		faultTypeIndex: [0],
		typeIndex: [0],
		longitude: 0,
		latitude: 0,
		status: {
			aa: {
				label: '待巡检',
				color: '#F8A038',
				bgColor: 'rgba(248, 160, 56, 0.2)'
			},
			bb: {
				label: '合格',
				color: '#2EE740',
				bgColor: 'rgba(46, 231, 64, 0.2)'
			},
			cc: {
				label: '不合格',
				color: '#F83838',
				bgColor: 'rgba(248, 56, 56, 0.2)'
			},
		},
		navColor: '#FFFFFF',
		navBgColor: 'transparent',
		scrollTop: 0
	})
	const controls = ref<any>([{
		id: '1',
		iconPath: '../../../static/img/icons/dw.png',
		position: { //控件在地图的位置
			left: 140,
			top: 30,
			width: 20,
			height: 20,
		},
	}])
	const rules = reactive<any>({
		title: [{
			required: true,
			message: '请输入事件标题',
		}],
		source: [{
			required: true,
			message: '请选择事件来源',
		}],
		type: [{
			required: true,
			message: '请选择事件类型',
			trigger: ['change', 'blur'],
		}],
		level: [{
			required: true,
			message: '请选择紧急程度',
			trigger: ['change', 'blur'],
		}],
		address: [{
			required: true,
			message: '请输入地址',
			trigger: ['change', 'blur'],
		}],
		remark: [{
			required: true,
			message: '请输入详细描述',
			trigger: ['change', 'blur'],
		}],
		processLevel: [{
			required: true,
			message: '处理级别',
			trigger: ['change', 'blur'],
		}]
	})
	const refToast = ref<any>()
	const processLevels = ref<any>()
	const refForm = ref<any>({})
	const workOrderTypes = ref<any>([])
	const refFileUpload = ref<any>({})
	const statusShow = ref<boolean>(false)
	const sourceTypes = ref<any>([])
	const faultTypes = ref<any>([])
	const statusList = reactive<any>([{
		value: '1',
		name: '合格'
	}, {
		value: '2',
		name: '不合格'
	}, {
		value: '3',
		name: '其他'
	},])

	const mapShow = computed(() => {
		return state.typesShow ||
			state.sourceShow ||
			statusShow.value ||
			state.processLevelShow ||
			state.faultTypeShow
	})
	const form = reactive<any>({
		isDirectDispatch: false,
		address: '',
		faultProject: '资产保养',
		processUserName: ''
	})

	const clickShow = (indexName, typeName) => {
		state[indexName] = [0]
		state[typeName] = true
	}
	const chooseAddress = () => {
		uni.navigateTo({
			url: '/pages/map/tianMap'
		})
	}
	const markers = reactive<any>([{
		id: 0,
		display: 'ALWAYS',
		latitude: 34.79977, //纬度
		longitude: 113.66072, //经度
		iconPath: '', //显示的图标        
		rotate: 0, // 旋转度数
		width: 10, //宽
		height: 20, //高
		title: '我在这里', //标注点名
		alpha: 0.5, //透明度
		label: { //为标记点旁边增加标签   //因背景颜色H5不支持
			content: 'Hello,I am here', //文本
			color: 'red', //文本颜色
		},
		callout: { //自定义标记点上方的气泡窗口 点击有效  
			content: '硅谷广场B座', //文本
			color: '#ffffff', //文字颜色
			fontSize: 14, //文本大小
			borderRadius: 2, //边框圆角
			bgColor: '#00c16f', //背景颜色
			display: 'ALWAYS', //常显
		},

	}])
	const toChooseUser = () => {
		uni.$u.route({
			url: 'pages/userTree/organization',
		})
	}

	// 新增抄送人员
	const chooseUser = (user : any) => {
		console.log(user)
		form.processUserId = removeSlash(user.id?.id)
		form.processUserName = user.firstName
	}

	// 选择紧急程度
	const selectProcessLevel = (val : any) => {
		state.processLevelIndex = val
		const processLevel = processLevels.value[val[0]]
		form.processLevelLabel = processLevel.name
		form.level = processLevel.dayTime * 1440 + processLevel.hourTime * 60 + processLevel.minuteTime
		// form.level = processLevel.value
	}
	// 选择事件来源
	// 选择事件来源
	const selectSource = (val : any) => {
		state.sourceIndex = val
		const type = sourceTypes.value[val[0]]
		form.source = type.name
	}

	// 选择事件类型
	const selectType = (val : any) => {
		state.typeIndex = val
		const type = workOrderTypes.value[val[0]]
		form.type = type.name
	}

	const selectFaultType = (val : any) => {
		state.faultTypeIndex = val
		const type = faultTypes.value[val[0]]
		form.faultTypeId = type.value
		form.faultTypeName = type.name
	}


	// 选择处理状态
	const chooseStatus = (item : any) => {
		console.log(item)
		form.val2 = item.name
	}
	
	const changeAddress = () => {
		queryGeocoder({ keyWord: form.address }).then((res : any) => {
			const location = res.data?.location
			state.latitude = location.lat
			state.longitude = location.lon
		})
	}
	// const locaton = () => {
	// 	uni.getLocation({
	// 		type: 'gcj02',
	// 		altitude: true,
	// 		geocode: true,
	// 		isHighAccuracy: true,
	// 		success: (info) => {
	// 			console.log(info)
	// 			state.latitude = info.latitude
	// 			state.longitude = info.longitude
	// 			form.address = info.address.street + info.address.streetNum
	// 		},
	// 		fail: () => {
	// 			uni.$u.toast('获取定位失败')
	// 		}
	// 	})
	// }

	const regionchange = (event) => {
		console.log('regionchange', event)
		if (event.type == 'onRegionchange') {
			getCenterLanLat()
		} else if (event.type == 'end') {
			getCenterLanLat()
		}
	}

	// 获取当前地图中心的经纬度
	const getCenterLanLat = () => {
		const mapContext = uni.createMapContext("maps", this)
		mapContext.getCenterLocation({
			success: (res) => {
				var point = new plus.maps.Point(res.longitude, res.latitude)
				plus.maps.Map.reverseGeocode(point, {}, (info : any) => {
					state.latitude = info.coord.latitude
					state.longitude = info.coord.longitude
					form.address = info.address
				})
			},
			fail: (err) => {
				console.log('获取当前地图中心的经纬度', err)
			}
		})
	}

	//提交工单
	const submit = () => {
		refForm.value.validate(valid => {
			if (valid) {
				uni.showModal({
					title: '提示',
					content: '确定提交',
					success: function (res) {
						if (res.confirm) {
							const params = {
								faultReportCList: [{
									deviceLabelCode: state.deviceLabelCode
								}],
								title: form.title,
								faultTypeId: form.faultTypeId,
								faultRemark: form.faultRemark,
								faultProject: form.faultProject,
								workOrder: {
									...form,
									videoUrl: refFileUpload.value.videoList.join(','),
									audioUrl: refFileUpload.value.voiceList.join(','),
									imgUrl: refFileUpload.value.imageList.join(','),
									otherFileUrl: refFileUpload.value.fileList.join(','),
									// coordinate: state.latitude + ',' + state.longitude,
								}
							}
							console.log(params)
							faultReport(params).then(res => {
								if (res.data?.code === 200) {
									let {
										userData
									} = storeToRefs(store);
									userData.value = null
									refToast.value.show({
										title: '提交成功',
										type: 'success',
										position: 'bottom',
										callback: () => {
											uni.navigateBack({
												delta: 1
											})
										}
									})
								} else {
									refToast.value.show({
										title: res.data?.err,
										position: 'bottom',
										type: 'error'
									})
								}
							}).catch(err => {
								console.log(err)
								refToast.value.show({
									title: err,
									position: 'bottom',
									type: 'error'
								})
							})
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			} else {
				console.log('验证失败');
			}
		})
	}
	
	
	const onLoadUpdate = item => {
		form.address = item.address
		form.coordinate = item.coordinate.lat + ',' + item.coordinate.lon
	}
	
	onMounted(async () => {
		var pages = getCurrentPages();
		var page = pages[pages.length - 1];
		state.deviceLabelCode = page.$page?.options.deviceLabelCode
	})
	
	onBeforeMount(async () => {
		workOrderTypes.value = await workOrderTypeList()
		sourceTypes.value = await workOrderResourceList()
		processLevels.value = await workOrderProcessLevelList()
		faultTypes.value = await getFaultKnowledgeValue()
	})
	onShow(async () => {
		// let {
		// 	userData
		// } = store;
		// if (userData) {
		// 	chooseUser(userData)
		// }
		uni.$on("updateLocation", onLoadUpdate);
		uni.$on("chooseUserData", chooseUser);
	})
	onReady(() => {
		refForm.value.setRules(rules);
	})
</script>

<style lang="scss" scoped>
	.i-main {
		padding-top: 0;
		background: #F9F9F9;
		font-size: 24rpx;
		color: #000000;
	}

	.bg {
		width: 100%;
		height: 480rpx;
		background: linear-gradient(117.58deg, #3862F8 0%, #6AA6FF 100%);
	}

	.detail {
		top: -260rpx;
		position: relative;

		// background-color: #F9F9F9;
		.card-box {
			width: 686rpx;
			margin: 0 auto;
			margin-top: 10px;
			border-radius: 16rpx;
			padding: 12rpx 28rpx;
			background-color: #FFFFFF;
			min-height: 112rpx;

			.icon {
				color: #3862F8;
			}

		}

		.content-card {
			width: 686rpx;
			margin: 20rpx auto;
			background-color: #FFFFFF;
			min-height: 224rpx;
			border-radius: 8px;
			padding: 24rpx 0 0 28rpx;

			text {
				color: #91949F;
				font-size: 28rpx;
			}

			.camera {
				width: 112rpx;
				height: 112rpx;
				margin-top: 20rpx;
				display: flex;
				justify-content: center;
				flex-direction: column;
				text-align: center;
				background: #F9F9F9;

				text {
					color: #91949F;
					font-size: 20rpx;
				}
			}

			.margin-center {
				margin: 0 auto;

				.icon {
					font-size: 48rpx;
				}
			}
		}
	}

	.map {
		height: 80rpx;
		width: 100%;
	}

	::v-deep .u-form-item__body {
		padding: 16rpx;
	}

	::v-deep .u-form-item {
		padding: 0;
	}
</style>