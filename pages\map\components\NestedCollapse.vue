<template>
	<NestCollapse v-for="(item,i) in props.data" :title="item[defaultProps.title]" :key="i" :default-expand="true" :icon="iconOfTree[item.labelOfTree]"
		:showArrow="!!item[defaultProps.children]?.length">
		<template class="nest-content" #content>
			<nested-collapse v-if="!!item[defaultProps.children]?.length" :data="item[defaultProps.children]"
				:item-props="props.itemProps"></nested-collapse>
		</template>
	</NestCollapse>
</template>

<script lang="ts" setup>
	import { computed, watch } from "vue";
	import NestCollapse from '@/components/NestCollapse.vue'
	const props = defineProps<{ data : any[]; itemProps ?: { title ?: string; name ?: string; children ?: string };icon?: string }>()
	const defaultProps = computed(() => {
		return {
			title: props.itemProps?.title || 'title',
			name: props.itemProps?.name || 'name',
			children: props.itemProps?.children || 'children'
		}
	})
	const iconOfTree = {
		Organization: '',
		Department: '',
		User: 'account'
	}
	watch(() => props.data, () => {
		setTimeout(() => {

		})
	})
</script>

<style lang="scss" scoped>
	.collapse-value {
		display: block;
		height: 100vh;
	}
</style>