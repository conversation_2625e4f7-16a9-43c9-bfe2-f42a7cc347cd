import { createRequest } from '../vmeitime-http/interface'
import { http as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '../vmeitime-http'
import { gisConfig } from '@/common/data/gisData'

const http = (defaultConfig ?: {
	config ?: {
		baseUrl ?: string
		dataType ?: string
		header ?: any
	}

	interceptor ?: {
		request ?: (config : any) => any
		response ?: (response : any) => any
	}
}) => {
	const request = createRequest(defaultConfig.config)
	// if (defaultConfig?.config) {
	// 	request.config = {
	// 		...request.config,
	// 		...(defaultConfig.config || {
	// 			header: {}
	// 		})
	// 	}
	// }
	if (defaultConfig?.interceptor?.request) {
		request.interceptor.request = defaultConfig.interceptor.request
	}
	if (defaultConfig?.interceptor?.response) {
		request.interceptor.response = defaultConfig.interceptor.response
	}
	return request
}
const config = ()=>{
	return {
		header: {},
		baseUrl: gisConfig().gisPipeDynamicService,
	}
} 
const apiConfig = ()=>{
	return {
		config: { baseUrl: gisConfig().gisApi },
		interceptor: {
			request: (config : any) => {
				const gToken = uni.getStorageSync('gToken')
				if (gToken) {
	
					config.header = {
						...(config.header || {}),
						'Authorization-Token': gToken
					}
				}
				return config
			}
		}
	}
} 
export const getGtoken = async () => {
	let token = uni.getStorageSync('gToken')
	if (!token) {
		try {
			const res = await http(apiConfig()).post('api/webapp/login', {
				username: '0000303',
				password: '111111'
			})
			token = res.data?.result?.token
			if (res.data?.code === 10000) {
				uni.setStorageSync('gToken', res.data?.result?.token)
			}
		} catch (e) {
			//TODO handle the exception
			console.log(e);
		}

	}
	return token
}
/**
 * 管网错误属性上报
 */
export const AddMapErrorUpload = async (params : {
	layer : string
	fid : string
	uploadContent : string
	remark: string
}) => {
	return ShuiWuApi().post('api/gis/exceptionUpload/save', params)
}
/**
 * 查询上报记录
 */
export const GetMapErrorRecord = async(params: {page: number;size: number;uploadUser?: string})=>{
	return ShuiWuApi().get('api/gis/exceptionUpload/list',params)
}
/**
 * 按年份统计管长
 */
export const getPipeByBuildYear = async () => {
	console.log(apiConfig());
	return http(apiConfig()).post('api/gis/PipeByBuildYear')
}
/**
 * 获取图层信息
 * @params layerids 要获取的图层id数组
 */
export const getLayerInfo = async (layerids : number[]) => {
	const token = await getGtoken()
	const ids = JSON.stringify(layerids)
	console.log(token,'token-------')
	console.log(ids,'idssss-------')
	return http({ config: config() }).get(`/exts/TFGeoAPISOE/getLayerInfor?f=pjson&usertoken=${token}&layerids=${ids}`)
}
/**
 * 获取管网图例信息
 */
export const getPipeLegends= async() => {
	return http({config: config()}).get(`/legend?f=pjson`)
}
/**
 * 统计管线
 * @param params
 * @returns
 */
export const PipeStatistics = (params : {
	layerids ?: string
	group_fields ?: string
	statistic_field ?: string
	statistic_type ?: string
	where ?: string
	geometry ?: any
	f ?: 'pjson'
}) => {
	return http({ config: config() }).get(
		'/exts/TFGeoAPISOE/statistic',
		{
			usertoken: uni.getStorageSync('gToken'),
			f: 'pjson',
			...(params || {})
		}
	)
}
/**
 * 查询管网的文件信息
 */
export const getPipeAdditionalInfo = (params : {
	layerid : number | string
	objectid : number | string
}) => {
	return ShuiWuApi().get(`api/gis/pipeAdditionalInfo/${params.layerid}/${params.objectid}`)
}
/**
 * 上传管网文件信息
 */
export const postPipeAdditionalInfo = (params : Record<string, any> & { layerid : string | number, objectid : string | number }) => {
	return ShuiWuApi().post('api/gis/pipeAdditionalInfo/save', params)
}

//地址转经纬度
export const queryGeocoder = (params: { keyWord?: string }) => {
  const paramsJson = JSON.stringify({
    ...params
  })
  const url = `http://api.tianditu.gov.cn/geocoder?ds=${paramsJson}&tk=${gisConfig().gisTdtToken}`
  return uni.request({
  	url:url,
	method:'GET'
  })
}