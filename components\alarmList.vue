<template>
	<view class="data-mian">
		<view class="title">
			报警详情
		</view>
		<view class="date">
			<u-form label-position="left" :model="form" ref="uForm" :label-style="{'color':'#91949F'}" border-bottom
				label-width="180">
				<u-form-item label="开始日期：">
					<input placeholder="选择开始日期" inputmode="none" placeholder-class="placeholderClass"
						v-model="form.start" @click="chooseDate('start')"></input>
					<template #right>
						<u-icon name="calendar" color="#91949F" size="34"></u-icon>
					</template>
				</u-form-item>
				<u-form-item label="结束日期：">
					<input placeholder="选择结束日期" inputmode="none" placeholder-class="placeholderClass" v-model="form.end"
						@click="chooseDate('end')"></input>
					<template #right>
						<u-icon name="calendar" color="#91949F" size="34"></u-icon>
					</template>
				</u-form-item>
			</u-form>
			<view style="margin-top: 10rpx;">
				<u-button type="primary" color="#3862F8" @click="submit">查询</u-button>
			</view>
		</view>
		<view class="d-content">
			<view class="">
				<view class="title-bold" style="padding-left:32rpx;">
					详情列表
				</view>
				<view class="list">
					<scroll-view scroll-x scroll-y style="height: 53vh;">
						<!-- <wyb-table first-col-bg-color="#FFFFFF" :show-vert-border="false" :default-col-width="320"
							header-bg-color="#FFFFFF" first-line-fixed ref="table" width="100%" :headers="headers"
							:contents="tableData" height="50vh" /> -->
						<uni-table ref="table" font-size="30" border stripe padding="14rpx" emptyText="暂无更多数据">
							<uni-tr>
								<uni-th align="center">
									报警时间
								</uni-th>
								<uni-th align="center">
									异常内容
								</uni-th>
							</uni-tr>
							<uni-tr class="u-tr" v-for="(item,index) in tableData" :key="index">
								<uni-td>
									{{proxy.formatTime(item.time) || '-'}}
								</uni-td>
								<uni-td>
									{{item.alarmInfo || '-'}}
								</uni-td>
							</uni-tr>
						</uni-table>
					</scroll-view>
				</view>
			</view>
		</view>
		<!-- <u-datetime-picker v-model="today" @cancel="show=false" :show="show" @close="show=false" mode="date"
			@confirm="confirmDate"></u-datetime-picker> -->
		<u-calendar v-model="show" ref="calendar" @close="show=false" @change="confirmDate">
		</u-calendar>
	</view>
</template>

<script lang="ts" setup>
	import {
		getAlarmListV2
	} from '@/common/api/alarmManage'
	import dayjs from 'dayjs'
	import {
		reactive,
		ref,
		onMounted,
		getCurrentInstance
	} from "vue";
	const {
		proxy
	} = getCurrentInstance()
	const emit = defineEmits(['onQuery']);
	const props = defineProps({
		stationId: String
	})
	const state = reactive<{
		showDateType : string
	}>({
		showDateType: ''
	})
	const today = ref(dayjs().format('YYYY-MM-DD'))
	const show = ref<boolean>(false)
	const form = reactive<any>({
		start: today.value,
		end: today.value,
	})

	const tableData = ref<any>([])

	const confirmDate = (date : any) => {
		show.value = false
		if (state.showDateType === 'start') {
			form.start = date.result
		} else {
			form.end = date.result
		}
	}

	const submit = () => {
		stationAlarmList(form.start, form.end)
	}

	const chooseDate = (type : string) => {
		show.value = true
		state.showDateType = type
	}
	//获取报警详情数据
	const stationAlarmList = async (start ?: string, end ?: string) => {
		const params = {
			startTime: dayjs(start).startOf('day').format('YYYY-MM-DD HH:MM:ss') || dayjs().startOf('day').format('YYYY-MM-DD HH:MM:ss'),
			endTime: dayjs(end).endOf('day').format('YYYY-MM-DD HH:MM:ss') || dayjs().endOf('day').format('YYYY-MM-DD HH:MM:ss'),
			page: 1,
			size: 9999,
			stationId: props.stationId
		}
		const res = await getAlarmListV2(params)
		console.log(res.data?.data.data)
		tableData.value = res.data?.data?.data
	}

	onMounted(async () => {
		await stationAlarmList()
	})
</script>

<style lang="scss" scoped>
	.data-mian {
		.title {
			width: 100%;
			text-align: center;
			line-height: 88rpx;
			font-size: 34rpx;
			color: #060F27;
		}

		.date {
			box-sizing: border-box;
			width: 686rpx;
			background: #F4F7FE;
			border-radius: 16rpx;
			margin: 0 auto;
			padding: 42rpx 32rpx;

			::v-deep .u-form-item__body {
				padding: 0;
			}
		}
	}

	.d-content {
		height: 788rpx;
		background: #FBFBFB;
		margin: 0 auto;
		border-radius: 32rpx 32rpx 0px 0px;
		padding: 24rpx 32rpx;

		.list {
			padding: 20rpx;
		}

		.ucharts {
			height: 40vh;
			width: 100%;
		}
	}

	::v-deep.u-form-item {
		padding: 0;
	}
</style>