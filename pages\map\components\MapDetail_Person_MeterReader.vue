<template>
	<view class="pipe-detail">
		<view class="pipe-detail-item pipe-detail-total">

			<view class="title">
				{{props.title}}统计
			</view>
			<view class="total-blocks">
				<view class="total-block" v-for="(item, i) in state.total" :key="i">
					<view class="value">{{item.value}} {{item.unit}}</view>
					<view class="text">{{item.title}}</view>
				</view>
			</view>
		</view>
	</view>

</template>

<script lang="ts" setup>
	import { onMounted, reactive } from 'vue'
	import { getMeterReaderStatistic } from '../../../common/api/inspection';
	const props = defineProps<{
		title ?: string
	}>()
	const state = reactive<{
		total : { value : number; title : string; unit ?: string }[],
		list : { name : string; status : string; lastTime : string }[]
	}>({
		total: [{ value: 0, title: props.title + '总数', unit: '人' }, { value: 0, title: '用户数', unit: '人' }],
		list: []
	})
	const refreshData = async () => {
		getMeterReaderStatistic().then((res : any) => {
			const data = res.data?.data || {}
			state.total[0].value = data.userCount || 0
			state.total[1].value = data.customerCount || 0
		})
	}
	onMounted(() => {
		refreshData()
	})
</script>

<style lang="scss" scoped>
	.pipe-detail {

		.pipe-detail-item {
			.title {
				font-size: 32rpx;
				margin: 32rpx 0 12rpx;
			}
		}

		.pipe-detail-total {
			.total-blocks {
				width: 100%;
				display: flex;
				padding: 24rpx;
				justify-content: flex-start;

				.total-block {
					background-color: #0073ff;
					padding: 10rpx;
					color: #fff;
					width: 280rpx;
					height: 120rpx;
					margin-right: 24rpx;
					text-align: center;

					.text,
					.value {
						line-height: 50rpx;
					}
				}
			}
		}

		.pipe-detail-chart {
			.chart-box {
				height: 480rpx;
			}
		}

	}
</style>