<!-- 工单协作列表 -->
<template>
	<view class="main">
		<u-navbar fixed placeholder safeAreaInsetTop bgColor="#FFFFFF" title="工单协作" :autoBack="true" leftIconSize="20">
			<template #right>
				<view class="nv-right" @click="showScreen">
					<image src="/static/img/icons/shaixuan-black.png" style="height: 48rpx;width: 48rpx;"></image>
				</view>
			</template>
		</u-navbar>
		<view class="card-box" v-for="(data,index) in tableData" :key="index" @click="toDetail(data)">
			<view class="flex-between hand">
				<view class="hand-title flex-center">
					<view class="icon-box">
						<u-icon name="custom-icon-gongdan" customPrefix="custom-icon" color="#FFFFFF" size="20">
						</u-icon>
					</view>
					<text>{{data.orderName}}</text>
				</view>
				<view class="status">
					<text
						:style="{color:state.statusMap[data.status].color}">{{state.statusMap[data.status].label}}</text>
				</view>
			</view>
			<view class="table">
				<!-- <view class="info">
					<text>工单编号：</text> <text>{{data.serialNo || '-'}}</text>
				</view> -->
				<view class="info">
					<text>申请原因：</text> <text>{{data.remark}}</text>
				</view>
				<view class="info">
					<text>申请人：</text> <text>{{data.userName}}</text>
				</view>
				<view class="info">
					<text>申请时间：</text> <text>{{data.time}}</text>
				</view>
			</view>
		</view>
		<u-loadmore bg-color="F9F9F9" :status="state.status" loading-text="努力加载中" loadmore-text="加载更多"
			nomore-text="没有了更多" />
		<!-- </scroll-view> -->
		<u-popup mode="right" width="528" v-model="state.screenShow" closeOnClickOverlay overlay
			@close="state.screenShow = false">
			<view class="popup">
				<view class="screen-list">
					<u-form :model="screenForm" ref="form1" :label-style="{'color':'#91949F'}" border-bottom
						input-align="right" label-width="180">
						<u-form-item label="工单状态：" prop="statusName" :borderBottom="false">
							<input placeholder="申请状态" v-model="screenForm.statusName" inputmode="none"
								placeholder-class="placeholderClass" @click="state.statusShow=true">
							</input>
						</u-form-item>
						<u-form-item label="申请人：" prop="userName">
							<u-input placeholder="申请人" v-model="screenForm.userName" :clearable="true"
								@click="toChooseUser">
							</u-input><u-icon name="close-circle" @click="clearUser"></u-icon>
						</u-form-item>
						<u-form-item label="快速查找：" prop="keyword">
							<u-input placeholder="编号/标题/电话" v-model="screenForm.keyword">
							</u-input>
						</u-form-item>
					</u-form>
				</view>
				<view class="popup-button">
					<u-button type="primary" color="#3862F8" @click="submitScreen">确定</u-button>
				</view>
			</view>
		</u-popup>
		<view class="">
			<u-calendar :max-date="maxDate" v-model="state.dateShow" ref="calendar" @close="state.dateShow=false"
				@change="chooseDate"></u-calendar>
		</view>
		<u-select v-model="state.statusShow" :list="statusList" @confirm="chooseStatus"></u-select>
	</view>
</template>

<script lang="ts" setup>
	import {
		onMounted,
		reactive,
		ref
	} from "vue";
	import dayjs from 'dayjs'
	import {
		onShow
	} from '@dcloudio/uni-app';
	import {
		onPullDownRefresh,
		onReachBottom
	} from '@dcloudio/uni-app'
	import {
		getWorkOrderCollaborations
	} from '@/common/api/workOrder'
	import {
		maxDate
	} from '@/common/data/publicdata'
	import {
		removeSlash
	} from '@/common/utils/removeIdSlash'
	import {
		useStore
	} from '@/store/index'
	import { statusOptions, statusMap } from './data'
	const store = useStore()
	// 选择区域
	const state = reactive<{
		activceTab : any,
		dateShow : boolean,
		statusShow : boolean,
		userShow : boolean,
		status : string,
		query : any,
		screenShow : boolean,
		statusMap : any
	}>({
		activceTab: {},
		dateShow: false,
		status: 'loadmore',
		statusShow: false,
		userShow: false,
		query: {
			page: 1,
			size: 10
		},
		screenShow: false,
		statusMap: statusMap()
	})
	const statusList = ref<any>(statusOptions())
	const triggered = ref<boolean>()
	const tableData = ref<any>([])

	// 选择区域
	const screenForm = reactive<any>({
		page: 1,
		size: 10,
		status: 'PENDING',
		statusName: '审核中'
	})

	const toDetail = (params ?: any) => {
		uni.$u.route({
			url: 'pages/smartOperation/WorkOrderCollaboration/detail/index',
			params: {
				orderId: params.orderId,
				id: params.id,
				fromType: 'approval',
				status: params.status
			}
		})
	}
	const toChooseUser = () => {
		uni.$u.route({
			url: 'pages/userTree/organization',
		})
	}

	const clearUser = () => {
		screenForm.userName = ''
		screenForm.userId = ''
	}

	const chooseStatus = val => {
		screenForm.statusName = val[0].label
		screenForm.status = val[0].value
	}

	// 选择区域
	const showScreen = () => {
		state.screenShow = true
	}
	//选择日期
	const chooseDate = () => { }

	// 加载更多
	const showMoreData = async () => {
		console.log('dddddd')
		state.status = 'loading'
		await getWorkOrderList()
	} ///

	// 下拉刷新
	const onRefresh = async () => {
		triggered.value = true
		screenForm.page = 1
		await getWorkOrderList()
	}
	// 巡检列表
	const getWorkOrderList = async () => {
		state.status = 'loadmore'
		const res = await getWorkOrderCollaborations(screenForm)
		const data = res.data?.data?.data
		const total = res.data?.data?.total

		if (data.length > 0) {
			if (screenForm.page === 1) {
				tableData.value = data
			} else {
				tableData.value = tableData.value.concat(data)
			}
			screenForm.page += 1
			state.status = 'loadmore'
			if (data.length === total) {
				state.status = 'nomore'
			}
		} else {
			state.status = 'nomore'
		}
		triggered.value = false
		uni.stopPullDownRefresh()
	}

	// 确定搜索
	const submitScreen = () => {
		tableData.value = []
		state.screenShow = false
		screenForm.page = 1
		getWorkOrderList()
	}
	onReachBottom(async () => {
		showMoreData()
	})

	onPullDownRefresh(async () => {
		onRefresh()
	})
	// onMounted(() => {
	// 	onRefresh()
	// })

	// 选择处理人
	const chooseUser = (user : any) => {
		if (user) {
			screenForm.userName = user.firstName
			screenForm.userId = removeSlash(user.id?.id)
		}
	}

	onShow(async () => {
		onRefresh()
		let {
			userData
		} = store;
		if (userData) {
			chooseUser(userData)
		}
	})
</script>

<style lang="scss" scoped>
	.card-box {
		width: 686rpx;
		margin: 20rpx auto;
		border-radius: 16rpx;
		padding: 22rpx 28rpx;
		background-color: #FFFFFF;

		.hand {
			.hand-title {
				height: 42rpx;
				line-height: 42rpx;

				.icon-box {
					text-align: center;
					line-height: 32rpx;
					height: 32rpx;
					width: 32rpx;
					background-color: #3862F8;
					border-radius: 4rpx;
				}

				text {
					font-size: 28rpx;
					margin-left: 16rpx;
					color: #060F27;
					font-weight: 600;
				}
			}

			.status {
				color: #91949F;
			}
		}

		.table {
			margin-top: 24rpx;

			.info {
				font-size: 24rpx;
				padding-bottom: 18rpx;
				display: flex;

				text {
					&:nth-child(1) {
						color: #91949F;
					}

					&:nth-child(2) {
						flex: 1;
					}
				}


				.bg {
					padding: 2rpx 12rpx;
					background: rgba(56, 98, 248, 0.2);
					border-radius: 8rpx;

					text {
						color: #3862F8;
					}
				}
			}
		}
	}

	.popup {
		width: 100%;
		height: 100%;
		position: relative;
		padding: 0 34rpx;


		.popup-button {
			width: 94%;
			position: absolute;
			bottom: 40rpx;
			left: 0;
			right: 0;
			margin: 0 auto;
		}

		::v-deep .placeholderClass {
			font-size: 28rpx;
		}

		.screen-list {
			padding: 222rpx 34rpx;
		}
	}
</style>