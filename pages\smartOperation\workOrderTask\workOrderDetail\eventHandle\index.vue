<!-- 事件分派 -->
<template>
	<view class="main">
		<u-navbar fixed placeholder safeAreaInsetTop bgColor="#FFFFFF" :title="wordOrderInfo.title" :autoBack="true"
			leftIconSize="20">
		</u-navbar>
		<u-form :model="form" ref="refForm" :label-style="{'color':'#91949F'}" borderBottom labelWidth="180" input-align="right">
			<view class="card-box">
				<!-- <u-form-item required label="预计完工时间：" prop="form.val2" labelWidth="220">
					<u-input placeholder="请选择预计完工时间" v-model="form.name" inputAlign="right">
					</u-input>
					<template #right>
						<u-icon name="calendar" size="34"></u-icon>
					</template>
				</u-form-item> -->
				<u-form-item required label="审核人员：" prop="form.nextProcessUserName" labelWidth="220"
					v-if="wordOrderInfo.stage=== 'SUBMIT'">
					<input placeholder="请选审核人员" inputmode="none" placeholder-class="placeholderClass" v-model="form.nextProcessUserName" inputAlign="right"
						@click="toChooseUser">
					</input>
					<template #right>
						<u-icon name="arrow-right" size="28"></u-icon>
					</template>
				</u-form-item>
				<u-form-item label="描述：" prop="form.processRemark" :borderBottom="false">
				</u-form-item>
				<u-form-item label="" prop="form.processRemark">
					<u-input type="textarea" input-align="left"  placeholder="请输入备注" v-model="form.processRemark" border height="160">
					</u-input>
				</u-form-item>
			</view>
		</u-form>

		<view class="card-box" style="padding-bottom: 80rpx">
			<file-upload ref="refFileUpload"></file-upload>
		</view>
		<view class="button">
			<u-button type="primary" color="#3862F8" @click="submit">提交</u-button>
		</view>
		<!-- <u-picker v-model="state.examineShow" mode="multiSelector" :default-selector="[0]"
			:range="examineList" range-key="name" @columnchange="acolumnchange" @confirm="selectClick">
		</u-picker> -->
		<!-- <u-select label-name="name" value-name="id" v-model="state.examineShow" mode="mutil-column-auto" :list="examineList" @confirm="selectClick"></u-select> -->
		<u-toast ref="refToast"></u-toast>
	</view>
</template>

<script lang="ts" setup>
	import {
		useStore
	} from '@/store/index'
	import {
		onMounted,
		reactive,
		ref
	} from "vue";
	import {
		onShow
	} from '@dcloudio/uni-app';
	import {
		workOrderHandle,
	} from '@/common/api/workOrder'
	import {
		removeSlash
	} from '@/common/utils/removeIdSlash'
	import {
		getOrganization,
		getDepartmentList,
		getStepProcessUser
	} from '@/common/data/workOrderData'
	import fileUpload from '@/components/fileUpload/fileUpload.vue'
	import {
		storeToRefs
	} from 'pinia'
	const store = useStore();
	const refToast = ref < any > ()
	const state = reactive < {
		examineShow: Boolean,
		examineIndex: any
	} > ({
		examineShow: false,
		examineIndex: [0, 0, 0]
	})

	const refFileUpload = ref < any > ({})
	const form = reactive < any > ({})
	const wordOrderInfo = ref < any > ({})
	const examineList = ref < any > ([{}, {}, {}])
	const organizations = ref < any > ([])
	const departments = ref < any > ([])
	const processUsers = ref < any > ([])
	const showExamine = async () => {
		organizations.value = await getOrganization()
		examineList.value[state.examineIndex[0]] = organizations.value
		await getDepartment(organizations.value[state.examineIndex[0]]?.id)
		state.examineShow = true
	}
	// 切换处理人组织部门获取用户列表
	const acolumnchange = async ({
		column: column,
		index: index
	}) => {
		console.log(column, index)
		if (column === 0) {
			await getDepartment(organizations.value[index]?.id)
		} else if (column === 1) {
			await getProcessUser(departments.value[index]?.id)
		}
	}

	// 获取组织部门列表
	const getDepartment = async (id: String) => {
		departments.value = await getDepartmentList(id)
		examineList.value[1] = departments.value
		const dId = departments.value[state.examineIndex[1]]?.id as String
		await getProcessUser(dId)

	}
	// 获取组织部门用户列表
	const getProcessUser = async (id: String) => {
		processUsers.value = await getStepProcessUser(id)
		examineList.value[2] = processUsers.value.length > 0 ? processUsers.value.map(data => {
			return {
				...data,
				// name: data.firstName
			}
		}) : [{}]
		console.log(examineList.value)
	}
	// 选择处理人信息
	const selectClick = (val: any) => {
		const processUser = processUsers.value[val[2]]
		if (processUser) {
			state.examineIndex = val
			form.nextProcessUserName = processUser.firstName
			form.nextProcessUserId = removeSlash(processUser.id?.id)
		} else {
			uni.showToast({
				title: '请选择处理人',
				icon: 'none'
			})
		}
	}

	// 选择审核人员
	const toChooseUser = () => {
		uni.$u.route({
			url: 'pages/userTree/organization'
		})
	}


	//提交工单
	const submit = () => {
		let processAdditionalInfo = {
			videoUrl: refFileUpload.value.videoList,
			audioUrl: refFileUpload.value.voiceList,
			imgUrl: refFileUpload.value.imageList,
			otherFileUrl: refFileUpload.value.fileList
		}
		uni.showModal({
			title: '提示',
			content: '确定提交',
			success: function(res) {
				if (res.confirm) {
					const params = {
						...form,
						processAdditionalInfo: JSON.stringify(processAdditionalInfo),
						stage: wordOrderInfo.value.stage,
						nextProcessUserId: form.nextProcessUserId
					}
					workOrderHandle(wordOrderInfo.value.id, params).then(res => {
						
						if (res.data?.code === 200 && res.data?.data.code!=500) {
							let {
								userData
							} = storeToRefs(store);
							userData.value = null
							refToast.value.show({
								title: '提交成功',
								type: 'success',
								callback: () => {
									uni.navigateBack({
										delta: 1
									})
								}
							})
						} else {
							refToast.value.show({
								title: res.data?.data.err,
								type: 'error'
							})
						}
					}).catch(() => {
						refToast.value.show({
							title: '提交失败',
							type: 'error'
						})
					})
				} else if (res.cancel) {
					console.log('用户点击取消');
				}
			}
		});
	}

	const chooseUser = (user: any) => {
		if (user) {
			form.nextProcessUserName = user.firstName
			form.nextProcessUserId = removeSlash(user.id?.id)
		}
	}

	onMounted(() => {
		var pages = getCurrentPages();
		var page = pages[pages.length - 1];
		wordOrderInfo.value = page.$page.options
	})

	onShow(async () => {
		let {
			userData
		} = store;
		if (userData) {
			chooseUser(userData)
		}
	})
</script>

<style lang="scss" scoped>
	.main {
		padding-top: 20rpx;

		.card-box {
			width: 686rpx;
			border-radius: 8px;
			min-height: 80rpx;
			padding: 20rpx 28rpx;
			margin: 0 auto 20rpx auto;
			color: #FFFFFF;
			background-color: #FFFFFF;

			.form {
				padding: 0 28rpx 10rpx 28rpx;
				color: #060F27;
			}

			.l-file {
				padding: 10rpx 0;
			}
		}

		.content-card {
			width: 686rpx;
			margin: 20rpx auto;
			background-color: #FFFFFF;
			min-height: 224rpx;
			border-radius: 8px;
			padding: 24rpx 0 0 28rpx;

			.title-text {
				color: #91949F;
				font-size: 28rpx;
			}

			.file-s {
				width: 116rpx;
				height: 116rpx;
				display: flex;
				justify-content: center;
				flex-direction: column;
				text-align: center;
				background: #F9F9F9;
				position: relative;

				text {
					color: #91949F;
					font-size: 20rpx;
				}

				.close-icon {
					border-radius: 50%;
					width: 32rpx;
					height: 32rpx;
					background-color: red;
					line-height: 32rpx;
					text-align: center;
					position: absolute;
					right: 0;
					top: 0;
				}
			}

			.margin-center {
				margin: 0 auto;

				.icon {
					font-size: 48rpx;
				}
			}
		}

		::v-deep.u-form-item {
			padding: 0;
		}
	}
</style>
