import { defineStore } from 'pinia'

// useStore 可以是 useUser、useCart 之类的任何东西
// 第一个参数是应用程序中 store 的唯一 id
export const useStore = defineStore('main', {
	// 推荐使用 完整类型推断的箭头函数
	state: () => {
		return {
			// 所有这些属性都将自动推断其类型
			userData: null,
			otherUserData: null,
			workOrder: null,
			pipeLayerInfoes: [] as { layerid : number; layername : string; geometrytype : string; layerdbname : string; icon?: string }[]
		};
	},
	getters: {
		pipeLayerInfoes_line(state) {
			return state.pipeLayerInfoes.filter(item => item.geometrytype === 'esriGeometryPolyline') || []
		},
		pipeLayerInfoes_point(state) {
			return state.pipeLayerInfoes.filter(item => item.geometrytype === 'esriGeometryPoint') || []
		}
	}
})