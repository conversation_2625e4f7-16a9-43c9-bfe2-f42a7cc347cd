<template>
	<view class="main">
		<view style="position: absolute;">
			<u-navbar :border-bottom="false" :background="{backgroundColor: state.navBgColor}" title="巡检详情" is-back
				:titleColor="state.navColor" :back-iconColor="state.navColor">
			</u-navbar>
		</view>
		<view class="bg">
			<view class="con">
				<view class="name">
					{{status[state.detail.status]}}
				</view>
				<view class="remark">
					责任就是使命，职务就是责任
				</view>
			</view>
			<view class="iamge">
				<image src="/static/img/icons/xunjian-bg.png" style="height:284rpx;width:306rpx"></image>
			</view>
		</view>
		<view class="detail-c">
			<view class="card-box">
				<view class="flex-between hand">
					<view class="hand-title">
						<text>巡检信息</text>
					</view>
				</view>
				<u-gap height="1" bgColor="#EBEDF6"></u-gap>
				<view class="table">
					<view class="info">
						<text>任务名称：</text> <text>{{state.detail.name}}</text>
					</view>
					<view class="info">
						<text>执行班组：</text> <text>{{state.detail.teamName}}</text>
					</view>
					<view class="info">
						<text>审核部门：</text> <text>{{state.detail.auditorDepartmentName}}</text>
					</view>
					<view class="info">
						<text>审核人员：</text> <text>{{state.detail.auditorName}}</text>
					</view>
					<block v-if="state.detail.status === '0'">
						<view class="info">
							<text>预计开始时间：</text> <text>{{proxy.formatTime(state.detail.startTime) }}</text>
						</view>
						<view class="info">
							<text>预计结束时间：</text> <text>{{proxy.formatTime(state.detail.endTime)}}</text>
						</view>
					</block>
					<block v-if="state.detail.status === '1'">
						<view class="info">
							<text>开始时间：</text> <text>{{state.detail.realStartTime}}</text>
						</view>
						<view class="info">
							<text>预计结束时间：</text> <text>{{state.detail.endTime}}</text>
						</view>
					</block>
					<block v-if="state.detail.status === '2'">
						<view class="info">
							<text>开始时间：</text> <text>{{state.detail.realStartTime}}</text>
						</view>
						<view class="info">
							<text>结束时间：</text> <text>{{state.detail.realStartTime}}</text>
						</view>
					</block>
				</view>
			</view>
		</view>
		<view class="content-list">
			<view class="content-card" v-for="(item,index) in taskItemList" :key="index"
				@click="toHandleInspection(item)">
				<view class="status-arrow flex-center">
					<view class="status"
						:style="{'background-color':state.status[item.status].bgColor,'color':state.status[item.status].color}">
						<!-- {{state.status['wait'].label}} -->
						{{item.status==='0'?'未完成':'完成'}}
					</view>
					<u-icon name="arrow-right" size="28" color="#91949F"></u-icon>
				</view>
				<view class="cont-table">
					<view class="info">
						<text>标签编码：</text> <text>{{item.deviceLabelCode}}</text>
					</view>
					<view class="info">
						<text>设备名称：</text> <text>{{item.name}}</text>
					</view>
					<view class="info flex-center">
						<text>规格/型号：</text><text>{{item.model}}</text>
					</view>
					<view class="info">
						<text>所属大类：</text> <text>{{item.topType}}</text>
					</view>
					<view class="info">
						<text>所属类型：</text> <text>{{item.type}}</text>
					</view>
					<view class="info">
						<text>安装区域：</text> <text>{{item.installAddressName}}</text>
					</view>
					<view class="info">
						<text>安装位置：</text> <text>{{item.detailInstallAddressName}}</text>
					</view>
					<!-- <view class="info">
						<text>最后保养时间：</text> <text>{{proxy.formatTime(item.lastModifyTime) }}</text>
					</view> -->
				</view>
			</view>
		</view>
		<view class="button" v-if="state.detail.status ==='0'">
			<u-button type="primary" color="#3862F8" @click="receiveTask">接收</u-button>
		</view>
		<!-- <view class="button" v-if="['2','3'].includes(state.detail.status)">
			<u-button type="primary" color="#3862F8" @click="toExamine">完成</u-button>
		</view> -->
		<u-modal v-model="state.modalShow" title="提示" @cancel="state.modalShow=false" showCancelButton content="确定接收任务吗"
			@confirm="receiveTask"></u-modal>
		<u-toast ref="refToast"></u-toast>
	</view>
</template>

<script lang="ts" setup>
	import {
		getCurrentInstance,
		reactive,
		ref
	} from "vue";
	import {
		onPageScroll,
		onShow
	} from '@dcloudio/uni-app'
	import {
		getCircuitTaskDetail,
		saveCircuitTask,
		receiveCircuitTask
	} from '@/common/api/asset'
	onPageScroll(e => {
		state.scrollTop = e.scrollTop
		if (e.scrollTop < 65) {
			state.navColor = '#FFFFFF'
			state.navBgColor = 'transparent'
		} else {
			state.navColor = '#000000'
			state.navBgColor = '#FFFFFF'
		}
	})
	const {
		proxy
	} = getCurrentInstance()
	const status = ref < any > (['待接收', '已接收', '按时完成', '超时完成', '未完成'])
	const state = reactive < {
		detail: any,
		taskId: string,
		status: any,
		modalShow: boolean,
		scrollTop: number,
		navColor: string,
		navBgColor: string
	} > ({
		detail: {},
		taskId: '',
		modalShow: false,
		status: {
			'0': {
				color: '#F8A038',
				bgColor: 'rgba(248, 160, 56, 0.2)'
			},
			'1': {
				color: '#2EE740',
				bgColor: 'rgba(46, 231, 64, 0.2)'
			},
			'3': {
				color: '#F83838',
				bgColor: 'rgba(248, 56, 56, 0.2)'
			},
		},
		navColor: '#FFFFFF',
		navBgColor: 'transparent',
		scrollTop: 0
	})
	const refToast = ref < any > ()
	const taskItemList = ref < any > ([])
	// 前往巡检处理
	const toHandleInspection = (item: any) => {
		if (['1'].includes(state.detail.status)) {
			uni.$u.route({
				url: 'pages/smartOperation/assetCircuitInspectionTask/handleInspection',
				params: {
					id: item.id,
					mainId: item.mainId,
					deviceLabelCode: item.deviceLabelCode
				},
			})
		} else {
			// uni.$u.toast('当前无权限操作')
		}
	}

	// 接收任务
	const receiveTask = async () => {
		uni.showModal({
			title: '提示',
			content: '确定接收任务吗',
			success: function(res) {
				if (res.confirm) {
					receiveCircuitTask(state.detail.id).then(res => {
						if (res.data?.code === 200) {
							state.detail.status = '1'
							refToast.value.show({
								title: '接收成功',
								type: 'success',
								back: true
							})
						} else {
							refToast.value.show({
								title: '接收失败',
								type: 'error'
							})
						}
						state.modalShow = false
					})
				}
			}
		})
	}
	// 提交审核
	const toExamine = async () => {
		uni.showModal({
			title: '提示',
			content: '确定提交审核吗',
			success: function(res) {
				if (res.confirm) {
					saveCircuitTask(state.detail.id).then(res => {
						if (res.data?.code === 200) {
							state.detail.status = '1'
							refToast.value.show({
								title: '接收成功',
								type: 'success'
							})
						} else {
							refToast.value.show({
								title: '接收失败',
								type: 'error'
							})
						}
						state.modalShow = false
					})
				}
			}
		})
	}

	const getInspecionItems = async (id: string) => {
		const res = await getCircuitTaskDetail(id)
		state.detail = res.data?.data
		taskItemList.value = res.data?.data?.circuitTaskCList
	}

	// onMounted(async () => {
	// 	var pages = getCurrentPages();
	// 	var page = pages[pages.length - 1];
	// 	state.detail = page.$page?.options || {}
	// 	console.log(state.detail)
	// 	getInspecionItems()
	// })

	onShow(async () => {
		var pages = getCurrentPages();
		var page = pages[pages.length - 1];
		state.taskId = page.$page?.options?.id || {}
		console.log(state.taskId)
		getInspecionItems(state.taskId)
	})
</script>

<style lang="scss" scoped>
	.main {
		height: 100vh;
		padding-top: 0;
	}

	.bg {
		width: 100%;
		height: 480rpx;
		background: linear-gradient(117.58deg, #3862F8 0%, #6AA6FF 100%);

		.iamge {
			position: absolute;
			top: 186rpx;
			right: 68rpx;
		}

		.con {
			position: absolute;
			top: 248rpx;
			left: 40rpx;
			color: #FFFFFF;

			.name {
				font-weight: 700;
				font-size: 40rpx;
				line-height: 60rpx;
			}

			.remark {}
		}
	}

	.detail-c {
		width: 686rpx;
		margin: 0 auto;
		position: relative;
		background-color: #FFFFFF;
		min-height: 376rpx;
		top: -84rpx;
		border-radius: 8px;

		.card-box {
			width: 686rpx;
			margin: 20rpx auto;
			border-radius: 16rpx;
			padding: 22rpx 28rpx;
			background-color: #FFFFFF;

			.hand {
				.hand-title {
					height: 80rpx;
					line-height: 80rpx;

					text {
						font-size: 28rpx;
						margin-left: 16rpx;
						color: #060F27;
						font-weight: 700;
					}
				}
			}

			.table {
				margin-top: 24rpx;

				.info {
					font-size: 24rpx;
					padding-bottom: 18rpx;

					text {
						&:nth-child(1) {
							color: #91949F;
						}

						&:nth-child(2) {
							flex: 1;
						}
					}
				}
			}
		}
	}

	.content-list {
		padding-bottom: 100rpx;

		.content-card {
			width: 686rpx;
			margin: 10rpx auto;
			position: relative;
			background-color: #FFFFFF;
			min-height: 276rpx;
			top: -84rpx;
			border-radius: 8px;

			.status-arrow {
				position: absolute;
				right: 28rpx;
				top: 24rpx;

				.status {
					height: 40rpx;
					width: 108rpx;
					border-radius: 8rpx;
					text-align: center;
					line-height: 40rpx;
				}
			}

			.cont-table {
				padding: 22rpx 28rpx;

				.info {
					font-size: 28rpx;
					padding-bottom: 18rpx;

					text {
						&:nth-child(1) {
							color: #91949F;
						}

						&:nth-child(2) {
							flex: 1;
						}
					}

					.bf-bg {
						padding: 2rpx 12rpx;
						background: rgba(56, 98, 248, 0.2);
						border-radius: 8rpx;

						text {
							color: #3862F8;
						}
					}
				}
			}
		}
	}
</style>
