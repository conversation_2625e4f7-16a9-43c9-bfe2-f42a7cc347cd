{
	"easycom": {
		"custom": {
			"^u-(.*)": "@/uni_modules/vk-uview-ui/components/u-$1/u-$1.vue",
			"^qiun-(.*)": "@/uni_modules/qiun-data-charts/components/qiun-$1/qiun-$1.vue",
			"^qiun-(.*)": "@/uni_modules/qiun-data-charts/components/qiun-$1/qiun-$1.vue"
		}
	},
	"pages": [
		//pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
		{
			"path": "pages/login/index",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/index/index",
			"style": {
				"navigationBarTitleText": "首页",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/wisdomProduce/waterWorksOverview/index",
			"style": {
				"navigationBarTitleText": "水厂总览",
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/mine/index",
			"style": {
				"navigationBarTitleText": "个人中心",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/needToDo/index",
			"style": {
				"navigationBarTitleText": "待办",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/map/index",
			"style": {
				"navigationBarTitleText": "地图",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/rtk/index",
			"style": {
				"navigationBarTitleText": "rtk",
				"enablePullDownRefresh": false
				// "navigationStyle" : "custom"
			}
		},
		// "app-plus": {
		// 	"subNVues": [{
		// 		"id": "map", // 唯一标识
		// 		"path": "pages/map/map", // 页面路径
		// 		/*"type": "popup",  这里不需要*/
		// 		"style": {
		// 			"background": "transparent"
		// 		}
		// 	}]
		// }
		{
			"path": "pages/allApplication/index",
			"style": {
				"navigationBarTitleText": "应用中心",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/wisdomProduce/pressureMonitoring/index",
			"style": {
				"navigationBarTitleText": "压力监测",
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/wisdomProduce/pressureMonitoring/dataDetail/index",
			"style": {
				"navigationBarTitleText": "压力监测",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/wisdomProduce/flowMonitoring/index",
			"style": {
				"navigationBarTitleText": "流量监测",
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/wisdomProduce/flowMonitoring/dataDetail/index",
			"style": {
				"navigationBarTitleText": "流量监测",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/wisdomProduce/secondaryWaterSupply/index",
			"style": {
				"navigationBarTitleText": "二次供水",
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/wisdomProduce/waterQualityMonitoring/index",
			"style": {
				"navigationBarTitleText": "水质监测",
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/wisdomProduce/waterQualityMonitoring/dataDetail/index",
			"style": {
				"navigationBarTitleText": "水质监测",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/wisdomProduce/secondaryWaterSupply/dataDetail/index",
			"style": {
				"navigationBarTitleText": "二次供水",
				"enablePullDownRefresh": true,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/wisdomProduce/secondaryWaterSupply/workmanshipPage/index",
			"style": {
				"navigationBarTitleText": "工艺图",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/wisdomProduce/secondSupplyInspection/inspecionDetail/index",
			"style": {
				"navigationBarTitleText": "巡检详情",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/wisdomProduce/secondSupplyInspection/index",
			"style": {
				"navigationBarTitleText": "二供巡检",
				"enablePullDownRefresh": true,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/wisdomProduce/secondSupplyInspection/faultReport/index",
			"style": {
				"navigationBarTitleText": "故障上报",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/wisdomProduce/secondSupplyInspection/handleInspection/index",
			"style": {
				"navigationBarTitleText": "巡检处理",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/wisdomProduce/waterFactoryInspection/inspecionDetail/index",
			"style": {
				"navigationBarTitleText": "巡检详情",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/wisdomProduce/waterFactoryInspection/index",
			"style": {
				"navigationBarTitleText": "水厂巡检",
				"enablePullDownRefresh": true,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/wisdomProduce/waterFactoryInspection/faultReport/index",
			"style": {
				"navigationBarTitleText": "故障上报",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/wisdomProduce/waterFactoryInspection/handleInspection/index",
			"style": {
				"navigationBarTitleText": "巡检处理",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/wisdomProduce/waterWorksReport/waterSupplyReport/index",
			"style": {
				"navigationBarTitleText": "供水报表",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/wisdomProduce/waterWorksReport/waterQualityReport/index",
			"style": {
				"navigationBarTitleText": "水质报表",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/wisdomProduce/waterWorksReport/productionReport/index",
			"style": {
				"navigationBarTitleText": "生产报表",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/wisdomProduce/waterWorksReport/energyConsumptionReport/index",
			"style": {
				"navigationBarTitleText": "能耗报表",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/wisdomProduce/waterWorksReport/unitConsumptionReport/index",
			"style": {
				"navigationBarTitleText": "吨水耗电",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/wisdomProduce/waterWorksReport/index",
			"style": {
				"navigationBarTitleText": "水厂报表",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/wisdomProduce/secondSupplyReport/index",
			"style": {
				"navigationBarTitleText": "二供报表",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/wisdomProduce/secondSupplyReport/energyConsumptionReport/index",
			"style": {
				"navigationBarTitleText": "能耗报表",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/wisdomProduce/secondSupplyReport/waterVolumeReport/index",
			"style": {
				"navigationBarTitleText": "水量报表",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/wisdomProduce/secondSupplyReport/unitConsumptionReport/index",
			"style": {
				"navigationBarTitleText": "单耗报表",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/pipeNetwork/mapBrowsing/index",
			"style": {
				"navigationBarTitleText": "地图浏览",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/pipeNetwork/mapBrowsing/index_leaflet",
			"style": {
				"navigationBarTitleText": "地图浏览",
				"enablePullDownRefresh": false
			}
		},
		// "app-plus": {
		// 	"subNVues": [{
		// 		"id": "map", // 唯一标识
		// 		"path": "pages/pipeNetwork/mapBrowsing/map", // 页面路径
		// 		/*"type": "popup",  这里不需要*/
		// 		"style": {
		// 			"background": "transparent"
		// 		}
		// 	}]
		// }
		{
			"path": "pages/pipeNetwork/spaceDeviceList/detailData/index",
			"style": {
				"navigationBarTitleText": "查看详情",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/pipeNetwork/spaceDeviceList/fileUpload/index",
			"style": {
				"navigationBarTitleText": "文件上传",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/pipeNetwork/spaceDeviceList/attributeReport/index",
			"style": {
				"navigationBarTitleText": "属性上报",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/pipeNetwork/spaceDeviceList/attributeReport/attributeReportRecords",
			"style": {
				"navigationBarTitleText": "上报记录",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/pipeNetwork/spaceDeviceList/attributeReport/attributeReportRecordDetail",
			"style": {
				"navigationBarTitleText": "记录详情",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/pipeNetwork/spaceDeviceList/index",
			"style": {
				"navigationBarTitleText": "查询列表",
				"enablePullDownRefresh": false
			}
		},
		// gis养护相关
		{
			"path": "pages/pipeNetwork/maintenanceTasks/index",
			"style": {
				"navigationBarTitleText": "养护任务",
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/pipeNetwork/maintenanceTasks/taskDetail/index",
			"style": {
				"navigationBarTitleText": "任务详情",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		// "app-plus": {
		// 	"subNVues": [{
		// 		"id": "map", // 唯一标识
		// 		"path": "pages/pipeNetwork/maintenanceTasks/taskDetail/map", // 页面路径
		// 		/*"type": "popup",  这里不需要*/
		// 		"style": {
		// 			"background": "transparent"
		// 		}
		// 	}]
		// }
		{
			"path": "pages/pipeNetwork/maintenanceTasks/taskDetail/maintainReport",
			"style": {
				"navigationBarTitleText": "养护上报",
				"enablePullDownRefresh": false
			}
		},
		// gis巡检相关
		{
			"path": "pages/pipeNetwork/inspectionTask/index",
			"style": {
				"navigationBarTitleText": "巡检任务",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/pipeNetwork/inspectionTask/taskDetail/index",
			"style": {
				"navigationBarTitleText": "任务详情",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/pipeNetwork/inspectionTask/checkList/index",
			"style": {
				"navigationBarTitleText": "巡检表单",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/pipeNetwork/inspectionTask/taskDetail/index-scan",
			"style": {
				"navigationBarTitleText": "任务详情",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/pipeNetwork/inspectionTask/inspectionReport2/index",
			"style": {
				"navigationBarTitleText": "巡检上报",
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/pipeNetwork/inspectionTask/inspectionReport/index",
			"style": {
				"navigationBarTitleText": "巡检上报",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/smartOperation/workOrderInitiation/index",
			"style": {
				"navigationBarTitleText": "工单发起",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/smartOperation/workOrderAssignment/index",
			"style": {
				"navigationBarTitleText": "工单分派",
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/smartOperation/workOrderTask/index",
			"style": {
				"navigationBarTitleText": "巡检处置",
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/smartOperation/workOrderApproval/index",
			"style": {
				"navigationBarTitleText": "待审核工单",
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/smartOperation/workOrderApproval/detail/index",
			"style": {
				"navigationBarTitleText": "工单详情",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/smartOperation/workOrderApproval/detail/returnHandle/index",
			"style": {
				"navigationBarTitleText": "审核驳回",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/smartOperation/workOrderAssignment/workOrderDetail/index",
			"style": {
				"navigationBarTitleText": "工单详情",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/smartOperation/workOrderAssignment/workOrderDetail/eventDispatch/index",
			"style": {
				"navigationBarTitleText": "分派",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/smartOperation/workOrderAssignment/workOrderDetail/invalidHandle/index",
			"style": {
				"navigationBarTitleText": "无效",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/smartOperation/workOrderTask/workOrderDetail/index",
			"style": {
				"navigationBarTitleText": "工单详情",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/smartOperation/workOrderTask/workOrderDetail/eventHandle/index",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/smartOperation/workOrderTask/workOrderDetail/returnHandle/index",
			"style": {
				"navigationBarTitleText": "退回",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/smartOperation/chargebackReviewed/index",
			"style": {
				"navigationBarTitleText": "退单审核",
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/smartOperation/chargebackReviewed/detail/index",
			"style": {
				"navigationBarTitleText": "工单详情",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/smartOperation/chargebackReviewed/detail/returnHandle/index",
			"style": {
				"navigationBarTitleText": "审核驳回",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/smartOperation/workOrderQuery/index",
			"style": {
				"navigationBarTitleText": "工单查询",
				"enablePullDownRefresh": true,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/smartOperation/workOrderStatistics/index",
			"style": {
				"navigationBarTitleText": "工单统计",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/smartOperation/workOrderQuery/detail/index",
			"style": {
				"navigationBarTitleText": "工单详情",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/smartOperation/workOrderStatistics/typeAnalysis/index",
			"style": {
				"navigationBarTitleText": "事件类型分析",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/smartOperation/workOrderStatistics/trendAnalysis/index",
			"style": {
				"navigationBarTitleText": "事件趋势分析",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/smartOperation/workOrderStatistics/escalationAnalysis/index",
			"style": {
				"navigationBarTitleText": "人员上报分析",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/userTree/organization",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/userTree/department",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/userTree/personnel",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/mine/updatePassword/index",
			"style": {
				"navigationBarTitleText": "修改密码",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/mine/aboutUs/index",
			"style": {
				"navigationBarTitleText": "关于我们",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/mine/aboutUs/webView",
			"style": {
				"navigationBarTitleText": "关于我们",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/mine/info/index",
			"style": {
				"navigationBarTitleText": "基本信息",
				"enablePullDownRefresh": false
			}
		},
		// #ifdef APP
		{
			"path": "uni_modules/hic-upgrade/pages/upgrade",
			"style": {
				"navigationBarTitleText": "版本更新",
				"navigationStyle": "custom", // 取消本页面的导航栏
				"backgroundColor": "transparent",
				"disableScroll": true,
				"app-plus": {
					"background": "transparent", // 背景透明
					"scrollIndicator": "none", // 不显示滚动条
					"animationType": "fade-in",
					"animationDuration": 200,
					"popGesture": "none" // 关闭IOS屏幕左边滑动关闭当前页面的功能
				}
			}
		},
		// #endif
		{
			"path": "pages/smartOperation/workOrderTask/workOrderDetail/otherHandle/index",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/smartOperation/assetMaintainTask/index",
			"style": {
				"navigationBarTitleText": "资产保养",
				"enablePullDownRefresh": true,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/smartOperation/assetMaintainTask/taskDetail",
			"style": {
				"navigationBarTitleText": "保养详情",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/smartOperation/assetMaintainTask/handleMaintain",
			"style": {
				"navigationBarTitleText": "",
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/smartOperation/assetCircuitInspectionTask/faultReport",
			"style": {
				"navigationBarTitleText": "故障上报",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/smartOperation/assetCircuitInspectionTask/index",
			"style": {
				"navigationBarTitleText": "资产巡检",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/smartOperation/rquipmentAssets/index",
			"style": {
				"navigationBarTitleText": "设备资产",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/smartOperation/rquipmentAssets/deviceDetail",
			"style": {
				"navigationBarTitleText": "设备详情",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},

    {
      "path": "pages/wisdomProduce/exceptionalManageV2/handleAlarm/index",
      "style": {
        "navigationBarTitleText": "报警处理",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/wisdomProduce/exceptionalManageV2/index",
      "style": {
        "navigationBarTitleText": "报警信息",
        "enablePullDownRefresh": true,
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/wisdomProduce/exceptionalManageV2/workOrderInitiation/index",
      "style": {
        "navigationBarTitleText": "工单发起",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/wisdomProduce/exceptionalManageV3/handleAlarm/index",
      "style": {
        "navigationBarTitleText": "报警处理",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/wisdomProduce/exceptionalManageV3/index",
      "style": {
        "navigationBarTitleText": "报警信息",
        "enablePullDownRefresh": true,
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/wisdomProduce/exceptionalManageV3/workOrderInitiation/index",
      "style": {
        "navigationBarTitleText": "工单发起",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/noticeList/index",
      "style": {
        "navigationBarTitleText": "消息中心",
        "enablePullDownRefresh": true,
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/webView/index",
      "style": {
        "navigationBarTitleText": "",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/pipeNetwork/inspectionTaskAssignment/index",
      "style": {
        "navigationBarTitleText": "巡检分派",
        "enablePullDownRefresh": false,
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/pipeNetwork/inspectionTaskQuery/index",
      "style": {
        "navigationBarTitleText": "巡检查询",
        "enablePullDownRefresh": true,
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/pipeNetwork/inspectionTaskAssignment/taskDetail/index",
      "style": {
        "navigationBarTitleText": "巡检详情",
        "enablePullDownRefresh": false
        // "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/pipeNetwork/inspectionTaskAssignment/taskDetail/eventDispatch",
      "style": {
        "navigationBarTitleText": "巡检分派",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/pipeNetwork/inspectionTaskQuery/taskDetail/index",
      "style": {
        "navigationBarTitleText": "巡检详情",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/mine/addressBook/index",
      "style": {
        "navigationBarTitleText": "通讯录"
      }
    },
    {
      "path": "pages/map/tianMap",
      "style": {
        "navigationBarTitleText": ""
      }
    },
    {
      "path": "pages/smartOperation/emergencyTasks/newTask/index",
      "style": {
        "navigationBarTitleText": "",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/income/meterReading/index",
      "style": {
        "navigationBarTitleText": "",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/income/orderList/index",
      "style": {
        "navigationBarTitleText": "账单列表"
      }
    },
    {
      "path": "pages/income/orderDetail/index",
      "style": {
        "navigationBarTitleText": "账单明细"
      }
    },
    {
      "path": "pages/InstallationApply/User/userAdd/index",
      "style": {
        "navigationBarTitleText": "立护新增"
      }
    },
    {
      "path": "pages/InstallationApply/User/userDetail/index",
      "style": {
        "navigationBarTitleText": "立护信息",
        "enablePullDownRefresh": true,
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/InstallationApply/User/userDetail/detail",
      "style": {
        "navigationBarTitleText": "立护详情",
        "enablePullDownRefresh": true,
        "navigationStyle": "custom"
      }
    }
  ],
  "tabBar": {
    "color": "#B2B7C7",
    "selectedColor": "#3862F8",
    "borderStyle": "black",
    "height": "70px",
    "backgroundColor": "#ffffff",
    "list": [
      // 标准首页
      {
        "pagePath": "pages/index/index",
        "iconPath": "/static/img/tabbar/home.png",
        "selectedIconPath": "/static/img/tabbar/home-c.png",
        "text": "首页"
      },
      // 全部菜单首页
      // {
      // 	"pagePath": "pages/allApplication/index",
      // 	"iconPath": "/static/img/tabbar/home.png",
      // 	"selectedIconPath": "/static/img/tabbar/home-c.png",
      // 	"text": "首页"
      // },
      {
        "pagePath": "pages/map/index",
        "iconPath": "/static/img/tabbar/map.png",
        "selectedIconPath": "/static/img/tabbar/map-c.png",
        "text": "地图"
      },
      {
        "pagePath": "pages/needToDo/index",
        "iconPath": "/static/img/tabbar/todo.png",
        "selectedIconPath": "/static/img/tabbar/todo-c.png",
        "text": "待办"
      },
      {
        "pagePath": "pages/mine/index",
        "iconPath": "/static/img/tabbar/mine.png",
        "selectedIconPath": "/static/img/tabbar/mine-c.png",
        "text": "我的"
      }
    ]
  },
  "globalStyle": {
    "fontWeight": "bold",
    "navigationBarTextStyle": "black",
    "navigationBarTitleText": "水务云",
    "navigationBarBackgroundColor": "#FFFFFF",
    "backgroundColor": "#F9F9F9 ",
    "pageOrientation": "portrait",
    "uniIdRouter": {}
  },
  "condition": {
    //模式配置，仅开发期间生效
    "current": 0, //当前激活的模式(list 的索引项)
    "list": [
      {
        "name": "", //模式名称
        "path": "", //启动页面，必选
        "query": "" //启动参数，在页面的onLoad函数里面得到
      }
    ]
  }
}
