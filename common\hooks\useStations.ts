import { getStationList as getStation<PERSON><PERSON><PERSON><PERSON> } from "../api/stations";
import {
	getStationData
} from '../api/waterplant'
import { computed, ref } from 'vue'
export const useStations = () => {
	const colors: Record<string, string> = {
		水厂: '#3c8da5',
		测流压站: '#9b4b62',
		泵站: '#8f5c3e',
		压力监测站: '#909c36',
		流量监测站: '#327c53',
		水质监测站: '#5f4894',
		水源地: '#43548f',
		大用户: '#489785',
		污水处理厂: '#9e561d'
	}
	const stationData = ref<Record<string, any[]>>({})

	const formatType = (type: string) => {
		return type
	}
	const currentType = ref<string>('')
	const currentData = computed(() => {
		return stationData.value[currentType.value]
	})
	const getStationList = async (curMenu: { name: string, alias?: string, icon: string, id: string, type: string, isActive?: boolean, color?: string }) => {
		try {
			const res = await getStationListApi({
				type: curMenu.name
			})
			const data = res.data.data || []
			stationData.value[curMenu.id] = data
			setCurrent(curMenu.id)
			return currentData.value
		} catch (e) {
			//TODO handle the exception
			return []
		}
	}
	const setCurrent = (type: string) => {
		currentType.value = type
	}
	const reset = () => {
		currentType.value = ''
	}

	return {
		reset,
		setCurrent,
		currentType,
		currentData,
		formatType,
		getStationList
	}
}

export const useStationRealTimeData = () => {
	const realtimeList = ref<any[]>([])
	const getRealTimeData = async (stationId?: string, type?: string) => {
		if (stationId) {
			const real = await getStationData(stationId, type)
			realtimeList.value = real.data || []
		} else {
			realtimeList.value = []
		}
		return realtimeList.value
	}
	return {
		getRealTimeData,
		realtimeList
	}
}