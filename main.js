import App from './App'
import uView from '@/uni_modules/vk-uview-ui';
import {
	createPinia
} from "pinia";
const pinia = createPinia();
// #ifndef VUE3
import Vue from 'vue'
Vue.config.productionTip = false
App.mpType = 'app'
const app = new Vue({
	...App
})
app.$mount()
// #endif

// #ifdef VUE3
import {
	createSSRApp
} from 'vue'
import dayjs from 'dayjs';
export function createApp() {
	const app = createSSRApp(App)
	// 引入请求封装，将app参数传递到配置中
	app.use(uView)
	app.use(pinia);
	app.config.globalProperties.formatTime = (time) => {
		if (time !== null && time !== '' && time > 0) {
			return dayjs(parseInt(time)).format('YYYY-MM-DD HH:mm:ss')
		}
		return ''
	}
	const path = uni.getStorageSync('url')
	app.config.globalProperties.$uploadUrl = path + 'file/api/upload/file'
	//是否开启自定义底部菜单
	app.config.globalProperties.$isHideTabBar = true
	app.config.globalProperties.$startDate = '2000-01-01'
	app.config.globalProperties.$token = ''
	app.config.globalProperties.$url = ''
	return {
		app
	}
}
// #endif