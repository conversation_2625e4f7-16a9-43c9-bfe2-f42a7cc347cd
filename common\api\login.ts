import { http } from '../vmeitime-http/index'

export const getKey = () => {
	return http().get('api/noauth/getLoginKey')
}


export const getInfo = (userId: string) => {
	return http().get(`api/user/${userId}`)
}

export const login = (params: any) => {
	return http().post('api/auth/login', params)
}

export const changePassword = (currentPassword: string, newPassword: string) => {
	return http().post('api/auth/changePassword', {
		currentPassword,
		newPassword
	})
}
/**
 * 获取所有用户
 */
export function getAllUser() {
  return http().get(`api/user/getAllByName`);
}
//修改用户信息
export const updateUser = (params: any) => {
	return http().post('api/updateUser', params)
}
