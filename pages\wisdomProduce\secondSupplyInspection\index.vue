<template>
	<view class="main">
		<u-navbar fixed :border-bottom="false" placeholder safeAreaInsetTop bgColor="#FFFFFF" :title="type+'巡检'"
			:autoBack="true" leftIconSize="20">
			<template #right>
				<view class="nv-right" @click="showDate">
					<image src="/static/img/icons/shaixuan-black.png" style="height: 48rpx;width: 48rpx;"></image>
				</view>
			</template>
		</u-navbar>
		<u-sticky bg-color="#FFFFFF">
			<u-tabs :list="state.tabs" active-color="#3862F8" name="label" v-model="state.currentTab" bgColor="#FFFFFF"
				:is-scroll="false" :scrollable="false" @change="changeStatus"></u-tabs>
		</u-sticky>
		<!-- <scroll-view style="height: 100vh;" :lower-threshold="50" :refresher-threshold="50" scroll-y refresher-enabled
			refresher-default-style="white" :refresher-triggered="triggered" @refresherrefresh="onRefresh"
			@scrolltolower="showMoreData"> -->
		<view class="card-box" v-for="(data,index) in tableData" :key="index" @click="toDetail(data)">
			<view class="flex-between hand">
				<view class="hand-title">
					<text>{{data.name}}</text>
				</view>
				<view class="status">
					{{data.statusName}}
				</view>
			</view>
			<view class="table">
				<view class="info">
					<text>任务编号：</text> <text>{{data.code}}</text>
				</view>
				<view class="info flex-center">
					<text>巡检泵房：</text>
					<view class="bg">
						<text>{{data.stationName}}</text>
					</view>
				</view>
				<block v-if="data.status === 'PENDING'">
					<view class="info">
						<text>预计开始时间：</text> <text>{{dayjs(data.startTime).format('YYYY-MM-DD')}}</text>
					</view>
					<view class="info">
						<text>预计结束时间：</text> <text>{{dayjs(data.endTime).format('YYYY-MM-DD')}}</text>
					</view>
				</block>
				<block v-if="data.status === 'RECEIVED'">
					<view class="info">
						<text>开始时间：</text> <text>{{data.realStartTime}}</text>
					</view>
					<view class="info">
						<text>预计结束时间：</text> <text>{{data.endTime}}</text>
					</view>
				</block>
				<block v-if="data.status === 'APPROVED'">
					<view class="info">
						<text>开始时间：</text> <text>{{data.realStartTime}}</text>
					</view>
					<view class="info">
						<text>结束时间：</text> <text>{{data.realStartTime}}</text>
					</view>
				</block>
			</view>
		</view>
		<u-loadmore bg-color="F9F9F9" :status="state.status" loading-text="努力加载中" loadmore-text="加载更多"
			nomore-text="没有了更多" />
		<!-- </scroll-view> -->
		<view class="">
			<u-calendar :max-date="maxDate" v-model="state.dateShow" ref="calendar" @close="state.dateShow=false"
				@change="chooseDate"></u-calendar>
		</view>
	</view>
</template>

<script lang="ts" setup>
	import {
		reactive,
		ref,
	} from "vue"
	import {
		onShow
	} from '@dcloudio/uni-app';
	import {
		onPullDownRefresh,
		onReachBottom
	} from '@dcloudio/uni-app'
	import {
		getCircuitTaskList,
		processingAndCompleteCount
	} from '@/common/api/inspection'
	import {
		maxDate
	} from '@/common/data/publicdata'
	import {
		removeSlash
	} from '@/common/utils/removeIdSlash'
	import dayjs from 'dayjs'
	// 选择区域
	const state = reactive<{
		tabs : any,
		activceTab : any,
		dateShow : boolean,
		status : string,
		query : any,
		currentTab : number,
	}>({
		currentTab: 0,
		tabs: [{
			label: '待接收',
			value: 'PENDING'
		},
		{
			label: '待处理',
			value: 'RECEIVED'
		},
		{
			label: '已完成',
			value: 'COMPLETED'
		},
		],
		activceTab: {
			label: '待接收',
			value: 'PENDING'
		},
		dateShow: false,
		status: 'loadmore',
		query: {
			page: 1,
			size: 10
		}
	})
	const triggered = ref<boolean>()
	const type = ref<string>('')
	const tableData = ref<any>([])
	const toDetail = (params ?: any) => {
		uni.$u.route({
			url: 'pages/wisdomProduce/secondSupplyInspection/inspecionDetail/index',
			params: {
				...params,
				type: type.value
			}
		})
	}
	//选择日期
	const chooseDate = () => { }

	// 加载更多
	const showMoreData = async () => {
		console.log('dddddd')
		state.status = 'loading'
		await inspectionList()
	} ///

	// 下拉刷新
	const onRefresh = async () => {
		triggered.value = true
		state.query.page = 1
		await inspectionList()
	}
	// 切换巡检状态数据
	const changeStatus = async (index : number) => {
		state.activceTab = state.tabs[index]
		state.query.page = 1
		tableData.value = []
		await inspectionList()
	}
	// 选择日期
	const showDate = () => {
		state.dateShow = true
	}

	// 巡检列表
	const inspectionList = async () => {
		state.query = {
			...state.query,
			status: state.activceTab.value,
			executionUserId: removeSlash(uni.getStorageSync('userInfo').id?.id),
			type: type.value == '净水厂' ? '污水处理厂' : type.value
		}
		const res = await getCircuitTaskList(state.query)
		const data = res.data?.data?.data
		const total = res.data?.data?.total
		if (state.query.page === 1) {
			tableData.value = data
		} else {
			tableData.value = tableData.value.concat(data)
		}
		if (data?.length > 0 && total > tableData.value.length) {
			state.query.page += 1
			state.status = 'loadmore'
		} else {
			state.status = 'nomore'
		}
		triggered.value = false
		uni.stopPullDownRefresh()
	}

	const getCount = async () => {
		const res = await processingAndCompleteCount()
		const data = res.data?.data
	}

	onReachBottom(async () => {
		showMoreData()
	})

	onPullDownRefresh(async () => {
		onRefresh()
	})
	onShow(async () => {
		var pages = getCurrentPages();
		var page = pages[pages.length - 1];
		type.value = page.$page.options.type || '二供'
		inspectionList()
		// getCount()
	})
</script>

<style lang="scss" scoped>
	.card-box {
		width: 686rpx;
		margin: 20rpx auto;
		border-radius: 16rpx;
		padding: 22rpx 28rpx;
		background-color: #FFFFFF;

		.hand {
			border-left: 4rpx solid #3862F8;

			.hand-title {
				height: 42rpx;
				line-height: 42rpx;

				text {
					font-size: 28rpx;
					margin-left: 16rpx;
					color: #060F27;
					font-weight: 600;
				}
			}

			.status {
				color: #91949F;
			}
		}

		.table {
			margin-top: 24rpx;

			.info {
				font-size: 28rpx;
				padding-bottom: 18rpx;

				text {
					&:nth-child(1) {
						color: #91949F;
					}

					&:nth-child(2) {
						flex: 1;
					}
				}

				.bg {
					padding: 2rpx 12rpx;
					background: rgba(56, 98, 248, 0.2);
					border-radius: 8rpx;

					text {
						color: #3862F8;
					}
				}
			}
		}
	}
</style>