<!--巡检分派 -->
<template>
	<view class="main">
		<u-navbar fixed placeholder safeAreaInsetTop bgColor="#FFFFFF" title="巡检分派" :autoBack="true" leftIconSize="20">
			<template #right>
				<view class="nv-right" @click="state.dateShow=true">
					<image src="/static/img/icons/shaixuan-black.png" style="height: 48rpx;width: 48rpx;"></image>
				</view>
			</template>
		</u-navbar>
		<view class="card-box" v-for="(data,index) in tableData" :key="index" @click="toDetail(data)">
			<view class="flex-between hand">
				<view class="hand-title flex-center">
					<view class="icon-box">
						<u-icon name="custom-icon-gongdan" customPrefix="custom-icon" color="#FFFFFF" size="20">
						</u-icon>
					</view>
					<text>{{data.code}}</text>
				</view>
				<view class="status">
					待分派
				</view>
			</view>
			<view class="line">
				<u-line color="#EBEDF6" />
			</view>
			<view class="table">
				<view class="info">
					<text>任务编号：</text> <text>{{data.code}}</text>
				</view>
				<view class="info flex-center">
					<text>计划类型：</text>
					<view class="bg">
						<text>{{data.districtAreaName}}</text>
					</view>
				</view>
				<view class="info">
					<text>派发时间：</text> <text>{{data.createTime}}</text>
				</view>
				<view class="info">
					<text>开始时间：</text> <text>{{data.beginTime}}</text>
				</view>
				<view class="info">
					<text>结束时间：</text> <text>{{data.endTime}}</text>
				</view>
				<view class="info">
					<text>是否需要反馈：</text>
					<text>{{data.isNeedFeedback===true?'是':data.isNeedFeedback===false?'否':'-'}}</text>
				</view>
				<view class="info">
					<text>任务描述：</text> <text>{{data.remark}}</text>
				</view>
			</view>
		</view>
		<u-loadmore bg-color="F9F9F9" :status="state.status" loading-text="努力加载中" loadmore-text="加载更多"
			nomore-text="没有了更多" />
		<!-- </scroll-view> -->
		<u-calendar :max-date="maxDate" v-model="state.dateShow" ref="calendar" @close="state.dateShow=false"
			@change="chooseDate"></u-calendar>
	</view>
</template>

<script lang="ts" setup>
	import {
		reactive,
		ref,
		getCurrentInstance
	} from "vue";
	import {
		onShow
	} from '@dcloudio/uni-app';
	import {
		onPullDownRefresh,
		onReachBottom
	} from '@dcloudio/uni-app'
	import {
		maxDate
	} from '@/common/data/publicdata'
	import {
		getInspectionTaskList
	} from '@/common/api/inspection'
	// 选择区域
	const state = reactive < {
		dateShow: boolean,
		status: string,
		query: any,
	} > ({
		dateShow: false,
		status: 'loadmore',
		query: {
			page: 1,
			size: 10
		}
	})
	const triggered = ref < boolean > ()
	const tableData = ref < any > ([])
	
	const toDetail = (params ? : any) => {
		uni.navigateTo({
			url: './taskDetail/index?taskId=' + params.id + '&areaId=' + params.districtAreaId +
				'&taskCode=' +
				params.code + '&type=' + params.status,
		})
	}
	
	//选择日期
	const chooseDate = () => {}

	// 加载更多
	const showMoreData = async () => {
		state.status = 'loading'
		await getWorkOrderList()
	} ///

	// 下拉刷新
	const onRefresh = async () => {
		triggered.value = true
		state.query.page = 1
		await getWorkOrderList()
	}

	// 巡检列表
	const getWorkOrderList = async () => {
		state.status = 'loadmore'
		state.query = {
			...state.query,
			isReceived: false,
		}
		const res = await getInspectionTaskList(state.query)
		const data = res.data?.data?.data
		const total = res.data?.data?.total
		if (state.query.page === 1) {
			tableData.value = data
		} else {
			tableData.value = tableData.value.concat(data)
		}
		if (data.length > 0 && total > tableData.value.length) {
			state.query.page += 1
			state.status = 'loadmore'
		} else {
			state.status = 'nomore'
		}
		triggered.value = false
		uni.stopPullDownRefresh()
	}

	onReachBottom(async () => {
		showMoreData()
	})

	onPullDownRefresh(async () => {
		onRefresh()
	})

	onShow(async () => {
		onRefresh()
	})
</script>

<style lang="scss" scoped>
	.card-box {
		width: 686rpx;
		margin: 20rpx auto;
		border-radius: 16rpx;
		// padding: 22rpx 28rpx;
		padding: 0;
		background-color: #FFFFFF;

		.hand {
			padding: 22rpx 28rpx;

			.hand-title {
				height: 42rpx;
				line-height: 42rpx;

				.icon-box {
					text-align: center;
					line-height: 32rpx;
					height: 32rpx;
					width: 32rpx;
					background-color: #3862F8;
					border-radius: 4rpx;
				}

				text {
					font-size: 28rpx;
					margin-left: 16rpx;
					color: #060F27;
					font-weight: 600;
				}
			}

			.status {
				color: #91949F;
			}
		}

		.line {
			padding-bottom: 20rpx;
		}

		.table {
			margin-top: 24rpx;
			padding: 0rpx 28rpx;

			.info {
				font-size: 24rpx;
				padding-bottom: 18rpx;
				display: flex;

				text {
					&:nth-child(1) {
						color: #91949F;
					}

					&:nth-child(2) {
						flex: 1;
					}
				}


				.bg {
					padding: 2rpx 12rpx;
					background: rgba(56, 98, 248, 0.2);
					border-radius: 8rpx;

					text {
						color: #3862F8;
					}
				}
			}
		}
	}
</style>
