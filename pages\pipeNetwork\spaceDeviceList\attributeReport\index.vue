<template>
	<view class="main">
		<u-navbar fixed placeholder safeAreaInsetTop bgColor="#FFFFFF" title="属性上报" :autoBack="true" leftIconSize="20">
			<template #right>
				<view class="nv-right" @click="toRecords">
					<text>上报记录</text>
				</view>
			</template>
		</u-navbar>
		<view class="card-box">
			<u-form :model="form" ref="form1" :label-style="{'color':'#91949F'}" borderBottom labelWidth="180">
				<u-form-item v-for="(field,i) in fields" :key="i" :label="field.alia+':'" :prop="field.name">
					<u-input :placeholder="'请输入'+field.alia" v-model="form[field.name]" inputAlign="right">
					</u-input>
				</u-form-item>

			</u-form>
		</view>
		<view class="button">
			<u-button type="primary" color="#3862F8" @click="submit" :loading="loading">提交</u-button>
		</view>
	</view>
</template>

<script lang="ts" setup>
	import {
		onLoad
	} from '@dcloudio/uni-app'
	import {
		reactive, ref
	} from "vue";
	import {
		AddMapErrorUpload
	} from '@/common/api/map'
	const fields = reactive<{ name : string; alia : string; oldvalue : string; newvalue : string }[]>([
		{ name: 'Z', alia: '地面高程', oldvalue: '', newvalue: '' },
		{ name: 'DIAMETER', alia: '口径', oldvalue: '', newvalue: '' },
		{ name: 'DEPTH', alia: '埋深', oldvalue: '', newvalue: '' },
		{ name: 'BURYTYPE', alia: '埋设方式', oldvalue: '', newvalue: '' },
		{ name: 'MATERIAL', alia: '材质', oldvalue: '', newvalue: '' },
		{ name: 'remark', alia: '备注', oldvalue: '', newvalue: '' },
	])
	const state = reactive<{
		layername : string
		layerid : string
		attributes : any
	}>({
		layername: '',
		layerid: '',
		attributes: {}
	})
	const loading = ref<boolean>(false)
	const form = reactive<any>({})
	const submit = async () => {
		try {
			const content : any[] = []
			fields.filter(item => item.name !== 'remark' && (form[item.name] ?? false)).map(item => {
				const obj = {
					...item,
					newvalue: form[item.name]
				}
				content.push(obj)
			})

			const res = await AddMapErrorUpload({
				layer: state.layername,
				fid: state.attributes.OBJECTID,
				remark: form.remark,
				uploadContent: JSON.stringify(content),
			})
			if (res.data.code === 200) {
				uni.showToast({
					title: '上报成功',
					icon: 'success',
					complete: () => {
						uni.navigateBack({
							delta: 1
						})
					}
				})

			} else {
				uni.showToast({
					title: '上报失败',
					icon: 'error'
				})
			}
		} catch (e) {
			//TODO handle the exception
			console.log(e);
		}
		loading.value = false

	}
	const toRecords = () => {
		uni.navigateTo({
			url: './attributeReportRecords'
		})
	}
	onLoad((options : any) => {
		state.layername = decodeURIComponent(options.layername)
		state.layerid = decodeURIComponent(options.layerid)
		state.attributes = JSON.parse(decodeURIComponent(options.attrs))
		fields.map(item => {
			item.oldvalue = state.attributes?.[item.name]
		})
	})
</script>

<style lang="scss" scoped>
	.main {
		padding-top: 20rpx;

		.card-box {
			width: 686rpx;
			border-radius: 8px;
			padding: 20rpx 28rpx;
			margin: 0 auto;
			color: #FFFFFF;
			background-color: #FFFFFF;

			.form {
				padding: 0 28rpx 10rpx 28rpx;
				color: #060F27;
			}

			.l-file {
				padding: 10rpx 0;
			}
		}

		::v-deep.u-form-item {
			padding: 0;
		}
	}
</style>