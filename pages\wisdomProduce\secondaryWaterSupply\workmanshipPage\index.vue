<template>
	<view>
		<u-navbar safeAreaInsetTop placeholder title="" :autoBack="true" bgColor="transparent">
		</u-navbar>
		<view class="">
			
		</view>
		<view v-if="true">
			<u-empty width="105" height="137" text="暂 无 数 据 " icon="/static/img/icons/empty.png">
			</u-empty>
		</view>
		
	</view>
</template>

<script lang="ts" setup>
	import {
		onMounted,
		onUnmounted
	} from "vue"

	onMounted(async () => {
		plus.screen.lockOrientation('landscape-primary');
	})

	onUnmounted(async () => {
		plus.screen.lockOrientation('portrait-primary');
	})
</script>

<style>

</style>
