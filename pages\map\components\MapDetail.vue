<template>
  <view class="detail-main">
    <MapDetailPipeLine
      v-if="props.menu.id === 'sbzc_gw'"
      :menu="props.menu"
      :layerids="params?.layerids"
    >
    </MapDetailPipeLine>
    <MapDetailPipePoint
      v-else-if="props.menu.id === 'sbzc_fm'"
      :static-items="{
        countByType: true,
        ratioField: 'OPENCLOSE',
        ratioFieldName: '状态',
        countByRoad: true,
        countByDiameter: true,
      }"
      :menu="props.menu"
      :layerids="params?.layerids"
    ></MapDetailPipePoint>
    <MapDetailPipePoint
      v-else-if="props.menu.id === 'sbzc_sb'"
      :static-items="{
        countByType: true,
        ratioField: 'SUBTYPE',
        ratioFieldName: '类型',
        countByManufacturer: true,
        countByDiameter: true,
      }"
      :menu="props.menu"
      :layerids="params?.layerids"
    ></MapDetailPipePoint>
    <MapDetailPipePoint
      v-else-if="props.menu.id === 'sbzc_xfs'"
      :static-items="{
        countByRoad: true,
        ratioField: 'DIAMETER',
        ratioFieldName: '口径',
        ratioPrefix: 'DN',
        countByDiameter: true,
        countByManager: true,
      }"
      :menu="props.menu"
      :layerids="params?.layerids"
    ></MapDetailPipePoint>
    <MapDetailRealTime
      v-else-if="props.menu.type === 'sssb'"
      :menu="props.menu"
    ></MapDetailRealTime>
    <MapDetailPerson v-else-if="props.menu.type === 'rycl'" :menu="props.menu"></MapDetailPerson>
    <MapDetailBusiness
      v-else-if="props.menu.type === 'ywlc'"
      :menu="props.menu"
    ></MapDetailBusiness>
    <MapDetailDataSceneDMA
      v-else-if="props.menu.id === 'sjcj_dmafq'"
      :title="props.menu.name"
    ></MapDetailDataSceneDMA>
    <view class="detail-footer"></view>
  </view>
</template>

<script lang="ts" setup>
import MapDetailPipeLine from './MapDetail_PipeLine.vue';
import MapDetailPipePoint from './MapDetail_PipePoint.vue';
import MapDetailRealTime from './MapDetail_RealTime.vue';
import MapDetailPerson from './MapDetail_Person.vue';
import MapDetailBusiness from './MapDetail_Business.vue';
import MapDetailDataSceneDMA from './MapDetailDataSceneDMA.vue';
const props = defineProps<{
  params: {
    layerids: number[];
  };
  menu?: {
    name: string;
    alias?: string;
    icon: string;
    id: string;
    type: string;
    isActive?: boolean;
    color?: string;
  };
}>();
</script>

<style lang="scss">
// 状态栏占位高度
.status-bar {
  height: var(--status-bar-height);
}

// 主体内容
.detail-main {
  height: 100%;

  padding: 16rpx;

  .detail-footer {
    height: 70rpx;
    /* #ifdef APP-PLUS */
    height: 0;

    /* #endif */
  }
}
</style>
