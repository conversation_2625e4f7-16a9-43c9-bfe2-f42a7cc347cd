<template>
  <view class="measure-selector-overlay" @click="$emit('close')">
    <view class="measure-selector" @click.stop>
      <view class="selector-header">
        <text class="selector-title">选择量测类型</text>
        <text class="close-btn" @click="$emit('close')">×</text>
      </view>
      <view class="measure-options">
        <view 
          class="measure-option"
          @click="selectMeasure('distance')"
        >
          <view class="option-icon">
            <text class="custom-icon custom-icon-map-ruler-full"></text>
          </view>
          <text class="option-text">距离测量</text>
        </view>
        <view 
          class="measure-option"
          @click="selectMeasure('area')"
        >
          <view class="option-icon">
            <text class="custom-icon custom-icon-mianji"></text>
          </view>
          <text class="option-text">面积测量</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'MeasureSelector',
  emits: ['close', 'select'],
  methods: {
    selectMeasure(type) {
      this.$emit('select', type)
      this.$emit('close')
    }
  }
}
</script>

<style scoped lang="scss">
.measure-selector-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  z-index: 1000;
}

.measure-selector {
  width: 100%;
  background-color: #ffffff;
  border-radius: 16rpx 16rpx 0 0;
  padding: 40rpx 32rpx 60rpx;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.selector-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40rpx;
  
  .selector-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #060F27;
  }
  
  .close-btn {
    font-size: 48rpx;
    color: #91949F;
    width: 48rpx;
    height: 48rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.measure-options {
  display: flex;
  justify-content: space-around;
  gap: 40rpx;
}

.measure-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx 60rpx;
  border-radius: 16rpx;
  background-color: #F7F8FA;
  min-width: 200rpx;
  flex: 1;
  
  &:active {
    background-color: #E2E3E5;
  }
  
  .option-icon {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    background-color: #3862F8;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20rpx;
    
    .custom-icon {
      color: #ffffff;
      font-size: 40rpx;
    }
  }
  
  .option-text {
    font-size: 28rpx;
    color: #060F27;
    text-align: center;
  }
}
</style>