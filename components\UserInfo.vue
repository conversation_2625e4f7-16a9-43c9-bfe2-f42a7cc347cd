<template>
  <view>
    <view class="info-item">
      <text class="label">营业区域：</text>
      <text class="value">{{ userInfo.businessArea }}</text>
    </view>
    <view class="info-item">
      <text class="label">抄表手册：</text>
      <text class="value">{{ userInfo.meterReadingManual }}</text>
    </view>
    <view class="info-item">
      <text class="label">用户编号：</text>
      <text class="value">{{ userInfo.userCode }}</text>
    </view>
    <view class="info-item">
      <text class="label">用户名：</text>
      <text class="value">{{ userInfo.userName }}</text>
    </view>
    <view class="info-item">
      <text class="label">手机号码：</text>
      <text class="value">{{ userInfo.phoneNumber }}</text>
    </view>
    <view class="info-item">
      <text class="label">用水地址：</text>
      <text class="value">{{ userInfo.address }}</text>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    userInfo: {
      type: Object,
      default: () => ({}),
    },
  },
};
</script>

<style scoped>

.info-item {
  display: flex;
  margin-bottom: 20rpx;
}

.info-item .label {
  width: 200rpx;
  color: #91949f;
}
</style>
