import { ref } from 'vue'
import { workOrderList } from '../api/workOrder'
export const useWorkOrders = () => {
	const workorders = ref<{ data: any[], total: number }>({ data: [], total: 0 })
	const getWorkOrders = async (params?: {
		status?: String,
		stageBetween?: String,
		page?: number
		size?: number
	}) => {
		const res = await workOrderList({
			page: 1,
			size: 999,
			...(params || {})
		})
		workorders.value = {
			data: res.data?.data?.data || [],
			total: res.data?.data?.total || 0
		}
		return workorders.value
	}
	const reset = () => {
		workorders.value = {
			data: [],
			total: 0
		}
	}
	return {
		workorders,
		getWorkOrders,
		reset
	}
}