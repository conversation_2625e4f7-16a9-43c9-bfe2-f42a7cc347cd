<template>
  <view class="stats-section" v-if="show">
    <view class="stats-header">
      <text class="stats-title">按{{ currentChart.title }}统计</text>
      <view class="stats-actions">
        <text class="chart-index">{{ currentIndex + 1 }}/{{ visibleCharts.length }}</text>
        <text class="stats-toggle" @click="$emit('update:show', false)">收起</text>
      </view>
    </view>
    <swiper class="stats-swiper" :current="currentIndex" @change="onSwiperChange">
      <swiper-item v-for="(chart, index) in visibleCharts" :key="index">
        <view class="stats-content">
          <qiun-data-charts :type="chart.type" :chartData="getChartData(chart)" :loading="false" />
        </view>
      </swiper-item>
    </swiper>
  </view>
  <view v-else class="stats-collapsed" @click="$emit('update:show', true)">
    <text class="stats-toggle">显示资产统计</text>
  </view>
</template>

<script>
export default {
  name: 'QueryResultStats',
  props: {
    features: {
      type: Array,
      required: true,
    },
    show: {
      type: Boolean,
      default: true,
    },
  },
  emits: ['update:show'],
  data() {
    // const vm = this;
    return {
      currentIndex: 0,
      defaultOpts: {},
      chartConfigs: [
        // 管线统计
        {
          type: 'pipe',
          charts: [
            {
              type: 'pie',
              title: '材质',
              statKey: 'byMaterial',
              format: v => Number(v.toFixed(2)),
            },
            {
              type: 'pie',
              title: '管径',
              statKey: 'byDiameter',
              format: v => Number(v.toFixed(2)),
            },
          ],
          match: layer => layer?.includes('管线'),
        },
        // 测点统计
        {
          type: 'point',
          charts: [
            {
              type: 'pie',
              title: '材质',
              statKey: 'byMaterial',
              format: v => v,
            },
            {
              type: 'pie',
              title: '管径',
              statKey: 'byDiameter',
              format: v => v,
            },
          ],
          match: layer =>
            layer?.includes('测点') ||
            layer?.includes('阀门') ||
            layer?.includes('水表') ||
            layer?.includes('消防栓'),
        },
        // 测流井统计
        {
          type: 'well',
          charts: [
            {
              type: 'pie',
              title: '管径',
              statKey: 'byDiameter',
              format: v => v,
            },
          ],
          match: layer => layer?.includes('测流井'),
        },
      ],
    };
  },
  computed: {
    // 当前图层类型的图表配置
    currentConfig() {
      if (!this.features?.length) return null;
      const layer = this.features[0].properties?.layer;
      return this.chartConfigs.find(config => config.match(layer));
    },

    // 当前图层可见的图表
    visibleCharts() {
      return this.currentConfig?.charts || [];
    },

    // 当前显示的图表
    currentChart() {
      return (
        this.visibleCharts[this.currentIndex] ||
        this.visibleCharts[0] || {
          title: '资产统计',
          type: 'column',
          opts: this.defaultOpts,
        }
      );
    },

    // 统计数据
    stats() {
      if (!this.features?.length) return {};

      const stats = {
        byMaterial: {},
        byDiameter: {},
      };

      this.features.forEach(feature => {
        const material = feature.properties?.['管材'] || '未知';
        const diameter = feature.properties?.['管径'] || '未知';
        const length = parseFloat(feature.properties?.['管长']) || 0;
        const isLengthStat = this.currentConfig?.type === 'pipe';

        // 按材质统计
        stats.byMaterial[material] =
          (stats.byMaterial[material] || 0) + (isLengthStat ? length : 1);
        // 按管径统计
        stats.byDiameter[diameter] =
          (stats.byDiameter[diameter] || 0) + (isLengthStat ? length : 1);
      });

      return this.sortStats(stats);
    },
  },
  methods: {
    onSwiperChange(e) {
      this.currentIndex = e.detail.current;
    },
    // 获取图表数据
    getChartData(chart) {
      if (!this.stats || !chart.statKey || !this.stats[chart.statKey]) {
        return { series: [{ data: [] }] };
      }

      const data = this.stats[chart.statKey];
      const format = chart.format || (v => v);

      if (chart.type === 'pie') {
        return {
          series: [
            {
              data: Object.entries(data).map(([key, value]) => ({
                name: key,
                value: format(value),
              })),
            },
          ],
        };
      }

      return {
        categories: Object.keys(data),
        series: [
          {
            name: chart.title,
            data: Object.values(data).map(format),
          },
        ],
      };
    },
    // 对统计数据排序
    sortStats(stats) {
      return Object.keys(stats).reduce((sorted, key) => {
        sorted[key] = Object.entries(stats[key])
          .sort(([, a], [, b]) => b - a)
          .reduce((r, [k, v]) => ({ ...r, [k]: v }), {});
        return sorted;
      }, {});
    },
  },
};
</script>

<style scoped lang="scss">
.stats-section {
  background: #fff;
  margin: 8px 20px;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
  overflow: hidden;
}

.stats-header {
  padding: 8px 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #f0f0f0;
}

.stats-title {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
}

.stats-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.chart-index {
  font-size: 12px;
  color: #8c8c8c;
}

.stats-toggle {
  font-size: 12px;
  color: #1890ff;
  cursor: pointer;
}

.stats-swiper {
  height: 200px;
}

.stats-content {
  height: 100%;
  padding: 0;
}

.stats-collapsed {
  margin: 8px 20px;
  padding: 8px 0;
  text-align: center;
  background: #fff;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
  cursor: pointer;

  &:hover {
    background: #f5f5f5;
  }
}
</style>
