
export const gaugeOption = (val: number) => {
	return {
		series: [
			{
				type: 'gauge',
				radius: '80%',
				center: ['50%', '38%'],
				min: 0,
				max: 10,
				pointer: {
					offsetCenter: [0, '10%'],
					length: '50%',
					width: 2,
					itemStyle: {
						color: '#3862F8'
					}
				},
				progress: {
					show: true,
					overlap: true,
					roundCap: true,
					width: 4,
					itemStyle: {
						color: '#3862F8'
					}
				},
				axisTick: {
					distance: 1,
					length: 5,
				},
				splitLine: {
					distance: 1,
					length: 6,
					lineStyle: {
						width: 2
					}
				},
				axisLabel: {
					distance: 10,
				},
				axisLine: {
					roundCap: true,
					lineStyle: {
						width: 4,
						color: [[1, '#91949F']]
					}
				},
				detail: {
					valueAnimation: true,
					precision: 2,
					offsetCenter: [0, '60%'],
					fontSize: 24,
					fontWeight: 'bold',
					color: '#FFFFFF'
				},
				title: {
					offsetCenter: [0, '30%'],
					fontSize: 14,
					fontWeight: 'bold',
					color: '#FFFFFF'
				},
				data: [
					{
						value: val,
						name: '压力值(Mpa)',
					}
				]
			}
		]
	}
}

export const lineOption = (dataX?: String[], data?: any, color?: String) => {
	let dataArray = []
	console.log(data)
	for (let i in data) {
		const nData = {
			name: data[i].key,
			data: data[i].data || [],
			type: 'line',
			color: color,
			areaStyle: {
				opacity: 0.1
			}
		}
		dataArray.push(nData)
	}

	return {
		color: ['#3862F8', '#ff0000', '#00ff26'],
		animation:false,
		grid: {
			left: 50,
			right: 20,
			top: 20,
		},
		xAxis: {
			boundaryGap: false,
			data: dataX || []
		},
		yAxis: {
			type: 'value',
			show: true
		},
		tooltip: {
			trigger: 'axis',
			formatter: function(params, ticket, callback) {
				console.log(params)
				var res = "读取时间" + ' : ' + params[0].name + "月";
				for (var i = 0, l = params.length; i < l; i++) {
					res += '\n' + params[i].seriesName + ' : ' + params[i].value;//鼠标悬浮显示的字符串内容
				}
				return res;
			}
		},
		dataZoom: {
			type: 'inside',
		},
		series: {
			name: '工单数量',
			data: data || [],
			type: 'line',
			color: color,
			areaStyle: {
				opacity: 0.1
			}
		}
	}
}


export const barOption = (dataX?: String[], data?: any, color?: String) => {
	const option = {
		tooltip: {
			trigger: 'axis',
			axisPointer: {
				type: 'shadow'
			}
		},
		xAxis: {
			type: 'category',
			//文字样式
			axisLabel: {
				show: true,
				textStyle: {
					color: '#595C61'
				},
				formatter: function(value) {
					return value.split("").join("\n");
				}
			},
			data: dataX
		},
		yAxis: {
			type: 'value'
		},
		series: [
			{
				label: {
					show: true,
					position: 'top'
				},
				data: data,
				type: 'bar'
			}
		]
	}
	return option
}

export const pieOption = ( data?: any, color?: String) => {
	const option = {
		tooltip: {
			trigger: 'item'
		},
		legend: {
			orient: 'vertical',
			left: 'left',
			itemWidth: 10,
			itemHeight: 10,
			textStyle: {
				fontSize: 10
			}
		},
		series: [
			{
				name: '',
				top: 10,
				type: 'pie',
				radius: '90%',
				left:'20%',
				label: {
					position: 'inner',
					fontSize: 12,
					color: '#ffffff',
					formatter: '{d}% \n {b}'
				},
				labelLine: {
					show: false
				},
				data: data
			}
		]
	}
	return option
}