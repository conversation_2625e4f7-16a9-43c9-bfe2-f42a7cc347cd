## 1.7.7（2024-03-26）
增加expandedMode=singe
## 1.7.6（2024-02-27）
修复特殊字符的处理方法
## 1.7.5（2024-02-26）
增加无子节点的父节点配置
## 1.7.4（2024-02-05）
修复vue2，语法使用问题
## 1.7.3（2024-02-05）
修复vue2，插槽不显示bug
## 1.7.2（2024-02-05）
优化大数据的性能
## 1.7.1（2024-01-25）
优化功能说明
## 1.7.0（2024-01-24）
增加expandedKeys默认展开项目，配置
## 1.6.9（2024-01-23）
增加change事件
## 1.6.8（2024-01-22）
搜索模式下，不再响应展开收起逻辑
## 1.6.7（2024-01-04）
修复异步加载节点搜索展示bug
## 1.6.6（2023-12-29）
修复next-tree异步加载节点关闭bug
## 1.6.5（2023-12-21）
fix bug
## 1.6.4（2023-12-21）
fix bug
## 1.6.3（2023-12-21）
更新vue2版本说明文档
## 1.6.2（2023-12-21）
修复说明文档
## 1.6.1（2023-12-18）
增加empty插槽
## 1.6.0（2023-12-18）
增加empty插槽
## 1.5.9（2023-12-15）
修改说明文件
## 1.5.8（2023-12-15）
修复changeVerify函数单选时，返回参数bug
## 1.5.7（2023-12-14）
修复checkStrictlyModel === 'strong'的bug
## 1.5.6（2023-12-13）
代码优化
## 1.5.5（2023-12-13）
优化changeVerify的使用
## 1.5.4（2023-12-12）
修复提示层级问题
## 1.5.3（2023-12-12）
优化uiMode=page模式下的使用
## 1.5.2（2023-12-11）
增加uiMode配置，实现页面模式展示
## 1.5.1（2023-12-06）
更新说明
## 1.5.0（2023-12-06）
更新插件使用注意事项
## 1.4.9（2023-12-01）
增加topBar插槽
## 1.4.8（2023-11-30）
增加changeVerify验证函数，实现change的各种控制
## 1.4.7（2023-11-30）
增加弹层容器高度可配置
## 1.4.6（2023-11-28）
修复bug
## 1.4.5（2023-11-28）
修复disabled是，需要显示灰色不可操作
## 1.4.4（2023-11-28）
增加说明
## 1.4.3（2023-11-28）
增加主题配置
## 1.4.2（2023-11-28）
增加异步加载时，子节点说明
## 1.4.1（2023-11-27）
修复说明bug
## 1.4.0（2023-11-27）
next-tree 全面说明文档
## 1.3.6（2023-11-27）
增加远程加载loadData，全面实现全功能覆盖
## 1.3.5（2023-11-27）
增加title的定义
## 1.3.4（2023-11-27）
增加title支持自定义定制
## 1.3.3（2023-11-21）
增加搜索模式searchModel=depHighlight模式，从属高亮显示模式
## 1.3.2（2023-11-20）
修复valueKey设置bug
## 1.3.1（2023-11-17）
增加说明文件，和demo
## 1.3.0（2023-11-17）
修复clear时不支持关联模式的设置
## 1.2.9（2023-11-17）
增加checkStrictlyModel模式设置，强关联，和弱关联
## 1.2.8（2023-11-16）
增加next-tree的辅助线模式
## 1.2.7（2023-11-16）
优化next-tree
## 1.2.6（2023-11-16）
修复搜索时，隐藏未打开的数据
## 1.2.5（2023-11-16）
修复搜索无法点击，和级联半选不生效问题
## 1.2.4（2023-11-16）
更新新功能插件使用说明
## 1.2.3（2023-11-16）
增加插槽模式，只是高ui要求定制
## 1.2.2（2023-11-15）
修复checkStrictly配置下，子关联父的选择状态
## 1.2.1（2023-11-15）
增加半选提示功能配置showHalfCheckedTips
## 1.2.0（2023-11-14）
修复disabled配置状态下，父子级联，不需要改变disabled设置项的选择状态
## 1.1.9（2023-11-13）
增强大数据量体验交互，增加筛选搜索模式
## 1.1.8（2023-11-13）
增加清除clear和取消cancel事件
## 1.1.7（2023-11-08）
更新next-tree插件功能清单说明
## 1.1.6（2023-11-07）
update说明文档
## 1.1.5（2023-11-07）
update
## 1.1.4（2023-11-07）
更新readme.md说明
## 1.1.3（2023-11-07）
更新说明demo
## 1.1.2（2023-11-07）
增加子节点按需渲染演示demo
## 1.1.1（2023-11-07）
增加清空功能
## 1.1.0（2023-11-07）
增加子孙节点按需渲染，扩展本插件支持大数据量渲染；
## 1.0.9（2023-10-26）
增加文件说明
## 1.0.8（2023-09-14）
增加禁用节点属性配置disabledKey
## 1.0.7（2023-09-06）
增加checkStrictly，实现父子节点关联
## 1.0.6（2023-09-06）
更新vue2使用过程视图不更新的技术说明
## 1.0.5（2023-09-06）
修复说明文档
## 1.0.4（2023-06-19）
修改demo
## 1.0.3（2023-06-19）
更新vue2的使用demo
## 1.0.2（2023-06-19）
修复说明文档

## 1.0.1（2023-05-10）
更新说明文件
## 1.0.0（2023-05-09）
初始化项目
