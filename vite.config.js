import { defineConfig } from "vite";
import uni from "@dcloudio/vite-plugin-uni";

export default defineConfig({
  plugins: [
    uni({
      vueOptions: {
        template: {
          compilerOptions: {
            // 将所有grtc-开头的标签作为自定义元素处理
            isCustomElement: (tag) => tag.startsWith("grtc-"),
          },
        },
      },
    }),
  ],
  // 配置代理
  server: {
    proxy: {
      "/api": {
        // target: 'http://10.6.4.106:8081',
        target: "http://10.0.31.224:8848",
        changeOrigin: true,
        ws: true,
        configure: (proxy, _options) => {
          proxy.on("error", (err, _req, _res) => {
            console.log("proxy error", err);
          });
          proxy.on("proxyReq", (proxyReq, req, _res) => {
            console.log(
              "Sending Request to the Target:",
              req.method,
              req.url,
              req.headers
            );
          });
          proxy.on("proxyRes", (proxyRes, req, _res) => {
            console.log(
              "Received Response from the Target:",
              proxyRes.statusCode,
              req.url
            );
          });
        },
        // rewrite: (path) => path.replace(/^\/api/, '')
      },
      "/server": {
        target: "http://10.6.6.110:8080",
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/server/, ""),
      },
      "/geoserver": {
        target: "http://10.0.31.216:18080",
        changeOrigin: true,
      },
      "/istar": {
        target: "http://10.0.31.224:8848",
        changeOrigin: true, // 是否跨域，虚拟的站点需要更管origin
      },
      "/prod-api": {
        target: "http://10.0.31.224:8202",
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/prod-api/, ""),
        secure: false,
        configure: (proxy, _options) => {
          proxy.on("error", (err, _req, _res) => {
            console.log("prod-api proxy error", err);
          });
          proxy.on("proxyReq", (proxyReq, req, _res) => {
            console.log("Sending prod-api Request:", req.method, req.url);
          });
          proxy.on("proxyRes", (proxyRes, req, _res) => {
            console.log(
              "Received prod-api Response:",
              proxyRes.statusCode,
              req.url
            );
          });
        },
      },
    },
  },
});
