<!-- 巡检上报 -->
<template>
	<view class="main">
		<u-navbar fixed placeholder safeAreaInsetTop bgColor="#FFFFFF" title="巡检上报" :autoBack="true" leftIconSize="20">
			<template #right>
				<view class="nv-right" @click="toEmergency">
					<text>应急上报</text>
				</view>
			</template>
		</u-navbar>
		<view class="detail">
			<u-form :model="form" ref="refForm" :label-style="{'color':'#91949F'}" borderBottom labelWidth="180"
				input-align="right" :error-type="['toast']">
				<view class="card-box">
					<u-form-item required label="关键点名称：" prop="pointId">
						<u-input placeholder=" " v-model="form.nameAlia" disabled>
						</u-input>
					</u-form-item>

					<u-form-item required label="有无隐患：" prop="isPitfall">
						<template #right>
							<u-switch slot="right" v-model="form.isPitfall"></u-switch>
						</template>
					</u-form-item>
					<template v-if="form.isPitfall">
						<u-form-item label="事件标题：" required prop="title" borderBottom>
							<u-input placeholder="请输入标题" v-model="form.title" input-align="right">
							</u-input>
						</u-form-item>
						<u-form-item label="事件来源：" required prop="source" borderBottom>
							<template #right>
								<u-icon name="arrow-right" size="28" color="#91949F"></u-icon>
							</template>
							<input inputmode="none" placeholder-class="placeholderClass" placeholder="事件来源"
								v-model="form.source" input-align="right"
								@click="WorkOrderSource.toggle(true,()=>Address.mapShow.value = false)">
							</input>
						</u-form-item>

						<u-form-item label="事件类型：" required prop="type" borderBottom>
							<template #right>
								<u-icon name="arrow-right" size="28" color="#91949F"></u-icon>
							</template>
							<input v-model="form.type" placeholder="事件类型" inputmode="none"
								placeholder-class="placeholderClass" input-align="right"
								@click="WorkOrderType.toggle(true,()=>Address.mapShow.value = false)">
							</input>
						</u-form-item>

						<!-- <u-form-item label="事件内容：" prop="form.val2" borderBottom @click="typesContentShow=true">
							<template #right>
								<u-icon name="arrow-right" size="28" color="#91949F"></u-icon>
							</template>
							<input v-model="form.val2" inputmode="none" placeholder-class="placeholderClass" placeholder="请选择事件内容" input-align="right">
							</input>
						</u-form-item> -->

						<u-form-item label="紧急程度：" required prop="level" borderBottom>
							<template #right>
								<u-icon name="arrow-right" size="28" color="#91949F"></u-icon>
							</template>
							<input v-model="form.level" inputmode="none" placeholder-class="placeholderClass"
								placeholder="紧急程度" input-align="right"
								@click="WorkOrderEmergency.toggle(true,()=>Address.mapShow.value = false)">
							</input>
						</u-form-item>
						<u-form-item label="事件地址：" required prop="address" borderBottom>
							<u-input v-model="Address.address.value" placeholder="请输入事件地址" input-align="right"
								@blur="changeAddress">
							</u-input>
							<template #right>
								<view @click="chooseAddress" class="dw">
									<image src="/static/img/icons/dw.png" style="height:40rpx;width:40rpx">
									</image>
								</view>
							</template>
						</u-form-item>
						<!-- <u-form-item label="事件地址：" required prop="address" borderBottom>
							<u-input v-model="Address.address.value" placeholder="事件地址" input-align="right">
							</u-input>
							<template #right>
								<image v-if="!readonly" src="/static/img/icons/location.png" @click="Address.locaton()"
									style="margin-bottom:20rpx;height: 36rpx;width: 36rpx;">
								</image>
							</template>
						</u-form-item>
						<u-form-item v-if="!readonly" label="" borderBottom>
							<view style="width: 100%; height: 120px;">
								<map v-show="Address.mapShow.value" style="width: 100%; height: 120px;" id="maps"
									:controls="Address.controls.value" :latitude="Address.latitude.value"
									:longitude="Address.longitude.value" @regionchange="Address.regionchange">
								</map>
							</view>
						</u-form-item> -->
						<u-form-item label="详细描述：" required :borderBottom="false">
						</u-form-item>
						<u-form-item label="" prop="remark" borderBottom>
							<u-input type="textarea" input-align="left" v-model="form.remark" placeholder="详细描述" border
								color="#91949F">
							</u-input>
						</u-form-item>

						<u-form-item label="处理级别：" required prop="processLevelName" borderBottom>
							<template #right>
								<u-icon name="arrow-right" size="28" color="#91949F"></u-icon>
							</template>
							<input v-model="form.processLevelName" inputmode="none" placeholder-class="placeholderClass"
								placeholder="处理级别" input-align="right"
								@click="WorkOrderProcessLevel.toggle(true,()=>Address.mapShow.value = false)">
							</input>
						</u-form-item>

						<u-form-item label="处理时长：" required prop="processLevel" borderBottom>
							<template #right>
								<text>分钟</text>
							</template>
							<u-input v-model="form.processLevel" disabled placeholder="处理时长" input-align="right">
							</u-input>
						</u-form-item>

						<u-form-item label="抄送人员：" prop="ccUserName">
							<input v-if="readonly" v-model="form.ccUserName" inputmode="none"
								placeholder-class="placeholderClass" placeholder="抄送人员" input-align="right">
							</input>
							<view v-else class="flex-center" @click.stop="WorkOrderCCUser.toChooseUser">
								<view class="flex-center" style="flex-wrap: wrap;margin: 4rpx;">
									<u-tag :text="user.firstName" type="primary" closeable
										v-for="(user,index) in WorkOrderCCUser.users.value" :key="index" :index="index"
										@close="WorkOrderCCUser.remove" />
									<!-- <text>{{user}}</text>
									<u-icon name="close-circle-fill" size="32" color="#FFFFFF" @click="removeCC(index)">
									</u-icon> -->
								</view>
							</view>
							<template v-if="!readonly" #right>
								<u-icon name="arrow-right" size="28" color="#91949F"></u-icon>
							</template>
						</u-form-item>
					</template>
				</view>

				<view v-if="form.isPitfall" class="card-box">
					<u-form-item required label="发起人员：">
						<template #right>
							<u-subsection fontSize="24" height="60" activeColor="#3862F8" inactiveColor="#91949F"
								mode="subsection" :list="WorkOrderUserType.list.value"
								v-model="WorkOrderUserType.selected.value">
							</u-subsection>
						</template>
					</u-form-item>
					<block v-if="WorkOrderUserType.selected.value ===1">
						<u-form-item label="姓名：" prop="form.uploadUserId" required>
							<u-input v-if="readonly" v-model="form.uploadUserName" placeholder="发起人姓名">
							</u-input>
							<u-input v-else v-model="form.uploadUserId" placeholder="发起人姓名">
							</u-input>
						</u-form-item>
						<u-form-item label="联系方式：" prop="form.uploadPhone" required>
							<u-input v-model="form.uploadPhone" placeholder="发起人联系方式">
							</u-input>
						</u-form-item>
						<u-form-item label="地址：" prop="form.uploadAddress">
							<u-input v-model="form.uploadAddress" placeholder="发起人地址">
							</u-input>
						</u-form-item>
					</block>
				</view>
			</u-form>
			<FileUpload :readonly="readonly" :videos="videoList" :files="fileList" :imgs="imageList" :audios="audioList"
				ref="refFileUpload"></FileUpload>
		</view>
		<view class="button">
			<u-button v-if="readonly" type="primary" color="#3862F8" @click="back">返回</u-button>
			<u-button v-else type="primary" color="#3862F8" @click="submit">提交</u-button>
		</view>
		<!-- 告警类型 -->
		<u-picker v-model="WorkOrderSource.show.value" mode="selector"
			:default-selector="WorkOrderSource.selected.value" :range="WorkOrderSource.list.value" range-key="name"
			@confirm="(val:any)=>WorkOrderSource.change(val,(option:any) =>{form.source = option;Address.mapShow.value = true})"
			@cancel="Address.mapShow.value = true">
		</u-picker>
		<!-- 告警类型 -->
		<!-- <u-picker v-model="state.typesShow" mode="selector" :default-selector="state.typeIndex" :range="workOrderTypes"
			range-key="name" @confirm="selectType"></u-picker> -->
		<u-select valueName="id" labelName="name" childName="children" v-model="WorkOrderType.show.value"
			mode="mutil-column-auto"
			@confirm="(val:any[])=>WorkOrderType.optionChange(val,(option:any) =>{form.type=option.label;Address.mapShow.value = true})"
			:list="WorkOrderType.list.value" @cancel="Address.mapShow.value = true"></u-select>
		<!-- 紧急程度 -->
		<u-picker v-model="WorkOrderEmergency.show.value" mode="selector"
			:default-selector="WorkOrderEmergency.selected.value" :range="WorkOrderEmergency.list.value"
			range-key="name" @confirm="(val:any[])=>WorkOrderEmergency.change(val,(option: any) =>{
				form.level = option.name
				Address.mapShow.value = true
			})" @cancel="Address.mapShow.value = true"></u-picker>
		<!-- 处理级别 -->
		<u-picker v-model="WorkOrderProcessLevel.show.value" mode="selector"
			:default-selector="WorkOrderProcessLevel.selected.value" :range="WorkOrderProcessLevel.list.value"
			range-key="name"
			@confirm="(val: any[]) => WorkOrderProcessLevel.change(val,handleWorkOrderProcessLevelChange)"
			@cancel="Address.mapShow.value = true"></u-picker>
		<u-toast ref="refToast" />
	</view>
</template>

<script lang="ts" setup>
	import {
		onReady,
		onLoad,
		onShow
	} from '@dcloudio/uni-app';
	import {
		onBeforeMount,
		onMounted,
		reactive,
		ref
	} from "vue";
	import {
		queryGeocoder
	} from '@/common/api/map'
	import {
		userTypes,
		sourceTypes
	} from '@/common/data/publicData'
	import FileUpload from '../../../../components/fileUpload/fileUpload.vue'
	import {
		useSelect
	} from "../hooks/useSelect";
	import {
		workOrderTypeList,
		workOrderEmergencyLevelList,
		processLevelList,
	} from '@/common/data/workOrderData'
	import {
		removeChildren
	} from "@/common/utils/uSelect";
	import {
		useAddress
	} from "../hooks/useAddress";
	import {
		useUserSelector
	} from '@/common/hooks';
	import {
		getInspectionReportRecord,
		postInspectionWorkOrder
	} from '@/common/api/inspection';
	import {
		removeSlash
	} from '@/common/utils/removeIdSlash';
	const refForm = ref<any>({})
	const refFileUpload = ref<any>({})
	const refToast = ref<any>({})
	const readonly = ref<boolean>(false)
	const form = ref<Record<string,
		any>>({})
	const rules = reactive<any>({
		title: [{
			required: true,
			message: '请输入事件标题',
		}],
		source: [{
			required: true,
			message: '请选择事件来源',
		}],
		type: [{
			required: true,
			message: '请选择事件类型',
			trigger: ['change', 'blur'],
		}],
		level: [{
			required: true,
			message: '请选择紧急程度',
			trigger: ['change', 'blur'],
		}],
		coordinateName: [{
			required: true,
			message: '请输入地址',
			trigger: ['change', 'blur'],
		}],
		remark: [{
			required: true,
			message: '请输入详细描述',
			trigger: ['change', 'blur'],
		}],
		processLevelName: [{
			required: true,
			message: '处理级别',
			trigger: ['change', 'blur'],
		}]
	})
	const handleWorkOrderProcessLevelChange = (option : any) => {
		console.log(option);
		form.value.processLevelName = option.name
		form.value.processLevel = option.dayTime * 1440 + option.hourTime * 60 + option.minuteTime
		Address.mapShow.value = true
	}
	const audioList = ref<any[]>([])
	const videoList = ref<any[]>([])
	const fileList = ref<any[]>([])
	const imageList = ref<any[]>([])
	const getDetail = async () => {
		const res = await getInspectionReportRecord(form.value.taskCode, form.value.pointId)
		const data = res.data.data || {}
		imageList.value = data.image?.split(',') || []
		videoList.value = data.video?.split(',') || []
		audioList.value = data.audio?.split(',') || []
		fileList.value = data.file?.split(',') || []
		form.value = {
			...form.value,
			...data,
			title: data.content,

		}
		const processLevelIndex = WorkOrderProcessLevel.list.value.findIndex(item => item.value === data
			.processLevel)
		WorkOrderProcessLevel.change([processLevelIndex], (option : any) => {
			form.value.processLevelName = option?.name
			form.value.processLevel = option?.value
			Address.mapShow.value = true
		})
		WorkOrderUserType.selected.value = data?.isOutside ? 1 : 0
		Address.address.value = data?.coordinateName
		if (data.coordinate) {
			const coords = data.coordinate.split(',')
			if (coords.length === 2) {
				Address.longitude.value = Number(coords[0])
				Address.latitude.value = Number(coords[1])
			}
		}
	}
	const Address = useAddress()
	const WorkOrderSource = useSelect()
	const WorkOrderType = useSelect()
	const WorkOrderEmergency = useSelect()
	const WorkOrderProcessLevel = useSelect()
	const WorkOrderCCUser = useUserSelector()
	const WorkOrderUserType = useSelect(0)
	const back = () => {
		uni.navigateBack()
	}
	const submit = () => {
		refForm.value.validate((valid : boolean) => {
			if (valid) {
				uni.showModal({
					title: '提示',
					content: '确定提交',
					success: function (res) {
						if (res.confirm) {
							const uploadUserInfo = WorkOrderUserType.selected.value === 1 ? {
								"uploadUserId": form.value.uploadUserId,
								"uploadPhone": form.value.uploadPhone,
								"uploadNo": form.value.uploadNo,
								"uploadAddress": form.value.uploadAddress,
							} : {}
							const pitfallForm = form.value.isPitfall ? {
								title: form.value.title,

								ccUserId: WorkOrderCCUser.users.value?.map(user => removeSlash(
									user.id?.id)).join(','),
								coordinate: Address.lonlat.value,
								coordinateName: Address.address.value,
								"level": form.value.level,
								"type": form.value.type,
								"processLevel": form.value.processLevel,
								"source": form.value.source,
								"remark": form.value.remark,
							} : {}
							const params = {
								code: form.value.taskCode,
								pointId: form.value.pointId,
								isPitfall: form.value.isPitfall,
								videoUrl: refFileUpload.value?.videoList.join(','),
								audioUrl: refFileUpload.value?.voiceList.join(','),
								imgUrl: refFileUpload.value?.imageList.join(','),
								otherFileUrl: refFileUpload.value?.fileList.join(','),
								...pitfallForm,
								...uploadUserInfo
							}
							console.log('提交数据', params)
							postInspectionWorkOrder(params).then((res : any) => {
								if (res.data?.code === 200) {
									refToast.value.show({
										title: '提交成功',
										type: 'success',
										back: true
									})
								} else {
									refToast.value.show({
										title: res.data?.err,
										type: 'error',
									})
								}
							}).catch((error : any) => {
								refToast.value.show({
									title: '提交失败',
									type: 'error',
								})
								console.log(error);
							})
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			} else {
				console.log('验证失败');
			}
		})
	}
	onBeforeMount(() => {
		// 事件源
		WorkOrderSource.setList(sourceTypes)
		const xunJianIndex = sourceTypes.findIndex(item => item === '巡检养护')
		WorkOrderSource.selected.value = [xunJianIndex]
		form.value.source = sourceTypes[xunJianIndex]

		// 事件类型
		WorkOrderType.setList(async () => {
			const res = await workOrderTypeList()
			removeChildren(res)
			return res
		})
		WorkOrderEmergency.setList(workOrderEmergencyLevelList)
		WorkOrderProcessLevel.setList(processLevelList)
		WorkOrderUserType.setList(userTypes)
	})

	const onLoadUpdate = item => {
		Address.address.value = item.address
		Address.latitude.value = item.coordinate.lat
		Address.longitude.value = item.coordinate.lon
	}
	const changeAddress = () => {
		queryGeocoder({ keyWord: Address.address.value }).then((res : any) => {
			const location = res.data?.location
			Address.latitude.value = location.lat
			Address.longitude.value = location.lon
		})
	}
	const chooseAddress = () => {
		uni.navigateTo({
			url: '/pages/map/tianMap'
		})
	}
	//应急任务
	const toEmergency = (data)=>{
		uni.$u.route({
			url: '/pages/smartOperation/emergencyTasks/newTask/index',
			params:{
				pointId:form.value.pointId
			}
		})
	}
	
	// onMounted(() => {
	// 	!readonly.value &&
	// 		Address.locaton()
	// })

	onReady(() => {
		// todo: 需要匹配上表单和传过来的值
		refForm.value.setRules(rules);
	})
	onLoad((options : any) => {
		if (!options?.row) return
		const SID = options.SID
		readonly.value = options.type === 'APPROVED'
		const row = JSON.parse(decodeURIComponent(options.row))
		form.value = {
			nameAlia: row.name + (SID ? '(' + SID + ')' : ''),
			pointId: row.deviceType,
			taskCode: row.taskCode
		}
		if (readonly.value) {
			getDetail()
		}
	})

	onShow(() => {
		uni.$on("updateLocation", onLoadUpdate);
		// uni.$on("chooseUserData", chooseUser);
	})
</script>

<style lang="scss" scoped>
	.main {
		background-color: #FBFBFB;
		padding: 20rpx 0 0 0;
	}

	.detail {
		padding-bottom: 120rpx;

		.card-box {
			width: 686rpx;
			margin: 0 auto;
			border-radius: 16rpx;
			padding: 12rpx 28rpx;
			background-color: #FFFFFF;

			.icon {
				color: #3862F8;
			}

		}

		.content-card {
			width: 686rpx;
			margin: 20rpx auto;
			background-color: #FFFFFF;
			min-height: 224rpx;
			border-radius: 8px;
			padding: 24rpx 0 0 28rpx;

			text {
				color: #91949F;
				font-size: 28rpx;
			}

			.file-s {
				width: 112rpx;
				height: 112rpx;
				margin-top: 20rpx;
				display: flex;
				justify-content: center;
				flex-direction: column;
				text-align: center;
				background: #F9F9F9;

				text {
					color: #91949F;
					font-size: 20rpx;
				}
			}

			.margin-center {
				margin: 0 auto;

				.icon {
					font-size: 48rpx;
				}
			}
		}
	}

	::v-deep .u-form-item__body {
		padding: 16rpx;
	}

	::v-deep .u-form-item {
		padding: 0;
	}
</style>