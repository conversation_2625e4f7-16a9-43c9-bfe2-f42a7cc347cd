// import {
// 	setSymbol,
// 	getLayerById,
// 	loadEsriModules
// } from '../../../../common/utils/arcMapHelper'

// export const useDistrict = () => {
// 	const staticState = {
// 		graphicsLayer: undefined,
// 		keyPointLayer: undefined,
// 		view: undefined
// 	}
// 	const removeAll = () => {
// 		staticState.graphicsLayer?.removeAll()
// 		staticState.keyPointLayer?.removeAll()
// 	}
// 	const addDistrict = async (
// 		view,
// 		areaId,
// 		pointjson
// 	) => {
// 		if (!view || areaId === undefined || !pointjson) return
// 		const [Graphic] = await loadEsriModules(['esri/Graphic'])
// 		staticState.view = view
// 		staticState.graphicsLayer = getLayerById(staticState.view, 'district-area')
// 		removeAll()
// 		const point = JSON.parse(pointjson)
// 		if (!point.geometry) return
// 		// 只有这两种情况：面和线
// 		const type = point.geometry.rings
// 			? 'polygon'
// 			: point.geometry.paths
// 				? 'polyline'
// 				: ''
// 		if (!type) return
// 		const geo = {
// 			type: type,
// 			spatialReference: point.geometry.spatialReference
// 		}
// 		type === 'polygon' ? (geo.rings = point.geometry.rings) : (geo.paths = pointjson.geometry.paths)

// 		const graphic = new Graphic({
// 			geometry: geo,
// 			symbol: setSymbol(type),
// 			attributes: { areaId }
// 		})
// 		staticState.graphicsLayer?.add(graphic)

// 		if (!pointjson.bufferGeometry?.rings) return
// 		const bufferGeometry = {
// 			type: 'polygon',
// 			rings: pointjson.bufferGeometry.rings,
// 			spatialReference: pointjson.bufferGeometry.spatialReference
// 		}
// 		const buffer = new Graphic({
// 			geometry: bufferGeometry,
// 			symbol: setSymbol(bufferGeometry?.type || 'polygon', {
// 				color: [0, 255, 0, 0.1],
// 				outlineWidth: 1,
// 				outlineColor: '#00ff00'
// 			})
// 		})
// 		staticState.graphicsLayer?.add(buffer)
// 	}

// 	const refreshKeyPoint = async (pointData = []) => {
// 		staticState.keyPointLayer = getLayerById(staticState.view, 'key-point')
// 		const [Graphic] = await loadEsriModules(['esri/Graphic'])

// 		pointData.map(item => {
// 			const geometry = {
// 				type: 'point',
// 				longitude: item.lon,
// 				latitude: item.lat,
// 				spatialReference: staticState.view?.spatialReference
// 			}
// 			const g = new Graphic({
// 				geometry,
// 				symbol: setSymbol('point', {
// 					outlineWidth: 1,
// 					outlineColor: '#00ffff',
// 					color: '#ff0000'
// 				}),
// 				attributes: { ...item }
// 			})
// 			const t = new Graphic({
// 				geometry,
// 				symbol: setSymbol('text', {
// 					text: item.name,
// 					color: '#ff0000',
// 					yOffset: -25
// 				}),
// 				attributes: { ...item }
// 			})
// 			staticState.keyPointLayer?.addMany([g, t])
// 		})
// 	}
// 	const destroy = () => {
// 		staticState.graphicsLayer
// 			&& staticState.view?.map?.remove(staticState.graphicsLayer)
// 		staticState.keyPointLayer
// 			&& staticState.view?.map?.remove(staticState.keyPointLayer)
// 	}
// 	const extentTo = (type: 'area' | 'point', id?: string) => {
// 		const graphic = type === 'area'
// 			? staticState.graphicsLayer?.graphics?.[0]
// 			: staticState.keyPointLayer?.graphics?.find(
// 				item => item.attributes?.id === id
// 			)
// 		if (!graphic) return
// 		staticState.view?.goTo({
// 			zoom: 17,
// 			target: graphic
// 		})
// 	}

// 	return {
// 		removeAll,
// 		addDistrict,
// 		destroy,
// 		refreshKeyPoint,
// 		extentTo
// 	}
// }
