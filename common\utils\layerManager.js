/**
 * 图层管理工具类
 * 提供图层的增删改查、状态管理等功能
 */

import { 
  getLayersFromView, 
  getLayerDetails, 
  getLayerFields, 
  getLayerStatistics,
  loadEsriModules 
} from './arcMapHelper'

export class LayerManager {
  constructor(view) {
    this.view = view
    this.layerCache = new Map() // 图层信息缓存
    this.eventHandlers = new Map() // 事件处理器
    this.initEventListeners()
  }

  /**
   * 初始化事件监听
   */
  initEventListeners() {
    if (!this.view || !this.view.map) return

    // 监听图层添加事件
    this.view.map.layers.on('after-add', (event) => {
      this.onLayerAdded(event.item)
    })

    // 监听图层移除事件
    this.view.map.layers.on('after-remove', (event) => {
      this.onLayerRemoved(event.item)
    })

    // 监听图层变化事件
    this.view.map.layers.on('change', () => {
      this.clearCache()
    })
  }

  /**
   * 图层添加事件处理
   */
  onLayerAdded(layer) {
    console.log('图层已添加:', layer.title || layer.id)
    this.clearCache()
    this.emit('layer-added', { layer })
  }

  /**
   * 图层移除事件处理
   */
  onLayerRemoved(layer) {
    console.log('图层已移除:', layer.title || layer.id)
    this.clearCache()
    this.emit('layer-removed', { layer })
  }

  /**
   * 获取所有图层
   */
  async getAllLayers(options = {}) {
    const cacheKey = JSON.stringify(options)
    
    if (this.layerCache.has(cacheKey)) {
      return this.layerCache.get(cacheKey)
    }
    const layers = await getLayersFromView(this.view, options)
    this.layerCache.set(cacheKey, layers)
    
    return layers
  }

  /**
   * 获取业务图层
   */
  async getOperationalLayers() {
    return this.getAllLayers({
      includeBaseLayers: false,
      includeGraphicsLayers: false,
      includeOperationalLayers: true
    })
  }

  /**
   * 获取可查询图层
   */
  async getQueryableLayers() {
    return this.getAllLayers({
      includeBaseLayers: false,
      includeGraphicsLayers: false,
      includeOperationalLayers: true,
      queryableOnly: true
    })
  }

  /**
   * 获取可见图层
   */
  async getVisibleLayers() {
    return this.getAllLayers({
      includeBaseLayers: true,
      includeGraphicsLayers: true,
      includeOperationalLayers: true,
      visibleOnly: true
    })
  }

  /**
   * 根据ID获取图层
   */
  getLayerById(layerId) {
    if (!this.view || !this.view.map) return null
    return this.view.map.findLayerById(layerId)
  }

  /**
   * 根据标题搜索图层
   */
  async searchLayersByTitle(title) {
    const allLayers = await this.getAllLayers()
    return allLayers.filter(layer => 
      layer.title && layer.title.toLowerCase().includes(title.toLowerCase())
    )
  }

  /**
   * 根据类型获取图层
   */
  async getLayersByType(type) {
    const allLayers = await this.getAllLayers()
    return allLayers.filter(layer => layer.type === type)
  }

  /**
   * 添加图层到地图
   */
  async addLayer(layerConfig, position = 'top') {
    if (!this.view || !this.view.map) return null

    try {
      let layer = null

      // 根据配置创建不同类型的图层
      switch (layerConfig.type) {
        case 'feature':
          layer = await this.createFeatureLayer(layerConfig)
          break
        case 'map-image':
          layer = await this.createMapImageLayer(layerConfig)
          break
        case 'wms':
          layer = await this.createWMSLayer(layerConfig)
          break
        case 'graphics':
          layer = await this.createGraphicsLayer(layerConfig)
          break
        default:
          throw new Error(`不支持的图层类型: ${layerConfig.type}`)
      }

      if (layer) {
        // 添加到地图
        if (position === 'top') {
          this.view.map.add(layer)
        } else if (position === 'bottom') {
          this.view.map.add(layer, 0)
        } else if (typeof position === 'number') {
          this.view.map.add(layer, position)
        }

        console.log('图层添加成功:', layer.title || layer.id)
        return layer
      }
    } catch (error) {
      console.error('添加图层失败:', error)
      throw error
    }

    return null
  }

  /**
   * 创建 FeatureLayer
   */
  async createFeatureLayer(config) {
    const [FeatureLayer] = await loadEsriModules(['esri/layers/FeatureLayer'])
    
    return new FeatureLayer({
      id: config.id,
      title: config.title,
      url: config.url,
      visible: config.visible !== false,
      opacity: config.opacity || 1,
      definitionExpression: config.definitionExpression,
      outFields: config.outFields || ['*'],
      ...config.options
    })
  }

  /**
   * 创建 MapImageLayer
   */
  async createMapImageLayer(config) {
    const [MapImageLayer] = await loadEsriModules(['esri/layers/MapImageLayer'])
    
    return new MapImageLayer({
      id: config.id,
      title: config.title,
      url: config.url,
      visible: config.visible !== false,
      opacity: config.opacity || 1,
      sublayers: config.sublayers,
      ...config.options
    })
  }

  /**
   * 创建 WMSLayer
   */
  async createWMSLayer(config) {
    const [WMSLayer] = await loadEsriModules(['esri/layers/WMSLayer'])
    
    return new WMSLayer({
      id: config.id,
      title: config.title,
      url: config.url,
      visible: config.visible !== false,
      opacity: config.opacity || 1,
      sublayers: config.sublayers,
      version: config.version || '1.3.0',
      ...config.options
    })
  }

  /**
   * 创建 GraphicsLayer
   */
  async createGraphicsLayer(config) {
    const [GraphicsLayer] = await loadEsriModules(['esri/layers/GraphicsLayer'])
    
    return new GraphicsLayer({
      id: config.id,
      title: config.title,
      visible: config.visible !== false,
      opacity: config.opacity || 1,
      ...config.options
    })
  }

  /**
   * 移除图层
   */
  removeLayer(layerId) {
    if (!this.view || !this.view.map) return false

    const layer = this.view.map.findLayerById(layerId)
    if (layer) {
      this.view.map.remove(layer)
      console.log('图层已移除:', layer.title || layer.id)
      return true
    }

    return false
  }

  /**
   * 切换图层可见性
   */
  toggleLayerVisibility(layerId) {
    const layer = this.getLayerById(layerId)
    if (layer) {
      layer.visible = !layer.visible
      console.log(`图层 ${layer.title || layer.id} 可见性已切换为: ${layer.visible}`)
      this.emit('layer-visibility-changed', { layer, visible: layer.visible })
      return layer.visible
    }
    return null
  }

  /**
   * 设置图层透明度
   */
  setLayerOpacity(layerId, opacity) {
    const layer = this.getLayerById(layerId)
    if (layer) {
      layer.opacity = Math.max(0, Math.min(1, opacity))
      console.log(`图层 ${layer.title || layer.id} 透明度已设置为: ${layer.opacity}`)
      this.emit('layer-opacity-changed', { layer, opacity: layer.opacity })
      return layer.opacity
    }
    return null
  }

  /**
   * 调整图层顺序
   */
  reorderLayer(layerId, newIndex) {
    if (!this.view || !this.view.map) return false

    const layer = this.view.map.findLayerById(layerId)
    if (layer) {
      this.view.map.reorder(layer, newIndex)
      console.log(`图层 ${layer.title || layer.id} 已移动到位置: ${newIndex}`)
      this.emit('layer-reordered', { layer, newIndex })
      return true
    }

    return false
  }

  /**
   * 缩放到图层范围
   */
  async zoomToLayer(layerId) {
    const layer = this.getLayerById(layerId)
    if (!layer || !this.view) return false

    try {
      if (layer.fullExtent) {
        await this.view.goTo(layer.fullExtent)
        return true
      } else if (layer.type === 'feature') {
        const extent = await layer.queryExtent()
        if (extent.extent) {
          await this.view.goTo(extent.extent)
          return true
        }
      }
    } catch (error) {
      console.error('缩放到图层失败:', error)
    }

    return false
  }

  /**
   * 获取图层详细信息
   */
  async getLayerInfo(layerId) {
    return await getLayerDetails(this.view, layerId)
  }

  /**
   * 获取图层字段信息
   */
  async getLayerFieldInfo(layerId) {
    return await getLayerFields(this.view, layerId)
  }

  /**
   * 获取图层统计信息
   */
  async getLayerStats(layerId) {
    return await getLayerStatistics(this.view, layerId)
  }

  /**
   * 批量操作图层
   */
  async batchLayerOperation(operations) {
    const results = []

    for (const operation of operations) {
      try {
        let result = null

        switch (operation.type) {
          case 'add':
            result = await this.addLayer(operation.config, operation.position)
            break
          case 'remove':
            result = this.removeLayer(operation.layerId)
            break
          case 'toggle':
            result = this.toggleLayerVisibility(operation.layerId)
            break
          case 'opacity':
            result = this.setLayerOpacity(operation.layerId, operation.opacity)
            break
          case 'reorder':
            result = this.reorderLayer(operation.layerId, operation.newIndex)
            break
          default:
            throw new Error(`不支持的操作类型: ${operation.type}`)
        }

        results.push({
          operation,
          success: true,
          result
        })
      } catch (error) {
        results.push({
          operation,
          success: false,
          error: error.message
        })
      }
    }

    return results
  }

  /**
   * 导出图层配置
   */
  async exportLayerConfig() {
    const layers = await this.getAllLayers()
    
    const config = {
      timestamp: Date.now(),
      layers: layers.map(layer => ({
        id: layer.id,
        title: layer.title,
        type: layer.type,
        visible: layer.visible,
        opacity: layer.opacity,
        url: layer.url,
        layerType: layer.layerType,
        minScale: layer.minScale,
        maxScale: layer.maxScale,
        sublayers: layer.sublayers
      }))
    }

    return JSON.stringify(config, null, 2)
  }

  /**
   * 导入图层配置
   */
  async importLayerConfig(configJson) {
    try {
      const config = JSON.parse(configJson)
      
      if (!config.layers || !Array.isArray(config.layers)) {
        throw new Error('无效的图层配置格式')
      }

      const results = []

      for (const layerConfig of config.layers) {
        try {
          // 跳过底图和图形图层
          if (layerConfig.layerType === 'basemap' || layerConfig.type === 'graphics') {
            continue
          }

          const layer = await this.addLayer(layerConfig)
          if (layer) {
            // 恢复图层状态
            layer.visible = layerConfig.visible
            layer.opacity = layerConfig.opacity || 1
          }

          results.push({
            layerConfig,
            success: true,
            layer
          })
        } catch (error) {
          results.push({
            layerConfig,
            success: false,
            error: error.message
          })
        }
      }

      return results
    } catch (error) {
      throw new Error(`导入图层配置失败: ${error.message}`)
    }
  }

  /**
   * 清空缓存
   */
  clearCache() {
    this.layerCache.clear()
  }

  /**
   * 事件发射器
   */
  emit(eventName, data) {
    const handlers = this.eventHandlers.get(eventName)
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(data)
        } catch (error) {
          console.error(`事件处理器执行失败 (${eventName}):`, error)
        }
      })
    }
  }

  /**
   * 添加事件监听器
   */
  on(eventName, handler) {
    if (!this.eventHandlers.has(eventName)) {
      this.eventHandlers.set(eventName, [])
    }
    this.eventHandlers.get(eventName).push(handler)
  }

  /**
   * 移除事件监听器
   */
  off(eventName, handler) {
    const handlers = this.eventHandlers.get(eventName)
    if (handlers) {
      const index = handlers.indexOf(handler)
      if (index > -1) {
        handlers.splice(index, 1)
      }
    }
  }

  /**
   * 销毁管理器
   */
  destroy() {
    this.clearCache()
    this.eventHandlers.clear()
    this.view = null
  }
}

/**
 * 创建图层管理器实例
 */
export const createLayerManager = (view) => {
  return new LayerManager(view)
}

/**
 * 图层配置预设
 */
export const layerPresets = {
  // 管网图层预设
  pipeNetwork: {
    id: 'pipe-network',
    title: '管网图层',
    type: 'map-image',
    url: '/geoserver/pipe_network/MapServer',
    visible: true,
    opacity: 0.8,
    sublayers: [
      { id: 0, title: '给水管道', visible: true },
      { id: 1, title: '污水管道', visible: true },
      { id: 2, title: '雨水管道', visible: false }
    ]
  },

  // 设备图层预设
  facilities: {
    id: 'facilities',
    title: '设备设施',
    type: 'feature',
    url: '/geoserver/facilities/FeatureServer/0',
    visible: true,
    opacity: 1,
    definitionExpression: "status = 'active'"
  },

  // 监测点图层预设
  monitoringPoints: {
    id: 'monitoring-points',
    title: '监测点',
    type: 'feature',
    url: '/geoserver/monitoring/FeatureServer/0',
    visible: true,
    opacity: 1,
    outFields: ['id', 'name', 'type', 'status', 'last_update']
  }
}

/**
 * 常用图层操作快捷方法
 */
export const layerUtils = {
  /**
   * 快速添加管网图层
   */
  addPipeNetworkLayer: async (layerManager) => {
    return await layerManager.addLayer(layerPresets.pipeNetwork)
  },

  /**
   * 快速添加设备图层
   */
  addFacilitiesLayer: async (layerManager) => {
    return await layerManager.addLayer(layerPresets.facilities)
  },

  /**
   * 快速添加监测点图层
   */
  addMonitoringPointsLayer: async (layerManager) => {
    return await layerManager.addLayer(layerPresets.monitoringPoints)
  },

  /**
   * 批量切换图层可见性
   */
  toggleMultipleLayers: (layerManager, layerIds, visible) => {
    const operations = layerIds.map(layerId => ({
      type: 'toggle',
      layerId
    }))
    return layerManager.batchLayerOperation(operations)
  },

  /**
   * 设置图层组透明度
   */
  setLayerGroupOpacity: (layerManager, layerIds, opacity) => {
    const operations = layerIds.map(layerId => ({
      type: 'opacity',
      layerId,
      opacity
    }))
    return layerManager.batchLayerOperation(operations)
  }
}