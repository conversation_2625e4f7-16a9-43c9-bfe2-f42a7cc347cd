import { http } from '../vmeitime-http/index'

export const getCircuitTaskList = (params: {
	fromTime?: number
	toTime?:number
	page: number
	size: number
	receiveUserId: string
	isComplete?: boolean
	isReceived?: boolean
}) => {
	return http().get('api/sm/circuitTask', params)
}

export const getMyCircuitTaskList = (params: {
	fromTime?: number
	toTime?:number
	page: number
	size: number
	receiveUserId: string
	isComplete?: boolean
	isReceived?: boolean
}) => {
	return http().get('api/sm/circuitTask/my', params)
}


// 处理中和完成统计
export const processingAndCompleteCount = () => {
	return http().get('api/sm/circuitTask/processingAndCompleteCount')
}