<template>
	<view class="recorder">
		<view class="re-top" v-if="showTop">
			<view class="re-cancel" @click="cancel">取消</view>
			<view class="re-confirm" v-if="!finishP" :style="{color: theme}" @click="confirm">{{ confirmText }}</view>
		</view>
		<text class="title">{{ finish ? '点击播放' : '长按录制语音' }}</text>
		<view class="recorder-box start-box" v-if="!finish" @click="handle" @longpress="onStartRecoder"
			@touchend="onEndRecoder">
			<u-circle-progress :active-color="theme" :duration="0" :percent="calcProgress">
				<view class="u-progress-content">
					<image src="/static/sound-recording/voice.png" mode="aspectFit" :style="{
            width: width,
            height: height
          }"></image>
				</view>
			</u-circle-progress>
		</view>
		<view class="recorder-box" v-else @click="playVoice">
			<u-circle-progress :active-color="theme" :duration="0" :percent="playProgress">
				<view class="u-progress-content">
					<image src="/static/sound-recording/play.png" mode="aspectFit" :style="{
            width: width,
            height: height
          }" v-if="!playStatus"></image>
					<image src="/static/sound-recording/pause.png" mode="aspectFit" :style="{
            width: width,
            height: height
          }" v-else></image>
				</view>
			</u-circle-progress>
		</view>
		<text class="now-date">{{ reDate }}</text>
		<view @click="reset">重新录制</view>
	</view>
</template>

<script lang="ts" setup>
	import {
		onLoad
	} from '@dcloudio/uni-app';
	import {
		onMounted,
		ref,
		computed,
		reactive
	} from "vue"
	const recorderManager = reactive<any>(uni.getRecorderManager())
	const innerAudioContext = reactive<any>(uni.createInnerAudioContext())
	const props = defineProps({
		voiceP: {
			type: String
		},
		finishP: {
			type: Boolean,
			default: false
		},
		width: {
			type: String,
			default: '60rpx'
		},
		height: {
			type: String,
			default: '60rpx'
		},
		showTop: {
			type: Boolean,
			default: true
		},
		maximum: {
			type: Number,
			default: 60
		},
		duration: {
			type: Number,
			default: 20
		},
		theme: {
			type: String,
			default: '#3862F8'
		},
		confirmText: {
			type: String,
			default: '完成'
		}
	})
	const emit = defineEmits(['confirm', 'cancel', 'reset', 'click', 'playVoice', 'start', 'stop', 'end'])
	let reDate = ref<String>('00:00')
	let sec = ref<any>(0)
	let min = ref<any>(0)
	let finish = ref<any>(props.finishP)
	let voicePath = ref<any>(props.voiceP)
	let playProgress = ref<Number>(100)
	let playTimer = ref<Boolean>(true)
	let timer = ref<any>(null)
	let playStatus = ref<Boolean>(false)

	let calcProgress = reactive<any>(0)
	calcProgress = computed(() => {
		return ((parseInt(sec.value) + (parseInt(min.value) * 60)) / 60) * 100
	})
	// 完成事件
	const confirm = () => {
		if (!innerAudioContext.paused) {
			innerAudioContext.stop()
		}
		console.log(voicePath.value)
		emit('confirm', voicePath.value)
	}
	// 取消事件
	const cancel = () => {
		if (!innerAudioContext.paused) {
			innerAudioContext.stop()
		}
		emit('cancel')
	}
	// 点击事件
	const handle = () => {
		// if (playTimer.value) {
		// 	onStartRecoder()
		// } else {
		// 	onEndRecoder()
		// }
		emit('click')
	}
	// 重新录制
	const reset = () => {
		voicePath.value = ''
		min.value = 0
		sec.value = 0
		reDate.value = '00:00'
		playProgress.value = 100
		finish.value = false
		emit('reset')
	}
	// 播放暂停录音
	const playVoice = () => {
		innerAudioContext.src = voicePath.value;

		if (innerAudioContext.paused) {
			innerAudioContext.play()
			playStatus.value = true
		} else {
			innerAudioContext.stop();
		}
		emit('playVoice', innerAudioContext.paused)
	}
	// 录制结束
	const onEndRecoder = () => {
		recorderManager.stop()
	}
	// 开始录制
	const onStartRecoder = () => {
		recorderManager.start({
			duration: props.maximum * 1000,
			sampleRate: 44100, // 采样率  
			format: 'wav',
		})
	}
	// 监听
	const onMonitorEvents = () => {
		// 录制开始
		recorderManager.onStart(() => {
			playTimer.value = false
			uni.showLoading({
				title: '录制中...'
			})
			startDate()
			emit('start')
		})
		// 录制结束
		recorderManager.onStop(res => {
			voicePath.value = res.tempFilePath
			clearInterval(timer.value)
			uni.hideLoading()
			finish.value = true
			playTimer.value = true
			emit('end')
		})
		// 报错
		recorderManager.onError(res => {
			uni.$u.toast(res)
		})
		// 播放进度
		innerAudioContext.onTimeUpdate(() => {
			let totalDate = innerAudioContext.duration
			let nowTime = innerAudioContext.currentTime
			let surplus = totalDate - nowTime
			playProgress.value = surplus / totalDate * 100

			let _min = Math.floor(surplus / 60) as any
			if (_min < 10) _min = '0' + _min;
			let _sec = Math.floor(surplus % 60) as any
			if (_sec < 10) _sec = '0' + _sec;
			reDate.value = _min + ':' + _sec
		})
		// 播放暂停
		innerAudioContext.onPause(() => {
			resetDate()
			playProgress.value = 100
			playStatus.value = false
			console.log('播放暂停')
			emit('stop')
		})
		// 播放停止
		innerAudioContext.onStop(() => {
			resetDate()
			playProgress.value = 100
			playStatus.value = false
			console.log('播放停止')
			emit('stop')
		})
	}
	// 录音计时
	const startDate = () => {
		clearInterval(timer.value)
		sec.value = 0
		min.value = 0
		timer.value = setInterval(() => {
			sec.value += props.duration / 1000
			if (sec.value >= 60) {
				min.value++
				sec.value = 0
			}
			console.log(Math.floor(sec.value))
			resetDate()
		}, props.duration)
	}
	// 播放时间
	const resetDate = () => {
		let _s = Math.floor(sec.value) < 10 ? '0' + Math.floor(sec.value) : Math.floor(sec.value)
		let _m = min.value < 10 ? '0' + min.value : min.value
		reDate.value = _m + ':' + _s
	}
	onMounted(() => {
		innerAudioContext.src = props.voiceP
		sec.value = 0
		innerAudioContext.onCanplay(() => {
			if (innerAudioContext.duration !== 0) {
				let totalDate = innerAudioContext.duration
				sec.value = totalDate
				resetDate()
			}
		});
	})

	onLoad(() => {
		onMonitorEvents()
	})
</script>

<style lang="scss">
	.recorder {
		position: relative;
		display: flex;
		align-items: center;
		flex-direction: column;
		background-color: #fff;
		font-size: 24rpx;
		width: 100%;

		.re-top {
			display: flex;
			justify-content: space-between;
			padding: 10rpx 20rpx;
			width: 100%;
			font-size: 28rpx;
			box-sizing: border-box;
		}

		.title {
			font-size: 36rpx;
			color: #333;
			padding: 20rpx 0 30rpx;
		}

		.recorder-box {
			position: relative;
		}

		.now-date {
			font-size: 28rpx;
			color: #666;
			padding: 20rpx 0;
		}
	}
</style>