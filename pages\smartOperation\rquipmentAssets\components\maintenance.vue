<!-- 保养 -->
<template>
	<view class="">
		<view class="card-box">
			<view class="flex-between hand">
				<view class="hand-title flex-center">
					<image src="@/static/img/icons/devices/title-icon.png" style="width: 20rpx;height: 40rpx;"></image>
					<text>保养统计</text>
				</view>
			</view>
			<u-gap height="1" bg-color="#bbb"></u-gap>
			<view class="table" style="padding: 0rpx;">
				<view class="info" style="background-color: #eaeaea;padding: 24rpx">
					<image src="@/static/img/icons/devices/maintenance-num.png" class="icon-img"></image>
					<text>保养次数：</text> <text>{{maintainInfo.count}}</text>
				</view>
				<view class="info" style="background-color: #eaeaea;padding: 24rpx;margin-top: 10rpx;">
					<image src="@/static/img/icons/devices/maintenance-time.png" class="icon-img"></image>
					<text>最近保养：</text> <text>{{maintainInfo.latestMaintainTime}}</text>
				</view>
			</view>
		</view>

		<view class="card-box">
			<view class="flex-between hand">
				<view class="hand-title flex-center">
					<image src="@/static/img/icons/devices/title-icon.png" style="width: 20rpx;height: 40rpx;"></image>
					<text>今年保养情况</text>
				</view>
			</view>
			<u-gap height="1" bg-color="#bbb"></u-gap>
			<view class="table">
				<view class="line-echarts">
					<l-echart ref="lineChart"></l-echart>
				</view>
			</view>
		</view>

		<view class="card-box">
			<view class="flex-between hand">
				<view class="hand-title flex-center">
					<image src="@/static/img/icons/devices/title-icon.png" style="width: 20rpx;height: 40rpx;"></image>
					<text>保养计划</text>
				</view>
			</view>
			<u-gap height="1" bg-color="#bbb"></u-gap>
			<view class="table" style="padding: 0rpx;min-height: 200rpx;">
				<view class="list" v-for="(item,index) in maintainList" :key="index">
					<text style="font-weight: 600;">{{item.name || '-'}}</text>
					<view class="info flex-between" style="padding: 10rpx 0rpx;">
						<view style="width: 50%;text-align: left;" class="flex-center">
							<view>限制时间：</view> <text>{{item.limitDays || '-'}}</text>
						</view>
						<view style="width: 50%;text-align: left;" class="flex-center">
							<view>循环周期：</view>
							<text>{{item.cycleDays || '-'}}</text>
						</view>
					</view>
					<view class="info flex-between" style="padding: 0rpx;">
						<view style="width: 50%;text-align: left;" class="flex-center">
							<view>下一次保养时间：</view>
							<text style="color: #0055ff;">{{item.nextTime || '-'}}</text>
						</view>
						<view style="width: 50%;text-align: left;" class="flex-center">
							<view>任务人： </view><text>{{item.userName || '-'}}</text>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script lang="ts" setup>
	import {
		onMounted,
		ref,
		nextTick
	} from 'vue'
	import * as echarts from 'echarts'
	import {
		getMaintainInfo,
		getMaintainList
	} from '@/common/api/rquipmentAssets'
	import {
		lineOption
	} from './echartsData'
	const lineChart = ref < any > ({})
	const maintainInfo = ref < any > ({})
	const maintainList = ref < any > ([])
	const props = defineProps({
		detail: {
			type: Object,
		}
	})

	//获取数据
	const refreshData = async (code: string) => {

		const res = await getMaintainInfo(code)
		maintainInfo.value = res.data.data
		const dataX = maintainInfo.value.nowYearMaintain.map((data: any) => data.month)
		const data = maintainInfo.value.nowYearMaintain.map((data: any) => data.count)
		initLine(dataX, data)
		const res2 = await getMaintainList(code, {
			page: 1,
			size: 9999
		})
		maintainList.value = res2.data?.data.data
	}

	onMounted(() => {
		nextTick(() => {
			refreshData(props.detail.deviceLabelCode)
		})

	})

	const initLine = (dataX: string[], data: any, color ? : string) => {
		lineChart.value.init(echarts, (chart: any) => {
			const options = lineOption(dataX, data)
			chart.setOption(options);
		});
	}
</script>

<style lang="scss" scoped>
	.card-box {
		width: 686rpx;
		margin: 20rpx auto;
		border-radius: 16rpx;
		// padding: 22rpx 28rpx;
		padding: 0;
		background-color: #FFFFFF;

		.hand {
			padding: 22rpx 28rpx;

			.hand-title {
				height: 42rpx;
				line-height: 42rpx;

				.icon-box {
					text-align: center;
					line-height: 32rpx;
					height: 32rpx;
					width: 32rpx;
					background-color: #3862F8;
					border-radius: 4rpx;
				}

				text {
					font-size: 28rpx;
					margin-left: 16rpx;
					color: #060F27;
					font-weight: 600;
				}
			}

			.status {
				color: #91949F;
			}
		}

		.line {
			padding-bottom: 20rpx;
		}

		.table {
			margin-top: 24rpx;
			padding: 0rpx 28rpx;

			.info {
				font-size: 24rpx;
				padding: 24rpx 0rpx;
				display: flex;

				text {
					&:nth-child(1) {
						color: #000000;
					}

					&:nth-child(2) {
						flex: 1;
						color: #000000;
						// color: #91949F;
					}
				}


				.bg {
					padding: 2rpx 12rpx;
					background: rgba(56, 98, 248, 0.2);
					border-radius: 8rpx;

					text {
						color: #3862F8;
					}
				}
			}

			.files {
				font-size: 36rpx;
				font-weight: 600;

				text {
					color: #626262;
					padding-left: 20rpx;
				}
			}

			.list {
				background-color: #f1f1f1;
				padding: 24rpx;
				margin-top: 10rpx;
			}
		}

		.line-echarts {
			height: 400rpx;
			width: 100%;
		}

		.icon-img {
			width: 40rpx;
			height: 40rpx;
			padding-right: 20rpx;
		}
	}
</style>
