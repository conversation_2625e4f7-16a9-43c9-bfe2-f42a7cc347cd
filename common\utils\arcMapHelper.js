import { loadModules } from 'esri-loader';
import { gisConfig } from '../data/gisData';
export const loadEsriModules = async modules => {
  const options = {
    url: 'https://js.arcgis.com/4.26/init.js',
    css: 'https://js.arcgis.com/4.26/esri/themes/light/main.css',
  };
  return await loadModules(modules, options);
};
export const coordinateConvert = async () => {
  try {
    console.log('正在加载坐标转换器...');
    const [coordinateFormatter] = await loadEsriModules(['esri/geometry/coordinateFormatter']);
    await coordinateFormatter.load();
    console.log('坐标转换器已加载');
    return coordinateFormatter;
  } catch (e) {
    //TODO handle the exception
    console.log('加载坐标转换器失败', e);
    return undefined;
  }
};
export const webMercatorToGeographic = async (x, y) => {
  try {
    const [webMercatorUtils] = await loadEsriModules(['esri/geometry/support/webMercatorUtils']);
    
    // 创建Web Mercator点
    const [Point] = await loadEsriModules(['esri/geometry/Point']);
    const mercatorPoint = new Point({
      x: x,
      y: y,
      spatialReference: { wkid: 3857 }
    });

    // 转换为地理坐标（WGS84）
    const geographicPoint = webMercatorUtils.webMercatorToGeographic(mercatorPoint);
    return {
      x: geographicPoint.x,  // 经度
      y: geographicPoint.y   // 纬度
    };
  } catch (e) {
    console.error('坐标转换失败', e);
    throw e;
  }
};export const geographicToWebMercator = async (lng, lat) => {
  try {
    const [webMercatorUtils] = await loadEsriModules(['esri/geometry/support/webMercatorUtils']);
    
    // 创建Web Mercator点
    const [Point] = await loadEsriModules(['esri/geometry/Point']);
    const mercatorPoint = new Point({
      x: lng,
      y: lat,
      spatialReference: { wkid: 4326 }
    });
    // 转换为地理坐标（WGS84）
    const geographicPoint = webMercatorUtils.geographicToWebMercator(mercatorPoint);
    return {
      x: geographicPoint.x,  // 经度
      y: geographicPoint.y   // 纬度
    };
  } catch (e) {
    console.error('坐标转换失败', e);
    throw e;
  }
};
export const initTintLayer = async () => {
  const [esriRequest, BaseTileLayer] = await loadEsriModules([
    'esri/request',
    'esri/layers/BaseTileLayer',
  ]);
  const TintLayer = BaseTileLayer.createSubclass({
    properties: {
      urlTemplate: null,
    },
    subDomains: ['t0', 't1', 't2', 't3', 't4', 't5', 't6', 't7'],
    // generate the tile url for a given level, row and column
    getTileUrl: function (level, row, col) {
      var length = this.subDomains.length;
      var idx = col % length;
      return this.urlTemplate
        .replace('{z}', level)
        .replace('{x}', col)
        .replace('{y}', row)
        .replace('{subDomain}', this.subDomains[idx]);
    },

    // This method fetches tiles for the specified level and size.
    // Override this method to process the data returned from the server.
    fetchTile: function (level, row, col) {
      if (level >= 19) return Promise.reject(new Error('当前等级下没有地图数据(请忽略此错误信息)'));
      // call getTileUrl() method to construct the URL to tiles
      // for a given level, row and col provided by the LayerView
      var url = this.getTileUrl(level, row, col);

      // request for tiles based on the generated url
      return esriRequest(url, {
        responseType: 'image',
      }).then(
        function (response) {
          // when esri request resolves successfully
          // get the image from the response
          var image = response.data;
          var width = this.tileInfo.size[0];
          var height = this.tileInfo.size[0];

          // create a canvas with 2D rendering context
          var canvas = document.createElement('canvas');
          var context = canvas.getContext('2d');
          canvas.width = width;
          canvas.height = height;

          // Apply the tint color provided by
          // by the application to the canvas
          if (this.tint) {
            // Get a CSS color string in rgba form
            // representing the tint Color instance.
            context.fillStyle = this.tint.toCss();
            context.fillRect(0, 0, width, height);

            // Applies "difference" blending operation between canvas
            // and steman tiles. Difference blending operation subtracts
            // the bottom layer (canvas) from the top layer (tiles) or the
            // other way round to always get a positive value.
            context.globalCompositeOperation = 'difference';
          }

          // Draw the blended image onto the canvas.
          context.drawImage(image, 0, 0, width, height);

          return canvas;
        }.bind(this),
      );
    },
  });
  return TintLayer;
};
export const initView = async (
  container,
  properties = {
    center: [114.525, 24.75],
  },
) => {
  const [Map, MapView] = await loadEsriModules(['esri/Map', 'esri/views/MapView']);
  // var TintLayer = await initTintLayer()
  // var vec_W = new TintLayer({
  // 	title: "矢量图",
  // 	// subDomains: ["t0", "t1", "t2", "t3", "t4", "t5", "t6", "t7"],
  // 	urlTemplate: 'http://{subDomain}.tianditu.gov.cn/img_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=img&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=e4e98a7455967290863f2f1bb245f7b5'
  // })
  // var cva_W = new TintLayer({
  // 	title: "矢量注记",
  // 	// subDomains: ["t0", "t1", "t2", "t3", "t4", "t5", "t6", "t7"],
  // 	urlTemplate: "http://{subDomain}.tianditu.gov.cn/cia_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cia&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=e4e98a7455967290863f2f1bb245f7b5",
  // })
  var map = new Map({
    basemap: {
      baseLayers: [],
    },
  });
  await changeBaseMap(map, 'arcgis');
  var view = new MapView({
    container: container,
    map: map,
    center: properties?.center || [107.91048225, 34.14961576], // longitude, latitude
    zoom: 17,
    constraints: {
      snapToZoom: true,
    },
  });
  view.ui.remove('attribution');
  view.ui.remove('zoom');
  view.ui.padding = {
    bottom: 15,
    left: 15,
    right: 15,
    top: 15,
  };
  return view;
};
export const changeBaseMap = async (map, type) => {
  if (!map) return;

  // 如果是ArcGIS卫星影像底图
  if (type === 'arcgis') {
    const [esriRequest, BaseTileLayer] = await loadEsriModules([
      'esri/request',
      'esri/layers/BaseTileLayer',
    ]);

    // 为ArcGIS创建专门的图层类
    const ArcGISLayer = BaseTileLayer.createSubclass({
      properties: {
        urlTemplate: null,
      },

      // generate the tile url for a given level, row and column
      getTileUrl: function (level, row, col) {
        return this.urlTemplate.replace('{z}', level).replace('{x}', col).replace('{y}', row);
      },

      // This method fetches tiles for the specified level and size.
      fetchTile: function (level, row, col) {
        // call getTileUrl() method to construct the URL to tiles
        var url = this.getTileUrl(level, row, col);

        // request for tiles based on the generated url
        return esriRequest(url, {
          responseType: 'image',
        }).then(
          function (response) {
            // when esri request resolves successfully
            // get the image from the response
            var image = response.data;
            var width = this.tileInfo.size[0];
            var height = this.tileInfo.size[0];

            // create a canvas with 2D rendering context
            var canvas = document.createElement('canvas');
            var context = canvas.getContext('2d');
            canvas.width = width;
            canvas.height = height;

            // Draw the image onto the canvas.
            context.drawImage(image, 0, 0, width, height);

            return canvas;
          }.bind(this),
        );
      },
    });

    var arcgisLayer = new ArcGISLayer({
      title: 'ArcGIS卫星影像',
      urlTemplate:
        'https://services.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}',
    });
    map.basemap.baseLayers.removeAll();
    map.basemap.baseLayers.add(arcgisLayer);
    return;
  }

  // 原有的天地图逻辑
  var TintLayer = await initTintLayer();
  var vec_W = new TintLayer({
    title: '矢量图',
    // subDomains: ["t0", "t1", "t2", "t3", "t4", "t5", "t6", "t7"],
    urlTemplate: `http://{subDomain}.tianditu.gov.cn/${type}_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=${type}&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=e4e98a7455967290863f2f1bb245f7b5`,
  });
  const types = {
    vec: 'cva',
    img: 'cia',
    ter: 'cta',
  };
  const poiType = types[type];
  var cva_W = new TintLayer({
    title: '矢量注记',
    // subDomains: ["t0", "t1", "t2", "t3", "t4", "t5", "t6", "t7"],
    urlTemplate: `http://{subDomain}.tianditu.gov.cn/${poiType}_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=${poiType}&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=e4e98a7455967290863f2f1bb245f7b5`,
  });
  map.basemap.baseLayers.removeAll();
  map.basemap.baseLayers.addMany([vec_W, cva_W]);
};
export const getLayerById = async (view, id) => {
  const [GraphicsLayer] = await loadEsriModules(['esri/layers/GraphicsLayer']);
  if (!view || id === undefined) return;
  let layer = view.map.findLayerById(id);
  if (!layer) {
    layer = new GraphicsLayer({
      id: id,
    });
    view.map.add(layer);
  }
  return layer;
};
export const getLayersByIds = async (view, ids) => {
  const [GraphicsLayer] = await loadEsriModules(['esri/layers/GraphicsLayer']);
  if (!view || !ids?.length) return;
  const layers = ids.map((id, i) => {
    let layer = view.map.findLayerById(id);
    if (!layer) {
      layer = new GraphicsLayer({
        id: id,
      });
      view.map.add(layer);
    }
    return layer;
  });
  return layers;
};
/**
 * 生成symbol
 * @param type 类型
 * @param options 可选项
 * @returns
 */
export const setSymbol = (
  // type:
  // |
  // 'extent' |
  // 'point' |
  // 'polyline' |
  // 'polygon' |
  // 'multipoint' |
  // 'mesh' |
  // 'text',
  type = 'point',
  // options ? : {
  // 	size ? : number
  // 	style ? : any
  // 	color ? : string | number[] | Color
  // 	width ? : number
  // 	outlineColor ? : string | number[]
  // 	outlineWidth ? : number
  // 	outlineStyle ? : any
  // 	text ? : string
  // 	textColor ? : string | number[] | Color
  // 	xOffset ? : number
  // 	yOffset ? : number
  // }
  options = {},
) => {
  let symbol = {};
  switch (type) {
    case 'polyline':
      symbol = {
        type: 'simple-line',
        color: '#0000ff',
        width: 2,
        style: 'solid',
      };
      break;

    case 'polygon':
      symbol = {
        type: 'simple-fill',
        color: [0, 0, 0, 0.1],
        style: 'solid',
        outline: {
          color: '#00ffff',
          width: 2,
          style: 'solid',
        },
      };
      break;

    case 'text':
      symbol = {
        type: 'text', // autocasts as new TextSymbol()
        text: '',
        // backgroundColor: [0, 0, 0, 0.1],
        xoffset: 0,
        yoffset: 0,
        color: '#4d8fd3',
        font: {
          size: 10,
          weight: 'bold',
        },
      };
      break;
    case 'point':
    default:
      symbol = {
        type: 'simple-marker', // autocasts as new SimpleMarkerSymbol()
        size: '12px',
        color: '||#00ffff',
        style: 'circle', // 点样式solid\cross\square|diamond|circle|x
        outline: {
          color: '#00ffff',
          width: 0.2,
        },
      };
      break;
  }
  symbol = {
    ...symbol,
    ...options,
  };
  return symbol;
};
/**
 * 初始化Query的参数
 * @param options
 *
 *
 * returnGeometry?: boolean
 *
 * outFields?: string[]
 *
 * objectIds?: number[]
 *
 * orderByFields?: string[]
 *
 * where?: string
 *
 * geometry?: Geometry
 * }
 * @returns
 */
export const initQueryParams = options => {
  return new Query({
    returnGeometry: true,
    outFields: ['*'],
    ...(options || {}),
  });
};
/**
 * 执行服务查询
 * @param url 查询路径
 * @param params 查询参数
 * @returns
 */
export const excuteQuery = async (url, params) => {
  const [query] = await loadEsriModules(['esri/rest/query']);
  return query.executeQueryJSON(url, params);
};
/**
 * 初始化buffer参数
 * options: {
	bufferSpatialReference ? : any
	distances ? : number[]
	geometries ? : Geometry[]
	outSpatialReference ? : any
	geodesic ? : boolean
	unit ? : 'feet' | 'kilometers' | 'meters' | 'miles' | 'nautical-miles' | 'yards'
	unionResults ? : boolean
}
 */
export const initBufferParams = async options => {
  const [BufferParameters] = await loadEsriModules(['esri/rest/support/BufferParameters']);
  return new BufferParameters({
    ...options,
  });
};
/**
 * buffer查询
 * params: __esri.BufferParameters
 */
export const queryBufferPolygon = async (geometry, distance, unit) => {
  const [geometryEngine] = await loadEsriModules(['esri/geometry/geometryEngine']);
  const result = await geometryEngine.buffer(geometry, distance, unit);
  return result;
};
/**
 * 绑定地图点击事件
 * @param mapView
 * @param callBack
 */
export const bindViewClick = (mapView, callBack) => {
  const mapClick = mapView?.on('click', e => {
    mapView.hitTest(e).then(response => {
      // if (response.results[0]) {
      //   const graphic = response.results[0].graphic
      //   mapView.whenLayerView(graphic.layer).then((lyrView: any) => {
      callBack && callBack(response);
      //   })
      // }
    });
  });
  return mapClick;
};

/**
 * 获取要素的中心点
 */
export const getGeometryCenterPoint = async geometry => {
  if (!geometry) return;
  if (geometry.type === 'point') {
    return geometry;
  }
  if (geometry.extent) {
    const result = [
      (geometry.extent.xmax + geometry.extent.xmin) / 2,
      (geometry.extent.ymax + geometry.extent.ymin) / 2,
    ];
    // 当为多边形时，点要在线上
    if (geometry.type === 'polyline') {
      const point = new Point({
        x: result[0],
        y: result[1],
        spatialReference: geometry.spatialReference,
      });
      const nearstPoint = await getNeerestPoint(geometry, point);
      return nearstPoint;
    }
  }
};
/**
 * 获取指定几何体上距离指定点最近的坐标
 * @param geometry 指定的几何体
 * @param point 指定点
 * @returns
 */
export const getNeerestPoint = async (geometry, point) => {
  if (!geometry || !point) return;
  const geometryEngine = await loadEsriModules(['esri/geometry/geometryEngine']);
  return geometryEngine.nearestCoordinate(geometry, point).coordinate;
};
export const locationIcon = 'http://111.229.240.180:8003/imgs/dw.png';
export const userMarker =
  'https://wimg.588ku.com/gif620/23/03/22/28d1b3f09c11fdccd6f8e21c5152f8db.gif';

/**
 * 从 MapView 获取所有图层列表
 * @param {MapView} view - ArcGIS MapView 实例
 * @param {Object} options - 可选参数
 * @returns {Array} 图层信息数组
 */
export const getLayersFromView = async (view, options = {}) => {
  if (!view || !view.map) {
    console.warn('MapView 或 Map 实例不存在');
    return [];
  }

  const {
    includeBaseLayers = false, // 是否包含底图图层
    includeGraphicsLayers = false, // 是否包含图形图层
    includeOperationalLayers = true, // 是否包含业务图层
    visibleOnly = false, // 是否只返回可见图层
    queryableOnly = false, // 是否只返回可查询图层
  } = options;

  const layers = [];

  try {
    // 获取底图图层
    if (includeBaseLayers && view.map.basemap) {
      const baseLayers = view.map.basemap.baseLayers;
      baseLayers.forEach((layer, index) => {
        if (!visibleOnly || layer.visible) {
          layers.push({
            id: layer.id || `basemap_${index}`,
            title: layer.title || `底图图层 ${index + 1}`,
            type: layer.type || 'base-layer',
            visible: layer.visible,
            opacity: layer.opacity || 1,
            url: layer.url || null,
            layerType: 'basemap',
            layer: layer,
            index: index,
            queryable: false,
          });
        }
      });
    }

    // 获取业务图层
    if (includeOperationalLayers && view.map.layers) {
      view.map.layers.forEach((layer, index) => {
        // 跳过图形图层（如果不需要的话）
        if (!includeGraphicsLayers && layer.type === 'graphics') {
          return;
        }

        // 检查可见性过滤
        if (visibleOnly && !layer.visible) {
          return;
        }

        // 检查可查询性过滤
        const isQueryable = checkLayerQueryable(layer);
        if (queryableOnly && !isQueryable) {
          return;
        }

        const layerInfo = {
          id: layer.id || `layer_${index}`,
          title: layer.title || layer.name || `图层 ${index + 1}`,
          type: layer.type,
          visible: layer.visible,
          opacity: layer.opacity || 1,
          url: layer.url || null,
          layerType: 'operational',
          layer: layer,
          index: index,
          queryable: isQueryable,
          minScale: layer.minScale || 0,
          maxScale: layer.maxScale || 0,
        };

        // 如果是 MapImageLayer 或 FeatureLayer，获取子图层信息
        if (layer.type === 'map-image' && layer.sublayers) {
          layerInfo.sublayers = [];
          layer.sublayers.forEach((sublayer, subIndex) => {
            layerInfo.sublayers.push({
              id: sublayer.id,
              title: sublayer.title || sublayer.name || `子图层 ${subIndex + 1}`,
              visible: sublayer.visible,
              minScale: sublayer.minScale || 0,
              maxScale: sublayer.maxScale || 0,
              definitionExpression: sublayer.definitionExpression || null,
              layerType: 'sublayer',
              parentId: layer.id,
              queryable: true,
            });
          });
        }

        // 如果有子图层，获取子图层
        if (layer.sublayers && layer.sublayers.items > 0) {
          layerInfo.sublayers = [];
          layer.layers.forEach((sublayer, subIndex) => {
            layerInfo.sublayers.push({
              id: sublayer.id || `${layer.id}_sub_${subIndex}`,
              title: sublayer.title || sublayer.name || `子图层 ${subIndex + 1}`,
              type: sublayer.type,
              visible: sublayer.visible,
              opacity: sublayer.opacity || 1,
              url: sublayer.url || null,
              layerType: 'sublayer',
              parentId: layer.id,
              queryable: checkLayerQueryable(sublayer),
            });
          });
        }

        layers.push(layerInfo);
      });
    }

    // 获取图形图层
    if (includeGraphicsLayers) {
      const graphicsLayers = view.map.layers.filter(layer => layer.type === 'graphics');
      graphicsLayers.forEach((layer, index) => {
        if (!visibleOnly || layer.visible) {
          layers.push({
            id: layer.id || `graphics_${index}`,
            title: layer.title || `图形图层 ${index + 1}`,
            type: 'graphics',
            visible: layer.visible,
            opacity: layer.opacity || 1,
            layerType: 'graphics',
            layer: layer,
            index: index,
            queryable: false,
            graphicsCount: layer.graphics ? layer.graphics.length : 0,
          });
        }
      });
    }
  } catch (error) {
    console.error('获取图层列表失败:', error);
  }

  return layers;
};

/**
 * 检查图层是否可查询
 * @param {Layer} layer - 图层实例
 * @returns {boolean} 是否可查询
 */
export const checkLayerQueryable = layer => {
  if (!layer) return false;

  // 图形图层不可查询
  if (layer.type === 'graphics') return false;

  // FeatureLayer 通常可查询
  if (layer.type === 'feature') return true;

  // MapImageLayer 可查询
  if (layer.type === 'map-image') return true;

  // WMSLayer 可查询
  if (layer.type === 'wms') return true;

  // 其他类型根据是否有 url 判断
  return !!layer.url;
};

/**
 * 获取指定图层的详细信息
 * @param {MapView} view - MapView 实例
 * @param {string} layerId - 图层ID
 * @returns {Object|null} 图层详细信息
 */
export const getLayerDetails = async (view, layerId) => {
  if (!view || !layerId) return null;

  const layer = view.map.findLayerById(layerId);
  if (!layer) return null;

  const details = {
    id: layer.id,
    title: layer.title || layer.name,
    type: layer.type,
    visible: layer.visible,
    opacity: layer.opacity,
    url: layer.url,
    minScale: layer.minScale,
    maxScale: layer.maxScale,
    spatialReference: layer.spatialReference,
    fullExtent: layer.fullExtent,
    capabilities: null,
    fields: null,
    geometryType: null,
  };

  // 获取 FeatureLayer 的详细信息
  if (layer.type === 'feature') {
    details.geometryType = layer.geometryType;
    details.objectIdField = layer.objectIdField;
    details.displayField = layer.displayField;
    details.fields = layer.fields
      ? layer.fields.map(field => ({
          name: field.name,
          alias: field.alias,
          type: field.type,
          length: field.length,
          nullable: field.nullable,
        }))
      : null;
    details.definitionExpression = layer.definitionExpression;
    details.capabilities = layer.capabilities;
  }

  // 获取 MapImageLayer 的详细信息
  if (layer.type === 'map-image') {
    details.sublayers = layer.sublayers
      ? layer.sublayers.map(sublayer => ({
          id: sublayer.id,
          title: sublayer.title,
          visible: sublayer.visible,
          minScale: sublayer.minScale,
          maxScale: sublayer.maxScale,
          definitionExpression: sublayer.definitionExpression,
        }))
      : null;
  }

  return details;
};

/**
 * 获取图层的字段信息
 * @param {MapView} view - MapView 实例
 * @param {string} layerId - 图层ID
 * @returns {Array} 字段信息数组
 */
export const getLayerFields = async (view, layerId) => {
  if (!view || !layerId) return [];

  const layer = view.map.findLayerById(layerId);
  if (!layer) return [];

  let fields = [];

  try {
    if (layer.type === 'feature' && layer.fields) {
      fields = layer.fields.map(field => ({
        name: field.name,
        alias: field.alias || field.name,
        type: field.type,
        length: field.length,
        nullable: field.nullable,
        editable: field.editable,
        domain: field.domain
          ? {
              type: field.domain.type,
              name: field.domain.name,
              codedValues: field.domain.codedValues,
            }
          : null,
      }));
    } else if (layer.type === 'map-image' && layer.sublayers) {
      // 对于 MapImageLayer，需要异步获取字段信息
      const [Query] = await loadEsriModules(['esri/rest/support/Query']);

      for (const sublayer of layer.sublayers.items) {
        try {
          const query = new Query({
            where: '1=1',
            returnGeometry: false,
            outFields: ['*'],
            num: 1,
          });

          const result = await sublayer.queryFeatures(query);
          if (result.fields) {
            const sublayerFields = result.fields.map(field => ({
              name: field.name,
              alias: field.alias || field.name,
              type: field.type,
              length: field.length,
              sublayerId: sublayer.id,
              sublayerTitle: sublayer.title,
            }));
            fields.push(...sublayerFields);
          }
        } catch (error) {
          console.warn(`获取子图层 ${sublayer.id} 字段信息失败:`, error);
        }
      }
    }
  } catch (error) {
    console.error('获取图层字段信息失败:', error);
  }

  return fields;
};

/**
 * 获取图层的统计信息
 * @param {MapView} view - MapView 实例
 * @param {string} layerId - 图层ID
 * @returns {Object} 统计信息
 */
export const getLayerStatistics = async (view, layerId) => {
  if (!view || !layerId) return null;

  const layer = view.map.findLayerById(layerId);
  if (!layer) return null;

  const statistics = {
    layerId: layerId,
    layerTitle: layer.title || layer.name,
    layerType: layer.type,
    featureCount: 0,
    extent: null,
    hasGeometry: false,
  };

  try {
    if (layer.type === 'feature') {
      // 获取要素数量
      const [Query] = await loadEsriModules(['esri/rest/support/Query']);
      const countQuery = new Query({
        where: '1=1',
        returnGeometry: false,
      });

      const countResult = await layer.queryFeatureCount(countQuery);
      statistics.featureCount = countResult;

      // 获取范围
      const extentQuery = new Query({
        where: '1=1',
        returnGeometry: true,
      });

      const extentResult = await layer.queryExtent(extentQuery);
      statistics.extent = extentResult.extent;
      statistics.hasGeometry = !!extentResult.extent;
    } else if (layer.type === 'graphics') {
      statistics.featureCount = layer.graphics ? layer.graphics.length : 0;
      statistics.hasGeometry = statistics.featureCount > 0;
    }
  } catch (error) {
    console.error('获取图层统计信息失败:', error);
  }

  return statistics;
};

/**
 * 按类型分组图层
 * @param {Array} layers - 图层数组
 * @returns {Object} 按类型分组的图层
 */
export const groupLayersByType = layers => {
  const grouped = {
    basemap: [],
    feature: [],
    'map-image': [],
    wms: [],
    graphics: [],
    group: [],
    other: [],
  };

  layers.forEach(layer => {
    const type = layer.layerType === 'basemap' ? 'basemap' : layer.type;
    if (grouped[type]) {
      grouped[type].push(layer);
    } else {
      grouped.other.push(layer);
    }
  });

  return grouped;
};

/**
 * 搜索图层
 * @param {Array} layers - 图层数组
 * @param {string} keyword - 搜索关键词
 * @returns {Array} 匹配的图层
 */
export const searchLayers = (layers, keyword) => {
  if (!keyword) return layers;

  const lowerKeyword = keyword.toLowerCase();

  return layers.filter(layer => {
    // 搜索图层标题
    if (layer.title && layer.title.toLowerCase().includes(lowerKeyword)) {
      return true;
    }

    // 搜索图层ID
    if (layer.id && layer.id.toLowerCase().includes(lowerKeyword)) {
      return true;
    }

    // 搜索图层类型
    if (layer.type && layer.type.toLowerCase().includes(lowerKeyword)) {
      return true;
    }

    // 搜索子图层
    if (layer.sublayers) {
      const matchingSublayers = layer.sublayers.filter(
        sublayer =>
          (sublayer.title && sublayer.title.toLowerCase().includes(lowerKeyword)) ||
          (sublayer.id && sublayer.id.toString().toLowerCase().includes(lowerKeyword)),
      );
      if (matchingSublayers.length > 0) {
        return true;
      }
    }

    return false;
  });
};

/**
 * 获取可查询的图层列表
 * @param {MapView} view - MapView 实例
 * @returns {Array} 可查询的图层列表
 */
export const getQueryableLayers = async view => {
  const allLayers = await getLayersFromView(view, {
    includeBaseLayers: false,
    includeGraphicsLayers: false,
    includeOperationalLayers: true,
    queryableOnly: true,
  });

  return allLayers;
};

// 添加加载GeoServer管网图层的方法
export const loadGeoServerWMSLayer = async (view, layerId = 'pipe-layer', options = {}) => {
  const [WMSLayer] = await loadEsriModules(['esri/layers/WMSLayer']);

  // 移除现有的图层（如果存在）
  const existingLayer = view?.map?.findLayerById(layerId);
  if (existingLayer) {
    view.map.remove(existingLayer);
  }

  // 创建WMS图层
  const wmsLayer = new WMSLayer({
    id: layerId,
    title: layerId === 'pipe-layer' ? '供水管网' : '设备图层',
    url: '/geoserver/guazhou/wms',
    sublayers: [
      { name: '管线', type: 'MultiLineString', spatialReferences: 'EPSG:3857' },
      { name: '测点', type: 'Point', spatialReferences: 'EPSG:3857' },
      { name: '测流井', type: 'Point', spatialReferences: 'EPSG:3857' },
    ],
    visible: true,
    version: '1.1.0', // WMS 版本
    ...options,
  });

  // 添加到地图
  view.map.add(wmsLayer);

  // 如果提供了center参数，则定位到指定位置
  if (options.center) {
    view.goTo({
      center: options.center,
      zoom: options.zoom || 17,
    });
  }

  return wmsLayer;
};
