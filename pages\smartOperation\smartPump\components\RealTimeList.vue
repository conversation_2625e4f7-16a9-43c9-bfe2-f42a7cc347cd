<template>
	<div ref="refBox" class="attr-list" :class="props.position ?? 'right'" @mouseenter="stopScroll"
		@mouseleave="startScroll">
		<div v-for="(item, i) in realtime.realtimeList.value" :key="i" class="attr-item">
			<div class="label">
				{{ item.propertyName }}
			</div>
			<div class="value">
				{{ item.value }}
				{{ item.unit }}
			</div>
		</div>
	</div>
</template>
<script lang="ts" setup>
	import {
		ref,
		watch,
		onMounted,
		onBeforeUnmount
	} from 'vue'
	import {
		useStationRealTimeData
	} from '@/common/hooks/useStations'

	const props = defineProps < {
		stationId ? : string,
		position ? : 'left' | 'right'
	} > ()
	const refBox = ref < HTMLDivElement > ()
	const realtime = useStationRealTimeData()
	const refreshData = () => {
		realtime.getRealTimeData(props.stationId)
	}
	let timer
	let timer1
	let timer2
	let timer3
	const startScroll = async () => {
		clearTimeout(timer3)
		clearTimeout(timer2)
		timer1 = setInterval(() => {
			const scrollTop = refBox.value?.scrollTop ?? 0
			const scrollHeight = refBox.value?.scrollHeight ?? 0
			const offsetHeight = refBox.value?.offsetHeight ?? 0
			// 到达底部，停止滚动
			if (scrollTop + offsetHeight >= scrollHeight) {
				// 3秒后回到顶部
				timer2 = setTimeout(() => {
					// toTop()
				}, 3000)
				clearInterval(timer1)
			} else {
				refBox.value?.scrollTo({
					top: scrollTop + 1,
					behavior: 'smooth'
				})
			}
		}, 100)
	}
	const toTop = () => {
		refBox.value?.scrollTo({
			top: 0,
			behavior: 'smooth'
		})
		// 等回到顶部后重新开始滚动
		timer3 = setTimeout(() => {
			startScroll()
		}, 500)
	}
	const stopScroll = () => {
		clearInterval(timer1)
	}
	watch(
		() => props.stationId,
		() => {
			// toTop()
			refreshData()
		}
	)
	onMounted(() => {
		refreshData()
		// toTop()
		clearInterval(timer)
		timer = setInterval(() => {
			refreshData()
		}, 10000)
	})
	onBeforeUnmount(() => {
		clearInterval(timer)
		clearInterval(timer1)
		clearTimeout(timer3)
		clearTimeout(timer2)
	})
</script>
<style lang="scss" scoped>
	.attr-list {
		user-select: none;
		position: absolute;
		transform: translateY(-50%);
		max-height: calc(100% - 180px);
		min-height: 180px;
		overflow-y: auto;
		overflow-x: hidden;
		top: 50%;
		width: 30%;
		box-shadow: 2px 2px 2px rgba(0, 0, 0, 0.2);

		&::-webkit-scrollbar {
			display: none;
		}

		&.right {
			right: 40px;
		}

		&.left {
			left: 30px;
		}

		.attr-item {
			display: flex;
			align-items: center;
			padding: 6px 12px;
			font-size: 14px;
			height:40rpx;
			&:nth-child(odd) {
				background-color: rgba(101, 150, 207, 0.8);
				color: #fff;
			}

			&:nth-child(even) {
				background-color: rgba(255, 255, 255, 0.8);
				color: rgb(101, 150, 207);
			}

			.label {
				min-width: 100px;
				width: 50%;
				font-weight: 600;
				text-align: center;
				word-break: keep-all;
				font-size: 24rpx;
			}

			.value {
				min-width: 100px;
				width: 50%;
				word-break: keep-all;
				text-align: right;
				font-size: 24rpx;
			}
		}
	}
</style>
