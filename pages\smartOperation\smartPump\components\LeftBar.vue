<template>
  <div
    class="left-bar-item"
    :class="{ 'is-active': props.isActive }"
    @click="handleClick"
  >
    <div class="img-box">
      <image
        class="img"
        :src="props.data?.img"
        alt=""
      />
    </div>
    <div class="text">
      <span>
        {{ props.data?.text }}
      </span>
    </div>
  </div>
</template>
<script lang="ts" setup>
const emit = defineEmits(['click'])
const props = defineProps<{ data?: { id: number; text: string; img: string }; isActive?: boolean }>()
const handleClick = () => {
  emit('click', props.data)
}
</script>
<style lang="scss" scoped>
.left-bar-item {
  height: 160px;
  width: 200px;
  justify-content: center;
  cursor: pointer;
  padding-bottom: 80px;
  .img-box {
    padding: 12px 20px;
    width: 100%;
    height: 100%;
    background-color: #567089;
    border-radius: 4px;
    .img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }
  .text {
    color: #fff;
    font-size: 28rpx;
    padding: 8px;
    height: 28px;
    text-align: center;
  }
  &.is-active {
    .img-box {
      background-color: #2ACFFF;
    }
    .text {
      color: #16aad5;
    }
  }
}
</style>
