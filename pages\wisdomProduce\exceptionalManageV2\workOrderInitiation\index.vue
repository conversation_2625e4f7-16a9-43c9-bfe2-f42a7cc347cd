<template>
	<view class="main">
		<view class="detail">
			<u-form :model="form" ref="refForm" :label-style="{'color':'#91949F'}" borderBottom labelWidth="180"
				input-align="right" :error-type="['toast']">
				<view class="card-box">
					<u-form-item label="事件标题：" required prop="title" borderBottom>
						<u-input placeholder="请输入标题" v-model="form.title" input-align="right">
						</u-input>
					</u-form-item>
					<u-form-item label="事件来源：" required prop="source" borderBottom>
						<template #right>
							<u-icon name="arrow-right" size="28" color="#91949F"></u-icon>
						</template>
						<input inputmode="none" placeholder-class="placeholderClass" placeholder="请选择事件来源"
							v-model="form.source" input-align="right">
						</input>
					</u-form-item>

					<u-form-item label="事件类型：" required prop="type" borderBottom>
						<template #right>
							<u-icon name="arrow-right" size="28" color="#91949F"></u-icon>
						</template>
						<input v-model="form.type" placeholder="请选择事件类型" inputmode="none"
							placeholder-class="placeholderClass" input-align="right" @click="state.typesShow=true">
						</input>
					</u-form-item>


					<!-- <u-form-item label="事件内容：" prop="form.val2" borderBottom @click="typesContentShow=true">
						<template #right>
							<u-icon name="arrow-right" size="28" color="#91949F"></u-icon>
						</template>
						<input v-model="form.val2" inputmode="none" placeholder-class="placeholderClass" placeholder="请选择事件内容" input-align="right">
						</input>
					</u-form-item> -->

					<u-form-item label="紧急程度：" required prop="level" borderBottom>
						<template #right>
							<u-icon name="arrow-right" size="28" color="#91949F"></u-icon>
						</template>
						<input v-model="form.level" inputmode="none" placeholder-class="placeholderClass"
							placeholder="请选择事件类型" input-align="right" @click="state.urgencyShow=true">
						</input>
					</u-form-item>
					<u-form-item label="事件地址：" required prop="address" borderBottom>
						<u-input v-model="form.address" placeholder="请输入事件地址" input-align="right" @blur="changeAddress">
						</u-input>
						<template #right>
							<view @click="chooseAddress" class="dw">
								<image src="/static/img/icons/dw.png" style="height:40rpx;width:40rpx">
								</image>
							</view>
						</template>
					</u-form-item>
					<!-- <u-form-item label="事件地址：" required prop="address" borderBottom>
						<u-input v-model="form.address" placeholder="请输入事件地址" input-align="right">
						</u-input>
						<template #right>
							<image src="/static/img/icons/location.png" @click="locaton"
								style="margin-bottom:20rpx;height:36rpx;width:36rpx">
							</image>
						</template>
					</u-form-item> -->
					<u-form-item label="" borderBottom>
						<view style="width: 100%; height: 80px;">
							<map v-show="!mapShow" style="width: 100%; height: 80px;" id="maps" :controls="controls"
								:latitude="state.latitude" :longitude="state.longitude" @regionchange="regionchange">
							</map>
						</view>
					</u-form-item>
					<u-form-item label="详细描述：" required :borderBottom="false">
					</u-form-item>
					<u-form-item label="" prop="remark" borderBottom>
						<u-input type="textarea" input-align="left" v-model="form.remark" placeholder="请输入详细描述" border
							color="#91949F">
						</u-input>
					</u-form-item>

					<u-form-item label="处理级别：" required prop="processLevelLabel" borderBottom>
						<template #right>
							<u-icon name="arrow-right" size="28" color="#91949F"></u-icon>
						</template>
						<input v-model="form.processLevelLabel" inputmode="none" placeholder-class="placeholderClass"
							placeholder="请选择处理级别" input-align="right" @click="state.processLevelShow=true">
						</input>
					</u-form-item>

					<u-form-item label="处理时长：" required prop="processLevelLabel" borderBottom>
						<u-input v-model="form.processLevelName" disabled placeholder="处理时长" input-align="right"
							@click="state.urgencyShow=true">
						</u-input>
					</u-form-item>
				</view>

				<view class="card-box">
					<u-form-item label="接收组织：" prop="form.organization" borderBottom>
						<template #right>
							<u-icon name="arrow-right" size="28" color="#91949F"></u-icon>
						</template>
						<input v-model="form.organizationName" inputmode="none" placeholder-class="placeholderClass"
							placeholder="请选择" input-align="right" @click="state.organizationShow=true">
						</input>
					</u-form-item>
					<u-form-item label="接收部门：" prop="form.receiveDepartmentName" borderBottom>
						<template #right>
							<u-icon name="arrow-right" size="28" color="#91949F"></u-icon>
						</template>
						<input v-model="form.receiveDepartmentName" inputmode="none"
							placeholder-class="placeholderClass" placeholder="请选择" input-align="right"
							@click="state.departmentShow=true">
						</input>
					</u-form-item>
					<u-form-item label="处理人员：" prop="stepProcessUserName">
						<template #right>
							<u-icon name="arrow-right" size="28" color="#91949F"></u-icon>
						</template>
						<input v-model="form.stepProcessUserName" inputmode="none" placeholder-class="placeholderClass"
							placeholder="选择处理人员" input-align="right" @click="state.stepProcessUserShow=true">
						</input>
					</u-form-item>
				</view>
			</u-form>
		</view>

		<view class="button">
			<u-button type="primary" color="#3862F8" @click="submit">提交</u-button>
		</view>
		<!-- <u-action-sheet @close="statusShow=false" @select="chooseStatus" closeOnClickOverlay closeOnClickAction
			title="处理结果" v-model="statusShow" keyname :actions="statusList">
		</u-action-sheet> -->
		<!-- 告警类型 -->
		<!-- <u-picker v-model="state.typesShow" mode="selector" :default-selector="[0]" :range="workOrderTypes"
			range-key="name" @confirm="selectType"></u-picker> -->
		<u-select valueName="id" labelName="name" childName="children" v-model="state.typesShow"
			mode="mutil-column-auto" @confirm="selectType" :list="workOrderTypes"></u-select>
		<!-- 紧急程度 -->
		<u-picker v-model="state.urgencyShow" mode="selector" :default-selector="[0]" :range="urgencys" range-key="name"
			@confirm="selectUrgency"></u-picker>
		<!-- 处理级别 -->
		<u-picker v-model="state.processLevelShow" mode="selector" :default-selector="[0]" :range="processLevels"
			range-key="name" @confirm="selectProcessLevel"></u-picker>
		<!-- 组织列表 -->
		<u-picker v-model="state.organizationShow" mode="selector" :default-selector="[0]" :range="organizations"
			range-key="name" @confirm="selectOrganization"></u-picker>
		<!-- 部门列表 -->
		<u-picker v-model="state.departmentShow" mode="selector" :default-selector="[0]" :range="departments"
			range-key="name" @confirm="selectDepartment"></u-picker>
		<!-- 处理人员 -->
		<u-picker v-model="state.stepProcessUserShow" mode="selector" :default-selector="[0]" :range="processUsers"
			range-key="firstName" @confirm="selectProcessUser"></u-picker>
	</view>
</template>

<script lang="ts" setup>
	import {
		onBeforeMount,
		onMounted,
		computed,
		reactive,
		ref
	} from "vue";
	import {
		onReady,
		onShow
	} from '@dcloudio/uni-app';
	import {
		saveWorkOrderV2
	} from '@/common/api/workOrder'
	import {
		workOrderTypeList,
		getOrganization,
		getDepartmentList,
		workOrderProcessLevelList,
		getStepProcessUser,
		workOrderEmergencyLevelList
	} from '@/common/data/workOrderData'
	import {
		queryGeocoder
	} from '@/common/api/map'
	import {
		removeSlash
	} from '@/common/utils/removeIdSlash'
	import {
		onPageScroll
	} from '@dcloudio/uni-app'
	// 监听页面滑动距离顶部
	onPageScroll(e => {
		state.scrollTop = e.scrollTop
		if (e.scrollTop < 65) {
			state.navColor = '#FFFFFF'
			state.navBgColor = 'transparent'
		} else {
			state.navColor = '#000000'
			state.navBgColor = '#FFFFFF'
		}
	})

	const processUsers = ref<any>([])
	const state = reactive<{
		status : any,
		typesShow : boolean,
		urgencyShow : boolean,
		processLevelShow : boolean,
		organizationShow : boolean,
		departmentShow : boolean,
		stepProcessUserShow : boolean,
		scrollTop : any,
		typeIndex : any,
		organizationIndex : any,
		departmentIndex : any,
		processUserIndex : any,
		urgencyIndex : any,
		processLevelIndex : any,
		navColor : string,
		navBgColor : string,
		exceptionId : string,
		latitude : Number,
		longitude : Number
	}>({
		typesShow: false,
		organizationShow: false,
		departmentShow: false,
		stepProcessUserShow: false,
		urgencyShow: false,
		processLevelShow: false,
		typeIndex: [0],
		urgencyIndex: [0],
		organizationIndex: [0],
		departmentIndex: [0],
		processUserIndex: [0],
		processLevelIndex: [0],
		exceptionId: '',
		longitude: 0,
		latitude: 0,
		status: {
			aa: {
				label: '待巡检',
				color: '#F8A038',
				bgColor: 'rgba(248, 160, 56, 0.2)'
			},
			bb: {
				label: '合格',
				color: '#2EE740',
				bgColor: 'rgba(46, 231, 64, 0.2)'
			},
			cc: {
				label: '不合格',
				color: '#F83838',
				bgColor: 'rgba(248, 56, 56, 0.2)'
			},
		},
		navColor: '#FFFFFF',
		navBgColor: 'transparent',
		scrollTop: 0
	})
	const refToast = ref<any>()
	const refForm = ref<any>({})
	const workOrderTypes = ref<any>([])
	const urgencys = ref<any>([])
	const departments = ref<any>([])
	const organizations = ref<any>([])
	const processLevels = ref<any>()
	const mapShow = computed(() => {
		return state.typesShow ||
			state.organizationShow ||
			state.departmentShow ||
			state.urgencyShow ||
			state.processLevelShow ||
			state.stepProcessUserShow

	})
	const controls = ref<any>([{
		id: '1',
		iconPath: '../../../../static/img/icons/dw.png',
		position: { //控件在地图的位置
			left: 140,
			top: 30,
			width: 20,
			height: 20,
		},
	}])
	const form = reactive<any>({
		source: '异常监测',
		title: '',
		address: '',
	})
	const rules = reactive<any>({
		title: [{
			required: true,
			message: '请输入事件标题',
		}],
		source: [{
			required: true,
			message: '请选择事件来源',
		}],
		type: [{
			required: true,
			message: '请选择事件类型',
			trigger: ['change', 'blur'],
		}],
		level: [{
			required: true,
			message: '请选择紧急程度',
			trigger: ['change', 'blur'],
		}],
		address: [{
			required: true,
			message: '请输入地址',
			trigger: ['change', 'blur'],
		}],
		remark: [{
			required: true,
			message: '请输入详细描述',
			trigger: ['change', 'blur'],
		}],
		processLevel: [{
			required: true,
			message: '处理级别',
			trigger: ['change', 'blur'],
		}]
	})

	const changeAddress = () => {
		queryGeocoder({ keyWord: form.address }).then((res : any) => {
			const location = res.data?.location
			state.latitude = location.lat
			state.longitude = location.lon
		})
	}
	const chooseAddress = () => {
		uni.navigateTo({
			url: '/pages/map/tianMap'
		})
	}
	// 选择处理状态
	const chooseStatus = (item : any) => {
		console.log(item)
		form.val2 = item.name
	}
	// 获取组织部门用户列表
	const getProcessUser = async (id : String) => {
		processUsers.value = await getStepProcessUser(id)
	}
	// 获取当前定位
	// const locaton = () => {
	// 	uni.getLocation({
	// 		type: 'gcj02',
	// 		altitude: true,
	// 		geocode: true,
	// 		isHighAccuracy: true,
	// 		success: (info) => {
	// 			console.log(info)
	// 			state.latitude = info.latitude
	// 			state.longitude = info.longitude
	// 			form.address = info.address.street + info.address.streetNum
	// 		},
	// 		fail: () => {
	// 			uni.$u.toast('获取定位失败')
	// 		}
	// 	})
	// }


	const regionchange = (event : any) => {
		console.log('regionchange', event)
		if (event.type == 'onRegionchange') {
			getCenterLanLat()
		}
	}

	// 获取当前地图中心的经纬度
	const getCenterLanLat = () => {
		const mapContext = uni.createMapContext("maps", this);
		mapContext.getCenterLocation({
			success: (res) => {
				var point = new plus.maps.Point(res.longitude, res.latitude);
				plus.maps.Map.reverseGeocode(point, {}, (info : any) => {
					state.latitude = info.coord.latitude
					state.longitude = info.coord.longitude
					form.address = info.address
				})
			},
			fail: (err) => {
				console.log('获取当前地图中心的经纬度', err);
			}
		})
	}

	// 选择事件类型
	// const selectType = (val: any) => {
	// 	state.typeIndex = val
	// 	const type = workOrderTypes.value[val[0]]
	// 	form.type = type.name
	// }
	// 选择事件类型
	const selectType = (val : any) => {
		const type = val[val.length - 1]
		console.log(type)
		form.type = type.label
	}
	// 选择紧急程度
	const selectUrgency = (val : any) => {
		state.urgencyIndex = val
		const level = urgencys.value[val[0]]
		form.level = level.name
	}
	// 选择处理级别
	const selectProcessLevel = (val : any) => {
		state.processLevelIndex = val
		const processLevel = processLevels.value[val[0]]
		form.processLevelLabel = processLevel.name
		form.processLevel = processLevel.dayTime * 1440 + processLevel.hourTime * 60 + processLevel.minuteTime
		form.processLevelName = processLevel.dayTime + '天' + processLevel.hourTime + '小时' + processLevel.minuteTime +
			'分钟'
		console.log('选择处理级别', form.processLevel)
	}
	// 选择组织
	const selectOrganization = async (val : any) => {
		state.organizationIndex = val
		const organization = organizations.value[val[0]]
		form.organizationName = organization.name
		departments.value = await getDepartmentList(organization.id)
	}
	// 选择部门
	const selectDepartment = (val : any) => {
		state.departmentIndex = val
		const department = departments.value[val[0]]
		form.receiveDepartmentName = department.name
		form.receiveDepartmentId = department.id
		getProcessUser(department.id)
	}
	// 选择部门
	const selectProcessUser = (val : any) => {
		state.processUserIndex = val
		const processUser = processUsers.value[val[0]]
		form.stepProcessUserName = processUser.firstName
		form.stepProcessUserId = removeSlash(processUser.id?.id)
	}
	const submit = () => {
		refForm.value.validate((valid : boolean) => {
			if (valid) {
				uni.showModal({
					title: '提示',
					content: '确定提交',
					success: function (res) {
						if (res.confirm) {
							const params = {
								...form,
							}
							saveWorkOrderV2(state.exceptionId, params).then(res => {
								console.log('da', res)
								if (res.data?.code === 200) {
									console.log('cc', res)
									refToast.value.show({
										title: '处理成功',
										type: 'success',
										callback: () => {
											var pages =
												getCurrentPages();
											var page = pages[pages
												.length - 2];
											console.log(page)
											page.$vm.onRefresh()
											uni.navigateBack({
												delta: 1
											})
										}
									})

								} else {
									refToast.value.show({
										title: res.data?.err,
										type: 'error'
									})
								}
							}).catch(() => {
								refToast.value.show({
									title: '处理失败',
									type: 'error'
								})
							})
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			} else {
				console.log('验证失败');
			}
		})
	}
	const onLoadUpdate = item => {
		form.address = item.address
		form.coordinate = item.coordinate.lat + ',' + item.coordinate.lon
	}
	// 获取工单类型树
	// const getWorkOrderTypeList = async () => {
	// 	const res = await workOrderTypeList({
	// 		isDel: 0
	// 	})
	// 	workOrderTypes.value = res.data?.data
	// }

	// 获取紧急程度
	// const getUrgencyList = async () => {
	// 	const res = await urgencyList({
	// 		isDel: 0
	// 	})
	// 	urgencys.value = res.data?.data
	// }

	// 获取组织列表
	// const getOrganization = async () => {
	// 	const res = await organizationList()
	// 	organizations.value = res.data?.data
	// }
	onReady(() => {
		refForm.value.setRules(rules);
	})
	onBeforeMount(async () => {
		// sourceTypes.value = await workOrderResourceList()
		workOrderTypes.value = await workOrderTypeList()
		urgencys.value = await workOrderEmergencyLevelList()
		organizations.value = await getOrganization()
		processLevels.value = await workOrderProcessLevelList()
	})
	onShow(async () => {
		uni.$on("updateLocation", onLoadUpdate);
	})
	onMounted(async () => {
		// locaton()
		// workOrderTypes.value = await getWorkOrderTypeList()
		// urgencys.value = await getUrgencyList()
		// organizations.value = await getOrganization()
		var pages = getCurrentPages();
		var page = pages[pages.length - 1];
		state.exceptionId = page.$page.options.exceptionId
	})
</script>

<style lang="scss" scoped>
	.main {
		background-color: #FBFBFB;
	}

	.detail {
		.card-box {
			min-height: 376rpx;
			width: 686rpx;
			margin: 20rpx auto;
			border-radius: 16rpx;
			padding: 22rpx 28rpx;
			background-color: #FFFFFF;

		}

		.content-card {
			width: 686rpx;
			margin: 20rpx auto;
			background-color: #FFFFFF;
			min-height: 224rpx;
			border-radius: 8px;
			padding: 24rpx 0 0 28rpx;

			text {
				color: #060F27;
				font-size: 28rpx;
			}

			.camera {
				width: 112rpx;
				height: 112rpx;
				margin-top: 20rpx;
				display: flex;
				justify-content: center;
				flex-direction: column;
				text-align: center;
				background: #F9F9F9;

				text {
					color: #91949F;
					font-size: 20rpx;
				}
			}
		}
	}

	::v-deep .u-form-item__body {
		padding: 16rpx;
	}

	::v-deep .u-form-item {
		padding: 0;
	}
</style>