<!-- 事件分派 -->
<template>
	<view class="main">
		<view class="uform">
			<u-form :model="form" ref="refForm" :label-style="{'color':'#91949F'}" borderBottom labelWidth="180"
				input-align="right" rrorType="['toast']">
				<view class="card-box">
					<!-- <u-form-item required label="处理人：" prop="form.stepProcessUserName">
						<input placeholder="请选择处理人"  inputmode="none" placeholder-class="placeholderClass" v-model="form.stepProcessUserName" inputAlign="right"
							@click="showAssignment">
						</input>
						<template #right>
							<u-icon name="arrow-right" size="28"></u-icon>
						</template>
					</u-form-item> -->
					<u-form-item required label="标题：" prop="title">
						<u-input v-model="form.title" placeholder="请填写标题" input-align="right"></u-input>
					</u-form-item>
					<u-form-item label="紧急程度：" required prop="levelName" borderBottom>
						<template #right>
							<u-icon name="arrow-right" size="28" color="#91949F"></u-icon>
						</template>
						<input v-model="form.levelName" inputmode="none" placeholder-class="placeholderClass"
							placeholder="请选择紧急程度" input-align="right" @click="state.urgencyShow=true">
						</input>
					</u-form-item>

					<u-form-item required label="地址：" prop="address">
						<u-input v-model="form.address" placeholder="请填写地址" input-align="right"></u-input>
					</u-form-item>
					<u-form-item required label="处理人：" prop="stepProcessUserName" labelWidth="220">
						<input placeholder="请选择处理人" inputmode="none" placeholder-class="placeholderClass"
							v-model="form.stepProcessUserName" inputAlign="right" @click="toChooseUser">
						</input>
						<template #right>
							<u-icon name="arrow-right" size="28"></u-icon>
						</template>
					</u-form-item>
					<u-form-item required label="处理级别：" prop="processLevelLabel">
						<input v-model="form.processLevelLabel" inputmode="none" placeholder-class="placeholderClass"
							placeholder="请选择处理级别" input-align="right" @click="state.processLevelShow=true"></input>
						<template #right>
							<u-icon name="arrow-right" size="28"></u-icon>
						</template>
					</u-form-item>
					<!-- <u-form-item required label="预计完工时间：" prop="form.endTime" labelWidth="220">
						<input placeholder="请选择预计完工时间" inputmode="none" placeholder-class="placeholderClass"  v-model="form.endTime" inputAlign="right">
						</input>
						<template #right>
							<u-icon name="calendar" size="34"></u-icon>
						</template>
					</u-form-item> -->
					<u-form-item label="描述：" :borderBottom="false">
					</u-form-item>
					<u-form-item label="" prop="remark">
						<u-input type="textarea" input-align="left" placeholder="请输入描述" v-model="form.remark" border height="160">
						</u-input>
					</u-form-item>
				</view>
				<!-- <view class="card-box">
					<u-form-item label="共同处理人：" prop="form.val1" :borderBottom="false">
						<u-input placeholder="请选择共同处理人" v-model="form.name" inputAlign="right">
						</u-input>
						<template #right>
							<u-icon name="arrow-right" size="28"></u-icon>
						</template>
					</u-form-item>
				</view> -->
			</u-form>
			<view class="card-box" style="padding: 0">
				<file-upload ref="refFileUpload"></file-upload>
			</view>
		</view>
		<view class="button">
			<u-button type="primary" color="#3862F8" @click="submit">提交</u-button>
		</view>
		<!-- 区域 -->
		<!-- <u-picker v-model="state.assignmentShow" mode="multiSelector" :default-selector="[0]"
			:range="assignmentList" range-key="name" @columnchange="acolumnchange" @confirm="selectClick">
		</u-picker> -->
		<!-- 紧急程度 -->
		<u-picker v-model="state.urgencyShow" mode="selector" :default-selector="[0]" :range="urgencys" range-key="name"
			@confirm="selectUrgency"></u-picker>
		<u-select v-model="state.assignmentShow" mode="mutil-column-auto" :list="assignmentList"></u-select>
		<!-- 处理级别 -->
		<u-picker v-model="state.processLevelShow" mode="selector" :default-selector="[0]" :range="processLevels"
			range-key="name" @confirm="selectProcessLevel"></u-picker>
		<u-toast ref="refToast" />
	</view>
</template>

<script lang="ts" setup>
	import {
		onMounted,
		reactive,
		ref
	} from "vue";
	import {
		onShow,
		onReady
	} from '@dcloudio/uni-app';
	import {
		useStore
	} from '@/store/index'
	import dayjs from 'dayjs'
	import {
		removeSlash
	} from '@/common/utils/removeIdSlash'
	import fileUpload from '@/components/fileUpload/fileUpload.vue'
	import {
		collaborateVerify
	} from '@/common/api/workOrder'
	import {
		workOrderProcessLevelList,
		workOrderEmergencyLevelList
	} from '@/common/data/workOrderData'
	import {
		storeToRefs
	} from 'pinia'
	const store = useStore();
	const refFileUpload = ref<any>({})
	const state = reactive<{
		assignmentShow : Boolean
		urgencyShow : Boolean
		processLevelShow : Boolean
		processLevelIndex : any
		assignmentIndex : any
		urgencyIndex : any
	}>({
		assignmentShow: false,
		processLevelShow: false,
		processLevelIndex: [0],
		assignmentIndex: [0, 0, 0],
		urgencyIndex: [0],
		urgencyShow: false
	})

	const rules = reactive<any>({
		title: [{
			required: true,
			message: '请填写标题'
		}],
		level: [{
			required: true,
			message: '请选择紧急程度'
		}],
		stepProcessUserName: [{
			required: true,
			message: '请选择处理人'
		}],
		processLevelLabel: [{
			required: true,
			message: '请选择处理级别'
		}],
		address: [{
			required: true,
			message: '请填写地址'
		}]
	})
	const urgencys = ref<any>([])
	const processLevels = ref<any>()
	const refForm = ref<any>()
	const refToast = ref<any>()
	const assignmentList = ref<any>([{}, {}, {}])
	const workorderId = ref<string>('')
	const form = reactive<any>({
		stepProcessUserId: '',
		processLevel: '',
	})

	// 选择处理级别
	const selectProcessLevel = (val : any) => {
		state.processLevelIndex = val
		const processLevel = processLevels.value[val[0]]
		form.processLevelLabel = processLevel.name
		form.processLevel = processLevel.dayTime * 1440 + processLevel.hourTime * 60 + processLevel.minuteTime
		let addTime = 0
		form.endTime = dayjs().add(form.processLevel, 'minute').format('YYYY-MM-DD HH:mm:ss')
	}

	const toChooseUser = () => {
		uni.$u.route({
			url: 'pages/userTree/organization',
		})
	}


	const clickShow = (indexName : any, typeName : any) => {
		state[indexName] = [0]
		state[typeName] = true
	}

	// 选择紧急程度
	const selectUrgency = (val : any) => {
		state.urgencyIndex = val
		const level = urgencys.value[val[0]]
		form.level = level.id
		form.levelName = level.name
	}
	const submit = () => {
		const params = {
			...form,
			videoUrl: refFileUpload.value.videoList.join(','),
			audioUrl: refFileUpload.value.voiceList.join(','),
			imgUrl: refFileUpload.value.imageList.join(','),
			otherFileUrl: refFileUpload.value.fileList.join(','),
			stepProcessUserId: form.stepProcessUserId,
			processLevel: form.processLevel,
			remark: form.remark,
			approved: true
		}
		refForm.value.validate(async (valid : any) => {
			if (valid) {
				uni.showModal({
					title: '提示',
					content: '确定提交审核吗',
					success: (res) => {
						if (res.confirm) {
							collaborateVerify(workorderId.value, params).then(res => {
								if (res.data?.code === 200 && res.data?.data.code != 500) {
									let {
										userData
									} = storeToRefs(store);
									userData.value = null
									refToast.value.show({
										title: '审核成功',
										type: 'success',
										callback: () => {
											uni.navigateBack({
												delta: 2
											})
										}
									})
								} else {
									refToast.value.show({
										title: res.data.data.err,
										type: 'error'
									})
								}
							})
						}
					}
				})
			}
		})
	}

	// 选择处理人
	const chooseUser = (user : any) => {
		if (user) {
			form.stepProcessUserName = user.firstName
			form.stepProcessUserId = removeSlash(user.id?.id)
		}
	}

	onReady(async () => {
		console.log('ready')
		refForm.value.setRules(rules)
	})

	onShow(async () => {
		let {
			userData
		} = store;
		if (userData) {
			chooseUser(userData)
		}
	})
	onMounted(async () => {
		var pages = getCurrentPages();
		var page = pages[pages.length - 1];
		workorderId.value = page.$page.options.id
		urgencys.value = await workOrderEmergencyLevelList()
		console.log('urgencys.value', urgencys.value)
		processLevels.value = await workOrderProcessLevelList()
		console.log('processLevels.value', processLevels.value)
	})
</script>

<style lang="scss" scoped>
	.main {
		.uform {
			padding: 20rpx 32rpx 40rpx 32rpx;
		}

		.card-box {
			border-radius: 8px;
			min-height: 80rpx;
			padding: 20rpx 28rpx;
			margin: 0 auto 20rpx auto;
			color: #FFFFFF;
			background-color: #FFFFFF;

			.form {
				padding: 0 28rpx 10rpx 28rpx;
				color: #060F27;
			}

			.l-file {
				padding: 10rpx 0;
			}
		}


		::v-deep.u-form-item {
			padding: 0;
		}
	}
</style>