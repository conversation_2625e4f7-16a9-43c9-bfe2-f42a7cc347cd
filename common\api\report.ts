import { http } from '../vmeitime-http/index'

export const getWaterSupplyDetailReport = (params: {
	stationIdList: String,
	queryType: String,
	start?: number,
	end?: number,
}) => {
	return http().get('istar/api/production/waterPlant/getWaterSupplyDetailReport', params)
}

export const getWaterPlantFlowReport = (params: {
	stationIds: String,
	queryType: String,
	time: String,
}) => {
	return http().get('istar/api/production/waterPlant/getWaterPlantFlowReport', params)
}

export const getWaterQualityStation = (params: {
	stationIds: String,
	queryType: String,
	time: String,
}) => {
	return http().get('istar/api/waterQualityStation/getReport', params)
}
export const getWaterPlantProductionReport = (params: {
	stationId: String,
	queryType: String,
	time: String,
	groupType: String
}) => {
	return http().get('istar/api/production/waterPlant/getWaterPlantProductionReport', params)
}

export const getWaterSupplyReport = (params: {
	stationId: String,
	start: number,
	end: number,
	queryType: String
}) => {
	return http().get('istar/api/boosterPumpStation/getWaterSupplyReport', params)
}

export const getEnergyMethodReport = (params: {
	stationId: String,
	start: number,
	end: number,
	queryType: String
}) => {
	return http().get('istar/api/boosterPumpStation/getEnergyMethodReport', params)
}

export const getWaterSupplyConsumptionReport = (params: {
	stationId: String,
	start: number,
	end: number,
	queryType: String
}) => {
	return http().get('istar/api/boosterPumpStation/getWaterSupplyConsumptionReport', params)
}

export const getSimpleTree = (params: {
	type: String
}) => {
	return http().get('api/station/simpleTree', params)
}
