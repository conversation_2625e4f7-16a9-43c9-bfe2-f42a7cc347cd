<template>
  <view>
    <!-- Right Popup -->
    <uni-popup ref="refPopRight" type="right">
      <PopLayout @back="closePop" :title="currentMenu?.alias || currentMenu?.name">
        <MapDetail :menu="currentMenu" :params="{ layerids: layerIds }" />
      </PopLayout>
    </uni-popup>

    <!-- Bottom Popup -->
    <uni-popup ref="refPopCenter" type="bottom">
      <view class="popup-center">
        <view class="popup-title">{{ popData.title }}</view>
        <scroll-view class="table-scroll" :scroll-y="true">
          <!-- Station Equipment Table -->
          <uni-table 
            v-if="currentModule === 'sssb'" 
            border 
            stripe 
            emptyText="暂无更多数据"
          >
            <uni-tr>
              <uni-th align="left" style="font-weight: bold; color: #000000; width: 35%;">
                监测点
              </uni-th>
              <uni-th align="left" style="font-weight: bold; color: #000000; width: 20%;">
                检测值
              </uni-th>
              <uni-th align="left" style="font-weight: bold; color: #000000; width: 45%;">
                采集时间
              </uni-th>
            </uni-tr>
            <uni-tr v-for="(row, i) in popData.data" :key="i">
              <uni-td 
                v-for="(column, j) in popData.columns" 
                :style="{ backgroundColor: column.bgColor }"
                :key="j"
              >
                {{ row[column.prop] }}{{ row[column.unit] ?? '' }}
              </uni-td>
            </uni-tr>
          </uni-table>

          <!-- Work Order Popup -->
          <WorkOrderListPopup 
            v-else-if="currentModule === 'rycl'" 
            :user="popData.obj" 
          />
        </scroll-view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import PopLayout from './PopLayout.vue'
import MapDetail from './MapDetail.vue'
import WorkOrderListPopup from './WorkOrderListPopup.vue'

export default {
  name: 'MapPopups',
  components: {
    PopLayout,
    MapDetail,
    WorkOrderListPopup
  },
  props: {
    currentMenu: {
      type: Object,
      default: () => ({})
    },
    layerIds: {
      type: Array,
      default: () => []
    },
    popData: {
      type: Object,
      default: () => ({
        title: '',
        data: [],
        columns: [],
        obj: {}
      })
    },
    currentModule: {
      type: String,
      default: ''
    }
  },
  methods: {
    openPop(name = 'refPopRight') {
      const pop = this.$refs[name]
      pop?.open()
    },
    closePop(name = 'refPopRight') {
      const pop = this.$refs[name]
      pop?.close()
    }
  }
}
</script>

<style scoped lang="scss">
.popup-center {
  width: 100vw;
  height: 520rpx;
  padding-bottom: 140rpx;
  background-color: #f5f5f5;

  .popup-title {
    height: 70rpx;
    display: flex;
    align-items: center;
    padding: 24rpx;
    font-size: 28rpx;
    font-weight: bold;
    color: #55aaff;
    background-color: #fff;
  }

  .table-scroll {
    height: 320rpx;
  }
}

:deep(.uni-table) {
  overflow-x: hidden;
  min-width: 650rpx !important;
}

:deep(.uni-table-scroll) {
  width: 750rpx;
}
</style>