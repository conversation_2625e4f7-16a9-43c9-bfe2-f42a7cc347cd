<!-- 巡检分派 -->
<template>
	<view class="main">
		<view class="uform">
			<u-form :model="form" ref="refForm" :label-style="{'color':'#91949F'}" borderBottom labelWidth="180"  input-align="right" 
				:errorType="['toast']">
				<view class="card-box">
					<u-form-item required label="接收人：" prop="receiveUserName">
						<input placeholder="请选择接收人" inputmode="none" placeholder-class="placeholderClass" v-model="form.receiveUserName" inputAlign="right"
							@click="toChooseUser('chooseUser')">
						</input>
						<template #right>
							<u-icon name="arrow-right" size="28"></u-icon>
						</template>
					</u-form-item>
					<u-form-item label="共同完成人：" prop="collaborateUserId" @click="toChooseUser('chooseListUser')">
						<view class="flex-center" @click="toChooseUser('chooseListUser')">
							<view class="flex-center" style="flex-wrap: wrap;margin: 4rpx;">
								<u-tag :text="user" type="primary" closeable v-for="(user,index) in users" :key="index"
									:index="index" @close="removeCC" />
								<!-- <text>{{user}}</text>
								<u-icon name="close-circle-fill" size="32" color="#FFFFFF" @click="removeCC(index)">
								</u-icon> -->
							</view>
						</view>
						<template #right>
							<u-icon name="arrow-right" size="28" color="#c0c4cc"></u-icon>
						</template>
					</u-form-item>
				</view>
			</u-form>
		</view>
		<view class="button">
			<u-button type="primary" color="#3862F8" @click="submit">提交</u-button>
		</view>
		<u-toast ref="refToast"></u-toast>
	</view>
</template>

<script lang="ts" setup>
	import {
		onMounted,
		reactive,
		ref
	} from "vue";
	import {
		onShow,
		onReady
	} from '@dcloudio/uni-app';
	import {
		storeToRefs
	} from 'pinia'
	import {
		useStore
	} from '@/store/index'
	import {
		removeSlash
	} from '@/common/utils/removeIdSlash'
	import {
		assignmentInspectionTask
	} from '@/common/api/inspection'
	const store = useStore();
	const rules = reactive < any > ({
		receiveUserName: [{
			required: true,
			message: '请选择接收人'
		}]
	})
	const eventName = ref < string > ('')
	const users = ref < any > ([])
	const refForm = ref < any > ()
	const refToast = ref < any > ()
	const taskId = ref < String > ('')
	const form = reactive < any > ({
		receiveUserId: '',
		collaborateUserId: [],
	})

	// 移除抄送人员
	const removeCC = (index: any) => {
		console.log(index, users.value)
		users.value.splice(index, 1)
		console.log(users.value)
		form.collaborateUserId.splice(index, 1)
	}

	// 新增共同完成人
	const chooseListUser = (user: any) => {
		if (users.value.indexOf(user.firstName) === -1) {
			users.value.push(user.firstName)
			form.collaborateUserId.push(removeSlash(user.id?.id))
		}
	}

	const toChooseUser = (name: string) => {
		eventName.value = name
		uni.$u.route({
			url: 'pages/userTree/organization',
		})
	}

	const submit = () => {
		const params = {
			receiveUserId: form.receiveUserId,
			collaborateUserId: form.collaborateUserId.join(','),
			taskIdList: [taskId.value]
		}
		refForm.value.validate(async (valid: any) => {
			if (valid) {
				uni.showModal({
					title: '提示',
					content: '确定分派吗',
					success: (res) => {
						if (res.confirm) {
							assignmentInspectionTask(params).then(res => {
							console.log(taskId.value, res)
								if (res.data?.code === 200) {
									let {
										userData
									} = storeToRefs(store);
									userData.value = null
									refToast.value.show({
										title: '分派成功',
										type: 'success',
										callback: () => {
											uni.navigateBack({
												delta: 2
											})
										}
									})
								} else {
									refToast.value.show({
										title: '分派失败',
										type: 'error'
									})
								}
							})
						}
					}
				})
			}
		})
	}

	// 选择处理人
	const chooseUser = (user: any) => {
		console.log('user', user);
		if (user) {
			form.receiveUserName = user.firstName
			form.receiveUserId = removeSlash(user.id?.id)
		}
	}

	onReady(async () => {
		console.log('ready')
		refForm.value.setRules(rules)
	})

	onShow(async () => {
		let {
			userData
		} = store;
		if (userData) {
			if (eventName.value === 'chooseListUser') {
				chooseListUser(userData)
			} else {
				chooseUser(userData)
			}
		}
	})
	onMounted(async () => {
		var pages = getCurrentPages();
		var page = pages[pages.length - 1];
		taskId.value = page.$page.options.taskId

	})
</script>

<style lang="scss" scoped>
	.main {
		.uform {
			padding: 20rpx 32rpx;
		}

		.card-box {
			height: 30vh;
			border-radius: 8px;
			min-height: 80rpx;
			padding: 20rpx 28rpx;
			margin: 0 auto 20rpx auto;
			color: #FFFFFF;
			background-color: #FFFFFF;

			.form {
				padding: 0 28rpx 10rpx 28rpx;
				color: #060F27;
			}

			.l-file {
				padding: 10rpx 0;
			}
		}


		::v-deep.u-form-item {
			padding: 0;
		}
	}
</style>
