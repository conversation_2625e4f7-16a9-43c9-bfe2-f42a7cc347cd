import { http } from '../vmeitime-http/index'


export const saveInstall = (params : any) => {
	return http().post('api/gan/install/save', params)
}


export const getTaskList = (params : {
	page : number
	size : number
	status ?: string
}) => {
	return http().get('api/gan/install/list', params)
}

export const getInstallDetail = (id : string) => {
	return http().get(`api/gan/install/detail/${id}`)
}

export const licenseAuth = (params ?: any) => {
	return http().get(`gan/api/gan/install/license/auth`, params)
}

export const myComplete = (params : {
	page : number
	size : number
}) => {
	return http().get(`api/gan/install/myComplete`, params)
}

/**
 *
 * @param params
 * @returns
 */
export const auditInstall = (params ?: {
	id : string,
	status : string,
	remark ?: string
}) => {
	return http().post(`api/gan/install/audit`, params)
}

export const saveStep = (params ?: {
	pid : string,
	stepNo : number,
	file : string,
	remark ?: string
}) => {
	return http().post(`/api/gan/install/step`, params)
}


export const getPdf = (params : any) => {
	return http().get(`gan/api/gan/install/pdf`, params)
}

export const getBase64 = (params : any) => {
	return http().get(`gan/api/gan/install/base64`, params)
}


export const editInstall = (params ?: any) => {
	return http().post(`api/gan/install/edit`, params)
}