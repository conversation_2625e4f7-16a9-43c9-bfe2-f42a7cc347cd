import {
	loadEsriModules,
	setSymbol
} from "../../utils/arcMapHelper"

export const useSketch = () => {
	let createHandler
	let update<PERSON>and<PERSON>
	let deleteHand<PERSON>
	let undoHandler
	let redoHand<PERSON>
	let sketch
	const staticState = {
		sketch: undefined
	}
	/**
	 * 初始化画笔
	 * @param view
	 * @param graphicsLayer
	 * @param operationCallbacks {
		
		createCallBack?: (result: ISketchHandlerParameter) => any
		
		updateCallBack?: (result: ISketchHandlerParameter) => any
		
		delCallBack?: (result: ISketchHandlerParameter) => any
		
		undoCallBack?: (result: ISketchHandlerParameter) => any
		
		redoCallBack?: (result: ISketchHandlerParameter) => any
		
		snappingOptions?: __esri.SnappingOptionsProperties
		}
	 * @returns
	 */
	const init = async (
		view,
		graphicsLayer,
		operationCallbacks
	) => {
		destroy()
		const [SketchViewModel] = await loadEsriModules(['esri/widgets/Sketch/SketchViewModel'])
		staticState.sketch = new SketchViewModel({
			view,
			layer: graphicsLayer,
			polygonSymbol: setSymbol('polygon'),
			polylineSymbol: setSymbol('polyline'),
			pointSymbol: setSymbol('point'),
			snappingOptions: operationCallbacks?.snappingOptions
		})
		createHandler = staticState.sketch?.on('create', result => {
			const res = {
				graphics: (result.graphic && [result.graphic]) || [],
				state: result.state,
				tool: result.tool,
				toolEventInfo: result.toolEventInfo,
				type: result.type
			}
			operationCallbacks?.createCallBack &&
				operationCallbacks.createCallBack(res)
		})
		updateHandler = staticState.sketch?.on('update', result => {
			const res = {
				graphics: result.graphics,
				state: result.state,
				tool: result.tool,
				toolEventInfo: result.toolEventInfo,
				type: result.type,
				aborted: result.aborted
			}
			operationCallbacks?.updateCallBack &&
				operationCallbacks.updateCallBack(res)
		})
		deleteHandler = staticState.sketch?.on('delete', result => {
			const res = {
				graphics: result.graphics,
				tool: result.tool,
				type: result.type
			}
			operationCallbacks?.delCallBack && operationCallbacks.delCallBack(res)
		})
		undoHandler = staticState.sketch?.on('undo', result => {
			const res = {
				graphics: result.graphics,
				tool: result.tool,
				type: result.type
			}
			operationCallbacks?.undoCallBack && operationCallbacks.undoCallBack(res)
		})
		redoHandler = staticState.sketch?.on('redo', result => {
			const res = {
				graphics: result.graphics,
				tool: result.tool,
				type: result.type
			}
			operationCallbacks?.redoCallBack && operationCallbacks.redoCallBack(res)
		})
		return staticState.sketch
	}
	const destroy = () => {
		createHandler?.remove()
		updateHandler?.remove()
		deleteHandler?.remove()
		undoHandler?.remove()
		redoHandler?.remove()
		staticState.sketch?.destroy()
	}
	return {
		init,
		destroy,
		staticState
	}
}

export default useSketch
