<template>
	<div class="pump-pics" :style="{zoom:screenWidth}">
		<div class="img-box">
			<image class="img" :src="currentMenu.img" alt="1" />
			<LAOGONGDIANJU v-if="currentMenu.id === EStationPageId.LAOGONGDIANJU"
				:station-id="currentMenu.stationId?.id"></LAOGONGDIANJU>
			<YIZHOUYIPIN v-if="currentMenu.id === EStationPageId.YIZHOUYIPIN" :station-id="currentMenu.stationId?.id">
			</YIZHOUYIPIN>
			<JUNQUJIAYAZHAN v-if="currentMenu.id === EStationPageId.JUNQUJIAYAZHAN"
				:station-id="currentMenu.stationId?.id"></JUNQUJIAYAZHAN>
			<JINSHUYUAN v-if="currentMenu.id === EStationPageId.JINSHUYUAN" :station-id="currentMenu.stationId?.id">
			</JINSHUYUAN>
			<FUXINJIAYUANXIAOQUBENGZHAN v-if="currentMenu.id === EStationPageId.FUXINJIAYUANXIAOQUBENGZHAN"
				:station-id="currentMenu.stationId?.id"></FUXINJIAYUANXIAOQUBENGZHAN>
		</div>
		<RealTimeList :station-id="currentMenu.stationId.id"></RealTimeList>
		<div style="height: 620rpx;overflow-y: auto;position: relative;top:-740rpx;width: 300rpx;">
			<div class="left-bars">
				<LeftBar v-for="(item, i) in blocks" :key="i" :data="item" :is-active="currentMenu.id === item.id"
					@click="handleMenuClick"></LeftBar>
			</div>
		</div>
	</div>
</template>
<script lang="ts" setup>
	import {
		ref,
		onMounted
	} from 'vue'
	import {
		onUnload,
		onLoad
	} from '@dcloudio/uni-app';
	import {
		EStationPageId
	} from './data'
	import LeftBar from './components/LeftBar.vue'
	import LAOGONGDIANJU from './components/LAOGONGDIANJU.vue'
	import FUXINJIAYUANXIAOQUBENGZHAN from './components/FUXINJIAYUANXIAOQUBENGZHAN.vue'
	import JINSHUYUAN from './components/JINSHUYUAN.vue'
	import JUNQUJIAYAZHAN from './components/JUNQUJIAYAZHAN.vue'
	import YIZHOUYIPIN from './components/YIZHOUYIPIN.vue'
	import RealTimeList from './components/RealTimeList.vue'
	const screenWidth = ref < any > (1)
	// 页面加载完横屏正方向
	onLoad(() => {
		// #ifdef APP-PLUS
		plus.screen.lockOrientation('landscape-secondary');
		// #endif
	})
	// 页面关闭时清除横屏正方向
	onUnload(() => {
		// #ifdef APP-PLUS
		plus.screen.lockOrientation('portrait-primary');
		// #endif
	})

	const getPumpImgBg = (name: string) => {
		return new URL(`./imgs/pump-bg/${name}`).href
	}
	const blocks = [{
			id: EStationPageId.YIZHOUYIPIN,
			text: '宜州一品',
			img: '/static/pump/imgs/pump-bg/yizhouyipin.png',
			stationId: {
				id: '402880b18a3aec86018a40a308c600d9'
			},
		},
		{
			id: EStationPageId.LAOGONGDIANJU,
			text: '老供电局',
			img: '/static/pump/imgs/pump-bg/lgdj.png',
			stationId: {
				id: ''
			},
		},
		{
			id: EStationPageId.JUNQUJIAYAZHAN,
			text: '军队加压站',
			img: '/static/pump/imgs/pump-bg/jdjyz.png',
			stationId: {
				id: '402880b18a3aec86018a40a393b900da'
			},
		},
		{
			id: EStationPageId.JINSHUYUAN,
			text: '金墅园',
			img: '/static/pump/imgs/pump-bg/jsy.png',
			stationId: {
				id: ''
			},
		},
		{
			id: EStationPageId.FUXINJIAYUANXIAOQUBENGZHAN,
			text: '福星家园小区泵站',
			img: '/static/pump/imgs/pump-bg/fxjy.png',
			stationId: {
				id: '402880b18a3aec86018a40a1b79900d8'
			},
		}
	]
	const currentMenu = ref < {
		id: any;text: string;img: string;stationId ? : any
	} > (blocks[0])

	const handleMenuClick = item => {
		currentMenu.value = item
	}

	onMounted(() => {
		// #ifndef H5
		screenWidth.value = uni.getWindowInfo().screenWidth / 1920
		// #endif
		// #ifdef APP-PLUS
		screenWidth.value = uni.getWindowInfo().screenHeight / 1920
		// #endif
	})
</script>
<style lang="scss" scoped>
	.pump-pics {
		width: 1920px;
		height: 780px;
		background-color: #6b8baa;
		background-image: radial-gradient(circle at 50% 11%, #a3c1df 0%, #4e6f8e 72%);
		position: relative;
		overflow: hidden;

		.left-bars {
			position: absolute;
			top: 4%;
			// transform: translateY(-50%);
			left: 30px;
			display: flex;
			flex-direction: column;
			justify-content: center;
		}

		.img-box {
			width: 100%;
			height: 100%;
			display: flex;
			align-items: center;
			justify-content: center;
			position: relative;

			.img {
				width: 1000px;
				height: 700px;
				object-fit: contain;
				position: absolute;
				left: 260px;
				top: 100px;
			}
		}
	}
</style>
