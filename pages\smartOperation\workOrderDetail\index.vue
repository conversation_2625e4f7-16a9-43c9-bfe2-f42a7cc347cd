<!-- 工单详情 -->
<template>
	<view class="main-detail">
		<view class="card-box" style="padding: 22rpx 28rpx;">
			<view class="flex-between hand">
				<view class="hand-title">
					<text>{{detail.title}}</text>
				</view>
				<view class="status">
					{{detail.statusName || (porps.fromType==='assignment'?'待派单': porps.statusName || detail.statusName)}}
				</view>
			</view>
			<view class="table">
				<view class="info">
					<text>工单编号：</text> <text>{{detail.serialNo}}</text>
				</view>
				<view class="info flex-between">
					<view class="flex-center">
						<text>紧急程度：</text>
						<view class="spot" :style="{'background-color': level?.color}">
						</view>
						<text :style="{'color':level?.color}">{{level?.name}}</text>
					</view>
					<view>
						<text>处理级别：</text> <text>{{detail.processLevelLabel?detail.processLevelLabel: '-'}}</text>
					</view>
				</view>
			</view>
		</view>
		<view class="card-box" style="padding: 22rpx 0;margin-top: 20rpx;">
			<view class="card-title flex-center">
				<text class="custom-icon custom-icon-caiyouduo_hemaixiangqing-dingdanxiangqing"></text>
				<view class="label">基础信息</view>
			</view>
			<u-gap height="2" bg-color="#EBEDF6"></u-gap>
			<view class="list">
				<view class="flex-center l-c">
					<text>事件来源：</text> <text>{{detail.source || '-'}}</text>
				</view>
				<view class="flex-center l-c">
					<text>事件类型：</text> <text>{{detail.type || '-'}}</text>
				</view>
				<view class="flex-center l-c">
					<text>发起部门：</text> <text>{{detail.uploadUserDepartmentName}}</text>
				</view>
				<view class="flex-center l-c">
					<text>发起人员：</text> <text>{{detail.uploadUserName}}</text>
				</view>
				<view class="flex-center l-c">
					<text>联系方式：</text> <text>{{detail.uploadPhone || '-'}}</text>
				</view>
				<view class="flex-center l-c">
					<text>发起时间：</text> <text>{{detail.createTime || '-'}}</text>
				</view>
				<view class="flex-between l-c">
					<view class=" flex-center">
						<text>地址：</text>
						<view class="address">{{detail.address || '-'}} </view>
					</view>
					<view class="navigation" v-if="detail.coordinate"
						@click="gomapApp(detail.coordinate.split(',')[0],detail.coordinate.split(',')[1],detail.address)">
						<text class="custom-icon custom-icon-daohang-Navigation">导航</text>
					</view>
				</view>
				<view class="flex-center l-c">
					<text>预计完成时间：</text> <text>{{detail.estimatedFinishTime || '-'}}</text>
				</view>
				<view class="flex-center l-c">
					<text>完成时间：</text> <text>{{detail.completeTime || '-'}}</text>
				</view>
				<view class="flex-center l-c">
					<text>处理时长：</text> <text>{{detail.diff || '-'}}</text>
				</view>
			</view>
		</view>
		<view class="card-box" style="padding: 22rpx 0 44rpx 0;margin-top: 20rpx;">
			<view class="card-title flex-center">
				<text class="custom-icon custom-icon-lishishijian"></text>
				<view class="label">操作日志</view>
			</view>
			<u-gap height="2" bg-color="#EBEDF6"></u-gap>
			<view class="time-line" v-if="stagesList.length>0">
				<u-time-line>
					<u-time-line-item nodeTop="2" v-for="(stage, index) in stagesList" :key="index">
						<!-- 此处自定义了左边内容，用一个图标替代 -->
						<template v-slot:node>
							<view class="u-node">
								<!-- 此处为uView的icon组件 -->
								<view class="circle-center"></view>
							</view>
						</template>
						<template v-slot:content>
							<view>
								<view class="flex-between">
									<view class="u-order-title">{{stage.typeName}}</view>
									<view class="u-order-time">{{stage.processTime}}</view>
								</view>
								<view>
									<view class="u-order-desc">
										<!-- <text class="custom-icon custom-icon-yonghu">操作人：</text> -->
										<text>{{stage.typeName}}人：</text>
										<text>{{stage.processUserName}}</text>
									</view>
									<view class="u-order-desc" v-if="stage.type==='HANDOVER_REVIEW'">
										<text>转发至：</text>
										<text>{{stage.nextProcessUserName}}</text>
									</view>
									<view class="u-order-desc" v-if="stage.type==='HANDOVER_REVIEW'">
										<text>审核人：</text>
										<text>{{stage.processUserName}}</text>
									</view>
									<view class="u-order-desc">
										<text>{{stage.typeName}}时间：</text>
										<text>{{stage.processTime}}</text>
									</view>
								</view>
								<view v-if="stage.type==='SUBMIT'">
									<view class="u-order-desc">
										<text>审核人：</text>
										<text>{{stage.nextProcessUserName}}</text>
									</view>
									<!-- <view class="u-order-desc">
										<text>审核组织：</text>
										<text>{{stage.nextProcessUserOrganizationName}}</text>
									</view> -->
									<view class="u-order-desc">
										<text>审核部门：</text>
										<text>{{stage.nextProcessUserDepartmentName}}</text>
									</view>
								</view>
								<view v-if="stage.type==='APPROVED'">
									<!-- <view class="u-order-desc">
										<text>审核组织：</text>
										<text>{{stage.processUserDepartmentName}}</text>
									</view> -->
									<view class="u-order-desc">
										<text>审核部门：</text>
										<text>{{stage.processUserOrganizationName}}</text>
									</view>
								</view>
								<view v-if="stage.processRemark">
									<view class="u-order-desc">
										<text>描述：</text>
										<text>{{stage.processRemark}}</text>
									</view>
								</view>
								<view class="flex-center">
									<u-grid :col="4" :border="false">
										<block
											v-if="stage.processAdditionalInfo && stage.processAdditionalInfo?.imgUrl">
											<u-grid-item
												v-for="(img,index) in typeof(stage.processAdditionalInfo.imgUrl)=='object'?stage.processAdditionalInfo?.imgUrl:stage.processAdditionalInfo?.imgUrl.split(',')"
												:key="index">
												<view class="u-order-content">
													<u-image :src="img"
														@tap="previewImage(stage.processAdditionalInfo.imgUrl,index)"
														style="width:112rpx;height:112rpx">
													</u-image>
												</view>
											</u-grid-item>
										</block>
										<block
											v-if="stage.processAdditionalInfo && stage.processAdditionalInfo?.audioUrl">
											<u-grid-item
												v-for="(audioUrl,index) in typeof(stage.processAdditionalInfo.audioUrl)=='object'?stage.processAdditionalInfo?.audioUrl:stage.processAdditionalInfo?.audioUrl.split(',')"
												:key="index">
												<view class="u-order-content">
													<image src="/static/img/audio-file.png" @tap="playVoice(audioUrl)"
														style="width:112rpx;height:112rpx">
													</image>
												</view>
											</u-grid-item>
										</block>
										<block
											v-if="stage.processAdditionalInfo && stage.processAdditionalInfo?.videoUrl">
											<u-grid-item
												v-for="(video,index) in typeof(stage.processAdditionalInfo.videoUrl)=='object'?stage.processAdditionalInfo?.videoUrl:stage.processAdditionalInfo?.videoUrl.split(',')"
												:key="index">
												<view class="u-order-content">
													<view class="margin-center" @click="palyVideo(video)">
														<image src="/static/sound-recording/play.png"
															style="width:112rpx;height:112rpx"></image>
													</view>
												</view>
											</u-grid-item>
										</block>
										<block
											v-if="stage.processAdditionalInfo && stage.processAdditionalInfo?.otherFileUrl">
											<u-grid-item
												v-for="(otherFileUrl,index) in typeof(stage.processAdditionalInfo.otherFileUrl)=='object'?stage.processAdditionalInfo?.otherFileUrl:stage.processAdditionalInfo?.otherFileUrl.split(',')"
												:key="index">
												<view class="u-order-content" @click="downloadFile(otherFileUrl)">
													<view class="margin-center">
														<u-icon name="custom-icon-wenjianjia" customPrefix="custom-icon"
															size="112" color="#2979ff">
														</u-icon>
													</view>
												</view>
											</u-grid-item>
										</block>
									</u-grid>
								</view>
							</view>
						</template>
					</u-time-line-item>
					<u-time-line-item>
						<!-- 此处自定义了左边内容，用一个图标替代 -->
						<template v-slot:node>
							<view class="u-node">
								<!-- 此处为uView的icon组件 -->
								<view class="circle-center"></view>
							</view>
						</template>
						<template v-slot:content>
							<view>
								<view class="flex-between">
									<view class="u-order-title">发起工单</view>
									<view class="u-order-time">{{detail.createTime}}</view>
								</view>
								<view>
									<view class="u-order-desc">
										<text class="custom-icon custom-icon-yonghu"></text>
										<text>{{detail.organizerName}}</text>
									</view>
								</view>
								<view class="flex-center">
									<u-grid :col="4" :border="false">
										<block v-if="detail.imgUrl">
											<u-grid-item v-for="(url,index) in detail.imgUrl.split(',')" :key="index">
												<view class="u-order-content">
													<image :src="url" @tap="previewImage([url],0)"
														style="width:112rpx;height:112rpx">
													</image>
												</view>
											</u-grid-item>
										</block>
										<block v-if="detail.audioUrl">
											<u-grid-item v-for="(url,index) in detail.audioUrl.split(',')" :key="index">
												<view class="u-order-content">
													<image src="/static/img/audio-file.png"
														@tap="playVoice(url)"
														style="width:112rpx;height:112rpx"></image>
												</view>
											</u-grid-item>
										</block>
										<block v-if="detail.videoUrl">
											<u-grid-item v-for="(url,index) in detail.videoUrl.split(',')" :key="index">
												<view class="u-order-content">
													<view class="margin-center" @click="palyVideo(url)">
														<image src="/static/sound-recording/play.png"
															style="width:112rpx;height:112rpx"></image>
													</view>
												</view>
											</u-grid-item>
										</block>
										<block v-if="detail.otherFileUrl">
											<u-grid-item v-for="(url,index) in detail.otherFileUrl.split(',')" :key="index">
												<view class="u-order-content" @click="downloadFile(url)">
													<view class="margin-center">
														<u-icon name="custom-icon-wenjianjia" customPrefix="custom-icon"
															size="112" color="#2979ff">
														</u-icon>
													</view>
												</view>
											</u-grid-item>
										</block>
									</u-grid>
								</view>
							</view>
						</template>
					</u-time-line-item>
				</u-time-line>
			</view>
			<view v-else class="empty">
				<u-empty text="无操作日志" mode="list"></u-empty>
			</view>
		</view>
		<!-- <view class="button flex-around">
			<u-button type="primary" @click="toEventDispatch">事件分派</u-button>
			<u-button type="primary" @click="toInvalidHandle">无效处理</u-button>
		</view> -->
		<video v-show="isOpenVideo" id="video" :src="videoUrl" @fullscreenchange="screenChange"></video>
		<u-popup mode="bottom" v-model="showVoice" height="500rpx">
			<sound-recording :voiceP="voiceP" :finishP="finish" :maximum="60" @confirm="confirmVoice"
				@cancel="showVoice=false"></sound-recording>
		</u-popup>
		<u-toast ref="refToast" />
	</view>
</template>

<script lang="ts" setup>
	import {
		ref,
		getCurrentInstance,
		onBeforeMount
	} from "vue";
	import {
		onShow
	} from '@dcloudio/uni-app';
	import {
		workOrderDetailById,
		workOrderStagesById
	} from '@/common/api/workOrder'
	import {
		levels
	} from '@/common/data/publicData'
	import dayjs from "dayjs"
	const porps = defineProps({
		workOrderId: String,
		fromType: String,
		statusName: String
	})
	const level = ref < any > ('')
	const videoUrl = ref < any > ()
	const refToast = ref < any > ()
	const dowloadTask = ref < any > ()
	const isOpenVideo = ref < Boolean > (false)
	const detail = ref < any > ({})
	const voiceList = ref < any > ()
	const actionUrl = ref < string > ('')
	const levelColor = ref < any > ('')
	const videoContext = ref < any > ()
	const stagesList = ref < any > ({})
	const showVoice = ref < Boolean > (false)
	const finish = ref < Boolean > (false)
	const voiceP = ref < String > ('')
	const playVoice = (voicePath: String) => {
		console.log('播放录音');
		voiceP.value = voicePath
		finish.value = true
		showVoice.value = true
	}
	//提交录音
	const confirmVoice = (file: any) => {
		console.log(file)
		uni.uploadFile({
			url: actionUrl.value, //仅为示例，非真实的接口地址
			filePath: file,
			header: {
				'X-Authorization': 'Bearer ' + uni.getStorageSync('token')
			},
			name: 'file',
			success: (uploadFileRes) => {

				const data = JSON.parse(uploadFileRes.data)
				voiceList.value.push(data.data?.url)
				showVoice.value = false
				console.log(voiceList.value);
			}
		});
	}
	const palyVideo = (url: string) => {
		videoUrl.value = url
		//打开视频（全屏播放）
		isOpenVideo.value = true
		videoContext.value = uni.createVideoContext('video', this)
		videoContext.value.play()
		videoContext.value.requestFullScreen()
	}

	//全屏
	const screenChange = (e) => {
		//退出全屏时停止播放
		if (!e.detail.fullScreen) {
			videoContext.value.stop()
			isOpenVideo.value = false
		}
	}

	const previewImage = (imageist, index) => {
		//uniapp预览轮播图
		uni.previewImage({
			current: index, //预览图片的下标
			urls: imageist //预览图片的地址，必须要数组形式，如果不是数组形式就转换成数组形式就可以
		})
	}

	const downloadFile = (url: string) => {
		uni.showLoading({
			title: '下载中'
		})
		// 文件下载
		uni.downloadFile({
			url: url, //下载地址接口返回
			success: data => {
				if (data.statusCode === 200) {
					//文件保存到本地
					uni.saveFile({
						tempFilePath: data.tempFilePath, //临时路径
						success: (res) => {
							console.log('下载',res)
							uni.hideLoading()
							refToast.value?.show({
								title: '下载成功',
								type: 'success',
								callback: () => {
									uni.openDocument({
										filePath: res.savedFilePath,
									});
								}
							})
						}
					});
				}
			},
			fail: err => {
				uni.showToast({
					icon: 'none',
					title: '失败请重新下载'
				});
			}
		})
	}


	const getWorkOrderById = async () => {
		console.log(porps)
		const res = await workOrderDetailById(porps.workOrderId)
		const data = res.data?.data
		detail.value = data
		const diff = data.completeTime ? dayjs(data.completeTime).diff(dayjs(data.createTime), 'minutes') + '分钟' :
			'-'
		detail.value.diff = diff
		console.log(detail.value)
		level.value = await levels(data.level)
		// levelColor.value = await levels(data.level)
	}

	const getWorkOrderStagesById = async () => {
		console.log(porps)
		const res = await workOrderStagesById(porps.workOrderId)
		const result = res.data?.data.reverse()
		stagesList.value = result.map((res2: any) => {
			return {
				...res2,
				processAdditionalInfo: res2.processAdditionalInfo ? JSON.parse(res2
					.processAdditionalInfo ||
					'{\"videoUrl\":[],\"audioUrl\":[],\"imgUrl\":[],\"otherFileUrl\":[]}') : {
					audioUrl: [],
					imgUrl: [],
					otherFileUrl: [],
					videoUrl: []
				}
			}
		})
		console.log('dada',stagesList.value)
  	}

	// 打开的点击事件，传经纬度和地点名
	const gomapApp = (latitude, longitude, name) => {
		let url = "";
		if (plus.os.name == "Android") { //判断是安卓端
			plus.nativeUI.actionSheet({ //选择菜单
				title: "选择地图应用",
				cancel: "取消",
				buttons: [{
					title: "腾讯地图"
				}, {
					title: "百度地图"
				}, {
					title: "高德地图"
				}]
			}, function(e) {
				switch (e.index) {
					//下面是拼接url,不同系统以及不同地图都有不同的拼接字段
					case 1:
						//注意referer=xxx的xxx替换成你在腾讯地图开发平台申请的key
						url = `qqmap://map/geocoder?coord=${latitude},${longitude}&referer=xxx`;
						break;
					case 2:
						url =
							`baidumap://map/marker?location=${latitude},${longitude}&title=${name}&coord_type=gcj02&src=andr.baidu.openAPIdemo`;
						break;
					case 3:
						url =
							`androidamap://viewMap?sourceApplication=appname&poiname=${name}&lat=${latitude}&lon=${longitude}&dev=0`;
						break;
					default:
						break;
				}
				if (url != "") {
					url = encodeURI(url);
					//plus.runtime.openURL(url,function(e){})调起手机APP应用
					plus.runtime.openURL(url, function(e) {
						plus.nativeUI.alert("本机未安装指定的地图应用");
					});
				}
			})
		} else {
			// iOS上获取本机是否安装了百度高德地图，需要在manifest里配置
			// 在manifest.json文件app-plus->distribute->apple->urlschemewhitelist节点下添加
			//（如urlschemewhitelist:["iosamap","baidumap"]）  
			plus.nativeUI.actionSheet({
				title: "选择地图应用",
				cancel: "取消",
				buttons: [{
					title: "腾讯地图"
				}, {
					title: "百度地图"
				}, {
					title: "高德地图"
				}]
			}, function(e) {
				switch (e.index) {
					case 1:
						url = `qqmap://map/geocoder?coord=${latitude},${longitude}&referer=xxx`;
						break;
					case 2:
						url =
							`baidumap://map/marker?location=${latitude},${longitude}&title=${name}&content=${name}&src=ios.baidu.openAPIdemo&coord_type=gcj02`;
						break;
					case 3:
						url =
							`iosamap://viewMap?sourceApplication=applicationName&poiname=${name}&lat=${latitude}&lon=${longitude}&dev=0`;
						break;
					default:
						break;
				}
				if (url != "") {
					url = encodeURI(url);
					plus.runtime.openURL(url, function(e) {
						plus.nativeUI.alert("本机未安装指定的地图应用");
					});
				}
			})
		}
	}

	// onMounted(async () => {
	// 	await getWorkOrderById()
	// 	await getWorkOrderStagesById()
	// })
	onBeforeMount(async () => {
		const globalProperties = getCurrentInstance()?.appContext.config.globalProperties
		// console.log('globalProperties-----', globalProperties)
		actionUrl.value = globalProperties.$uploadUrl
	})
	onShow(async () => {
		await getWorkOrderById()
		await getWorkOrderStagesById()
	})
</script>

<style lang="scss" scoped>
	.main-detail {
		padding: 0rpx 32rpx 40rpx 32rpx;

		.card-box {
			margin: 0 auto;
			border-radius: 16rpx;
			background-color: #FFFFFF;

			.hand {
				.hand-title {
					height: 42rpx;
					line-height: 42rpx;
					overflow:hidden; //超出的文本隐藏
					text-overflow:ellipsis; //溢出用省略号显示
					white-space:nowrap; //溢出不换行
					max-width: 85%;
					text {
						font-size: 28rpx;
						color: #060F27;
						font-weight: 600;
					}
				}

				.status {
					color: #91949F;
				}
			}

			.table {
				margin-top: 24rpx;

				.info {
					font-size: 24rpx;
					padding-bottom: 18rpx;
					display: flex;

					.spot {
						width: 12rpx;
						height: 12rpx;
						border-radius: 50%;
						margin-right: 12rpx;
						margin-top: 6rpx;
					}

					text {
						&:nth-child(1) {
							color: #91949F;
						}

						&:nth-child(2) {
							flex: 1;
						}
					}


					.bg {
						padding: 2rpx 12rpx;
						background: rgba(56, 98, 248, 0.2);
						border-radius: 8rpx;

						text {
							color: #3862F8;
						}
					}
				}
			}


			.card-title {
				padding: 14rpx 28rpx;

				text {
					font-size: 32rpx;
					color: #3862F8;
				}

				.label {
					padding-left: 10rpx;
					font-style: normal;
					font-weight: 600;
					font-size: 28rpx;
					color: #060F27;
				}
			}

			.list {
				padding: 0 28rpx 10rpx 28rpx;
				color: #060F27;

				.l-c {
					padding: 10rpx 0;

					text {
						&:nth-child(1) {
							color: #91949F;
						}

						&:nth-child(3) {
							color: #060F27;
						}
					}

					.address {
						color: #FBB934;
						overflow: hidden;
						white-space: nowrap;
						width: 426rpx;
						text-overflow: ellipsis;
					}

					.navigation {
						text-align: right;

						text {
							font-size: 24rpx;
							color: #3862F8;
						}
					}
				}

				.l-file {
					padding: 10rpx 0;
				}
			}

			.time-line {
				padding: 20rpx 28rpx;
			}

			.u-node {
				width: 32rpx;
				height: 32rpx;
				border-radius: 50%;
				display: flex;
				justify-content: center;
				align-items: center;
				background: rgba(56, 98, 248, 0.2);

				.circle-center {
					height: 16rpx;
					width: 16rpx;
					border-radius: 50%;
					background-color: #3862F8;
				}
			}

			.u-order-title {
				color: #91949F;
				font-size: 24rpx;
			}

			.u-order-desc {
				color: rgb(150, 150, 150);
				font-size: 28rpx;
				margin: 16rpx 0;
				font-size: 24rpx;

				text {
					&:nth-child(1) {
						font-size: 24rpx;
					}

					&:nth-child(2) {
						color: #060F27;
						padding-left: 10rpx;
					}
				}
			}

			.u-order-time {
				color: rgb(200, 200, 200);
				font-size: 26rpx;
			}

			.empty {
				padding-top: 40rpx;
			}
		}

		.button {
			width: 100%;
			background-color: #FFFFFF;
			position: fixed;
			bottom: 0rpx;
			height: 120rpx;
			z-index: 9999;
			box-shadow: 0px 4rpx 30rpx rgba(178, 183, 199, 0.5);

			button {
				border-radius: 8rpx;
				width: 343rpx;
				margin: 20rpx auto;
			}
		}
	}

	.u-order-content {
		// background-color: red;
	}
</style>
