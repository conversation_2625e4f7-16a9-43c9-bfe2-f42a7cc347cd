<!-- 无效处理 -->
<template>
	<view class="main">
		<view class="card-box">
			<u-form :model="form" ref="form1" :label-style="{'color':'#91949F'}" labelWidth="180">
				<u-form-item required label="无效原因：" prop="form.val1" :borderBottom="false">
				</u-form-item>
				<u-form-item prop="form.val1" :borderBottom="false">
					<u-input type="textarea" input-align="left"  placeholder="请输入无效原因" v-model="form.name" height="400" border>
					</u-input>
				</u-form-item>
			</u-form>
		</view>
		<view class="button">
			<u-button type="primary" color="#3862F8" @click="">提交</u-button>
		</view>
	</view>
</template>

<script lang="ts" setup>
	import {
		reactive
	} from "vue";
	const form = reactive<any>({})
</script>

<style lang="scss" scoped>

</style>