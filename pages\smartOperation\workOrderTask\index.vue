<!-- 工单处理 -->
<template>
	<view class="main">
		<u-sticky bg-color="#FFFFFF">
			<u-tabs :list="state.tabs" active-color="#3862F8" v-model="state.currentTab" bg-color="#FFFFFF"
				:offset="[0,60]" :is-scroll="false" @change="changeStatus" count="count">
			</u-tabs>
		</u-sticky>
		<!-- <scroll-view :lower-threshold="50" :refresher-threshold="50" scroll-y refresher-enabled
			refresher-default-style="white" :refresher-triggered="triggered" @refresherrefresh="onRefresh"
			@scrolltolower="showMoreData"> -->
		<view class="card-box" v-for="(data,index) in tableData" :key="index" @click="toDetail(data)">
			<view class="flex-between hand">
				<view class="hand-title flex-center">
					<u-icon name="custom-icon-gongdan" customPrefix="custom-icon" color="#3862F8" size="34" bold>
					</u-icon>
					<text>{{data.title}}</text>
				</view>
				<view class="status">
					<!-- {{state.tabs[state.currentTab].name}} -->
				</view>
			</view>
			<view class="table">
				<view class="info">
					<text>工单编号：</text> <text>{{data.serialNo}}</text>
				</view>
				<view class="info">
					<text>工单类型：</text> <text>{{data.type}}</text>
				</view>
				<view class="info">
					<text>地址：</text> <text>{{data.address}}</text>
				</view>

				<block v-if="true">
					<view class="info">
						<text>剩余时间：</text> <text class="text-color-o">{{durationTime(data.estimatedFinishTime)}}</text>
					</view>
					<view class="info">
						<u-icon name="custom-icon-position" custom-prefix="custom-icon" size="38" color="#3862F8">
						</u-icon>
						<view style="color: #91949F;margin-left: 8rpx;">距离 <text
								style="color: #3862F8;">{{distance(data.coordinate)}}</text> 公里</view>
					</view>
				</block>

				<view class="info" v-else>
					<text>完成时间：</text> <text class="text-color-o">{{data.startTime}}</text>
				</view>
			</view>
		</view>
		<u-loadmore bg-color="F9F9F9" :status="state.status" loading-text="努力加载中" loadmore-text="加载更多"
			nomore-text="没有了更多" />
		<!-- </scroll-view> -->
		<view class="">
			<u-calendar :max-date="maxDate" v-model="state.dateShow" ref="calendar" @close="state.dateShow=false"
				@change="chooseDate"></u-calendar>
		</view>
	</view>
</template>

<script lang="ts" setup>
	import {
		onMounted,
		reactive,
		ref
	} from "vue";
	import {
		onShow
	} from '@dcloudio/uni-app';
	import {
		onPullDownRefresh,
		onReachBottom
	} from '@dcloudio/uni-app'
	import dayjs from 'dayjs'
	import {
		workOrderList,
		countOfStage
	} from '@/common/api/workOrder'
	import {
		spatialDistance
	} from '@/common/utils/mapUtils'
	import {
		maxDate
	} from '@/common/data/publicdata'
	import {
		removeSlash
	} from "@/common/utils/removeIdSlash";
	// 选择区域
	const state = reactive < {
		tabs: any,
		activceTab: any,
		dateShow: Boolean,
		status: String,
		latitude: Number,
		longitude: Number,
		query: any,
		currentTab: number,
	} > ({

		latitude: 0,
		longitude: 0,
		currentTab: 0,
		tabs: [{
				name: '未接收',
				value: 'ASSIGN',
				count: 0
			},
			{
				name: '已接收',
				value: 'RESOLVING,REJECTED',
				count: 0
			},
			{
				name: '已完成',
				value: 'APPROVED',
				count: 0
			},
		],
		activceTab: {},
		dateShow: false,
		status: 'loadmore',
		query: {
			page: 1,
			size: 10
		}
	})
	const triggered = ref < Boolean > ()
	const tableData = ref < any > ([])
	//计算经纬度
	const distance = (coordinate: string) => {
		if (coordinate && state.latitude != 0) {
			const [endLat, endLng] = coordinate.split(',')
			return spatialDistance(state.latitude, state.longitude, endLat, endLng)
		} else {
			return '0'
		}
	}

	//
	const toDetail = (params ? : any) => {
		uni.$u.route({
			url: 'pages/smartOperation/workOrderTask/workOrderDetail/index',
			params: {
				id: params.id,
				fromType: 'task',
				status: params.status,
				statusName: state.tabs[state.currentTab].name
			}
		})
	}
	//选择日期
	const chooseDate = () => {}

	// 加载更多
	const showMoreData = async () => {
		console.log('dddddd')
		state.status = 'loading'
		await getWorkOrderList()
	} ///

	// 下拉刷新
	const onRefresh = async () => {
		triggered.value = true
		state.query.page = 1
		await getWorkOrderList()
		await getCountOfStage()
	}
	// 切换工单状态数据
	const changeStatus = async (index: number) => {
		state.query.page = 1
		tableData.value = []
		state.currentTab = index
		await getWorkOrderList()
	}

	// 工单数量统计
	const getCountOfStage = async () => {
		const query = {
			self: true
		}
		const res = await countOfStage(query)
		const data = res.data?.data
		state.tabs[0].count = data.waitingCount
		state.tabs[1].count = data.receiveCount
		console.log('data', data)
	}

	// 工单列表
	const getWorkOrderList = async () => {
		console.log(state.query.page)
		state.status = 'loadmore'
		state.query = {
			...state.query,
			stageBetween: state.tabs[state.currentTab].value,
			// stage: state.tabs[state.currentTab].value,
			// self: true
			stepProcessUserId: removeSlash(uni.getStorageSync('userInfo').id?.id)
			// type: '二供泵房'
		}
		const res = await workOrderList(state.query)
		const data = res.data?.data?.data
		const total = res.data?.data?.total
		if (state.query.page === 1) {
			tableData.value = data
		} else {
			tableData.value = tableData.value.concat(data)
		}
		if (data.length > 0) {
			state.query.page += 1
			state.status = 'loadmore'
		}
		 if(total === tableData.value.length ) {
			state.status = 'nomore'
		}

		triggered.value = false
		uni.stopPullDownRefresh()
	}

	// 计算剩余时间
	const durationTime = (endTime: any) => {
		const now = dayjs()
		const end = dayjs(endTime)
		const days = end.diff(now, 'minutes')
		if (days > 0) {
			//传入的分钟数  转换成天、时、分
			const day = parseInt(days / 60 / 24);
			const hour = parseInt(days / 60 % 24);
			const min = parseInt(days % 60);
			let duration = '';
			if (day > 0) {
				duration = day + '天';
			}
			if (hour > 0) {
				duration += hour + '小时';
			}
			if (min > 0) {
				duration += parseFloat(min) + '分钟';
			}
			//三元运算符 传入的分钟数不够一分钟 默认为0分钟，else return 运算后的StatusMinute
			return duration == '' ? '0分钟' : duration;
		}
		return '0天0小时0分钟';
	}
	// 获取当前定位
	// const locaton = () => {
	// 	uni.getLocation({
	// 		type: 'gcj02',
	// 		altitude: true,
	// 		geocode: true,
	// 		isHighAccuracy: true,
	// 		success: (info) => {
	// 			console.log(info)
	// 			state.latitude = info.latitude
	// 			state.longitude = info.longitude
	// 		},
	// 		fail: () => {
	// 			uni.$u.toast('获取定位失败')
	// 		}
	// 	})
	// }

	onReachBottom(async () => {
		showMoreData()
	})

	onPullDownRefresh(async () => {
		onRefresh()
	})
	onMounted(async () => {
		console.log(removeSlash('af2b2690-3d42-11ed-8fc1-cfeed8efd29c'))
	})

	onShow(async () => {
		onRefresh()
	})
</script>

<style lang="scss" scoped>
	.main {
		padding-top: 0;
		height: 95vh;
	}

	.card-box {
		width: 92%;
		margin: 20rpx auto;
		border-radius: 16rpx;
		padding: 22rpx 28rpx;
		background-color: #FFFFFF;

		.hand {
			.hand-title {
				height: 42rpx;
				line-height: 42rpx;

				text {
					font-size: 28rpx;
					color: #060F27;
					font-weight: 600;

					&:nth-child(2) {
						margin-left: 16rpx;
					}

					&:nth-child(1) {
						font-size: 24rpx;
						color: #FFFFFF;
					}
				}
			}

			.status {
				color: #91949F;
			}
		}

		.table {
			margin-top: 24rpx;

			.info {
				font-size: 24rpx;
				padding-bottom: 18rpx;
				display: flex;

				text {
					&:nth-child(1) {
						color: #91949F;
					}

					&:nth-child(2) {
						flex: 1;
					}
				}

				.bg {
					padding: 2rpx 12rpx;
					background: rgba(56, 98, 248, 0.2);
					border-radius: 8rpx;

					text {
						color: #3862F8;
					}
				}
			}
		}
	}
</style>
