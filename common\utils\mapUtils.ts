

// 计算经纬度之间的距离
export const spatialDistance = (lat1: any, lng1: any, lat2: any, lng2: any) => {
	console.log(lat1, lng1, lat2, lng2)
	const radLat1 = lat1 * Math.PI / 180.0;
	const radLat2 = lat2 * Math.PI / 180.0;
	const a = radLat1 - radLat2;
	const b = lng1 * Math.PI / 180.0 - lng2 * Math.PI / 180.0;
	let s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) +
		Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2)));
	s = s * 6378.137;
	s = Math.round(s * 10000) / 10000;
	return s  // 单位千米
}

/**
 * 获取两经纬度之间的距离
 * @param {number} e1 点1的东经, 单位:角度, 如果是西经则为负
 * @param {number} n1 点1的北纬, 单位:角度, 如果是南纬则为负
 * @param {number} e2
 * @param {number} n2
 */
export const getDistance = (e1: any, n1: any, e2: any, n2: any) => {
	const R = 6371
	const { sin, cos, asin, PI, hypot } = Math

	/** 根据经纬度获取点的坐标 */
	let getPoint = (e, n) => {
		e *= PI / 180
		n *= PI / 180
		//这里 R* 被去掉, 相当于先求单位圆上两点的距, 最后会再将这个距离放大 R 倍
		return { x: cos(n) * cos(e), y: cos(n) * sin(e), z: sin(n) }
	}

	let a = getPoint(e1, n1)
	let b = getPoint(e2, n2)
	let c = hypot(a.x - b.x, a.y - b.y, a.z - b.z)
	let r = asin(c / 2) * 2 * R
	return r
}
