<template>
  <view class="main">
    <scroll-view scroll-y="true" scroll-with-animation="true">
      <view
        class="div-radius menus card"
        v-for="(application, index) in applicationList"
        :key="index"
      >
        <view class="title">
          <text>{{ application.title }}</text>
        </view>
        <view class="menu-list" v-if="true">
          <u-grid :border="false" col="4">
            <u-grid-item
              v-for="(item, i) in application.menus"
              :key="i"
              @click="routerTo(item.url)"
              :custom-style="{ padding: 0 }"
            >
              <view class="menu">
                <image class="image" :src="item.icon"></image>
              </view>
              <text class="grid-text">{{ item.title }}</text>
            </u-grid-item>
          </u-grid>
        </view>
        <view class="menu-list" v-if="false">
          <u-grid :border="false" col="4">
            <u-grid-item
              v-for="(item, i) in application.menus"
              :key="i"
              @click="routerTo(item.path)"
              :custom-style="{ padding: 0 }"
            >
              <view class="menu">
                <image
                  class="image"
                  :src="item.url || '/static/img/menus/gongdanchaxun.png'"
                ></image>
              </view>
              <text class="grid-text">{{ item.meta.title }}</text>
            </u-grid-item>
          </u-grid>
        </view>
      </view>
    </scroll-view>
    <!-- <block v-if="proxy.$isHideTabBar">
			<u-tabbar v-model="current" :list="bottomMenu"></u-tabbar>
		</block> -->
  </view>
</template>

<script lang="ts" setup>
import { onBeforeMount, ref, getCurrentInstance } from "vue";
import {
  findMenuByTenantApplication,
  getTenantApplication,
} from "@/common/api/system";
import { removeSlash } from "@/common/utils/removeIdSlash";
import { applications } from "./data";
const { proxy } = getCurrentInstance();
const applicationList = ref<any>([]);
const userInfo = ref<any>({});
const bottomMenu = ref<any>([]);
// 路由跳转
const routerTo = (url: string, params?: any) => {
  uni.showLoading({
    title: "加载中",
    mask: true,
  });
  uni.navigateTo({
    url: "/" + url,
    success: () => {
      uni.hideLoading();
    },
  });
};

const getApp = async () => {
  userInfo.value = uni.getStorageSync("userInfo");
  let tenantId = userInfo.value.tenantId?.id;
  tenantId = removeSlash(tenantId);
  const res = await getTenantApplication({
    tenantId,
    resourceType: "APP",
  });
  return res.data[0]?.id;
};

onBeforeMount(async () => {
  const id = await getApp();
  findMenuByTenantApplication({
    tenantApplicationId: id,
  }).then((res: any) => {
    const data = res.data[0]?.children;
    console.log(data);
    applicationList.value = data?.map((menu: any) => {
      return {
        title: menu.meta.title,
        menus: menu.children,
      };
    });
    console.log(applicationList.value);
    if (false) {
      const hasApplicationList = data?.map((d: any) => {
        const menus = applications().find(
          (app) => app.title === d.meta.title
        )?.menus;
        const hasMenus: any = [];
        menus?.map((menu: any) => {
          const state = d.children?.filter(
            (child: any) => child.path.indexOf(menu.url) !== -1
          );
          if (state.length > 0) {
            for (const i in state) {
              menu.title = state[i].meta.title;
              menu.url = state[i].path;
              menu.icon = state[i].url || menu.icon;
              hasMenus.push(menu);
            }
          }
        });
        d.menus = hasMenus;
        return {
          title: d.meta.title,
          menus: hasMenus,
        };
      });
      applicationList.value = hasApplicationList;
      // const userInfo = uni.getStorageSync('userInfo')
      // if(userInfo.authority === 'TENANT_ADMIN'){
      // 	applictionList.value = applictions()
      // }else{
      // 	applictionList.value = hasApplictionList
      // }
    } else {
      applicationList.value = applications();
    }
    if (proxy.$isHideTabBar) {
      bottomMenu.value = uni.getStorageSync("bottomMenus");
    }
  });
});
</script>

<style lang="scss" scoped>
.menus {
  margin-top: 30rpx;
  background-color: #ffffff;

  .menu-list {
    width: 90%;
    margin: 24rpx auto;

    .menu {
      width: 96rpx;
      height: 96rpx;
      background: #f0f0fc;
      border-radius: 16rpx;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      line-height: 96rpx;
    }

    .grid-text {
      font-size: 24rpx;
      color: #060f27;
      font-weight: 500;
      padding-bottom: 24rpx;
      padding-top: 16rpx;
    }
  }

  .image {
    width: 64rpx;
    height: 64rpx;
    border-radius: 10rpx;
  }
}
</style>
