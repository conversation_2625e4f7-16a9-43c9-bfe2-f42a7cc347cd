// 环境配置
export const getEnvConfig = () => {
  // 判断是否为生产环境
  const isProd = process.env.NODE_ENV === 'production'
  
  if (isProd) {
    // 生产环境配置
    return {
      apiBaseUrl: 'https://api-prod.example.com/', // 替换为实际的API服务地址
      serverBaseUrl: 'https://server-prod.example.com/', // 替换为实际的Server服务地址
    }
  } else {
    // 开发环境配置 - 使用空字符串，由Vite代理处理
    return {
      apiBaseUrl: '',
      serverBaseUrl: '',
    }
  }
}

// 初始化环境配置
export const initEnvConfig = () => {
  const config = getEnvConfig()
  uni.setStorageSync('apiBaseUrl', config.apiBaseUrl)
  uni.setStorageSync('serverBaseUrl', config.serverBaseUrl)
}