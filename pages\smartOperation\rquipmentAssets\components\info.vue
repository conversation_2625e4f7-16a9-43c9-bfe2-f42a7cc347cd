<template>
	<view class="">
		<view class="card-box">
			<view class="flex-between hand">
				<view class="hand-title flex-center">
					<image src="@/static/img/icons/devices/title-icon.png" style="width: 20rpx;height: 40rpx;"></image>
					<text>基本信息</text>
				</view>
				<view class="status">
					{{props.detail.statusName}}
				</view>
			</view>
			<u-gap height="1" bg-color="#bbb"></u-gap>
			<view class="table">
				<view class="info">
					<text>设备编号：</text> <text>{{props.detail.serialId}}</text>
				</view>
				<view class="info">
					<text>设备名称：</text> <text>{{props.detail.deviceName}}</text>
				</view>
				<view class="info">
					<text>设备单位：</text> <text>{{props.detail.deviceUnit}}</text>
				</view>
				<view class="info">
					<text>设备类型：</text> <text>{{props.detail.deviceType}}</text>
				</view>
				<view class="info">
					<text>设备型号：</text> <text>{{props.detail.deviceModel}}</text>
				</view>
				<view class="info">
					<text>供应商名称：</text> <text>{{props.detail.supplierName}}</text>
				</view>
				<view class="info">
					<text>采购金额：</text> <text>{{props.detail.price}}</text>
				</view>
				<view class="info">
					<text>入库时间：</text> <text>{{props.detail.createTime}}</text>
				</view>
				<view class="info">
					<text>使用年限：</text> <text>{{props.detail.deviceUseYear}}</text>
				</view>
				<view class="info">
					<text>出库时间：</text> <text>{{props.detail.outTime}}</text>
				</view>
				<view class="info">
					<text>报废时间：</text> <text>{{props.detail.scrappedTime}}</text>
				</view>
				<view class="info">
					<text>备注：</text> <text>{{props.detail.remark}}</text>
				</view>
			</view>
		</view>

		<view class="card-box">
			<view class="flex-between hand">
				<view class="hand-title flex-center">
					<image src="@/static/img/icons/devices/title-icon.png" style="width: 20rpx;height: 40rpx;"></image>
					<text>自定义属性</text>
				</view>
				<view class="status">
					{{props.detail.statusName}}
				</view>
			</view>
			<u-gap height="1" bg-color="#bbb"></u-gap>
			<view class="table">
				<view class="info" v-for="(item,index) in deviceTypeAttr" :key="index">
					<text>{{item.name}}：</text> <text>{{deviceTypeData['autoField.'+(index+1)]}}</text>
				</view>
			</view>
		</view>

		<view class="card-box">
			<view class="flex-between hand">
				<view class="hand-title flex-center">
					<image src="@/static/img/icons/devices/title-icon.png" style="width: 20rpx;height: 40rpx;"></image>
					<text>设备相关文件</text>
				</view>
				<view class="status">
					{{props.detail.statusName}}
				</view>
			</view>
			<u-gap height="1" bg-color="#bbb"></u-gap>
			<view class="table">
				<view class="info flex-center files">
					<u-icon name="custom-icon-word" stomPrefix="custom" size="78" color="#00aaff"></u-icon>
					<text style="color: #626262;padding-left: 20rpx;">设备使用说明手册.doc</text>
				</view>
				<view class="info flex-center files">
					<u-icon name="custom-icon-excel" customPrefix="custom" size="78" color="#00d567"></u-icon>
					<text style="color: #626262;padding-left: 20rpx;">设备相关参数.xls</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script lang="ts" setup>
	import {
		ref,
		onMounted,
		watch,
		nextTick
	} from 'vue'
	import {
		getDeviceTypeAttr
	} from '@/common/api/rquipmentAssets'
	const deviceTypeAttr = ref < any > ([])
	const deviceTypeData = ref < any > ([])
	const filesData = ref < any > ([])
	const props = defineProps({
		detail: {
			type: Object
		}
	})
	watch(() => props.detail, async () => {
		refreshAddAtrr()

	})
	
	const refreshAddAtrr = async () => {
		let code = props.detail.serialId
		if (code) {
			code = code.substring(0, code.length - 6) + '000000'
			console.log(code)
			const res = await getDeviceTypeAttr(code)
			deviceTypeAttr.value = res.data?.data
			deviceTypeData.value = JSON.parse(props.detail.autoField)
			//TODO 修改文件读取
			filesData.value = props.detail.deviceFiles ? JSON.parse(props.detail?.deviceFiles): ''
		}
	}

	onMounted(() => {
		nextTick(() => {
			refreshAddAtrr()
		})
	})
</script>

<style lang="scss" scoped>
	.card-box {
		width: 686rpx;
		margin: 20rpx auto;
		border-radius: 16rpx;
		// padding: 22rpx 28rpx;
		padding: 0;
		background-color: #FFFFFF;

		.hand {
			padding: 22rpx 28rpx;

			.hand-title {
				height: 42rpx;
				line-height: 42rpx;

				.icon-box {
					text-align: center;
					line-height: 32rpx;
					height: 32rpx;
					width: 32rpx;
					background-color: #3862F8;
					border-radius: 4rpx;
				}

				text {
					font-size: 28rpx;
					margin-left: 16rpx;
					color: #060F27;
					font-weight: 600;
				}
			}

			.status {
				color: #91949F;
			}
		}

		.line {
			padding-bottom: 20rpx;
		}

		.table {
			margin-top: 24rpx;
			padding: 0rpx 28rpx;

			.info {
				font-size: 24rpx;
				padding: 24rpx 0rpx;
				display: flex;

				text {
					&:nth-child(1) {
						color: #000000;
					}

					&:nth-child(2) {
						flex: 1;
						color: #91949F;
					}
				}


				.bg {
					padding: 2rpx 12rpx;
					background: rgba(56, 98, 248, 0.2);
					border-radius: 8rpx;

					text {
						color: #3862F8;
					}
				}
			}

			.files {
				font-size: 36rpx;
				font-weight: 600;

				text {
					color: #626262;
					padding-left: 20rpx;
				}
			}
		}

	}
</style>
