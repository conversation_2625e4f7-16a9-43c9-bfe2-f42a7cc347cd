<template>
	<view class="pipe-detail">
		<view class="pipe-detail-item pipe-detail-total">

			<view class="title">
				管线统计
			</view>
			<view class="total-blocks">
				<view class="total-block" v-for="(item, i) in state.total" :key="i">
					<view class="value">{{item.value}} {{item.unit}}</view>
					<view class="text">{{item.title}}</view>
				</view>
			</view>
		</view>
		<scroll-view scroll-y style="height: 70vh;">
		<view class="pipe-detail-item pipe-detail-chart">
			<view class="title">管径占比统计</view>
			<view class="chart-box">
				<l-echart ref="refLEchart_DiameterPie"></l-echart>
			</view>

		</view>
		<view class="pipe-detail-item pipe-detail-chart">
			<view class="title">年份统计管长</view>
			<view class="chart-box">
				<l-echart ref="refLEchart_Year_Length"></l-echart>
			</view>

		</view>
		<view class="pipe-detail-item pipe-detail-chart">
			<view class="title">按管径统计管长</view>
			<view class="chart-box">
				<l-echart ref="refLEchart_Diameter_Length"></l-echart>
			</view>

		</view>
		<view class="pipe-detail-item pipe-detail-chart">
			<view class="title">按管材统计管长</view>
			<view class="chart-box">
				<l-echart ref="refLEchart_Material_Length"></l-echart>
			</view>
		</view>
		</scroll-view>
	</view>

</template>

<script lang="ts" setup>
	import * as echarts from 'echarts'
	import { PipeStatistics, getPipeByBuildYear } from '@/common/api/map'
	import lEchart from '@/uni_modules/lime-echart/components/l-echart/l-echart.vue'
	import { onMounted, reactive, ref } from 'vue'
	const props = defineProps<{
		layerids ?: number[]
		menu ?: {
			name : string,
			alias ?: string, icon : string, id : string, type : string, isActive ?: boolean, color ?: string
		}
	}>()
	const refLEchart_DiameterPie = ref<InstanceType<typeof lEchart>>()
	const refLEchart_Material_Length = ref<InstanceType<typeof lEchart>>()
	const refLEchart_Year_Length = ref<InstanceType<typeof lEchart>>()
	const refLEchart_Diameter_Length = ref<InstanceType<typeof lEchart>>()
	const state = reactive<{
		total : { title : string; value : string; unit : string }[]
	}>({
		total: [{ title: '管线总长度', value: '0', unit: 'km' }, { title: '管线总条数', value: '0', unit: '条' }]
	})
	const refreshTotalCount = async () => {
		try {
			const res = await PipeStatistics({
				layerids: JSON.stringify((props.layerids)),
				group_fields: JSON.stringify([]),
				statistic_field: 'OBJECTID',
				statistic_type: '1',
				where: '1=1'
			})
			if (res.data.code === 10000) {
				const data = res.data?.result?.rows[0]?.rows || []
				let totalCount = 0
				data.map((item : any) => {
					totalCount += item.OBJECTID
				})
				state.total[1].value = totalCount.toFixed(0)
			} else {
				console.log('统计失败');
			}
		} catch (e) {
			console.log(e)
		}
	}

	const refreshTotalLength = async () => {
		try {
			const res = await PipeStatistics({
				layerids: JSON.stringify((props.layerids)),
				group_fields: JSON.stringify(['DIAMETER']),
				statistic_field: 'SHAPE.Len',
				statistic_type: '2',
				where: '1=1'
			})

			if (res.data.code === 10000) {
				const data = res.data?.result?.rows[0]?.rows || []
				let totalLength = 0
				const optionData : any[] = []
				const xData : string[] = []
				const yData : string[] = []
				data.map((item : any) => {
					totalLength += item['SHAPE.Len']
					xData.push('DN' + item['DIAMETER'])
					yData.push((item['SHAPE.Len'] / 1000).toFixed(4))
					optionData.push({ name: 'DN' + item['DIAMETER'], value: (item['SHAPE.Len'] / 1000).toFixed(4) })
				})
				state.total[0].value = (totalLength / 1000).toFixed(2)
				refLEchart_DiameterPie.value.init(echarts, (chart : any) => {
					const option = {
						tooltip: {
							trigger: 'item'
						},
						title: {
							text:
								'{name|'
								+ '合计'
								+ '(km)'
								+ '}\n{val|'
								+ state.total[0].value
								+ '}',
							top: 'center',
							left: '33%',
							textAlign: 'center',
							textStyle: {
								rich: {
									name: {
										fontSize: 10,
										fontWeight: 'normal',
										padding: [8, 0],
										align: 'center',
										color: '#2A2A2A'
									},
									val: {
										fontSize: 16,
										fontWeight: 'bold',
										color: '#2A2A2A'
									}
								}
							}
						},
						legend: {
							// selectedMode: false, // 取消图例上的点击事件
							type: 'scroll',
							icon: 'circle',
							orient: 'vertical',
							left: 'right',
							top: 'center',
							align: 'left',
							itemGap: 10,
							itemWidth: 10, // 设置宽度
							itemHeight: 10, // 设置高度
							symbolKeepAspect: true,
							textStyle: {
								color: '#fff',
								rich: {
									name: {
										align: 'left',
										width: 50,
										fontSize: 12,
										color: '#2A2A2A'
									},
									value: {
										align: 'left',
										width: 50,
										fontSize: 12,
										color: '#00ff00'
									},
									unit: {
										align: 'left',
										width: 30,
										fontSize: 12,
										color: '#00ff00'
									}
								}
							},
							data: optionData.map(item => item.name),
							formatter(name : any) {
								if (optionData && optionData.length) {
									for (let i = 0; i < optionData.length; i++) {
										if (name === optionData[i].name) {
											return (
												'{name| '
												+ (optionData[i].name || name)
												+ '}'
												+ '{value| '
												+ (optionData[i].valueAlias || optionData[i].value)
												+ ' '
												+ '}'
												+ '{unit| '
												+ (optionData[i].unit || 'km')
												+ '}'
											)
										}
									}
								}
							}
						},
						series: [
							{
								name: '管线管网',
								type: 'pie',
								radius: ['50%', '80%'],
								center: ['33%', '50%'],
								data: optionData,
								// emphasis: {
								// },
								itemStyle: {
									shadowBlur: 10,
									shadowOffsetX: 0,
									shadowColor: 'rgba(0, 0, 0, 0.5)'
								},
								label: {
									show: true,
									position: 'inside',
									textStyle: {
										fontSize: 12
									},
									formatter(param : any) {
										// correct the percentage
										return param.percent + '%';
									}
								},

								labelLine: {
									show: false
								},
							}
						]
					}
					chart.setOption(option)
				})
				refLEchart_Diameter_Length.value.init(echarts, (chart : any) => {
					const option = {
						tooltip: {
							trigger: 'axis',
							axisPointer: {
								type: 'cross',
								label: {
									backgroundColor: '#283b56'
								}
							}
						},
						xAxis: [
							{
								type: 'category',
								boundaryGap: true,
								data: xData
							},
						],
						yAxis: [
							{
								type: 'value',
								name: 'km',
								min: 0,
								boundaryGap: [0.2, 0.2]
							},
						],
						series: [
							{
								name: '管长',
								type: 'bar',
								data: yData
							},
						]
					}
					chart.setOption(option)
				})

			} else {
				console.log('统计失败');
			}
		} catch (e) {
			console.log(e)
		}
	}
	const refreshYearPipe = async () => {
		try {
			const res = await getPipeByBuildYear()

			if (res.data.code === 10000) {
				const data = res.data?.result || []
				const xData : string[] = []
				const yData : string[] = []
				data.map((item : any) => {
					xData.push(item.itemname)
					yData.push(item.sumlength)

				})

				refLEchart_Year_Length.value.init(echarts, (chart : any) => {
					const option = {
						tooltip: {
							trigger: 'axis',
							axisPointer: {
								type: 'cross',
								label: {
									backgroundColor: '#283b56'
								}
							}
						},
						xAxis: [
							{
								type: 'category',
								boundaryGap: true,
								data: xData
							},
						],
						yAxis: [
							{
								type: 'value',
								name: 'km',
								min: 0,
								boundaryGap: [0.2, 0.2]
							},
						],
						series: [
							{
								name: '管长',
								type: 'bar',
								data: yData,
								itemStyle: {
									color: '#0064df'
								}

							},
						]
					}
					chart.setOption(option)
				})

			} else {
				console.log('统计失败');
			}
		} catch (e) {
			console.log(e)
		}
	}
	const refreshMaterialLength = async () => {
		try {
			const res = await PipeStatistics({
				layerids: JSON.stringify((props.layerids)),
				group_fields: JSON.stringify(['MATERIAL']),
				statistic_field: 'SHAPE.Len',
				statistic_type: '2',
				where: '1=1'
			})

			if (res.data.code === 10000) {
				const data = res.data?.result?.rows[0]?.rows || []
				const xData : string[] = []
				const yData : string[] = []
				data.map((item : any) => {
					xData.push(item['MATERIAL'])
					yData.push((item['SHAPE.Len'] / 1000).toFixed(4))
				})

				refLEchart_Material_Length.value.init(echarts, (chart : any) => {
					const option = {
						tooltip: {
							trigger: 'axis',
							axisPointer: {
								type: 'cross',
								label: {
									backgroundColor: '#283b56'
								}
							}
						},
						xAxis: [
							{
								type: 'category',
								boundaryGap: true,
								data: xData
							},
						],
						yAxis: [
							{
								type: 'value',
								name: 'km',
								min: 0,
								boundaryGap: [0.2, 0.2]
							},
						],
						series: [
							{
								name: '管长',
								type: 'bar',
								data: yData
							},
						]
					}
					chart.setOption(option)
				})

			} else {
				console.log('统计失败');
			}
		} catch (e) {
			console.log(e)
		}
	}
	const refreshData = async () => {
		refreshTotalLength()
		refreshTotalCount()
		refreshYearPipe()
		refreshMaterialLength()
	}
	onMounted(() => {

		refreshData()
	})
</script>

<style lang="scss" scoped>
	.pipe-detail {

		padding-bottom: 140rpx;

		.pipe-detail-item {
			.title {
				font-size: 32rpx;
				margin: 32rpx 0 0;
			}
		}

		.pipe-detail-total {
			.total-blocks {
				width: 100%;
				display: flex;
				padding: 24rpx;
				justify-content: flex-start;

				.total-block {
					background-color: #0073ff;
					padding: 10rpx;
					color: #fff;
					width: 280rpx;
					height: 120rpx;
					margin-right: 24rpx;
					text-align: center;

					.text,
					.value {
						line-height: 50rpx;
					}
				}
			}
		}

		.pipe-detail-chart {
			.chart-box {
				height: 480rpx;
			}
		}
	}
</style>