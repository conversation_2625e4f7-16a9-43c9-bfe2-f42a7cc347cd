<template>
	<view class="main">
		<u-navbar fixed placeholder safeAreaInsetTop bgColor="#FFFFFF" title="巡检查询" :autoBack="true" leftIconSize="20">
			<template #right>
				<view class="nv-right" @click="state.screenShow=true">
					<image src="/static/img/icons/shaixuan-black.png" style="height: 48rpx;width: 48rpx;"></image>
				</view>
			</template>
		</u-navbar>
		<u-sticky bg-color="#FFFFFF">
			<u-tabs :list="state.tabs" active-color="#3862F8" v-model="state.currentTab" bg-color="#FFFFFF"
				:offset="[0,120]" :is-scroll="false" @change="changeStatus" count="count">
			</u-tabs>
		</u-sticky>
		<!-- <scroll-view :lower-threshold="50" :refresher-threshold="50" scroll-y refresher-enabled
			refresher-default-style="white" :refresher-triggered="triggered" @refresherrefresh="onRefresh"
			@scrolltolower="showMoreData"> -->
		<view class="card-box" v-for="(data,index) in tableData" :key="index" @click="toDetail(data)">
			<view class="flex-between hand">
				<view class="hand-title">
					<text>{{data.name}}</text>
				</view>
				<view class="status">
					{{data.statusName || '处理中'}}
				</view>
			</view>
			<view class="table">
				<view class="info">
					<text>任务编号：</text> <text>{{data.code}}</text>
				</view>
				<view class="info flex-center">
					<text>计划类型：</text>
					<view class="bg">
						<text>{{data.districtAreaName}}</text>
					</view>
				</view>
				<view class="info">
					<text>派发时间：</text> <text>{{data.createTime}}</text>
				</view>
				<view class="info">
					<text>开始时间：</text> <text>{{data.beginTime}}</text>
				</view>
				<view class="info">
					<text>结束时间：</text> <text>{{data.endTime}}</text>
				</view>
				<view class="info">
					<text>是否需要反馈：</text>
					<text>{{data.isNeedFeedback===true?'是':data.isNeedFeedback===false?'否':'-'}}</text>
				</view>
				<view class="info">
					<text>任务描述：</text> <text>{{data.remark}}</text>
				</view>
			</view>
		</view>
		<view class="popup" v-if="state.screenShow">
			<u-popup mode="right" width="528" v-model="state.screenShow" closeOnClickOverlay overlay
				@close="state.screenShow = false">
				<view class="screen-list">
					<u-form :model="screenForm" ref="form1" :label-style="{'color':'#91949F'}" border-bottom  input-align="right" 
						label-width="180">
						<u-form-item label="关键词：" prop="keyword">
							<u-input fontSize="14" placeholder="任务编号/任务名称/备注" v-model="screenForm.keyword"
								:placeholderStyle="placeholderStyle">
							</u-input>
						</u-form-item>
						<u-form-item label="巡检人：" prop="userName">
							<input placeholder="巡检人" inputmode="none" placeholder-class="placeholderClass" v-model="screenForm.userName" :clearable="true"
								@click="toChooseUser">
							</input>
							<u-icon name="close-circle" @click="clearUser"></u-icon>
						</u-form-item>
						<u-form-item label="开始日期：" prop="beginTimeFrom">
							<input fontSize="14" @click="openCalendar('beginTimeFrom')" inputmode="none" placeholder-class="placeholderClass" placeholder="点击选择"
								v-model="screenForm.beginTimeFrom">
							</input>
							<template #right>
								<u-icon name="calendar" color="#91949F" size="34"></u-icon>
							</template>
						</u-form-item>
						<u-form-item label="截止日期：" prop="beginTimeTo">
							<input fontSize="14" @click="openCalendar('beginTimeTo')" inputmode="none" placeholder-class="placeholderClass" placeholder="点击选择"
								v-model="screenForm.beginTimeTo">
							</input>
							<template #right>
								<u-icon name="calendar" color="#91949F" size="34"></u-icon>
							</template>
						</u-form-item>
					</u-form>
				</view>
				<view class="popup-button">
					<u-button type="primary" color="#3862F8" @click="submitScreen">确定</u-button>
				</view>
			</u-popup>
		</view>
		<u-loadmore bg-color="F9F9F9" :status="state.status" loading-text="努力加载中" loadmore-text="加载更多"
			nomore-text="没有了更多" />
		<!-- </scroll-view> -->
		<u-calendar v-model="state.dateShow" ref="calendar" @close="state.dateShow=false" @change="chooseDate">
		</u-calendar>
	</view>
</template>

<script lang="ts" setup>
	import {
		onMounted,
		reactive,
		ref
	} from "vue";
	import {
		onPullDownRefresh,
		onReachBottom,
		onShow
	} from '@dcloudio/uni-app'
	import {
		getCircuitTaskList,
		processingAndCompleteCount
	} from '@/common/api/pipeNetworkTask'
	import {
		removeSlash
	} from '@/common/utils/removeIdSlash'
	import {
		useStore
	} from '@/store/index'
	import dayjs from 'dayjs'
	const store = useStore();
	// 选择区域
	const state = reactive < {
		tabs: any,
		activceTab: any,
		dateShow: boolean,
		status: string,
		query: any,
		currentTab: number,
		screenShow: boolean,
		dateType: string
	} > ({
		screenShow: false,
		currentTab: 0,
		tabs: [{
				name: '待处理',
				value: false,
				count: 0
			},
			{
				name: '已完成',
				value: true,
				count: 0
			},
		],
		activceTab: {
			name: '待处理',
			value: false,
			count: 0
		},
		dateShow: false,
		status: 'loading',
		query: {
			page: 1,
			size: 10
		},
		dateType: ''
	})
	const placeholderStyle = ref < any > ('textAlign:right')
	// 选择区域
	let screenForm = reactive < any > ({})
	const triggered = ref < boolean > ()
	const tableData = ref < any > ([])
	// 
	const toDetail = (params ? : any) => {
		uni.navigateTo({
			url: './taskDetail/index?taskId=' + params.id + '&areaId=' + params.districtAreaId +
				'&taskCode=' +
				params.code + '&type=' + params.status,
		})
	}
	// 选择日期
	const chooseDate = (date: any) => {
		console.log(date)
		state.dateShow = false
		screenForm[state.dateType] = dayjs(date.result).format('YYYY-MM-DD')
	}

	const openCalendar = (type: string) => {
		state.dateType = type
		state.dateShow = true
	}
	// 加载更多
	const showMoreData = async () => {
		state.status = 'loading'
		await inspectionList()
	} ///
	const clearUser = () => {
		screenForm.userName = ''
		screenForm.receiveUserId = ''
	}
	// 确定搜索
	const submitScreen = () => {
		tableData.value = []
		state.screenShow = false
		screenForm.page = 1
		onRefresh()
	}

	// 下拉刷新
	const onRefresh = async () => {
		triggered.value = true
		state.query.page = 1
		await inspectionList()
	}
	// 切换巡检状态数据
	const changeStatus = async (index: number) => {
		state.activceTab = state.tabs[index]
		state.query.page = 1
		tableData.value = []
		await inspectionList()
	}
	const toChooseUser = () => {
		uni.$u.route({
			url: 'pages/userTree/organization',
		})
	}

	// 选择处理人
	const chooseUser = (user: any) => {
		if (user) {
			screenForm.userName = user.firstName
			screenForm.receiveUserId = removeSlash(user.id?.id)
		}
	}

	// 巡检列表
	const inspectionList = async () => {
		state.status = 'loadmore'
		state.query = {
			...state.query,
			isReceived: true,
			isComplete: state.activceTab.value,
			...screenForm
		}
		console.log('111111',state.query)
		const res = await getCircuitTaskList(state.query)
		const data = res.data?.data?.data
		const total = res.data?.data?.total
		if (state.query.page === 1) {
			tableData.value = data
		} else {
			tableData.value = tableData.value.concat(data)
		}
		if (data.length > 0 && total > tableData.value.length) {
			state.query.page += 1
			state.status = 'loadmore'
		} else {
			state.status = 'nomore'
		}
		triggered.value = false
		uni.stopPullDownRefresh()
	}

	const getCount = async () => {
		const res = await processingAndCompleteCount()
		const data = res.data?.data
		state.tabs[0].count = data.processingCount
	}

	onReachBottom(async () => {
		showMoreData()
	})

	onPullDownRefresh(async () => {
		onRefresh()
	})
	
	onMounted(()=>{
		screenForm={}
		state.query = {
			page: 1,
			size: 10
		}
		inspectionList()
		getCount()
	})
	onShow(async () => {
		
		let {
			userData
		} = store;
		if (userData) {
			chooseUser(userData)
		}
	})
</script>

<style lang="scss" scoped>
	.card-box {
		width: 686rpx;
		margin: 20rpx auto;
		border-radius: 16rpx;
		padding: 22rpx 28rpx;
		background-color: #FFFFFF;

		.hand {
			border-left: 4rpx solid #3862F8;

			.hand-title {
				height: 42rpx;
				line-height: 42rpx;

				text {
					font-size: 28rpx;
					margin-left: 16rpx;
					color: #060F27;
					font-weight: 600;
				}
			}

			.status {
				color: #91949F;
			}
		}

		.table {
			margin-top: 24rpx;

			.info {
				font-size: 24rpx;
				padding-bottom: 18rpx;

				text {
					&:nth-child(1) {
						color: #91949F;
					}

					&:nth-child(2) {
						flex: 1;
					}
				}

				.bg {
					padding: 2rpx 12rpx;
					background: rgba(56, 98, 248, 0.2);
					border-radius: 8rpx;

					text {
						color: #3862F8;
					}
				}
			}
		}
	}


	.popup {
		width: 100%;
		height: 100%;
		position: relative;
		padding: 0 34rpx;


		.popup-button {
			width: 94%;
			position: absolute;
			bottom: 40rpx;
			left: 0;
			right: 0;
			margin: 0 auto;
		}

		::v-deep .placeholderClass {
			font-size: 28rpx;
		}

		.screen-list {
			padding-top: 222rpx;
		}
	}


	::v-deep .u-form-item__body {
		padding: 8rpx 34rpx;
	}

	::v-deep.u-form-item {
		padding: 0;
	}
</style>
