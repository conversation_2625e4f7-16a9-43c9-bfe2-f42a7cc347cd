<template>
	<view class="data-mian">
		<view class="title">
			查看详情
		</view>
		<view class="date">
			<u-form labelPosition="left">
				<u-form-item label="开始日期：" :labelStyle="{'color':'#91949F'}" borderBottom labelWidth="80"
					@click="chooseStart('start')">
					<u-input placeholder="选择开始日期" readonly v-model="form.start" border="bottom"></u-input>
				</u-form-item>
				<u-form-item label="结束日期：" :labelStyle="{'color':'#91949F'}" borderBottom labelWidth="80"
					@click="chooseStart('end')">
					<u-input placeholder="选择结束日期" readonly v-model="form.end" border="bottom"></u-input>
				</u-form-item>
				<u-form-item label="时间间隔：" :labelStyle="{'color':'#91949F'}" borderBottom labelWidth="80"
					@click="typeShow=true">
					<u-input placeholder="选择时间间隔" readonly v-model="form.type" border="bottom"></u-input>
				</u-form-item>
			</u-form>
			<view style="margin-top: 10rpx;">
				<u-button type="primary" color="#3862F8" @click="submit">查询</u-button>
			</view>
		</view>
		<view class="button-tabs flex-center">
			<view :class="['b-tab',{'b-check-tab':state.activeName==='list'}]" @click="state.activeName='list'">
				列表模式
			</view>
			<view :class="['b-tab',{'b-check-tab':state.activeName==='echart'}]" @click="state.activeName='echart'">
				图表模式
			</view>
		</view>
		<view class="d-content">
			<view class="" v-if="state.activeName==='list'">
				<view class="title-bold" style="padding-left:32rpx;">
					详情列表
				</view>
				<view class="list">
					<scroll-view scroll-x scroll-y>
						<!-- <wyb-table first-col-bg-color="#FFFFFF" :show-vert-border="false" header-bg-color="#FFFFFF"
							first-line-fixed ref="table" width="100%" :headers="props.headers"
							:contents="props.tableData" height="600rpx" /> -->
						<u-table font-size="30" padding="14rpx">
							<u-tr class="u-tr">
								<u-th class="u-th" v-for="(item,index) in props.headers" :key="index" :width="item.width+'rpx'">
									{{item.label}}
								</u-th>
							</u-tr>
							<u-tr class="u-tr" v-for="(item,index) in props.tableData" :key="index">
								<u-td class="u-td" v-for="(key,index) in props.headers" :key="index" :width="key.width+'rpx'">
									{{item[key.key] || '0'}}
								</u-td>
							</u-tr>
						</u-table>
						
					</scroll-view>
				</view>
			</view>

			<view class="ucharts" v-if="state.activeName==='echart'">
				<!-- 通用折现图表 -->
				<view class="detail">
					<view class="flex-between" style="margin-bottom: 20rpx;">
						<view class="title-bold flex-center label" @click="showMrore">
							{{props.currentProperty.label}}
							<view v-if="propertyList.length>0">
								<u-icon name="arrow-down" color="#060F27" bold size="12"></u-icon>
							</view>
						</view>
						<view class="tabs flex-between" v-if="props.uchartsConfig">
							<view v-for="(tab,i) in props.uchartsConfig.tabs" :key="i"
								:class="['tab',{'check-tab':activeTab.value===tab.value}]" @click="checkActiveTab(tab)">
								{{tab.title}}
							</view>
						</view>
					</view>
					<view class="bottom">
						<view class="y-title flex-between">
							<view>
								{{props.currentProperty.unit?' 单位:'+props.currentProperty.unit:''}}
							</view>
							<view class="flex-center">
								<view class="color-view" :style="{'background-color':activeTab.color}"></view>
								<view style="color:#B2B7C7;">{{activeTab.title}}</view>
							</view>
						</view>
						<view class="line-ucharts">
							<!-- <qiun-data-charts type="area" background="##FFFFFF" :onzoom="true" :opts="opts"
								:ontouch="true" :canvas2d="true" :chartData="chartData"></qiun-data-charts> -->
						</view>
					</view>
				</view>
			</view>
		</view>
		<!-- 时间选择器 -->
		<!-- <u-datetime-picker v-model="today" @cancel="show=false" :show="show" @close="show=false" mode="date"
			@confirm="confirmDate">
		</u-datetime-picker> -->
		<u-calendar v-model="show" ref="calendar" @change="confirmDate">
		</u-calendar>
		<!-- 查询类型选择器 -->
		<u-picker :show="typeShow" @cancel="typeShow=false" @close="typeShow=false" keyName="label"
			:columns="state.queryTypeList" @confirm="confirmType"></u-picker>
		<!-- 站点属性选择器 -->
		<u-picker :show="pShow" @cancel="pShow=false" @close="pShow=false" keyName="label" :columns="propertyList"
			@confirm="confirmProperty"></u-picker>
	</view>
</template>

<script lang="ts" setup>
	import wybTable from './wyb-table/wyb-table.vue'
	import dayjs from 'dayjs'
	import {
		onMounted,
		reactive,
		ref
	} from "vue";
	const emit = defineEmits(['onQuery']);
	const props = defineProps({
		headers: Array,
		tableData: Array,
		uchartsConfig: Object,
		currentProperty: Object,
		propertyList: Array
	})
	const today = ref(dayjs().format('YYYY-MM-DD'))
	const state = reactive < {
		activeName: string,
		showDateType: string,
		queryTypeList: any[]
	} > ({
		activeName: 'list',
		showDateType: '',
		queryTypeList: [{
			label: '1分钟',
			value: '1m'
		}, {
			label: '15分钟',
			value: '15m'
		}, {
			label: '30分钟',
			value: '30m'
		}, ]
	})
	const show = ref < boolean > (false)
	const pShow = ref < boolean > (false)
	const typeShow = ref < boolean > (false)

	let activeTab = ref < any > (props.uchartsConfig.tabs[1])
	const checkActiveTab = (tab: any) => {
		activeTab.value = tab
		console.log(activeTab)
	}
	const form = reactive < any > ({
		start: today.value,
		end: today.value,
		type: '15m'
	})

	let propertyList = reactive < any > ([])

	// 提交
	const confirmDate = (date: any) => {
		show.value = false
		if (state.showDateType === 'start') {
			form.start = date.result
		} else {
			form.end = date.result
		}
	}
	// confirmProperty
	const confirmProperty = (data: any) => {
		console.log(data)
		pShow.value = false
	}
	//confirmType
	const confirmType = (data: any) => {
		form.type = data.value
		typeShow.value = false
	}

	const submit = () => {
		console.log(form)
		emit('onQuery', form);
	}
	// 选择更多属性
	const showMrore = () => {
		if (propertyList.length > 0) {
			pShow.value = true
		}
	}
	const chooseStart = (type: string) => {
		show.value = true
		state.showDateType = type
	}
	onMounted(async () => {
		propertyList = [props.propertyList]
	})
</script>

<style lang="scss" scoped>
	.data-mian {
		.title {
			width: 100%;
			text-align: center;
			line-height: 88rpx;
			font-size: 34rpx;
			color: #060F27;
		}

		.date {
			box-sizing: border-box;
			width: 686rpx;
			background: #F4F7FE;
			border-radius: 16rpx;
			margin: 0 auto;
			padding: 42rpx 32rpx;

			::v-deep .u-form-item__body {
				padding: 0;
			}
		}
	}

	.button-tabs {
		margin: 0 auto;
		width: 686rpx;

		.b-tab {
			margin: 24rpx auto;
			width: 342rpx;
			height: 64rpx;
			/* color-main */
			background: #F9F9F9;
			border-radius: 16rpx;
			line-height: 64rpx;
			text-align: center;
			color: #91949F;
		}

		.b-check-tab {
			background: #3862F8;
			color: #FFFFFF;
		}
	}

	.d-content {
		height: 788rpx;
		background: #FBFBFB;
		margin: 0 auto;
		border-radius: 32rpx 32rpx 0px 0px;
		padding: 24rpx 32rpx;

		.list {
			padding: 20rpx;
		}

		.ucharts {
			height: 40vh;
			width: 100%;

			.detail {
				height: 788rpx;
				background: #FBFBFB;
				margin: 0 auto;
				border-radius: 32rpx 32rpx 0px 0px;
				padding: 0 32rpx;

				.tabs {
					width: 50%;
					height: 46rpx;
					line-height: 46rpx;
					text-align: center;

					.tab {
						width: 88rpx;
						color: #91949F;
					}

					.check-tab {
						width: 88rpx;
						border-radius: 8rpx;
						background-color: #3862F8;
						color: #FFFFFF;
					}
				}

				.bottom {
					background-color: #FFFFFF;
					border-radius: 16rpx;
					padding-bottom: 98rpx;

					.y-title {
						color: #B2B7C7;
						padding: 20rpx 40rpx;

						.color-view {
							width: 20rpx;
							height: 20rpx;
							background-color: #3862F8;
							margin-right: 8rpx;
						}
					}

					.line-ucharts {
						height: 35vh;
						margin: 0rpx auto;
					}
				}
			}
		}
	}
</style>
