import { ref } from 'vue'
export const useTouch = () => {
	let startPageX: number = 0
	let startPageY: number = 0
	let endPageX: number = 0
	let endPageY: number = 0
	const directionX = ref<'left' | 'right' | ''>('')
	const directionY = ref<'up' | "down" | ''>('')
	const touchStart = (e: any) => {
		startPageX = e.changedTouches?.[0].pageX || 0
		startPageY = e.changedTouches?.[0].pageY || 0
	}
	const touchEnd = (e: any) => {
		endPageX = e.changedTouches?.[0].pageX || 0
		endPageY = e.changedTouches?.[0].pageY || 0
		calcX()
		calcY()
	}
	const calcX = () => {
		const isLeft = startPageX - endPageX
		const ignored = Math.abs(isLeft) < 100
		if (!ignored) {
			directionX.value = ignored ? '' : isLeft > 0 ? 'left' : 'right'
		}
	}
	const calcY = () => {
		const isTop = startPageY - endPageY
		const ignored = Math.abs(isTop) < 100
		if (!ignored) {
			directionY.value = isTop > 0 ? 'up' : 'down'
		}
	}
	return {
		touchStart,
		touchEnd,
		directionX,
		directionY
	}
}